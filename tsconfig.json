{"compilerOptions": {"target": "esnext", "typeRoots": ["src/globalDeclare"], "module": "esnext", "strict": false, "jsx": "preserve", "importHelpers": true, "isolatedModules": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowJs": true, "allowSyntheticDefaultImports": true, "sourceMap": false, "baseUrl": ".", "types": ["vite/client"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "src/components/chinamap/province.js"], "exclude": ["node_modules"]}