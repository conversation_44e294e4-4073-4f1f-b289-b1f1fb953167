/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAffix: typeof import('ant-design-vue/es')['Affix']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AMenuItemGroup: typeof import('ant-design-vue/es')['MenuItemGroup']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APageHeader: typeof import('ant-design-vue/es')['PageHeader']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    Cloudmenu: typeof import('./src/components/cloudmenu/cloudmenu.vue')['default']
    Contextmenu: typeof import('./src/components/contextmenu/index.vue')['default']
    Cropper: typeof import('./src/components/cropper/cropper.vue')['default']
    Empty: typeof import('./src/components/empty/empty.vue')['default']
    Minitopo: typeof import('./src/components/minitopo/minitopo.vue')['default']
    Operatebtn: typeof import('./src/components/operatebtn/operatebtn.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Straighttopo: typeof import('./src/components/minitopo/straighttopo.vue')['default']
    Topo: typeof import('./src/components/topo/topo.vue')['default']
    Version: typeof import('./src/components/version/index.vue')['default']
  }
}
