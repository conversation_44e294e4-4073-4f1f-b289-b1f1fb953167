import { createApp } from "vue";
import App from "./App.vue";
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import router from "./router";
import { getToken } from "./utils/auth";
import {ByteFormat} from "./utils/byteformat";
import * as permission from "./permission";
permission

import vueRightMenu from "vue-right-click-menu-next";


// 国际化
import VueI18n from "@/common/language/index";
import Antd from "ant-design-vue";
import "ant-design-vue/dist/antd.css";
// import 'ant-design-vue/dist/antd.dark.css';

import "@/styles/index.scss";
import "@/utils/modalclose/index";
// 错误的统一处理
import { handleDel,getlist,handleSave, isShowBtn, modalBindNode } from "./utils/tool";
import { filterMemory, filterGB, filterNumber } from "./utils/format";

// 引入全部图标
import * as Icons from "@ant-design/icons-vue";
const icons: any = Icons;
import { week } from "./utils/dateformat";
import * as echarts from "echarts";
import mitt from "mitt";
import {dispatchEventStroage} from "./utils/tool";
import { getSysconfigInfo } from "./api/backend/systems/sysconfig";
import { BpmnElement } from '@logicflow/extension';
import LogicFlow from "@logicflow/core";
LogicFlow.use(BpmnElement);
// (async function() {
  
// })();
// import VueCropper from 'vue-cropper'
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
const app = createApp(App);

app
  .use(router)
  .use(Antd)
  .use(VueI18n)
  .use(pinia)
  .use(vueRightMenu)
  // .use(VueCropper)
  .use(dispatchEventStroage)
  .mount("#app");
// .use(plugin)

app.config.globalProperties.$filterMemory = filterMemory;
app.config.globalProperties.$filterGB = filterGB;
app.config.globalProperties.$filterNumber = filterNumber;
app.config.globalProperties.$isShowBtn = isShowBtn;
app.config.globalProperties.$handleDel = handleDel;
app.config.globalProperties.$getList = getlist;
app.config.globalProperties.$handleSave = handleSave;
app.config.globalProperties.$week = week();
app.config.globalProperties.$echarts = echarts;
app.config.globalProperties.$mitt = mitt();
app.config.globalProperties.$ByteFormat = ByteFormat;
app.config.globalProperties.modalBindNode = modalBindNode;
// 全局注册icons
Object.keys(icons).forEach(key => {
  app.component(key, icons[key]);
});
app.config.globalProperties.$icons = icons;

// vue3.x废除了过滤器,可以使用此方法
app.config.globalProperties.$filters = {
  timeToDate(val: any) {
    if (val != null) {
      return val.toString().split(" ")[0];
    }
  },
  // 受限图片相当于后台接口，拼接token获取
  imgToken(val: any) {
    return val;
  },
  // 富文本中授权图片处理
  editorToken(val: any) {
    const token = getToken();
    if (val != null) {
      if (val.indexOf("?token=") > -1) {
        val = val.substr(0, val.indexOf("?token="));
      }

      return val + "?token=" + token;
    }
  }
};
