<template>
    <a-page-header :title="route.query.title" class="header-title">
        <template #extra v-if="userlimit">
            <a-tooltip title="查看" v-if="!isViewHide"><EyeTwoTone @click="()=>{emit('view')}" style="color:rgb(24, 144, 255)" /></a-tooltip>
            <a-tooltip title="添加" v-if="!isAddHide"><PlusCircleTwoTone @click="()=>{emit('add')}" /></a-tooltip>
            <a-tooltip title="修改" v-if="!isEditHide"><EditTwoTone @click="()=>{emit('edit')}" /></a-tooltip>
            <a-tooltip title="删除" v-if="!isDeleteHide"><DeleteTwoTone @click="()=>{emit('delete')}" /></a-tooltip>
            <a-tooltip :placement="Initializing ? 'bottom' : 'top'" v-if="isSyncShow">
                <template #title>
                    <span v-if="Initializing"><b>{{createUserName}}</b>正在进行初始化</span>
                    <span v-else>初始化</span>
                </template>
                <a-button type="text" @click="()=>{emit('sync')}" :disabled="Initializing">
                    <SyncOutlined style="color:rgb(24, 144, 255)" :spin="Initializing" />
                </a-button>
            </a-tooltip>
            <a-tooltip title="初始化记录" v-if="isSyncHistoryShow"><ClockCircleTwoTone @click="()=>{emit('sync_history')}" /></a-tooltip>
            <a-tooltip title="配置" v-if="isQuotaShow"><ControlTwoTone @click="()=>{emit('quota')}" /></a-tooltip>
            <a-tooltip title="RC文件" v-if="isRCShow"><DownloadOutlined style="color:rgb(24, 144, 255)" @click="()=>{emit('rc')}" /></a-tooltip>
        </template>
        <slot name="cloud"></slot>
        <slot name="domain"></slot>
    </a-page-header>
</template>
<script lang='ts' setup>
import { userStore } from '@/store/user';
import { computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute()
const props = defineProps({
		isViewHide: Boolean,
		isAddHide: Boolean,
		isEditHide: Boolean,
		isDeleteHide: Boolean,
		isSyncShow: Boolean,
		isSyncHistoryShow: Boolean,
		isQuotaShow: Boolean,
		isRCShow: Boolean,
        Initializing: Boolean,
        createUserName: String
	});
const emit = defineEmits(['add','edit','delete','quota','rc','sync'])
const userlimit = computed(()=>userStore().userInfo.roleIdList.includes(1))
onMounted(() => {})
</script>
<style lang='scss' scoped>
.operate-btn{width: 100%;padding: 10px 10px 0;font-size: 20px;
text-align: right;
.anticon{margin-right: 10px;cursor: pointer;}
}
.header-title{ 
    :deep(.ant-page-header-heading){background-color:#fff};
    :deep(.ant-page-header-heading-extra){line-height: 40px;margin: 0;};
    .anticon{font-size: 20px;margin-right: 10px;cursor: pointer;}
    :deep(.ant-page-header-content){padding-top: 0;}
    }
.ant-btn{padding: 0;}
</style>