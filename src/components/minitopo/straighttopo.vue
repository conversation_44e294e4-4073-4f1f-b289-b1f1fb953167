<template>
    <!-- <div class=''> -->
    <div class="ant-popover ant-popover-placement-right" style="left: 240px; top: 137px;display:none">
        <div class="ant-popover-content">
            <div class="ant-popover-arrow"><span class="ant-popover-arrow-content"></span></div>
            <div class="ant-popover-inner" role="tooltip">
                <div>
                    <div class="ant-popover-title">{{popform.type}}</div>
                    <div class="ant-popover-inner-content">
                        <p data-v-f71753dd="">{{popform.label}}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <svg id="topology" v-if="!isEmpty"></svg>
    <a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    <!-- </div> -->
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import * as d3 from "d3";
import $ from "jquery";
import { Empty } from "ant-design-vue";
import { selectNetworkTopology } from '@/api/backend/devops/network';
import { randomColor } from "@/utils/randomcolor";
import router from '@/router';
const {proxy} = getCurrentInstance();
const isEmpty = ref(false);
const popform = reactive({type:'',label:''});
// 间隔
var smallCap = 50;
var largeCap = 100;
// svg宽
var svgWidth = 0;
// 矩形宽确定
var rectWidth = 15;
// 矩形高确定
var rectHeight = 0;
// 图标宽高
var iconWidth = 20;
var iconHeight = 20;
var smallLineWidth = 1;
var largeLineWidth = 3;


var nodes = [];
var edges = [];
var colorArr = [];
const svg = ref();
// console.log("svg",svg.value)
const drawSvg = (rect_height,svg_width) => {
    svg.value.attr("width",svg_width)
        .attr("height",rect_height+10)
        .style('shape-rendering','crispEdges')
}
const drawRectGroup = (rect_nodes, rect_height, port_nodes) => {
    // 网络数量为零的情况(无网络但有游离设备要单独处理，因为后面的画图都是依赖于网络的分组)
    if(rect_nodes.length <= 0){
        let g = svg.value
            .append("g")
            .attr("id", "n1")
            .attr("class", "network")
        let gc = g.append("g")
            .attr("class","network_container")
    }else{
        let g = svg.value.selectAll("g")
            .data(rect_nodes)
            .enter()
            .append("g")
            .attr("id",(d, i) => 'n'+d.id)
            .attr("class", "network")
            .attr("transform", (d, i) => `translate(${i * largeCap}, 5)`)
        let gc = g.append("g")
            .attr("class","network_container")
        gc.append("rect")
            .attr("class", "network_rect")
            .attr("width",rectWidth)
            .attr("height",rect_height)
            .attr("rx", 7)
            .attr("ry", 7)
            .attr("fill",(d)=>{colorArr[d.id] = randomColor();return colorArr[d.id]})
        gc.append("text")
            .attr("class", "network-name")
            .attr("transform", "rotate(90 0 0)")
            .attr("x", rect_height / 2 + 10)
            .attr("y", -4)
            .text((d, i) => d.label)
    }
    port_nodes.forEach((pitem,pindex)=>{
        pitem.toPortCount = 0;
        pitem.toPortLeftCount = 0;
        pitem.toPortRightCount = 0;
        edges.forEach((item,index)=>{
            if(pitem.id == item.toId){
                pitem.toPortCount++;
                if(!pitem.first){
                    pitem.firstfromId = item.fromId;
                    pitem.first = true;
                    let gd = d3.select("#n"+item.fromId)
                    .append("g")
                    .attr("id", 'n'+pitem.id)
                    .attr("class","device")
                    .attr("transform", `translate(${rectWidth + (largeCap - rectWidth - iconWidth) / 2 }, ${(pindex + 1) * smallCap})`)
                    let gp = gd.append("g")
                    .attr("class","ports")
                    let gdot = gd.append("rect")
                    .attr("class","dot")
                    .attr("width",iconWidth)
                    .attr("height",iconHeight)
                    .attr("rx", 3)
                    .attr("ry", 3)
                    var body = d3.select("body");
                    // let pop = body.selectAll(".ant-popover-placement-right")
                    gdot.on("mouseover",(e)=>{
                        $(".ant-popover-placement-right").css({
                            display: "block",
                            left: e.offsetX + 10,
                            top: e.offsetY - 50,
                        });
                        popform.type = (pitem.type == 'net-route' ? '路由' : '虚机')
                        popform.label = pitem.label;
                        body.on("click",()=>{
                            $(".ant-popover-placement-right").css({
                                display: "none"
                            });
                        })
                    });
                    // 导入不起作用
                    // d3.svg("./route.svg").then((res)=>{
                    //     gd.node().append(res.documentElement)
                    // })
                    let gi = gd.append("g")
                    .attr("class","icon")
                    if(pitem.type == 'net-route'){
                        gi.attr("transform","translate(3.5,3)")
                        gi.append("polygon")
                        .attr("points","12.51,4.23 12.51,0.49 8.77,0.49 9.68,1.4 6.92,4.16 8.84,6.08 11.6,3.3")
                        gi.append("polygon")
                        .attr("points","0.49,8.77 0.49,12.51 4.23,12.51 3.32,11.6 6.08,8.83 4.17,6.92 1.4,9.6")
                        gi.append("polygon")
                        .attr("points","1.85,5.59 5.59,5.59 5.59,1.85 4.68,2.76 1.92,0 0,1.92 2.76,4.68")
                        gi.append("polygon")
                        .attr("points","11.15,7.41 7.41,7.41 7.41,11.15 8.32,10.24 11.08,13 13,11.08 10.24,8.32")
                    }else{
                        gi.attr("transform","translate(5,3)")
                        gi.append("rect")
                        .attr("class","instance_bg")
                        .attr("width",10)
                        .attr("height",13)
                        gi.append("rect")
                        .attr("x",2)
                        .attr("y",1)
                        .attr("fill","#fff")
                        .attr("width",6)
                        .attr("height",2)
                        gi.append("rect")
                        .attr("x",2)
                        .attr("y",4)
                        .attr("fill","#fff")
                        .attr("width",6)
                        .attr("height",2)
                        // 状态，此版本无
                        // gi.append("circle")
                        // .attr("class","active")
                        // .attr("cx",3)
                        // .attr("cy",10)
                        // .attr("r",1.3)
                    }   
                }
                let portTrans = item.fromId - pitem.firstfromId == 0 ? 0 : 20;
                if(portTrans == 0){
                    pitem.toPortLeftCount++;
                    item.LeftCount = pitem.toPortLeftCount;
                }else{
                    pitem.toPortRightCount++;
                    item.rightCount = pitem.toPortRightCount;
                }
                d3.select("#n"+pitem.id)
                    .select("g")
                    .append("g")
                    .attr("id","p" + item.fromId + "to" + item.toId+index)
                    .attr("class","port")
                    .append("line")
                    .attr("class","port_line")
                    .attr('x1',0)
                    .attr('y1',0)
                    .attr('x2',(item.fromId - pitem.firstfromId) * largeCap - ((largeCap - rectWidth - iconWidth) / 2 + portTrans))
                    .attr('y2',0)
                    .attr("stroke", colorArr[item.fromId])
            }
        })
        if(pitem.toPortCount == 0){
            let gd = d3.select(".network")
            .append("g")
            .attr("id", 'n'+pitem.id)
            .attr("class","device")
            .attr("transform", `translate(${rectWidth + (largeCap - rectWidth - iconWidth) / 2 }, ${(pindex + 1) * smallCap})`)
            let gdot = gd.append("rect")
            .attr("class","dot")
            .attr("width",iconWidth)
            .attr("height",iconHeight)
            .attr("rx", 3)
            .attr("ry", 3)
            var body = d3.select("body");
            var tabc = d3.select(".ant-tabs-top-content")
            // let pop = body.selectAll(".ant-popover-placement-right")
            gdot.on("mouseover",(e)=>{
                // console.log(e)
                $(".ant-popover-placement-right").css({
                    display: "block",
                    left: e.offsetX + 15,
                    top: e.offsetY - 46,
                });
                popform.type = (pitem.type == 'net-route' ? '路由' : '虚机')
                popform.label = pitem.label;
                body.on("click",()=>{
                    $(".ant-popover-placement-right").css({
                        display: "none"
                    });
                })
                tabc.on("scroll",()=>{
                    $(".ant-popover-placement-right").css({
                        display: "none"
                    });
                })
            });
            let gi = gd.append("g")
            .attr("class","icon")
            if(pitem.type == 'net-route'){
                gi.attr("transform","translate(3.5,3)")
                gi.append("polygon")
                .attr("points","12.51,4.23 12.51,0.49 8.77,0.49 9.68,1.4 6.92,4.16 8.84,6.08 11.6,3.3")
                gi.append("polygon")
                .attr("points","0.49,8.77 0.49,12.51 4.23,12.51 3.32,11.6 6.08,8.83 4.17,6.92 1.4,9.6")
                gi.append("polygon")
                .attr("points","1.85,5.59 5.59,5.59 5.59,1.85 4.68,2.76 1.92,0 0,1.92 2.76,4.68")
                gi.append("polygon")
                .attr("points","11.15,7.41 7.41,7.41 7.41,11.15 8.32,10.24 11.08,13 13,11.08 10.24,8.32")
            }else{
                gi.attr("transform","translate(5,3)")
                gi.append("rect")
                .attr("class","instance_bg")
                .attr("width",10)
                .attr("height",13)
                gi.append("rect")
                .attr("x",2)
                .attr("y",1)
                .attr("fill","#fff")
                .attr("width",6)
                .attr("height",2)
                gi.append("rect")
                .attr("x",2)
                .attr("y",4)
                .attr("fill","#fff")
                .attr("width",6)
                .attr("height",2)
                // 状态，此版本无
                // gi.append("circle")
                // .attr("class","active")
                // .attr("cx",3)
                // .attr("cy",10)
                // .attr("r",1.3)
            }  
        }else{
            let maxPortCount = Math.max(pitem.toPortLeftCount,pitem.toPortRightCount)
            d3.select("#n"+pitem.id).selectAll("line")
        .attr("stroke-width", Math.ceil(3 / maxPortCount))
            edges.forEach((item,index)=>{
        if(pitem.id == item.toId){
            let portTrans = item.fromId - pitem.firstfromId == 0 ? 0 : 20;
                if(portTrans == 0){
                    d3.select("#p" + item.fromId + "to" + item.toId+index)
                    .attr("transform",`translate(${portTrans},${(iconHeight / (pitem.toPortLeftCount + 1)) * item.LeftCount})`)
                }else{
                    d3.select("#p" + item.fromId + "to" + item.toId+index)
                    .attr("transform",`translate(${portTrans},${(iconHeight / (pitem.toPortRightCount + 1)) * item.rightCount})`)
                }
        }         
            })
        }
    })
};
const initData = async () => {
    svg.value = d3.select("#topology");
    await selectNetworkTopology({ cloudId: router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId })
    // d3.json("./network.json")
    .then((res)=>{
        if(res.code == 0 && (res.data.nodes && res.data.nodes.length > 0)){
            // console.log("res",res)
            nodes = res.data.nodes;
            edges = res.data.edges.sort((a,b)=>{
                if(a.toId == b.toId)
                return a.fromId - b.fromId;
            });
            console.log("edges",edges)
            let nodesnum = 0;
            let rectsnum = 0;
            let rectnodes = [];
            let portnodes = [];
            nodes.forEach((item,index)=>{
                if(item.type == "net-route" || item.type == "net-server"){
                    nodesnum++;
                    item.item = false;
                    portnodes.push(item)
                }
                if(item.type == "net-work"){
                    rectnodes.push(item)
                    rectsnum++;
                }
                if(index >= nodes.length - 1){
                    rectHeight = (nodesnum + 1) * 50;
                    svgWidth = rectsnum*100;
                    // rectHeight小于文字长度
                    // ①只有网络没有设备时网络②有设备无网络，两种情况都需要高度
                    if(rectHeight <= 250){
                        rectHeight = 250;
                    }
                    if(svgWidth <= 100){
                        svgWidth = 100;
                    }
                    drawSvg(rectHeight,svgWidth)
                    console.log("portnodes",portnodes.length)
                    console.log("rectnodes",rectnodes)
                    drawRectGroup(rectnodes, rectHeight, portnodes)
                }
            })
        }else{
            isEmpty.value = true;
        }
        
    })
}
onMounted(() => {
    proxy.$nextTick(()=>{
        initData()
    })
})
</script>
<style lang='scss'>
#topology .network-name{
    font-weight: bold;
    font-size: 13px;
    fill: #fff;
    text-anchor: middle;
}
#topology .dot{
    fill: #fff;
    stroke: #333;
    stroke-width: 3;
}
#topology .icon polygon{
    fill: #333;
}
</style>