<template>
  <div class="ant-popover ant-popover-placement-right" style="left: 240px; top: 137px;display:none">
    <div class="ant-popover-content">
      <div class="ant-popover-arrow"><span class="ant-popover-arrow-content"></span></div>
      <div class="ant-popover-inner" role="tooltip">
        <div>
          <div class="ant-popover-title">{{popform.type}}</div>
          <div class="ant-popover-inner-content">
            <p data-v-f71753dd="">{{popform.label}}</p>
            <!-- <p data-v-f71753dd="">Content</p> -->
          </div>
        </div>
      </div>
    </div>
  </div>
  <div id="mynetwork" class="myChart"></div>
   <!-- v-if="!subnetinfo.isSub" -->
  <!-- <Subnet :info="subnetinfo" ref="subnetRef" v-else /> -->
  <!-- </div> -->
</template>
<script lang='ts' setup>
// 此拓扑图在visjs拓扑图基础上进行改造
// 显示为长条、蓝色节点、连接线的图形(即全部图形)全部由visjs中的nodes节点实现
// 长条、蓝色节点的id为数字形式，连接线id是不能数字化的字符串
// js控制a-popover的css，在拓扑图上做鼠标悬浮提示
// import Subnet from "@/views/backend/devops/subnet/index.vue";
import { onMounted, reactive, ref } from "vue";
import $ from "jquery";
import "vis-network/dist/dist/vis-network.min.css";
import * as vis from "vis-network/dist/vis-network.min";
import "@/assets/iconfont/iconfont.css";
import { randomColor } from "@/utils/randomcolor";
import { selectNetworkTopology } from "@/api/backend/devops/network";
import {quickSort} from "@/utils/quicksort";
import router from "@/router";
const Network = ref();
const subnetRef = ref();
const subnetinfo = reactive({
    isAdd:true,
    isShow:false,
    isInfo:false,
    isSub:false
})
const popform = reactive({type:'',label:''});
const handleSub = (record) => {
    info.isSub = true;
    // const _this = this;
            proxy.$nextTick(()=>{

    subnetRef.value.getList(record.id)
    subnetRef.value.getPortList(record.id)
    subnetRef.value.id = record.id
    Object.assign(subnetRef.value.netinfo,record);
            })
}
const hoverNodeEvent = (network,nodes) => {
  network.on("hoverNode", (props) => {
    nodes.forEach((item,index)=>{
      // 不是连接点、长条的节点(即蓝色节点)
      if(!isNaN(props.node) && (props.node == item.id && item.type != "net-work" && item.type != "child-net-work")){
        popform.label = item.label1;
        if(item.type == 'net-server')
        popform.type = '虚机';
        if(item.type == 'net-route')
        popform.type = '路由';
        $(".ant-popover-placement-right").css({
          display: "block",
          left: props.pointer.DOM.x + 15,
          top: props.pointer.DOM.y + 15,
        });
      }
    })
  });
};
// 鼠标离开节点
const leaveNodeEvent = (network,nodes) => {
  network.on("blurNode", (props) => {
    $(".ant-popover-placement-right").css({
      display: "none"
    });
  });
};
// const clickNodeEvent = (network,nodes) => {
//   network.on('click',(props)=>{
//     console.log('por',props)

//   })
// }
// 加载拓扑
const makeVis = async () => {
  let edges = [];
  let nodes = [];
  let res = await selectNetworkTopology({ cloudId: router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId });
  if (res.code == 0) {
    if (res.data.edges) {
      edges = quickSort(res.data.edges);
    }
    if (res.data.nodes) {
      nodes = res.data.nodes;
    }

    let edgesDistanceArr = [];
    let edgesToArr = [];
    edges.forEach((item, index) => {
      if (item.toNodeType == "net-work" || item.type == "child-net-work") {
        let temp;
        temp = item.fromId;
        item.fromId = item.toId;
        item.toId = temp;
        temp = item.fromNodeType;
        item.fromNodeType = item.toNodeType;
        item.toNodeType = temp;
      }
      edgesDistanceArr[item.fromId] = { length: 0, index: 0 };
      edgesToArr[item.toId] = { length: 1, pre: 0, fromIndex: 0 };
    });

    let nodeServerCount = -1;
    nodes.forEach((item, index) => {
      item.heightConstraint = {};
      item.widthConstraint = {};
      item.shapeProperties = {};
      item.color = {};
      if (item.type == "net-work" || item.type == "child-net-work") {
        item.label = "<b>" + item.label + "</b>";
        nodeServerCount++;
        if (edgesDistanceArr[item.id]) {
          edgesDistanceArr[item.id].index = nodeServerCount;
        }
        item.color.background = randomColor();
        item.shapeProperties.borderRadius = 60;
        // item.heightConstraint.minimum = 500;
        item.widthConstraint = 1;
        item.x = -1300 + nodeServerCount * 150;
        // item.y = -230;
      } else {
        item.label1 = item.label
        item.label = "";
        item.shape = "icon";
        item.icon = {
          face: "Iconfont",
          weight: 5,
          size: 30,
        };
        if (item.type == "net-server") item.icon.code = "\ue609";
        if (item.type == "net-route") item.icon.code = "\ue601";
      }
    });

    edges.forEach((item, index) => {
      
      item.heightConstraint = {};
      item.widthConstraint = {};
      item.color = {};
      edgesDistanceArr[item.fromId].length++;
      nodes.forEach((t, i) => {
        if (item.fromId == t.id) {
          item.color.background = t.color.background;
        }
      });
      if (edgesToArr[item.toId].length <= 1) {
        item.heightConstraint.minimum = 0;
        item.widthConstraint = 48;
        item.y = -500 + edgesDistanceArr[item.fromId].length * 70;
        item.x = -1264 + edgesDistanceArr[item.fromId].index * 150;
        edgesToArr[item.toId].pre = edgesDistanceArr[item.fromId].length;
        edgesToArr[item.toId].fromIndex = edgesDistanceArr[item.fromId].index;

        nodes.forEach((t, i) => {
          if (item.toId == t.id) {
            if (t.type != "net-work" && t.type != "child-net-work") {
              if (edgesToArr[t.id]) {
                t.x = -1262 + edgesDistanceArr[item.fromId].index * 150 + 37;
                t.y = -500 + edgesDistanceArr[item.fromId].length * 70;
              }
            }
          }
        });
      } else {
        item.heightConstraint.minimum = 0;
        item.widthConstraint = (edgesDistanceArr[item.fromId].index - edgesToArr[item.toId].fromIndex-1) * 150 + 48;
        // item.widthConstraint = (edgesDistanceArr[item.fromId].index - 1) * 150 + 48;
        item.x = -1264 + (edgesToArr[item.toId].fromIndex) * 150+item.widthConstraint/2+54;
        if (edgesToArr[item.toId].length <= 2) {
          // console.log("1");
          item.y = -500 + edgesToArr[item.toId].pre * 70;
        } else {
          // console.log("2");
          // edgesDistanceArr[item.fromId].length * 5
          item.y = -500 + edgesToArr[item.toId].pre * 70 + Math.pow(-1, edgesToArr[item.toId].length) * 5;
        }
      }
      edgesToArr[item.toId].length++;
      item.shapeProperties = {};
      item.id = "l" + index;
      item.shapeProperties.borderRadius = 0;
      item.margin = 2;
    });
    let surplus = 0;
    let maxLength = 0;
    nodes.forEach((item, index) => {
      if (item.type == "net-work" || item.type == "child-net-work") {
        if(edgesDistanceArr[item.id]){
          if(edgesDistanceArr[item.id].length > maxLength)
          maxLength = edgesDistanceArr[item.id].length;
        }
          
      }
      if (item.type != "net-work" && item.type != "child-net-work" && !edgesToArr[item.id]) {
        surplus++;
        item.x = -1262 + 37 - 150;
        item.y = -500 + surplus * 70;
      }
    });
     nodes.forEach((item, index) => {
      
       if (item.type == "net-work" || item.type == "child-net-work"){
        // console.log('maxLength',maxLength)
        item.heightConstraint = 50 + maxLength * 70;
        item.y = -500 + ((maxLength+1) * 70)/2 
       }
     })
    nodes = new vis.DataSet(edges.concat(nodes));
    var data = {
      nodes: nodes,
    };
    var container = document.getElementById("mynetwork");
    var options = {
      // autoResize:false,
      layout: {
        randomSeed: 1,
      },
      nodes: {
        chosen: true,
        fixed: true,
        font: {
          multi: true,
          bold: "14px arial white",
        },
        borderWidth: 0,
        shape: "box",
      },
      interaction: {
        hover:true,
        zoomView: true, // 是否能缩放画布
      },
    };
    Network.value = new vis.Network(container, data, options);
    Network.value.once('stabilized', function() {
        Network.value.moveTo({ scale : 1 });
    })
    // clickNodeEvent(Network.value,nodes)
    hoverNodeEvent(Network.value,nodes);
    leaveNodeEvent(Network.value,nodes);
  }
};
onMounted(() => {
  makeVis();
});
</script>
<style lang='scss' scoped>
.myChart {
  height: 720px;
  width: 1640px;
}
// :deep(.ant-popover-placement-right){display: block !important;}
</style>