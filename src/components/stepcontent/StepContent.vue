<template>
    <!-- <div class="steps-content"> -->
        <a-form
            ref="serverForm"
            id="serverForm"
            :model="serverform" 
            :rules="rules"
            :label-col="{offset:1,span:2}" 
            :wrapper-col="{span:20}" 
            :colon="false" 
            label-align="left" 
            scrollToFirstError>
            <div v-show="(info.total - info.current) == 6">
                <slot name="ticket" :serverform="serverform"></slot>
            </div>
            <div v-show="(info.total - info.current) == 5">
                <slot name="cloud" :serverform="serverform"></slot>
            </div>
            <div v-show="(info.total - info.current) == 4">
                <a-form-item label="项目" name="projectName">{{data.projectName}}</a-form-item>
                <a-form-item label="可用域" :name="['ticketOpenstackServerInfoEntity','availabilityZone']">
                    <a-select v-model:value="serverform.ticketOpenstackServerInfoEntity.availabilityZone" @change="(e,option)=>changeSetText(option,'availabilityZone')" :getPopupContainer="triggerNode => triggerNode.parentNode" show-search>
                        <template #notFoundContent v-if="data.zoneloading">
                            <a-spin :spinning="data.zoneloading"></a-spin>
                        </template>
                        <a-select-option v-for="(item,index) in data.zonelist" :key="index" :value="item.id" :label="item.zoneName">{{item.zoneName}}</a-select-option>
                        <!-- <a-select-option value="nova">nova</a-select-option> -->
                    </a-select>
                </a-form-item>
                <a-divider></a-divider>
                <a-form-item label="虚机类型" :name="['ticketOpenstackServerInfoEntity','flavorId']">
                    <span>
                        请合理选择虚机类型。您可以
                        <a @click="()=>data.flavorinfo.isAdd = data.flavorinfo.isShow = true">
                            新建虚机类型 >
                        </a>
                        <FlavorInfo :info="data.flavorinfo" @getlist="flavorFunc(serverform.cloudId,serverform.projectId,data.flavorsearch,data.flavorPagination.current,10)" />
                    </span>
                    <a-space>
                        <a-input-search v-model:value="data.flavorsearch" @search="(value)=>flavorFunc(serverform.cloudId,serverform.projectId,value,1,10)" placeholder="筛选" allow-clear>
                            <template #prefix>
                                名称
                            </template>
                        </a-input-search>
                        vcpus
                        <a-select v-model:value="data.vcpussearch" style="width:100px" :options="uniqueVcpus" @change="flavorFunc(serverform.cloudId,serverform.projectId,data.flavorsearch,1,10)">
                        </a-select>
                        内存
                        <a-select v-model:value="data.ramsearch" style="width:100px" :options="uniqueRam" @change="flavorFunc(serverform.cloudId,serverform.projectId,data.flavorsearch,1,10)">
                        
                        </a-select>
                        硬盘
                        <a-input type="number" v-model:value="data.disksearch" :min="1" :step="1" :precision="0" style="width:100px" suffix="GB" @pressEnter="flavorFunc(serverform.cloudId,serverform.projectId,data.flavorsearch,1,10)"></a-input>
                        <!-- <a-select v-model:value="data.disksearch" style="width:100px" allow-clear>
                            <template v-for="(item,index) in uniqueDisk" :key="item.id">
                                <a-select-option :value="item.totalDisk">{{$filterGB(item.totalDisk)}}</a-select-option>
                            </template>
                        </a-select> -->
                    </a-space>
                    <br><br>
                    <a-table
                        size="middle" 
                        :columns="columns.flavor" 
                        :data-source="data.flavorlist" 
                        row-key="id"
                        :row-selection="{selectedRowKeys:serverform.ticketOpenstackServerInfoEntity.flavorId,onChange:(selectedRowKeys, selectedRows)=>onSelectChange(selectedRowKeys, selectedRows,'flavor'),type:'radio',columnTitle:'单选'}"
                        :pagination="data.flavorPagination"
                        @change="(pagination)=>flavorFunc(serverform.cloudId,serverform.projectId,data.flavorsearch,pagination.current,10)"
                        :loading="data.flavorloading"
                        >
                        <template #totalDisk="{record}">
                            {{$filterGB(record.totalDisk)}}
                        </template>
                        <template #ram="{record}">
                            {{$filterMemory(record.ram)}}
                        </template>
                        </a-table>
                        <br>
                    <div v-if="serverform.ticketOpenstackServerInfoEntity.flavorId && serverform.ticketOpenstackServerInfoEntity.flavorId.length > 0">
                        <span>已选：</span>
                        <a-tag @close="close('flavor')" closable>{{data.flavorSelectedRow.name}}</a-tag>
                    </div>
                </a-form-item>
                <a-divider></a-divider>
                <a-form-item label="启动源" :name="['ticketOpenstackServerInfoEntity','source']">
                    <a-radio-group v-model:value="serverform.ticketOpenstackServerInfoEntity.source" @change="sourceChange" button-style="solid">
                        <a-radio-button :value="1">镜像</a-radio-button>
                        <a-radio-button :value="2">虚机快照</a-radio-button>
                        <a-radio-button :value="3">可用卷</a-radio-button>
                        <a-radio-button :value="4">卷快照</a-radio-button>
                    </a-radio-group>
                </a-form-item>
                <a-form-item class="label" label=" " :name="['ticketOpenstackServerInfoEntity','imageId']" v-if="serverform.ticketOpenstackServerInfoEntity.source == 1">
                    <a-radio-group v-model:value="serverform.ticketOpenstackServerInfoEntity.imageOS" @click="osChange" button-style="outline" class="img-btn-group">
                        <a-radio-button v-for="(item,index) in data.imageOSs" :key="item.id" :value="item.dictValue" class="img-btn">
                            <img :src="imageOSs[(item.dictLabel).toLowerCase()] ? imageOSs[(item.dictLabel).toLowerCase()] : imageOSs['其他']" alt="">
                            <p style="font-size:12px">{{item.dictLabel}}</p>
                        </a-radio-button>
                    </a-radio-group>
                    <br>
                    <span>
                        请合理选择镜像。您可以
                        <a @click="()=>{
                            data.imageinfo.isAdd = data.imageinfo.isShow = true;
                            imageDialog.getCode();
                            imageDialog.helper();}">
                            新建镜像 >
                        </a>
                        <ImageInfo ref="imageDialog" :info="data.imageinfo" @getlist="imageFunc(serverform.cloudId,serverform.projectId,'image',serverform.ticketOpenstackServerInfoEntity.imageOS,data.imagesearch,data.imagePagination.current,10)" />
                    </span>
                    <a-input-search v-model:value="data.imagesearch" @search="(value)=>imageFunc(serverform.cloudId,serverform.projectId,'image',serverform.ticketOpenstackServerInfoEntity.imageOS,value,1,10)" placeholder="筛选" allow-clear>
                        <template #prefix>
                            名称
                        </template>
                    </a-input-search>
                    <br><br>
                    <a-table
                        size="middle" 
                        :data-source="data.imagelist" 
                        :columns="columns.image" 
                        row-key="id" 
                        :row-selection="{selectedRowKeys:serverform.ticketOpenstackServerInfoEntity.imageId,onChange:(selectedRowKeys, selectedRows)=>onSelectChange(selectedRowKeys, selectedRows,'image'),type:'radio',columnTitle:'单选'}"
                        :pagination="data.imagePagination"
                        @change="(pagination)=>imageFunc(serverform.cloudId,serverform.projectId,'image',serverform.ticketOpenstackServerInfoEntity.imageOS,data.imagesearch,pagination.current,10)"
                        :loading="data.imageloading"
                        >
                        <template #imageOSText="{record}">
                            {{record.imageOSText ? record.imageOSText : '其他'}}
                        </template>
                        <template #imageSize="{record}">
                            {{(record.imageSize / (1024 * 1024 * 1024)).toFixed(2)}}
                        </template>
                        <template #status="{record}">
                            {{record.statusText}}
                        </template>
                    </a-table>
                    <br>
                    <div v-if="serverform.ticketOpenstackServerInfoEntity.imageId && serverform.ticketOpenstackServerInfoEntity.imageId.length > 0">
                        <span>已选：</span>
                        <a-tag @close="close('image')" closable>{{data.imageSelectedRow.name}}</a-tag>
                    </div>
                </a-form-item>
                <a-form-item class="label" label=" " :name="['ticketOpenstackServerInfoEntity','imageId']" v-if="serverform.ticketOpenstackServerInfoEntity.source == 2">
                    <a-input-search v-model:value="data.snapshotsearch" @search="(value)=>imageFunc(serverform.cloudId,serverform.projectId,'snapshot',null,value,1,10)" placeholder="筛选" allow-clear>
                        <template #prefix>
                            名称
                        </template>
                    </a-input-search>
                    <br><br>
                    <a-table
                        size="middle" 
                        :data-source="data.imagelist" 
                        :columns="columns.image" 
                        row-key="id" 
                        :row-selection="{selectedRowKeys:serverform.ticketOpenstackServerInfoEntity.imageId,onChange:(selectedRowKeys, selectedRows)=>onSelectChange(selectedRowKeys, selectedRows,'image'),type:'radio',columnTitle:'单选'}"
                        :pagination="data.snapshotPagination"
                        @change="(pagination)=>imageFunc(serverform.cloudId,serverform.projectId,'snapshot',null,data.snapshotsearch,pagination.current,10)"
                        :loading="data.imageloading"
                        >
                        <template #imageOSText="{record}">
                            {{record.imageOSText ? record.imageOSText : '其他'}}
                        </template>
                    </a-table>
                    <br>
                    <div v-if="serverform.ticketOpenstackServerInfoEntity.imageId && serverform.ticketOpenstackServerInfoEntity.imageId.length > 0">
                        <span>已选：</span>
                        <a-tag @close="close('image')" closable>{{data.imageSelectedRow.name}}</a-tag>
                    </div>
                </a-form-item>
                <a-form-item class="label" label=" " :name="['ticketOpenstackServerInfoEntity','volumeId']" v-if="serverform.ticketOpenstackServerInfoEntity.source == 3">
                    <span>
                        请合理选择卷。您可以
                        <a @click="()=>{
                            data.volumeinfo.isAdd = data.volumeinfo.isShow = true;
                            nextTick(()=>{
                                volumeDialog.helper()
                            })}">
                            新建卷 >
                        </a>
                        <VolumeInfo :info="data.volumeinfo" ref="volumeDialog" @getlist="volumeFunc(serverform.cloudId,serverform.projectId,data.volumesearch,data.volumePagination.current,10)" />
                    </span>
                    <a-input-search v-model:value="data.volumesearch" @search="(value)=>volumeFunc(serverform.cloudId,serverform.projectId,value,1,10)" placeholder="筛选" allow-clear>
                        <template #prefix>
                            名称
                        </template>
                    </a-input-search>
                    <br><br>
                    <a-table
                        size="middle" 
                        :data-source="data.volumelist" 
                        :columns="columns.volume" 
                        row-key="id" 
                        :row-selection="{selectedRowKeys:serverform.ticketOpenstackServerInfoEntity.volumeId,onChange:(selectedRowKeys, selectedRows)=>onSelectChange(selectedRowKeys, selectedRows,'volume'),type:'radio',columnTitle:'单选'}"
                        :pagination="data.volumePagination"
                        @change="(pagination)=>volumeFunc(serverform.cloudId,serverform.projectId,data.volumesearch,pagination.current,10)"
                        :loading="data.volumeloading">
                        <template #imageSize="{record}">
                            {{(record.imageSize / (1024 * 1024 * 1024)).toFixed(2)}}
                        </template>
                        <template #status="{record}">
                            {{record.statusText}}
                        </template>
                    </a-table>
                    <br>
                    <div v-if="serverform.ticketOpenstackServerInfoEntity.volumeId && serverform.ticketOpenstackServerInfoEntity.volumeId.length > 0">
                        <span>已选：</span>
                        <a-tag @close="close('volume')" closable>{{data.volumeSelectedRow.name}}</a-tag>
                    </div>
                </a-form-item>
                <a-form-item class="label" label=" " :name="['ticketOpenstackServerInfoEntity','volumeSnapShotId']" v-if="serverform.ticketOpenstackServerInfoEntity.source == 4">
                    <a-input-search v-model:value="data.volumeSnapShotsearch" @search="(value)=>volumeSnapShotFunc(serverform.cloudId,serverform.projectId,value,1,10)" placeholder="筛选" allow-clear>
                        <template #prefix>
                            名称
                        </template>
                    </a-input-search>
                    <br><br>
                    <a-table
                        size="middle" 
                        :data-source="data.volumeSnapShotlist" 
                        :columns="columns.volumeSnapShot" 
                        row-key="id" 
                        :row-selection="{selectedRowKeys:serverform.ticketOpenstackServerInfoEntity.volumeSnapShotId,onChange:(selectedRowKeys, selectedRows)=>onSelectChange(selectedRowKeys, selectedRows,'volumeSnapShot'),type:'radio',columnTitle:'单选'}"
                        :pagination="data.volumeSnapShotPagination"
                        @change="(pagination)=>volumeSnapShotFunc(serverform.cloudId,serverform.projectId,data.volumeSnapShotsearch,pagination.current,10)"
                        :loading="data.volumeSnapShotLoading">
                        <template #statusText="{record}">
                            {{record.statusText?record.statusText : '-'}}
                        </template>
                    </a-table>
                    <br>
                    <div v-if="serverform.ticketOpenstackServerInfoEntity.volumeSnapShotId && serverform.ticketOpenstackServerInfoEntity.volumeSnapShotId.length > 0">
                        <span>已选：</span>
                        <a-tag @close="close('volumeSnapShot')" closable>{{data.volumeSnapShotSelectedRow.name}}</a-tag>
                    </div>
                </a-form-item>
                <a-divider></a-divider>
                <a-form-item label="系统盘" :name="['ticketOpenstackServerInfoEntity','volList',0,'type']" extra="根磁盘大小受虚机类型、镜像等的最小磁盘限制。" :rules="[{required:true, message:'请选择系统盘类型',trigger:'change'}]">
                    <div style="display:inline-block;width: calc(100% - 46px);">
                    <a-row>
                        <a-col :span="8" style="padding:0 12px">
                            <span style="margin-right:10px">类型</span>
                            <a-select v-model:value="serverform.ticketOpenstackServerInfoEntity.volList[0].type" @change="()=>handleValidate(['ticketOpenstackServerInfoEntity','volList',0,'type'])" placeholder="请选择类型" :getPopupContainer="triggerNode => triggerNode.parentNode" style="max-width:80%">
                                <template #notFoundContent v-if="data.volumeTypeloading">
                                    <a-spin :spinning="data.volumeTypeloading"></a-spin>
                                </template>
                                <a-select-option v-for="(item,index) in data.volumeTypelist" :key="item.id" :value="item.typeName">{{item.typeName}}</a-select-option>
                            </a-select>
                        </a-col>
                        <a-col :span="16" style="padding:0 12px">
                            <span style="margin-right:10px">容量</span>
                            <a-input-number v-model:value="serverform.ticketOpenstackServerInfoEntity.volList[0].size" :formatter="(value)=>Number(value) == 0 ? 1 : Number(value)" :defaultValue="1" :min="1" :step="1" :precision="0"></a-input-number> 
                            <span style="margin-right:40px"> GiB</span>
                        <!-- </a-col>
                        <a-col :span="8"> -->
                            <a-checkbox v-model:checked="serverform.ticketOpenstackServerInfoEntity.volList[0].deleteOnTermination"/> 随虚机删除
                        </a-col>
                    </a-row>
                    </div>
                        <a-button></a-button>
                </a-form-item>
                <template v-for="(item,index) in serverform.ticketOpenstackServerInfoEntity.volList">
                    <a-form-item :label="index == 1 ? '数据盘' : ' '" :name="['ticketOpenstackServerInfoEntity','volList',index,'type']" v-if="item.key > 1" :extra="index == serverform.ticketOpenstackServerInfoEntity.volList.length - 1 ? '虚机挂载太多磁盘会影响读写性能，建议不要超过 16 块。' : null" :rules="[{validator:validatorDataDisk,trigger:'change'}]" :colon="false">
                        <div style="display:inline-block;width: calc(100% - 46px);">
                            <a-row>
                                <a-col :span="8" style="padding:0 12px">
                                    <span style="margin-right:10px">类型</span>
                                    <a-select v-model:value="item.type" @change="()=>handleValidate(['ticketOpenstackServerInfoEntity','volList',index,'type'])" placeholder="请选择类型" :getPopupContainer="triggerNode => triggerNode.parentNode" style="max-width:80%">
                                        <a-select-option v-for="(it,ii) in data.volumeTypelist" :key="it.id" :value="it.typeName">{{it.typeName}}</a-select-option>
                                    </a-select>
                                </a-col>
                                <a-col :span="10" style="padding:0 12px">
                                    <span style="margin-right:10px">容量</span>
                                    <a-input-number v-model:value="item.size" :formatter="(value)=>Number(value) == 0 ? 1 : Number(value)" :defaultValue="1" :min="1" :step="1" :precision="0"></a-input-number> 
                                    <span style="margin-right:40px"> GiB</span>
                                <!-- </a-col>
                                <a-col :span="8"> -->
                                    <a-checkbox v-model:checked="item.deleteOnTermination"/> 随虚机删除
                                </a-col>
                                <a-col :span="1">
                                    <a-button type="link" @click="()=>{serverform.ticketOpenstackServerInfoEntity.volList.splice(index,1);}"><MinusCircleFilled /></a-button>
                                </a-col>
                            </a-row>
                        </div>
                    </a-form-item>
                </template>
                
                <a-form-item :label="serverform.ticketOpenstackServerInfoEntity.volList.length > 1 ? ' ' : '数据盘'" :colon="false">
                    <a-button type="link" @click="pushVolList"><PlusCircleFilled />添加数据盘</a-button>
                </a-form-item>
            </div>
            <div v-show="(info.total - info.current) == 3">
                <a-form-item label="网络" :name="['ticketOpenstackServerInfoEntity','networkIds']" :rules="[{type:'array',required:(!serverform.ticketOpenstackServerInfoEntity.portIds || (serverform.ticketOpenstackServerInfoEntity.portIds && serverform.ticketOpenstackServerInfoEntity.portIds.length <= 0)), message:'请选择网络',trigger:'change'}]">
                    <span>
                        请合理规划虚拟网卡所属的网络和子网。您可以
                        <a @click="()=>{
                            data.networkinfo.isAdd = data.networkinfo.isShow = true;
                            nextTick(()=>{
                                // networkDialog.value.cloudform.projectId = Number(route.query.projectId);
                                networkDialog.setInitInfo();
                            })}">
                            新建网络/子网 > 
                        </a>
                        <NetworkInfo ref="networkDialog" :info="data.networkinfo" @getlist="networkFunc(serverform.cloudId,serverform.projectId,data.networksearch,data.networkPagination.current,10)" />
                    </span>
                    <!-- <a-tabs v-model:activeKey="activeKey" :animated="false">
                        <a-tab-pane key="1" tab="当前项目网络">
                            <a-input-search></a-input-search>
                            <br><br>
                            <a-table></a-table>
                        </a-tab-pane>
                        <a-tab-pane key="2" tab="共享网络" force-render>
                            <a-input-search></a-input-search>
                            <br><br>
                            <a-table></a-table>
                        </a-tab-pane>
                        <a-tab-pane key="3" tab="外部网络">
                            <a-input-search></a-input-search>
                            <br><br>
                            <a-table></a-table>
                        </a-tab-pane>
                        <a-tab-pane key="4" tab="所有网络">
                            <a-input-search></a-input-search>
                            <br><br>
                            <a-table></a-table>
                        </a-tab-pane>
                    </a-tabs> -->
                    <a-input-search v-model:value="data.networksearch" @search="(value)=>networkFunc(serverform.cloudId,serverform.projectId,value,1,10)" placeholder="筛选" allow-clear>
                        <template #prefix>
                            名称
                        </template>
                    </a-input-search>
                    <br><br>
                    <a-table
                        :data-source="data.networklist" 
                        :columns="columns.network" 
                        row-key="id" 
                        :row-selection="{selectedRowKeys:serverform.ticketOpenstackServerInfoEntity.networkIds,onChange:(selectedRowKeys, selectedRows)=>onSelectChange(selectedRowKeys, selectedRows,'network'),columnTitle:'多选'}" 
                        size="middle" 
                        :pagination="data.networkPagination"
                        @change="(pagination)=>networkFunc(serverform.cloudId,serverform.projectId,data.networksearch,pagination.current,10)"
                        :loading="data.networkloading"
                        bordered>
                    </a-table>
                    <br>
                    <div v-if="serverform.ticketOpenstackServerInfoEntity.networkIds && serverform.ticketOpenstackServerInfoEntity.networkIds.length > 0">
                        <span>已选：</span>
                        <a-tag @close="close('network',index,item.id)" v-for="(item,index) in data.networkSelectedRow" :key="item.id" closable>{{item.networkName}}</a-tag>
                    </div>
                </a-form-item>
                <a-form-item class="label" label=" " :name="['ticketOpenstackServerInfoEntity','vnics']" v-if="serverform.ticketOpenstackServerInfoEntity.networkIds && serverform.ticketOpenstackServerInfoEntity.networkIds.length > 0">
                    <div>
                        <div v-for="(item,index) in serverform.ticketOpenstackServerInfoEntity.vnics" :key="item.id" style="margin-bottom:24px">
                            <div style="display:inline-block;width: calc(100% - 46px);">
                                <a-form-item :name="['ticketOpenstackServerInfoEntity','vnics',index,'networkId']" :rules="[{required:true,validator:(rule,value)=>{return vnicsValidator(rule,value,item)}}]">
                                    <a-row>
                                        <a-col :span="8" style="padding:0 12px">
                                            <a-select v-model:value="item.networkId" @select="(value,option)=>subnetFunc(option,item,item.networkId,index,'network')" style="max-width:80%" v-if="index < serverform.ticketOpenstackServerInfoEntity.networkIds.length" :getPopupContainer="triggerNode => triggerNode.parentNode">
                                                <a-select-option :value="data.networkSelectedRow[index].id" :label="data.networkSelectedRow[index].networkName">
                                                    <a-tooltip :title="data.networkSelectedRow[index].networkName">{{data.networkSelectedRow[index].networkName}}</a-tooltip>
                                                </a-select-option>
                                            </a-select>
                                            <a-select v-model:value="item.networkId" @select="(value,option)=>subnetFunc(option,item,item.networkId,index,'network')" style="max-width:80%" v-else :getPopupContainer="triggerNode => triggerNode.parentNode">
                                                <a-select-option v-for="(t,i) in data.networklist" :key="i" :value="t.id" :label="t.networkName">
                                                    <a-tooltip :title="t.networkName">{{t.networkName}}</a-tooltip>
                                                </a-select-option>
                                            </a-select>
                                        </a-col>
                                        <a-col :span="8" style="padding:0 12px" v-if="item.networkId">
                                            <a-select v-model:value="item.method" @select="(value,option)=>subnetFunc(option,item,item.networkId,index,'method')" style="max-width:80%" :getPopupContainer="triggerNode => triggerNode.parentNode">
                                                <a-select-option value="a" label="自动分配地址">自动分配地址</a-select-option>
                                                <a-select-option value="b" label="子网">子网</a-select-option>
                                                <a-select-option value="c" label="IP地址">IP地址</a-select-option>
                                            </a-select>
                                        </a-col>
                                        <a-col :span="8" v-if="item.method == 'b'">
                                            <a-select v-model:value="item.subnetId" @select="(value,option)=>{subnetSelect(option,item,index)}" placeholder="请选择子网" style="max-width:80%" :getPopupContainer="triggerNode => triggerNode.parentNode">
                                                <template #notFoundContent v-if="data.subnetloading">
                                                    <a-spin :spinning="data.subnetloading"></a-spin>
                                                </template>
                                                <a-select-option v-for="(it,ii) in item.subnetlist" :key="it.id" :value="it.id" :label="it.cidr+' ('+it.subnetName+')'">
                                                    <a-tooltip :title="it.cidr+' ('+it.subnetName+')'">{{it.cidr+' ('+it.subnetName+')'}}</a-tooltip>
                                                </a-select-option>
                                            </a-select>
                                        </a-col>
                                        <a-col :span="8" v-if="item.method == 'c'">
                                            <a-input v-model:value="item.ip" @change="()=>{serverForm.validateFields([['ticketOpenstackServerInfoEntity','vnics',index,'networkId']])}" placeholder="请输入固定IP"></a-input>
                                        </a-col>
                                    </a-row>
                                </a-form-item>
                                
                            </div>
                        </div>
                    </div>
                </a-form-item>
                <a-divider></a-divider>
                <a-form-item label="端口" :name="['ticketOpenstackServerInfoEntity','portIds']" :rules="[{type:'array',required:(!serverform.ticketOpenstackServerInfoEntity.networkIds || (serverform.ticketOpenstackServerInfoEntity.networkIds && serverform.ticketOpenstackServerInfoEntity.networkIds.length <= 0)), message:'请选择端口',trigger:'change'}]">
                    端口为您的虚机提供了额外的通信渠道。您可以选择已创建的端口而非网络或者二者都选（端口默认执行本身的安全组规则）。
                    <a-input-search v-model:value="data.portsearch" @search="(value)=>portFunc(serverform.cloudId,serverform.projectId,value,1,10)" placeholder="筛选" allow-clear>
                        <template #prefix>
                            名称
                        </template>
                    </a-input-search>
                    <br><br>
                    <a-table
                        size="middle" 
                        :data-source="data.portlist" 
                        :columns="columns.port" 
                        row-key="id" 
                        :row-selection="{selectedRowKeys:serverform.ticketOpenstackServerInfoEntity.portIds,onChange:(selectedRowKeys, selectedRows)=>onSelectChange(selectedRowKeys, selectedRows,'port'),columnTitle:'多选'}" 
                        :pagination="data.portPagination"
                        @change="(pagination)=>portFunc(serverform.cloudId,serverform.projectId,data.portsearch,pagination.current,10)"
                        :loading="data.portloading"
                        bordered>
                        <template #portIpEntityList={record,index}>
                            {{record.portIpEntityList?.[0]?.ipAddress}}
                        </template>
                    </a-table>
                    <br>
                    <div v-if="serverform.ticketOpenstackServerInfoEntity.portIds && serverform.ticketOpenstackServerInfoEntity.portIds.length > 0">
                        <span>已选：</span>
                        <a-tag @close="close('port',index,item.id)" v-for="(item,index) in data.portSelectedRow" :key="item.id" closable>{{item.portName}}</a-tag>
                    </div>
                </a-form-item>
                <a-divider></a-divider>
                <a-form-item label="安全组" :name="['ticketOpenstackServerInfoEntity','groupIds']">
                    <span>
                        安全组类似防火墙功能，用于设置网络访问控制。您可以
                        <a @click="()=>{data.groupinfo.isAdd = data.groupinfo.isShow = true;}">新建安全组> 
                        </a>
                        <GroupInfo :info="data.groupinfo" @getlist="groupFunc(serverform.cloudId,serverform.projectId,data.groupsearch,data.groupPagination.current,10)" />
                        注：您所用的安全组将作用于虚机的全部虚拟网卡。
                    </span>
                    <a-input-search v-model:value="data.groupsearch" @search="(value)=>groupFunc(serverform.cloudId,serverform.projectId,value,1,10)" placeholder="筛选" allow-clear>
                        <template #prefix>
                            名称
                        </template>
                    </a-input-search>
                    <br><br>
                    <a-table
                        size="middle" 
                        :data-source="data.grouplist" 
                        :columns="columns.group" 
                        row-key="id" 
                        :row-selection="{selectedRowKeys:serverform.ticketOpenstackServerInfoEntity.groupIds,onChange:(selectedRowKeys, selectedRows)=>onSelectChange(selectedRowKeys, selectedRows,'group'),columnTitle:'多选'}" 
                        :pagination="data.groupPagination"
                        @change="(pagination)=>groupFunc(serverform.cloudId,serverform.projectId,data.groupsearch,pagination.current,10)"
                        :loading="data.grouploading"
                        bordered>
                    </a-table>
                    <br>
                    <div v-if="serverform.ticketOpenstackServerInfoEntity.groupIds && serverform.ticketOpenstackServerInfoEntity.groupIds.length > 0">
                        <span>已选：</span>
                        <a-tag @close="close('group',index,item.id)" v-for="(item,index) in data.groupSelectedRow" :key="item.id" closable>{{item.groupName}}</a-tag>
                    </div>
                </a-form-item>
            </div>
            <div v-show="(info.total - info.current) == 2">
                <a-form-item label="名称" :name="['ticketOpenstackServerInfoEntity','serverName']">
                    <template #extra>
                        名称应以大写字母，小写字母或中文开头，最长为128字符，且只包含“0-9, a-z, A-Z, "-'_.”。
                    </template>
                    <a-input v-model:value="serverform.ticketOpenstackServerInfoEntity.serverName"></a-input>
                </a-form-item>
                <a-form-item label="数量" :name="['ticketOpenstackServerInfoEntity','serverNumber']">
                    <a-input-number v-model:value="serverform.ticketOpenstackServerInfoEntity.serverNumber" placeholder="请输入正整数" :min="1" :max="100"></a-input-number> 台
                </a-form-item>
                <!--
                <a-form-item label="登录凭证" :name="['ticketOpenstackServerInfoEntity','credent']">
                    <a-radio-group v-model:value="serverform.ticketOpenstackServerInfoEntity.credent" @change="changeCredent" button-style="solid">
                        <a-radio-button value="pwd">密码</a-radio-button>
                        <a-radio-button value="ssh">SSH密钥对</a-radio-button>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="登录名">
                    root
                </a-form-item>
                <a-form-item label="SSH密钥对" :name="['ticketOpenstackServerInfoEntity','keyPairId']" v-if="serverform.ticketOpenstackServerInfoEntity.credent == 'ssh'">
                    <span>
                        密钥对允许您SSH到您新创建的虚机。 您可以选择一个已存在的密钥对、导入一个密钥对或生成一个新的密钥对。
                        <a @click="()=>{
                            data.keypairinfo.isAdd = data.keypairinfo.isShow = true;
                            data.keypairinfo.isInfo = false;
                            }">新建密钥> </a>
                        <KeypairInfo :info="data.keypairinfo" @getlist="keypairFunc(serverform.cloudId,serverform.projectId,data.keypairsearch,data.keypairPagination.current,10)" />
                    </span>
                    <a-input-search v-model:value="data.keypairsearch" @search="(value)=>keypairFunc(serverform.cloudId,serverform.projectId,value,1,10)" placeholder="筛选" allow-clear>
                        <template #prefix>
                            名称
                        </template>
                    </a-input-search>
                    <br><br>
                    <a-table
                        size="middle" 
                        :data-source="data.keylist" 
                        :columns="columns.keypair" 
                        row-key="id" 
                        :row-selection="{selectedRowKeys:serverform.ticketOpenstackServerInfoEntity.keyPairId,onChange:(selectedRowKeys, selectedRows)=>onSelectChange(selectedRowKeys, selectedRows,'keyPair'),type:'radio',columnTitle:'单选'}" 
                        :pagination="data.keypairPagination"
                        @change="(pagination)=>keypairFunc(serverform.cloudId,serverform.projectId,data.keypairsearch,pagination.current,10)"
                        :loading="data.keypairloading"
                        bordered>

                    </a-table>
                    <br>
                    <div v-if="serverform.ticketOpenstackServerInfoEntity.keyPairId && serverform.ticketOpenstackServerInfoEntity.keyPairId.length > 0">
                        <span>已选：</span>
                        <a-tag @close="close('keyPair')" closable>{{data.keyPairSelectedRow.name}}</a-tag>
                    </div>
                </a-form-item>
                <template v-if="serverform.ticketOpenstackServerInfoEntity.credent == 'pwd'">
                    <a-form-item label="登录密码" :name="['ticketOpenstackServerInfoEntity','password']">
                        <a-input-password v-model:value="serverform.ticketOpenstackServerInfoEntity.password" placeholder="请输入密码" />
                    </a-form-item>
                    <a-form-item label="确认密码">
                        <a-input-password v-model:value="serverform.ticketOpenstackServerInfoEntity.confirmpwd" placeholder="请再次输入密码" />
                    </a-form-item>
                </template> -->
                <!--<a-divider></a-divider>
                <a-form-item label="高级选项">
                    <a-button type="link" v-show="!state.isExpandSenior" @click="()=>{state.isExpandSenior = true}">展开高级选项<CaretDownOutlined /></a-button>
                    <a-button type="link" v-show="state.isExpandSenior" @click="()=>{state.isExpandSenior = false}">隐藏高级选项<CaretUpOutlined /></a-button>
                </a-form-item>
                <template v-if="state.isExpandSenior">
                    <a-form-item label="物理节点" :name="['ticketOpenstackServerInfoEntity','physicalNode']">
                        <a-radio-group v-model:value="serverform.ticketOpenstackServerInfoEntity.physicalNode" button-style="solid">
                            <a-radio-button value="a">智能调度</a-radio-button>
                            <a-radio-button value="b">手动指定</a-radio-button>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item label="指定物理节点" extra="您可以手动指定一台物理节点来创建虚机。" v-if="serverform.ticketOpenstackServerInfoEntity.physicalNode == 'b'">
                        <a-input-search></a-input-search>
                        <br><br>
                        <a-table></a-table>
                        <br>
                        <div>
                            <span>已选：</span>
                            <a-tag closable>Tag 2</a-tag>
                        </div>
                    </a-form-item>
                    <a-form-item label="虚机组" extra="使用虚机组功能，您可以将虚机尽量创建在同一个/不同的物理节点上，满足业务应用的亲和/非亲和性需求。">
                        <a-input-search></a-input-search>
                        <br><br>
                        <a-table></a-table>
                        <br>
                        <div>
                            <span>已选：</span>
                            <a-tag closable>Tag 2</a-tag>
                        </div>
                    </a-form-item>
                    <a-form-item label="用户数据" extra="请确保输入的是能完整正常运行的 shell 脚本。">
                        <a-textarea v-model:value="serverform.ticketOpenstackServerInfoEntity.userData" id="textareaNode" showCount allow-clear></a-textarea>
                        <a-upload :file-list="[]" :before-upload="beforeUpload">
                            <a-button type="link">
                                从本地文件读取
                            </a-button>
                        </a-upload>
                    </a-form-item>
                </template>-->
            </div>
            <!-- <div v-show="(info.total - info.current) == 1">
                <a-form-item label="数量">{{serverform.ticketOpenstackServerInfoEntity.serverNumber}} 台</a-form-item>
                <a-form-item label="配置概览">
                    <a-descriptions>
                        <template #title>
                            实例规格
                            <a-button type="link" @click="emit('set_current',info.total - 4)"><FormOutlined /></a-button>
                        </template>
                        <a-descriptions-item label="启动源">{{emptyText(sourceOptions[serverform.ticketOpenstackServerInfoEntity.source])}}</a-descriptions-item>
                        <a-descriptions-item label="系统盘">
                            <span v-if="serverform.ticketOpenstackServerInfoEntity.volList[0]">{{serverform.ticketOpenstackServerInfoEntity.volList[0].type}} {{serverform.ticketOpenstackServerInfoEntity.volList[0].size}}GiB {{serverform.ticketOpenstackServerInfoEntity.volList[0].deleteOnTermination ? '随虚机删除' : ''}}</span>
                            <span v-else>-</span>
                        </a-descriptions-item>
                        <a-descriptions-item label="可用域">{{emptyText(data.availabilityZone)}}</a-descriptions-item>
                        <a-descriptions-item label="启动源名称">{{emptyText(data.sourceName)}}</a-descriptions-item>
                        <a-descriptions-item label="数据盘">
                            <div v-if="serverform.ticketOpenstackServerInfoEntity.volList.length > 1">
                                <div v-for="(item,index) in serverform.ticketOpenstackServerInfoEntity.volList"><span v-if="index > 0">{{item.type}} {{item.size}}GiB {{item.deleteOnTermination ? '随虚机删除' : ''}}</span></div>
                            </div>
                            <span v-else>-</span>
                            
                        
                        </a-descriptions-item>
                        <a-descriptions-item label="项目">{{data.projectName}}</a-descriptions-item>
                        <a-descriptions-item label="虚机类型">{{data.flavorSelectedRow.vcpus}}VCPU/{{$filterGB(data.flavorSelectedRow.totalDisk)}}/{{$filterMemory(data.flavorSelectedRow.ram)}}</a-descriptions-item>
                    </a-descriptions>
                    <a-divider></a-divider>
                    <a-descriptions>
                        <template #title>
                            网络和安全组
                            <a-button type="link" @click="emit('set_current',info.total - 3)"><FormOutlined /></a-button>
                        </template>
                        <a-descriptions-item label="网络(新建)">
                            <div v-if="serverform.ticketOpenstackServerInfoEntity.vnics.length > 0">
                                <span v-for="(item,index) in serverform.ticketOpenstackServerInfoEntity.vnics" :key="index">{{item.method == 'a' ? `${item.networkName} ${item.methodName}` : `${item.networkName} ${item.methodName} ${item.subnetName} ${item.ip}`}}<br></span>
                            </div>
                            <span v-else>-</span>
                        </a-descriptions-item>
                        <a-descriptions-item label="网络(已创建)">
                            <div v-if="data.portSelectedRow.length > 0">
                                <span v-for="(item,index) in data.portSelectedRow" :key="index">{{item.portName}}<br></span>
                            </div>
                            <span v-else>-</span>
                        </a-descriptions-item>
                        <a-descriptions-item label="安全组">
                            <div v-if="data.groupSelectedRow.length > 0">
                                <span v-for="(item,index) in data.groupSelectedRow" :key="index">{{item.groupName}}<br></span>
                            </div>
                            <span v-else>-</span>
                        </a-descriptions-item>
                    </a-descriptions>
                    <a-divider></a-divider>
                    <a-descriptions>
                        <template #title>
                            管理设置
                            <a-button type="link" @click="emit('set_current',info.total - 2)"><FormOutlined /></a-button>
                        </template>
                        <a-descriptions-item label="名称">{{serverform.ticketOpenstackServerInfoEntity.serverName}}</a-descriptions-item>
                        <a-descriptions-item label="登录凭证">{{credentOptions[serverform.ticketOpenstackServerInfoEntity.credent]}} {{data.keyPairSelectedRow.name}}</a-descriptions-item>
                    </a-descriptions>
                </a-form-item>
            </div> -->
        </a-form>
    <!-- </div> -->
</template>
<script lang='ts' setup>
import router from '@/router';
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref, watch } from 'vue';
import $ from "jquery";
import { message } from 'ant-design-vue';
import { getFlavorList, selectFlavorList } from '@/api/backend/devops/flavor';
import { getKeypairList, selectKeypairList } from '@/api/backend/devops/keypair';
import { selectDictList } from '@/api/backend/systems/dictionary';
import { getImageList, selectImageList } from '@/api/backend/devops/image';
import { getVolumeList, getVolumeSnapShotList, selectVolumeList, selectVolumeTypeList } from '@/api/backend/storage';
import { getNetworkList, getPortlist, selectNetworkList, selectPortList, selectSubnetList } from '@/api/backend/devops/network';
import { getSecugroupList, selectSecugroupList } from '@/api/backend/devops/security';
import { selectZoneList } from '@/api/backend/devops';
import { saveServer, updateServer } from '@/api/backend/devops/server';
import FlavorInfo from "@/views/backend/devops/flavor/info.vue";
import ImageInfo from "@/views/backend/devops/image/info.vue";
import VolumeInfo from "@/views/backend/storage/volume/info.vue";
import NetworkInfo from "@/views/backend/devops/network/info.vue";
import GroupInfo from "@/views/backend/devops/secugroup/info.vue";
import KeypairInfo from "@/views/backend/devops/key/info.vue";
import {regexps} from "@/common/regexp/index";
import { jsReadFiles } from '@/utils/tool';
import { menuStore } from '@/store/menu';
import emiter from '@/utils/Bus';
const menu_store = menuStore();
const {proxy} = getCurrentInstance();
const emit = defineEmits(['set_current','select_cloud']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return{
                current:1
            }
        }
    },
    // ticketform:{
    //     type:Object,
    //     default(){
    //         return {
    //             "cloudId": undefined,
    //             "ticketTitle": "",
    //             "ticketInfo": "",
    //             // "transcatUserId": "",
    //             "osType":"",
    //             "diskSize":"",
    //             "ramSize":"",
    //             "cpuSize":"",
    //             "osRemark":"",
    //             "startTime" : null,
    //             "endTime": null,
    //         }
    //     }
    // }
    // data:{
    //     type:Object,
    //     default(){
    //         return{
    //         }
    //     }
    // }
})
const emptyText = (value) => value ? value : '-';
const uniqueVcpus = ref([{value:'',label:'请选择'},{value:1},{value:2},{value:4},{value:8},{value:16},{value:32},{value:64},{value:128}]);
const uniqueRam = ref([{value:'',label:'请选择'},{value:1,label:'1 GB'},{value:2,label:'2 GB'},{value:4,label:'4 GB'},{value:8,label:'8 GB'},{value:16,label:'16 GB'},{value:32,label:'32 GB'},{value:64,label:'64GB'},{value:128,label:'128 GB'}]);
const uniqueDisk = ref('');

const data = reactive({
    projectName:'',
    zonelist:[],
    zoneloading:false,
    flavorPagination:{
        current:1,
        total:0
    },
    flavorsearch:'',
    flavorinfo:{isAdd:true,isShow:false},
    flavorlist:[],
    flavorloading:false,
    flavorSelectedRow:{},
    imagePagination:{
        current:1,
        total:0
    },
    snapshotPagination:{
        current:1,
        total:0
    },
    imagesearch:'',
    snapshotsearch:'',
    imageinfo:{isAdd:true,isShow:false},
    imageOSs:[],
    imagelist:[],
    imageloading:false,
    imageSelectedRow:{},
    volumePagination:{
        current:1,
        total:0
    },
    volumesearch:'',
    volumeinfo:{isAdd:true,isShow:false},
    volumelist:[],
    volumeloading:false,
    volumeSelectedRow:{},
    volumeTypelist:[],
    volumeTypeloading:false,
    volumeSnapShotPagination:{
        current:1,
        total:0
    },
    volumeSnapShotsearch:'',
    volumeSnapShotlist:[],
    volumeSnapShotLoading:false,
    volumeSnapShotSelectedRow:{},
    networkPagination:{
        current:1,
        total:0
    },
    networksearch:'',
    networkinfo:{isAdd:true,isShow:false},
    networklist:[],
    networkloading:false,
    networkSelectedRow:[],
    subnetlist:[],
    subnetloading:false,
    portPagination:{
        current:1,
        total:0
    },
    portsearch:'',
    groupinfo:{isAdd:true,isShow:false},
    portlist:[],
    portloading:false,
    portSelectedRow:[],
    groupPagination:{
        current:1,
        total:0
    },
    groupsearch:'',
    grouplist:[],
    grouploading:false,
    groupSelectedRow:[],
    keypairPagination:{
        current:1,
        total:0
    },
    keypairsearch:'',
    keypairinfo:{isAdd:true,isShow:false},
    keylist:[],
    keypairloading:false,
    keyPairSelectedRow:{},
    vcpussearch:'',
    ramsearch:'',
    disksearch:undefined
});
// const showflavors = computed(()=>{
//     return data.flavorlist.filter((item)=>{
//         console.log("item",item,data.vcpussearch,data.ramsearch,data.disksearch,((data.vcpussearch && data.vcpussearch != item.vcpus) || (data.ramsearch && data.ramsearch != item.ram) || (data.disksearch && data.disksearch != item.totalDisk)))
//         return !((data.vcpussearch && data.vcpussearch != item.vcpus) || (data.ramsearch && data.ramsearch != item.ram) || (data.disksearch && data.disksearch != item.totalDisk))
//     })
// })
const data_raw = {...data};
const credentOptions = reactive({
    'ssh':'SSH密钥对',
    'pwd':'密码'
})
const sourceOptions = reactive({
    1:'镜像',
    2:'虚机快照',
    3:'可用卷',
    4:'卷快照'
})
const imageDialog = ref();
const imageOSs = 
    reactive({
        '统信uos':'data:image/vnd.microsoft.icon;base64,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',
        'centos':'data:image/svg+xml;base64,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',
        'red hat':'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MC4zIDM4Ij48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6I2U2MTEyNzt9PC9zdHlsZT48L2RlZnM+PHRpdGxlPnJlZGhhdC1sb2dvPC90aXRsZT48ZyBpZD0i44Os44Kk44Ok44O8XzIiIGRhdGEtbmFtZT0i44Os44Kk44Ok44O8IDIiPjxnIGlkPSLjg6zjgqTjg6Tjg7xfMS0yIiBkYXRhLW5hbWU9IuODrOOCpOODpOODvCAxIj48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik0zMy41MSwyMS45YzMuMywwLDguMDctLjY4LDguMDctNC42MWEzLjQzLDMuNDMsMCwwLDAtLjA4LS45bC0yLTguNTRjLS40Ni0xLjg4LS44Ni0yLjczLTQuMTYtNC4zOEMzMi44MiwyLjE2LDI3LjI1LDAsMjUuNiwwcy0yLDItMy44MSwyUzE4LjcyLjUxLDE3LjA3LjUxcy0yLjYyLDEuMDgtMy40MiwzLjNjMCwwLTIuMjIsNi4yNi0yLjUsNy4xN2ExLjU0LDEuNTQsMCwwLDAtLjA2LjUxYzAsMi40Myw5LjU4LDEwLjQxLDIyLjQyLDEwLjQxIi8+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNMzUuMjcsMzhjMTEuOTUsMCwxNS01LjQsMTUtOS42NywwLTMuMzYtMi45LTcuMTctOC4xMy05LjQ0Ii8+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNNDIuMSwxOC44OWExMy4zNSwxMy4zNSwwLDAsMSwuNDUsMi42N2MwLDMuNy00LjE1LDUuNzUtOS42MSw1Ljc1LTEyLjM1LDAtMjMuMTYtNy4yMy0yMy4xNi0xMmE1LDUsMCwwLDEsLjQtMS45M0M1Ljc1LDEzLjYsMCwxNC4zOSwwLDE5LjQ2LDAsMjcuNzYsMTkuNjgsMzgsMzUuMjcsMzgiLz48cGF0aCBkPSJNNDIuMSwxOC44OWExMy4zNSwxMy4zNSwwLDAsMSwuNDUsMi42N2MwLDMuNy00LjE1LDUuNzUtOS42MSw1Ljc1LTEyLjM1LDAtMjMuMTYtNy4yMy0yMy4xNi0xMmE1LDUsMCwwLDEsLjQtMS45M2wxLTIuMzlhMS41NCwxLjU0LDAsMCwwLS4wNi41MWMwLDIuNDMsOS41OCwxMC40MSwyMi40MiwxMC40MSwzLjMsMCw4LjA3LS42OCw4LjA3LTQuNjFhMy40MywzLjQzLDAsMCwwLS4wOC0uOVoiLz48L2c+PC9nPjwvc3ZnPg==',
        'ubuntu':'data:image/svg+xml;base64,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',
        'fedora':'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyBjbGFzcz0iaWNvbiIgd2lkdGg9IjIwMHB4IiBoZWlnaHQ9IjIwMC4wMHB4IiB2aWV3Qm94PSIwIDAgMTAyNCAxMDI0IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbD0iIzMzMzMzMyIgZD0iTTUxMiAwQzIyOS4zNDQgMCAwLjIyNCAyMjkuMDI0IDAgNTExLjY0OFY5MDcuODRhMTE2LjM4NCAxMTYuMzg0IDAgMCAwIDExNi4zODQgMTE2LjEyOGgzOTUuODA4YzI4Mi42NTYtMC4xMjggNTExLjc3Ni0yMjkuMjggNTExLjc3Ni01MTIgMC0yODIuNzUyLTIyOS4yNDgtNTEyLTUxMi01MTJ6IG0xOTYuMDY0IDIzNy45NTJjLTE2LjE2IDAtMjIuMDE2LTMuMTA0LTQ1LjcyOC0zLjEwNGExMjYuODQ4IDEyNi44NDggMCAwIDAtMTI2Ljg0OCAxMjYuNjI0djExMC4yMDhjMCA5Ljg4OCA4LjAzMiAxNy45MiAxNy45MiAxNy45Mmg4My4zMjhjMzEuMDcyIDAgNTYuMTYgMjQuNzM2IDU2LjE2IDU1LjkwNCAwIDMxLjMyOC0yNS4zNDQgNTUuOTY4LTU2LjczNiA1NS45NjhoLTEwMC42MDh2MTI3LjM2YTI0MC4zMiAyNDAuMzIgMCAwIDEtMjQwLjI4OCAyNDAuMjg4aC0xLjI0OGExOTAuOTQ0IDE5MC45NDQgMCAwIDEtNTMuMjE2LTcuNTJsMS4zNDQgMC4zMmMtMjcuMTY4LTcuMDcyLTQ5LjM3Ni0yOS40MDgtNDkuMzc2LTU1LjI5NiAwLTMxLjMyOCAyMi43NTItNTQuMTEyIDU2LjczNi01NC4xMTIgMTYuMTI4IDAgMjIuMDE2IDMuMDcyIDQ1LjY5NiAzLjA3MmExMjYuODQ4IDEyNi44NDggMCAwIDAgMTI2Ljg0OC0xMjYuNjI0di0xMTAuMjA4YTE3LjkyIDE3LjkyIDAgMCAwLTE3LjkyLTE3Ljg4OGgtODMuMzI4YTU1LjgwOCA1NS44MDggMCAwIDEtNTYuMDk2LTU1LjkwNGMwLTMxLjMyOCAyNS4zNDQtNTUuOTY4IDU2LjczNi01NS45NjhoMTAwLjU3NnYtMTI3LjM2YTI0MC4zMiAyNDAuMzIgMCAwIDEgMjQwLjI4OC0yNDAuMjg4YzIwLjEyOCAwIDM0LjQzMiAyLjI3MiA1My4wODggNy4xMzYgMjcuMTY4IDcuMTM2IDQ5LjQwOCAyOS40NCA0OS40MDggNTUuMjk2IDAgMzEuMzYtMjIuNzUyIDU0LjE0NC01Ni43MDQgNTQuMTQ0eiIgIC8+PC9zdmc+',
        'windows':'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyBjbGFzcz0iaWNvbiIgd2lkdGg9IjIwMHB4IiBoZWlnaHQ9IjIwMC4wMHB4IiB2aWV3Qm94PSIwIDAgMTAyNCAxMDI0IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4Ni40IDEzNC40TDk2MCA2NHY0MjIuNEg0ODYuNFYxMzQuNHoiIGZpbGw9IiM5MEMzMDAiIC8+PHBhdGggZD0iTTY0IDE5OC40bDM1OC40LTUxLjJ2MzQ1LjZINjRWMTk4LjR6IiBmaWxsPSIjRjg2NzJDIiAvPjxwYXRoIGQ9Ik00ODYuNCA4ODkuNkw5NjAgOTYwVjU1MC40SDQ4Ni40djMzOS4yeiIgZmlsbD0iI0ZGQzQwMCIgLz48cGF0aCBkPSJNNjQgODI1LjZsMzU4LjQgNTEuMlY1NTAuNEg2NHYyNzUuMnoiIGZpbGw9IiMwMEI0RjIiIC8+PC9zdmc+',
        'harmonyos':'data:image/vnd.microsoft.icon;base64,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',
        'openeuler':'data:image/vnd.microsoft.icon;base64,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',
        'debian':'data:image/svg+xml;base64,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',
        '银河麒麟':'data:image/png;base64,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',
        'coreos':'data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyBjbGFzcz0iaWNvbiIgd2lkdGg9IjIwMHB4IiBoZWlnaHQ9IjIwMC4wMHB4IiB2aWV3Qm94PSIwIDAgMTAyNCAxMDI0IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMi4wMzIgMEMyMjkuNjcyIDAgMCAyMjkuNjggMCA1MTJjMCAyODIuMzM2IDIyOS42NjggNTEyIDUxMi4wMzIgNTEyQzc5NC4zMTIgMTAyNCAxMDI0IDc5NC4zMzYgMTAyNCA1MTIgMTAyNCAyMjkuNjggNzk0LjMxMiAwIDUxMi4wMzIgMHoiIGZpbGw9IiM1M0EzREEiIC8+PHBhdGggZD0iTTUxNC4yODggNDUuMDEyYy04MS41MzYgMC0xNTAuNjg0IDEzMC40MTYtMTc1LjY2IDMxMS42OC0wLjggNS44MzItMS41NzIgMTEuNzMyLTIuMjU2IDE3LjY3MmExMDk4LjU0IDEwOTguNTQgMCAwIDAtNi44OCA4OC43MjggMTA3My45NCAxMDczLjk0IDAgMCAwLTAuODIgNDIuNGMwIDE0LjI5MiAwLjI3MiAyOC40NDQgMC44MjQgNDIuMzg4IDEuMTU2IDMwLjUzMiAzLjQ2OCA2MC4yMTYgNi44OCA4OC43NDQgMzguMjI4IDQuOTQ4IDc5LjEwOCA4LjMwNCAxMjEuNzQ0IDkuOSAxOC40MzIgMC42ODggMzcuMTM2IDEuMSA1Ni4xNzIgMS4xIDE4Ljk3NiAwIDM3Ljc0LTAuNDE2IDU2LjExNi0xLjEgNDIuNjg4LTEuNTk2IDgzLjUzNi00Ljk1MiAxMjEuOC05LjkgNC4yMDgtMC41NCA4LjM1Mi0xLjEyOCAxMi41NDQtMS43MzYgMTU5LjIzNi0yMi4zNiAyNzAuMDA4LTcxLjg2IDI3MC4wMDgtMTI5LjM5Ni0wLjAwNC0yNTQuMzI4LTIwNi4xODQtNDYwLjQ4LTQ2MC40NzItNDYwLjQ4eiIgZmlsbD0iI0YxNjA2RCIgLz48cGF0aCBkPSJNNzA0Ljc0OCAzNzYuMDg4YTIzMy43MDggMjMzLjcwOCAwIDAgMC0xNC44NTItMTkuMzg4Yy00Mi4yNDgtNDkuODA0LTEwNS4xODgtODEuNDUyLTE3NS42MDQtODEuNDUyLTIyLjgyOCAwLTQzLjE2IDM0Ljk0NC01Ni4xNzIgODkuMjA0LTUuMTcyIDIxLjY0LTkuMTg0IDQ2LjMyLTExLjc3MiA3My4xMTZhNzIxLjg4IDcyMS44OCAwIDAgMC0zLjEzNiA2Ny45MjRjMCAyMy42MjggMS4xIDQ2LjQ2NCAzLjEzNiA2Ny45MTYgMjEuNDg0IDIuMDQgNDQuMjg0IDMuMTMyIDY3Ljk0NCAzLjEzMiAyMy42MjggMCA0Ni40MjgtMS4wOTYgNjcuOTEyLTMuMTMyIDQ3LjIyOC00LjQ4IDg4LTEzLjU2IDExNi44OC0yNS41MjggMjguNTItMTEuOCA0NS40LTI2LjQ4OCA0NS40LTQyLjM4OC0wLjAwNC00Ny45NjgtMTQuNjUyLTkyLjUyLTM5LjczNi0xMjkuNDA0eiIgZmlsbD0iI0ZGRkZGRiIgLz48L3N2Zz4=',
        'arch':'data:image/svg+xml;base64,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',
        'freebsd':'data:image/svg+xml;base64,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',
        '其他':'data:image/svg+xml;base64,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'
    })
const volumeDialog = ref();
const networkDialog = ref();
const columns = reactive({
    flavor: [
        {title: '名称', dataIndex: 'name', key: 'name' ,align:'left'},
        {title: '硬盘大小', dataIndex: 'totalDisk', slots: { customRender: 'totalDisk' }, key: 'id', width:100, align:'center'},
        {title: 'vcpus', dataIndex: 'vcpus', key: 'id' ,align:'center'},
        {title: '内存', dataIndex: 'ram', slots: { customRender: 'ram' }, key: 'id' ,align:'center'},
    ],
    image:[
        {title: '名称', dataIndex: 'name', key: 'name',align:'left',ellipsis:true},
        {title: 'OS类型', dataIndex: 'imageOSText', slots: { customRender: 'imageOSText' },　key: 'id',width:200,align:'center'},
        {title: '磁盘格式', dataIndex: 'diskFormat', key: 'id',width:80,align:'center'}
    ],
    volume:[
        {title:'名称', dataIndex:'name',key:'id'},
        {title:'类型', dataIndex:'type',key:'id',align:'center'},
        {title:'大小', dataIndex:'size',key:'id',align:'center'},
    ],
    volumeSnapShot:[
        {title: '名称', dataIndex: 'name', key: 'id' ,align:'left'},
        {title: 'ID', dataIndex: 'thirdSnapshotId', key: 'id' ,align:'left'},
        {title: '大小(GiB)', dataIndex: 'size', key: 'id',align:'center'},
        {title: '状态', dataIndex: 'statusText', slots: { customRender: 'statusText' }, key: 'id',align:'center'}
    ],
    network:[
        {title: '名称', dataIndex: 'networkName', key: 'id',align:'left'},
        {title: '类型', dataIndex: 'networkType', key: 'id',align:'center'},
    ],
    port:[
        {title: '端口名称', dataIndex: 'portName', key: 'portName',align:'left'},
        {title: 'IP地址', dataIndex: 'portIpEntityList', slots: { customRender: 'portIpEntityList' }, key: 'id',align:'left'},
        {title: 'MAC地址', dataIndex: 'macAddress', key: 'id',align:'left'}
    ],
    group:[
        {title: '名称', dataIndex: 'groupName', key: 'id',align:'left'},
        {title: '描述', dataIndex: 'description', key: 'id',align:'center'},
    ],
    keypair:[
        {title: '名称', dataIndex: 'name', key: 'id',align:'left'},
        {title: '指纹', dataIndex: 'fingerPrint', key: 'id',align:'left'},
    ]
});
const vol_count = ref(2);
const vnic_count = ref(0);
const defaultform = {
    availabilityZone:undefined,
    flavorId:[],
    imageId:[],
    imageOS:'',
    volumeId:[],
    volumeSnapShotId:[],
    volList: [
        {
        "key":1,
        "bootable": 0,
        "deleteOnTermination": true,
        "size": 10,
        "type": undefined
        },
        // {
        // "key":2,
        // "bootable": 1,
        // "cloudId": router.currentRoute.value.query.cloudId,
        // "deleteOnTermination": true,
        // "projectId": router.currentRoute.value.query.cloudId,
        // "size": 10,
        // "type": undefined
        // }
    ],
    source:1,
    networkIds:[],
    vnics:[],
    portIds:[],
    groupIds:[],
    keyPairId:[],
    serverName:'',
    serverNumber:1,
    password:'',
    confirmpwd:'',
    credent:'pwd',
    physicalNode:'a',
    userData:''
};
const serverForm = ref();
const serverform = reactive({
    ticketOpenstackServerInfoEntity:{
        availabilityZone:undefined,
        flavorId:[],
        imageId:[],
        imageOS:'',
        volumeId:[],
        volumeSnapShotId:[],
        volList: [
            {
                "key":1,
            "bootable": 0,
            "deleteOnTermination": true,
            "size": 10,
            "type": undefined
            },
            // {
            //     "key":2,
            // "bootable": 1,
            // "cloudId": router.currentRoute.value.query.cloudId,
            // "deleteOnTermination": true,
            // "projectId": router.currentRoute.value.query.cloudId,
            // "size": 10,
            // "type": undefined
            // }
        ],
        source:1,
        networkIds:[],
        vnics:[],
        portIds:[],
        groupIds:[],
        keyPairId:[],
        serverName:'',
        serverNumber:1,
        password:'',
        confirmpwd:'',
        credent:'pwd',
        physicalNode:'a',
        userData:''
    }
    
});
const state = reactive({
    value1:'a',
    value2:'',
    value3:'',
    value4:'',
    value5:'',
    isExpandSenior:false,
});
const imageValidator = async (rule,value) => {
    if(!value || (value && value.length <= 0)){
        if(serverform.ticketOpenstackServerInfoEntity.source == 1){
            return Promise.reject("请选择操作系统");
        }else if(serverform.ticketOpenstackServerInfoEntity.source == 2){
            return Promise.reject("请选择虚机快照");
        }else if(serverform.ticketOpenstackServerInfoEntity.source == 3){
            return Promise.reject("请选择可用卷");
        }else if(serverform.ticketOpenstackServerInfoEntity.source == 4){
            return Promise.reject("请选择卷快照");
        }else{
            return Promise.reject("请选择");
        }
    }else{
        return Promise.resolve();
    }
}
const vnicsValidator = (rule,value,item) => {
    if(!value){
        return Promise.reject("请选择网络");
    }else{
        if(item.method == 'b'){
            if(!item.subnetId){
                return Promise.reject("请选择子网");
            }
        }else if(item.method == 'c'){
            if(!item.ip){
                return Promise.reject("请输入ip");
            }else if(!regexps.ip.test(item.ip)){
                return Promise.reject("请输入合法ip")
            }
        }
        return Promise.resolve();
    }
}
const rulesfields = {
    2:[['ticketOpenstackServerInfoEntity','serverName'],['ticketOpenstackServerInfoEntity','serverNumber'],['ticketOpenstackServerInfoEntity','keyPairId']],
    3:[['ticketOpenstackServerInfoEntity','networkIds'],['ticketOpenstackServerInfoEntity','portIds'],['ticketOpenstackServerInfoEntity','groupIds']],
    4:[['ticketOpenstackServerInfoEntity','availabilityZone'],['ticketOpenstackServerInfoEntity','flavorId'],['ticketOpenstackServerInfoEntity','source'],['ticketOpenstackServerInfoEntity','imageId'],['ticketOpenstackServerInfoEntity','volumeId'],['ticketOpenstackServerInfoEntity','volumeSnapShotId'],['ticketOpenstackServerInfoEntity','volList',0,'type']],
    5:['cloudId','projectId'],
    6:['ticketTitle','endTime']
}
const validateTime : any = (rule, value) => {
    // console.log("value",value)
  if (!value) {
    return Promise.reject("请选择时间");
  } else {
    return Promise.resolve();
  }
}
const rules = {
    ticketTitle:[{required:true, message:'请选输入工单标题'}],
    endTime:[{required:true, validator:validateTime,trigger:'change'}],
    cloudId:[{type:'number',required:true, message:'请选择云平台'}],
    projectId:[{type:'number',required:true, message:'请选择云平台域'}],
    ticketOpenstackServerInfoEntity:{
        availabilityZone:[{type:'number',required:true, message:'请选择可用域',trigger:'change'}],
        flavorId:[{type:'array',required:true, message:'请选择虚机类型',trigger:'change'}],
        source:[{type:'number',required:true, message:'请选择启动源',trigger:'change'}],
        imageId:[{type:'array',required:true, validator:imageValidator,trigger:'change'}],
        volumeId:[{type:'array',required:true, validator:imageValidator,trigger:'change'}],
        volumeSnapShotId:[{type:'array',required:true, validator:imageValidator,trigger:'change'}],
        groupIds:[{type:'array',required:true, message:'请选择安全组',trigger:'change'}],
        serverName:[
            {required:true, message:'请输入虚机名称',trigger:'change'},
            {pattern:regexps.namespace, message:'请输入合法名称',trigger:'change'}
        ],
        serverNumber:[{required:true, type:'number', message:'请输入虚机数量',trigger:'change'}],
        keyPairId:[{type:'array',required:true, message:'请选择密钥对',trigger:'change'}],
        vnics:[{type:'array',required:true}]
    },
}
const handleValidate = (key) => {
    serverForm.value.validateFields([key])
}
const changeSetText = (option,dataName) => {
    data[dataName] = option.label;
}
const flavorFunc = async (cloudId,projectId,name,pageIndex,pageSize) => {
    data.flavorloading = true;
    let ram = data.ramsearch ? data.ramsearch * 1024 : null;
    let vcpus = data.vcpussearch;
    let totalDisk = data.disksearch;
    let res;
    try{
        res = await getFlavorList({cloudId,projectId,name,ram,vcpus,totalDisk,pageIndex,pageSize});
    }catch{
        data.flavorloading = false;
    }finally{
        data.flavorloading = false;
        if(res.code == 0){
            data.flavorlist = res.data.list;
            data.flavorPagination.current = res.data.currPage;
            data.flavorPagination.total = res.data.totalCount;
        }
    }
}
const vnicsPush = (networkId,selectedRows) => {
    if(selectedRows)
    selectedRows.forEach((item,index)=>{
        if(item.id == networkId){
            serverform.ticketOpenstackServerInfoEntity.vnics.splice(index,0,{id:'temp'+(++vnic_count.value),networkId,networkName:item.networkName,networkType:item.networkType,method:'a',methodName:'自动分配地址'})
            // serverform.ticketOpenstackServerInfoEntity.vnics.push({id:'temp'+(++vnic_count.value),networkId,networkName:item.networkName,method:'a',methodName:'自动分配地址'});
        }
    })
    else
    serverform.ticketOpenstackServerInfoEntity.vnics.push({id:'temp'+(++vnic_count.value),method:'a',methodName:'自动分配地址'});
    nextTick(()=>{
        serverForm.value.validateFields([['ticketOpenstackServerInfoEntity','vnics',serverform.ticketOpenstackServerInfoEntity.vnics.length - 1,'networkId']]);
    })
}
const onSelectChange = (selectedRowKeys, selectedRows, name) => {
    // console.log("selectedRowKeys",selectedRowKeys,selectedRows)
    if(name == 'group' || name == 'network' || name == 'port'){
        if(name == 'network'){
            if(selectedRowKeys.length > 0)
                serverForm.value.clearValidate('portIds');
            if(selectedRowKeys.length > serverform.ticketOpenstackServerInfoEntity.vnics.length)
            vnicsPush(selectedRowKeys[selectedRowKeys.length - 1],selectedRows);
            else{
                data.networkSelectedRow.forEach((item,index)=>{
                    // console.log("change",selectedRowKeys,item.id)
                    if(!selectedRowKeys.includes(item.id))
                    close(name,index,item.id)
                })
            }
            
        }
        serverform.ticketOpenstackServerInfoEntity[name+'Ids'] = selectedRowKeys;
        serverForm.value.validateFields([['ticketOpenstackServerInfoEntity',name+'Ids']]);
        if(name == 'port'){
            if(selectedRowKeys.length > 0)
                serverForm.value.clearValidate('networkIds');
            // else
            //     serverForm.value.validateFields(['networkId']);
        }
        // data[name+'SelectedRow'] = [];
        data[name+'SelectedRow'] = [...selectedRows];
    }else{
        serverform.ticketOpenstackServerInfoEntity[name+'Id'] = selectedRowKeys;
        serverForm.value.validateFields([['ticketOpenstackServerInfoEntity',name+'Id']]);
        
        if(name == 'image' || name == 'volume' || name == 'volumeSnapShot'){
            data.sourceName = selectedRows[0].name;
        }
        Object.assign(data[name+'SelectedRow'],...selectedRows);
    }
    
}
const close = (name,index,Id) => {
    if(name == 'network' || name == 'port' || name == 'group'){
        if(name == 'network'){
            // serverform[name+'Ids'] = [];
            // console.log("network",name,index,Id,serverform.ticketOpenstackServerInfoEntity.vnics)
            serverform.ticketOpenstackServerInfoEntity.vnics = serverform.ticketOpenstackServerInfoEntity.vnics.filter((it,ii)=>it.networkId != Id);
            // console.log("after",serverform.ticketOpenstackServerInfoEntity.vnics)
            if(serverform.ticketOpenstackServerInfoEntity.vnics.length <= 0)
            data.groupSelectedRow=[];
        }
        serverform.ticketOpenstackServerInfoEntity[name+'Ids'] = serverform.ticketOpenstackServerInfoEntity[name+'Ids'].filter((it,ii)=>it != Id);
        serverForm.value.validateFields([['ticketOpenstackServerInfoEntity',name+'Ids']]);
        // console.log("data[name+'SelectedRow']",data[name+'SelectedRow'])
        data[name+'SelectedRow'] = data[name+'SelectedRow'].filter((it,ii)=>it.id != Id);
        // data[name+'SelectedRow'] = 
    }
    else{
        serverform.ticketOpenstackServerInfoEntity[name+'Id'] = [];
        serverForm.value.validateFields([['ticketOpenstackServerInfoEntity',name+'Id']]);
        data[name+'SelectedRow']={};

    }
}
const keypairFunc = async (cloudId,projectId,name,pageIndex,pageSize) => {
    data.keypairloading = true;
    let res;
    try{
        res = await getKeypairList({cloudId,projectId,name,pageIndex,pageSize});
    }catch{
        data.keypairloading = false;
    }finally{
        data.keypairloading = false;
        if(res.code == 0){
            data.keylist = res.data.list;
            data.keypairPagination.current = res.data.currPage;
            data.keypairPagination.total = res.data.totalCount;
        }
    }
}
const changeCredent = (e) => {
    if(e.target.value == 'ssh'){
        serverform.ticketOpenstackServerInfoEntity.password = serverform.ticketOpenstackServerInfoEntity.confirmpwd = null;
        keypairFunc(serverform.cloudId,serverform.projectId,null,1,10);;
    }else{
        close('keyPair')
        data.keylist = [];
    }
}
const osFunc = async () => {
    let res = await selectDictList({dictType:'IMAGE_OS'});
    if(res.code == 0){
        data.imageOSs = res.data;
        
    }
}
const imageFunc = async (cloudId,projectId,imageType,imageOS,name,pageIndex,pageSize) => {
    data.imageloading = true;
    let res;
    try{
        res = await getImageList({cloudId,projectId,imageType,imageOS,name,pageIndex,pageSize})
    }catch{
        data.imageloading = false;
    }finally{
        data.imageloading = false;
        if(res.code == 0){
            data.imagelist = res.data.list;
            data[imageType+'Pagination'].current = res.data.currPage;
            data[imageType+'Pagination'].total = res.data.totalCount;
        }
    }
}
const volumeFunc = async (cloudId,projectId,volumeName,pageIndex,pageSize) => {
    data.volumeloading = true;
    let res;
    try{
        res = await getVolumeList({cloudId,projectId,bootable:1,status:'available',volumeName,pageIndex,pageSize})
    }catch{
        data.volumeloading = false;
    }finally{
        data.volumeloading = false;
        if(res.code == 0){
            data.volumelist = res.data.list;
            data.volumePagination.current = res.data.currPage;
            data.volumePagination.total = res.data.totalCount;
        }
    }
}
const volumeSnapShotFunc = async (cloudId,projectId,volumeSnapShotName,pageIndex,pageSize) => {
    data.volumeSnapShotLoading = true;
    let res;
    try{
        res = await getVolumeSnapShotList({cloudId,projectId,name:volumeSnapShotName,pageIndex,pageSize})
    }catch{
        data.volumeSnapShotLoading = false;
    }finally{
        data.volumeSnapShotLoading = false;
        if(res.code == 0){
            data.volumeSnapShotlist = res.data.list;
            data.volumeSnapShotPagination.current = res.data.currPage;
            data.volumeSnapShotPagination.total = res.data.totalCount;
        }
    }
}
const sourceChange = (e,option) => {
    data['imageSelectedRow'].name = undefined;
    serverform.ticketOpenstackServerInfoEntity.imageOS = null;
    serverform.ticketOpenstackServerInfoEntity.imageId = [];
    serverform.ticketOpenstackServerInfoEntity.volumeId = [];
    serverform.ticketOpenstackServerInfoEntity.volumeSnapShotId = [];
    if(e.target.value == 1){
        osFunc();
        imageFunc(serverform.cloudId,serverform.projectId,'image',null,null,1,10);
    }else if(e.target.value == 2){
        imageFunc(serverform.cloudId,serverform.projectId,'snapshot',null,null,1,10);
    }else if(e.target.value == 3){
        if(!(data.volumelist && data.volumelist.length > 0))
            volumeFunc(serverform.cloudId,serverform.projectId,null,1,10);
    }else{
        if(!(data.volumeSnapShotlist && data.volumeSnapShotlist.length > 0))
            volumeSnapShotFunc(serverform.cloudId,serverform.projectId,null,1,10);
    }
}
const osChange = (e) => {
    if(e.target.tagName == 'INPUT'){
        let classList = e.target.labels[0].classList;
        if(classList[3]!='double-flag' && classList[1]=='ant-radio-button-wrapper-checked'){
            $('.ant-radio-button-wrapper-checked').addClass('double-flag');
            imageFunc(serverform.cloudId,serverform.projectId,'image',null,null,1,10);
        }else{
            if(classList[1]=='ant-radio-button-wrapper-checked'){
                $('.ant-radio-button-wrapper-checked').removeClass('double-flag');
            }
            imageFunc(serverform.cloudId,serverform.projectId,'image',e.target.value,null,1,10);
        } 
    }
}
const networkFunc = async (cloudId,projectId,networkName,pageIndex,pageSize) => {
    data.networkloading = true;
    let res;
    try{
        res = await getNetworkList({cloudId,projectId,networkName,pageIndex,pageSize})
    }catch{
        data.networkloading = false;
    }finally{
        data.networkloading = false;
        if(res.code == 0){
            data.networklist = res.data.list;
            data.networkPagination.current = res.data.currPage;
            data.networkPagination.total = res.data.totalCount;
        }
    }
}
const subnetFunc = async (option,item,networkId,index,key) => {
    item[key+'Name'] = option.label;
    if(item.method == 'b'){
        data.subnetloading = true;
        let res;
        try{
            res = await selectSubnetList({networkId});
        }catch{
            data.subnetloading = false;
        }finally{
            data.subnetloading = false;
            if(res.code == 0){
                item.subnetlist = res.data;
            }
        }
    }
    serverForm.value.validateFields([['ticketOpenstackServerInfoEntity','vnics',index,'networkId']]);
}
const subnetSelect = (option,item,index) => {
    item.subnetName = option.label;
    serverForm.value.validateFields([['ticketOpenstackServerInfoEntity','vnics',index,'networkId']])
}
const portFunc = async (cloudId,projectId,portName,pageIndex,pageSize) => {
    data.portloading = true;
    let res;
    try{
        res = await getPortlist({cloudId,projectId,state:'down',portName,pageIndex,pageSize})
    }catch{
        data.portloading = false;
    }finally{
        data.portloading = false;
        if(res.code == 0){
            data.portlist = res.data.list;
            data.portPagination.current = res.data.currPage;
            data.portPagination.total = res.data.totalCount;
        }
    }
}
const groupFunc = async (cloudId,projectId,groupName,pageIndex,pageSize) => {
    data.grouploading = true;
    let res;
    try{
        res = await getSecugroupList({cloudId,projectId,groupName,pageIndex,pageSize})
    }catch{
        data.grouploading = false;
    }finally{
        data.grouploading = false;
        if(res.code == 0){
            data.grouplist = res.data.list;
            data.groupPagination.current = res.data.currPage;
            data.groupPagination.total = res.data.totalCount;
        }
    }
}
const zoneFunc = async (cloudId) => {
    data.zoneloading = true;
    let res;
    try{
        res = await selectZoneList({cloudId,module:'nova'});
    }catch{
        data.zoneloading = false;
    }finally{
        data.zoneloading = false;
        if(res.code == 0){
            data.zonelist = res.data;
        }
    }
}
const volumeTypeFunc = async (cloudId) => {
    data.volumeTypeloading = true;
    let res;
    try{
        res = await selectVolumeTypeList({cloudId});
    }catch{
        data.volumeTypeloading = false;
    }finally{
        data.volumeTypeloading = false;
        if(res.code == 0){
            data.volumeTypelist = res.data;
        }
    }
    
}
const importScript = async (files) => {
    let res = await jsReadFiles(files,'textareaNode')
    if(res){
        serverform.ticketOpenstackServerInfoEntity.userData = res;
    }
}
const beforeUpload = file => {
    importScript([file])
    return false;
};
const pushVolList = () => {
    serverform.ticketOpenstackServerInfoEntity.volList.push({
        "key":++vol_count.value,
        "bootable": 1,
        "cloudId": serverform.cloudId,
        "deleteOnTermination": true,
        "projectId": serverform.projectId,
        "size": 10,
        "type": undefined
    })
}
const cancel = () => {
    emit('set_current',0);
    serverForm.value.resetFields();
    Object.assign(serverform,defaultform);
    Object.assign(data,data_raw);
}
const next = () => {
    // console.log("ticketform",ticketform)
    // if(props.info.total - props.info.current == 6 || props.info.total - props.info.current == 5){
    //     Object.assign(serverform,{...ticketform,...serverform});
    // }
    if((props.info.total - props.info.current) == 4){
        let n = 0;
        while(n < serverform.ticketOpenstackServerInfoEntity.volList.length){
            rulesfields[props.info.total - props.info.current].push(['volList',n,'type']);
            n++;
        }
    }
    // console.log("serverform",serverform)
    serverForm.value.validateFields(rulesfields[props.info.total - props.info.current]).then((res)=>{
        if(props.info.total - props.info.current == 6){
            emit('select_cloud');
        }
        if(props.info.total - props.info.current == 5){
            init(true);
        }
        if(props.info.total - props.info.current == 4){
            networkFunc(serverform.cloudId,serverform.projectId,null,1,10);
            portFunc(serverform.cloudId,serverform.projectId,null,1,10);
            groupFunc(serverform.cloudId,serverform.projectId,null,1,10);
        }
        // else if(props.info.current == 1){
        //     keypairFunc(serverform.cloudId,serverform.projectId,null,1,10);
        // }
        props.info.current++;
        emit('set_current',props.info.current);
        nextTick(()=>{
            if(props.info.total - props.info.current >= 5)
            serverForm.value.scrollToField(rulesfields[props.info.total - props.info.current][0]);
            else if(props.info.total - props.info.current > 2 && props.info.total - props.info.current < 5)
            serverForm.value.scrollToField([rulesfields[props.info.total - props.info.current][0]]);
        })
    }).catch((err)=>{
        // console.log("err",err,err.errorFields)
        message.error(err.errorFields[0].errors[0])
        if(props.info.total - props.info.current >= 5)
        serverForm.value.scrollToField(err.errorFields[0].name)
        else if(props.info.total - props.info.current > 1 && props.info.total - props.info.current < 5)
        serverForm.value.scrollToField([err.errorFields[0].name])
    })
};
const handleSave = (callback) => {
    if(callback)
        callback(serverform,serverForm);
}
const init = (isTicket) => {
    if(!isTicket){
        let cloudId = router.currentRoute.value.query.cloudId;
        let projectId = router.currentRoute.value.query.projectId;
        serverform.cloudId = cloudId;
        serverform.projectId = projectId;
        serverform.ticketOpenstackServerInfoEntity.cloudId = cloudId;
        serverform.ticketOpenstackServerInfoEntity.projectId = projectId;
        serverform.ticketOpenstackServerInfoEntity.volList[0].cloudId = cloudId;
        serverform.ticketOpenstackServerInfoEntity.volList[0].projectId = projectId;
        defaultform.cloudId = cloudId;
        defaultform.projectId = projectId;
        defaultform.volList[0].cloudId = cloudId;
        defaultform.volList[0].projectId = projectId;
        // 项目
        data.projectName = menu_store.projectInfo.projectName;
    }else{
        data.projectName = serverform.projectName
    }
    
    console.log("serverform.cloudId",serverform.cloudId)
    // 可用域
    zoneFunc(serverform.cloudId);
    osFunc();
    flavorFunc(serverform.cloudId,serverform.projectId,null,1,10);
    volumeTypeFunc(serverform.cloudId);
    imageFunc(serverform.cloudId,serverform.projectId,'image',null,null,1,10);
    
    // selectDomainlist();
    // selectProjectlist()
    // selectZonelist();
    // queryworker();
    // selectImagelist('image');
    // getFlavorlist();
    // selectNetworklist();
    // selectPortlist();
    // selectSecugrouplist();
    // selectKeypairlist();
    // SelectVolumeList();
    // SelectVolumeSnapShotList();
    // SelectRuleservelist();
}
const setConfirm = () => {
    let temp = {}
    temp.projectName = menu_store.projectInfo.projectName;
    temp.availabilityZone = data.availabilityZone;
    temp.flavorName = data.flavorSelectedRow.name;
    temp.sourceName = sourceOptions[serverform.ticketOpenstackServerInfoEntity.source];
    if(serverform.ticketOpenstackServerInfoEntity.source == 1){
        temp.imageName = data.imageSelectedRow.name;
        temp.snapshotName = undefined;
        temp.volumeName = undefined;
        temp.volumeSnapShotName = undefined;
    }else if(serverform.ticketOpenstackServerInfoEntity.source == 2){
        temp.snapshotName = data.imageSelectedRow.name;
        temp.imageName = undefined;
        temp.volumeName = undefined;
        temp.volumeSnapShotName = undefined;
    }else if(serverform.ticketOpenstackServerInfoEntity.source == 3){
        temp.volumeName = data.volumeSelectedRow.name;
        temp.imageName = undefined;
        temp.snapshotName = undefined;
        temp.volumeSnapShotName = undefined;
    }else{
        temp.volumeSnapShotName = data.volumeSnapShotSelectedRow.name;
        temp.imageName = undefined;
        temp.snapshotName = undefined;
        temp.volumeName = undefined;
    }
    temp.sysdiskName = serverform.ticketOpenstackServerInfoEntity.volList[0].size + 'GiB ' + (serverform.ticketOpenstackServerInfoEntity.volList[0].deleteOnTermination ? '随虚机删除' : '')
    if(serverform.ticketOpenstackServerInfoEntity.volList.length == 2){
        temp.datadiskName = serverform.ticketOpenstackServerInfoEntity.volList[1].size + 'GiB ' + (serverform.ticketOpenstackServerInfoEntity.volList[1].deleteOnTermination ? '随虚机删除' : '');
    }else if(serverform.ticketOpenstackServerInfoEntity.volList.length > 2){
        let diskname = [];
        serverform.ticketOpenstackServerInfoEntity.volList.forEach((item,index)=>{
            if(index > 0){
                diskname.push(item.size + 'GiB ' + (item.deleteOnTermination ? '随虚机删除' : ''))
            }
        })
        temp.datadiskName = diskname;
    }
    let tempvnics = serverform.ticketOpenstackServerInfoEntity.vnics;
    if(tempvnics.length == 1){
        // let tempnet = [];
            temp.networkName = tempvnics[0].networkName;
            temp.networkType = tempvnics[0].networkType;
        if(tempvnics[0].method == 'a'){
            // tempnet.push({networkName:tempvnics[0].networkName});
        }else if(tempvnics[0].method == 'b'){
            temp.subnetName = tempvnics[0].subnetName;
            // tempnet.push({networkName:tempvnics[0].networkName,subnetName:tempvnics[0].subnetName})
        }else{
            temp.ip = tempvnics[0].ip;
            // tempnet.push({networkName:tempvnics[0].networkName,ip:tempvnics[0].ip})
        }
        // temp.networkName = tempnet;
    }else if(tempvnics.length > 1){
        let tempnet = [];
        tempvnics.forEach((item,index)=>{
            if(item.method == 'a'){
                tempnet.push({networkName:item.networkName,networkType:item.networkType});
            }else if(item.method == 'b'){
                tempnet.push({networkName:item.networkName,networkType:item.networkType,subnetName:item.subnetName});
            }else{
                tempnet.push({networkName:item.networkName,networkType:item.networkType,ip:item.ip});
            }
        })
        temp.networkName = tempnet;
    }
    
    if(data.portSelectedRow.length == 1){
        temp.portName = data.portSelectedRow[0].portName;
    }else if(data.portSelectedRow.length > 1){
        let tempport = [];
        data.portSelectedRow.forEach((item)=>{
            tempport.push(item.portName );
        })
        temp.portName = tempport;
    }
    if(data.groupSelectedRow.length == 1){
        temp.groupName = data.groupSelectedRow[0].groupName;
    }else if(data.groupSelectedRow.length > 1){
        let tempgroup = [];
        data.groupSelectedRow.forEach((item)=>{
            tempgroup.push(item.groupName);
        })
        temp.groupName = tempgroup;
    }
    // for(let item in temp){
    //     console.log("item",temp[item])
    //     if(temp[item])
    // }
    let {serverName,serverNumber,credent} = serverform.ticketOpenstackServerInfoEntity;
    temp.serverName = serverName;
    temp.serverNumber = serverNumber + ' 台';
    // temp.credent = credentOptions[credent] + ' ' + data.keyPairSelectedRow.name;
    emiter.emit("updateConfirm",temp)
}
watch(serverform.ticketOpenstackServerInfoEntity,(newval,oldval)=>{
    console.log("val",newval,oldval)
    setConfirm()
})
onMounted(() => {})
defineExpose({init,cancel,next,handleSave,osFunc,flavorFunc,imageFunc,handleValidate,setConfirm})
</script>
<style lang='scss' scoped>
.img-btn-group{display: flex;}
.img-btn{
    flex: 1 auto;width: 70px;height: 50px;text-align: center;border:none;
    &::before{width: 0;}
    img{display: block;width: 20px;height: 20px;margin: 0 auto;}
}
.footer-custom{display: flex;justify-content: space-between;}

    :deep(.ant-form-item-has-error .ant-input-affix-wrapper){
        border-color: #d9d9d9;
        &:focus{
            box-shadow: 0 0 0 2px rgb(255 77 79 / 20%);
        }
    }
        :deep(.ant-radio-group .ant-form-item-has-error .ant-radio-button-wrapper){
        border-color: #d9d9d9 !important;
        &:not(:first-child)::before{
            background-color: #d9d9d9;
        }
    }
    // :deep(.ant-form-item-has-error .ant-radio-button-wrapper:not(:first-child)::before){
    //     background-color: #d9d9d9;
    // }
    :deep(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within){
        box-shadow: none;
    }
.double-flag,:deep(.ant-input-prefix){
    color: #000000D9;
}
:deep(.label .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before){
    color: transparent;
}
.label .ant-radio-button-wrapper{padding: 0 8px;}
:deep(.ant-descriptions-item-container .ant-descriptions-item-content){display: block;}
:deep(.ant-form-item-has-error .ant-input-affix-wrapper-focused){
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
:deep(.ant-form-item-has-error .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input) .ant-select-selector){border-color: #d9d9d9 !important;}
:deep(.ant-form-item-has-error .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-focused .ant-select-selector){border-color: #40a9ff !important;box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);}
:deep(.ant-form-item-has-error .ant-input-number){border-color: #40a9ff;}
:deep(.ant-form-item-has-error .ant-input-number:not([disabled]):hover){border-color: #d9d9d9;}
:deep(.ant-form-item-has-error .ant-input-number-focused){border-color: #40a9ff !important;box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);}
</style>