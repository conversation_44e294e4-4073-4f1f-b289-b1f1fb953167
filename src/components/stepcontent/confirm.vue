<template>
    <a-descriptions title="配置概览" :column="1">
        <a-descriptions-item v-for="(item,index) in list" :label="item.label">
            <span style="word-break: break-all" v-html="item.value" v-if="item.label"></span>
            <div class="network-area" v-else>
                <div v-for="(t,i) in item.value">{{t.label}}：<span style="word-break: break-all">{{t.value}}</span></div>
            </div>
        </a-descriptions-item>
    </a-descriptions>
</template>
<script lang='ts' setup>
import emiter from '@/utils/Bus';
import { onMounted, ref } from 'vue';
const labelObj = {
    "projectName":"项目",
    "availabilityZone":"可用域",
    "flavorName":"虚机类型",
    "sourceName":"启动源",
    "imageName":"镜像",
    "snapshotName":"虚机快照",
    "volumeName":"卷",
    "volumeSnapShotName":"卷快照",
    "sysdiskName":"系统盘",
    "datadiskName":"数据盘",
    "networkName":"网络",
    "networkType":"网络类型",
    "subnetName":"子网",
    "ip":"IP地址",
    "portName":"端口",
    "groupName":"安全组",
    "serverName":"虚机名称",
    "serverNumber":"数量"
}
const list = ref([]);
const setList = (obj) => {
    let templist = [];
    for(let item in obj){
        if(obj[item]){
            if(typeof obj[item] == 'string'){
                console.log("item",item,obj[item])
                templist.push({label:labelObj[item],value:obj[item]})
            }else if(Array.isArray(obj[item])){
                if(item == 'networkName'){
                    obj[item].forEach((t,i)=>{
                        let value = [];
                        for(let tt in t){
                            if(t[tt]){
                                value.push({label:tt == 'networkName' ? labelObj[tt] + (i+1) : labelObj[tt],value:t[tt]});
                            }
                        }
                        templist.push({value});
                    })
                }else{
                    obj[item].forEach((t,i)=>{
                        templist.push({label:labelObj[item]+(i+1),value:t})
                    })
                }
            }
        }
    }
    list.value = templist;
}

onMounted(() => {
    emiter.on("updateConfirm",setList)
})
</script>
<style lang='scss' scoped>
.network-area{width: 100%;padding: 0.4em 0.6em;border: 1px solid rgba(100, 100, 100, 0.2);border-radius: 3px;}
</style>