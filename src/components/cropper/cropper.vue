<template>
    <div class=''>
        <a-modal
            v-model:visible="visible"
            title="请裁剪图片"
            :maskClosable="false"
            :confirmLoading="confirmLoading"
            ok-text="提交"
            cancel-text="取消"
            @cancel="handleCancel"
            @ok="handleOk"
            centered
        >
            <div class="cropper-wrapper">
            <vue-cropper
                ref="cropper"
                :img="img"
                :info="true"
                :original="true"
                :autoCrop="options.autoCrop"
                :fixed="true"
                :fixedBox="options.fixedBox"
                :centerBox="true"
                outputType="png"
            ></vue-cropper>
            </div>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { onMounted, reactive, ref } from 'vue';
import { VueCropper } from "vue-cropper";
import 'vue-cropper/dist/index.css';
const emit = defineEmits(['ok'])
const cropper = ref();
const visible = ref(false);
const confirmLoading = ref(false);
const img = ref(null);
const options = reactive({
    autoCrop: true, //是否默认生成截图框
    autoCropWidth: 200, //默认生成截图框宽度
    autoCropHeight: 200, //默认生成截图框高度
    fixedBox: false, //固定截图框大小 不允许改变
})
const handleCancel = (e) => {
    visible.value = false;
}
const handleOk = () => {
    confirmLoading.value = true;
    cropper.value.getCropData((data)=>{
        emit('ok',data);
        handleCancel();
    })
}
// 调用此方法需传入一个 [url地址 || base64 || blob]
// 父组件调用: this.$refs['cropperModal'].edit(参数)
const edit = (image) => {
    img.value = image;
    visible.value = true;
}
onMounted(() => {})
defineExpose({edit})
</script>
<style lang='scss' scoped>
.cropper-wrapper {
  width: 100%;
  height: 400px;
}
:deep(.cropper-modal){
    background: rgba(255, 255, 255, 0.5);
}
</style>