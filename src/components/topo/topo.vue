<template>
    <div id="mynetwork1" class="myChart" v-if="!isEmpty"></div>
    <a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
</template>
<script lang='ts' setup>
import { onMounted, ref } from 'vue';
import { Empty } from "ant-design-vue";
import "vis-network/dist/dist/vis-network.min.css";
import * as vis from "vis-network/dist/vis-network.min";
import "@/assets/iconfont/iconfont.css";
import {selectNetworkTopology} from "@/api/backend/devops/network";
import router from '@/router';
const isEmpty = ref(false);
const Network1 = ref();
const makeVis = async () => {
  let edges = [];
  let nodes = [];
  let res = await selectNetworkTopology({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId})
  if(res.code == 0 && (res.data.nodes && res.data.nodes.length > 0)){
      if(res.data.edges){
              res.data.edges.forEach((item,index) => {
              item.from = item.fromId;
              item.to = item.toId;
          })
          edges = res.data.edges;
      }
      if(res.data.nodes){
          res.data.nodes.forEach((item,index) => {
              item.icon = {}
              if(item.type == 'net-work')
              item.icon.code = '\ue626';
              if(item.type == 'net-server')
              item.icon.code = '\ue609';
              if(item.type == 'net-route')
              item.icon.code = '\ue601';
              if(item.type == 'child-net-work')
              item.icon.code = '\ue6fa';  
          })
          nodes = res.data.nodes;
      }
      var data = {
        nodes: nodes,
        edges: edges,
      };
      var container = document.getElementById("mynetwork1");
      var options = {
        nodes: {
          // fixed:true,
          font: {
            color: "#000", // 字体的颜色
            size: 13, // 显示字体大小
          },
          scaling: {
            min: 0,
            max: 0, // 缩放效果比例
            label: {
              enabled: true,
              min: 8,
              max: 8,
              maxVisible: 0,
            },
          },
          borderWidth: 1,
          shape: "icon",
          icon: {
              face: 'Iconfont',
              // code: '\uf2bb',
              weight: 5,
              size: 30
          },
          shapeProperties: {
            interpolation: true,
          },
          size: 50
        },
        interaction: {
          hover: true, // 鼠标移过后加粗该节点和连接线
        },
      };
      Network1.value = new vis.Network(container, data, options)
      Network1.value.once('stabilized', function() {
      Network1.value.moveTo({ scale : 1 });
  })
  }else{
    isEmpty.value = true;
  }
}
onMounted(() => {
    makeVis();
})
</script>
<style lang='scss' scoped>
.myChart{height: calc(100vh - 199px);}
</style>