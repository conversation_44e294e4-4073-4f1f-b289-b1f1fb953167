<template>
    <a-space class="button-group">
        <a-button type="primary" @click="emit('search')"> {{ $t("m.search") }} </a-button>
        <a-button @click="emit('reset')"> {{ $t("m.reset") }} </a-button>
        <a-button type="primary" @click="emit('export')" v-if="isShowExport">导出</a-button>
        <div class="expand-button">
            <a class="down" @click="buttonExpand(true)">展开<DownOutlined/></a>
            <a class="up" @click="buttonExpand(false)">收起<UpOutlined/></a>
        </div>
    </a-space>
</template>
<script lang='ts' setup>
import { onMounted } from 'vue';
import { buttonExpand, handleWidth } from "@/utils/moreform";
const emit = defineEmits(['search','reset','export']);
const props = defineProps({
    isShowExport:Boolean
})
onMounted(() => {})
</script>
<style lang='scss' scoped>
.expand-button{width: 42px;}
</style>