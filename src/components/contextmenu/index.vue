<template>
    <transition >
        <div
            class="popper is-pure is-light el-dropdown__popper ba-contextmenu"
            :style="`top: ${state.axis.y + 5}px;left: ${state.axis.x - 14}px;width:${props.width}px`"
            :key="Math.random()"
            v-show="state.show"
            aria-hidden="false"
            data-popper-placement="bottom"
        >
            <ul class="el-dropdown-menu">
                <template v-for="(item, idx) in props.items" :key="idx">
                    <li class="el-dropdown-menu__item" :class="item.disabled ? 'is-disabled' : ''" tabindex="-1" @click="onContextmenuItem(item)">
                        <Icon size="12" :name="item.icon" />
                        <span>{{ item.label }}</span>
                    </li>
                </template>
            </ul>
            <span class="el-popper__arrow" :style="{ left: `${state.arrowAxis}px` }"></span>
        </div>
    </transition>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, reactive, toRaw } from 'vue'
import type { ContextMenuItem, Axis, ContextmenuItemClickEmitArg } from './interface'
import { viewMenu } from '/@/stores/interface'

// 不能使用导出的 interface vue的issue已存在，尚未解决
interface Props {
    width?: number
    items: ContextMenuItem[]
}

const props = withDefaults(defineProps<Props>(), {
    width: 150,
    items: () => [],
})

const emits = defineEmits<{
    (e: 'contextmenuItemClick', item: ContextmenuItemClickEmitArg): void
}>()

const state = reactive({
    show: false,
    axis: {
        x: 0,
        y: 0,
    },
    menu: {},
    arrowAxis:5,
})

const onShowContextmenu = (menu: viewMenu, axis: Axis) => {
    state.menu = menu
    state.axis = axis
    state.show = true
}

const onContextmenuItem = (item: ContextmenuItemClickEmitArg) => {
    if (item.disabled) return
    item.menu = toRaw(state.menu) as viewMenu
    emits('contextmenuItemClick', item)
}

const onHideContextmenu = () => {
    state.show = false
}

defineExpose({
    onShowContextmenu,
    onHideContextmenu,
})

onMounted(() => {
    document.body.addEventListener('click', onHideContextmenu)
})
onUnmounted(() => {
    document.body.removeEventListener('click', onHideContextmenu)
})
</script>

<style scoped lang="scss">
.ba-contextmenu {
    z-index: 9999;
}
.popper,
.popper.is-light .el-popper__arrow::before {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border: none;
}
.el-dropdown-menu__item {
    padding: 8px 20px;
    user-select: none;
}
.el-dropdown-menu__item .icon {
    margin-right: 5px;
}

</style>
