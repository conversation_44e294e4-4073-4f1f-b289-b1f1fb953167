<template>
    <div class='menuPadding'>
        <a-menu
      mode="vertical"
      v-model:selectedKeys="selectedKeys"
      @select="({item}) =>{emit('refreshList',item.cloudId)}"
    >
      <a-menu-item v-for="(item,index) in cloudlist" :key="index" :cloudId="item.id" >
        {{item.cloudName}}
      </a-menu-item>
    </a-menu>
    </div>
</template>
<script lang="ts" setup>
import { selectCloudList } from "@/api/backend/cloud";
import { onMounted, ref } from "vue";
const emit = defineEmits(['refreshList'])
const selectedKeys = ref([0])
const cloudlist = ref([]);
const SelectCloudList = async () => {
  let res = await selectCloudList();
  if(res.code == 0){
    cloudlist.value = res.data;
    emit('refreshList',cloudlist.value[0].id)
  }
}
const select = ({item}) =>{
// console.log('se',item.cloudId)
}
onMounted(()=>{
  // SelectCloudList()
})
</script>
<style lang='scss' scoped>
.menuPadding{
    // min-height: calc(100vh - 144px);
    width: 225px;
    height: 100%;
    background-color: #fff;
    overflow-y: auto;
    overflow-x: visible;
    margin-right: 6px;
}
:deep(.ant-menu-vertical){
  border-right: none;
}
// :deep(.ant-menu-root.ant-menu-vertical){width: 225px;}
</style>