.main{
    width: 62%;
    min-width: 820px;
    margin: 30px auto;
}
a{
    // color: #003296;
}
html{
    overflow: hidden;
}
ul,li{list-style: none;}
.clearfix:before,
  .clearfix:after {
    display: table;
    content: '';
  }

  .clearfix:after {
    clear: both;
  }
/* 设置页面主体部分的内边距 */
.contentPadding{
    padding:20px;
    background-color: #ffffff;
    height: calc(100vh - 96px);
    // width: 100%;
    overflow-y: auto;
    margin: 0 16px;
}

/* 查询项下的操作按钮 */
.contentPadding .buttonGroup{
    margin-bottom: 20px;
}

.buttonPadding{
    display: flex;
    align-items: flex-start;
    position: relative;
    padding: 20px 20px 0;
    background-color: rgb(255, 255, 255);
    border-bottom: 6px solid #f0f2f5;
    min-width: 610px;
    height: 78px;
    overflow: hidden;
    // .button-group{
    //     position: absolute;
    //     bottom: 20px;
    //     right: 20px;
    // }
}

.innerPadding{
    padding: 20px;
    background-color: #fff;
    min-width: 610px;
    // height: calc(100vh - 214px);
    // overflow-y: auto;
    // border-top: 4px solid #f0f2f5;
    .buttonGroup{
        margin-bottom: 20px;
    }
    .ant-table th{ white-space: nowrap; }
    .ant-table td{ white-space: nowrap; }
    ::-webkit-scrollbar {
        height: 8px;
    }
}

.innerPadding1{
    background-color: #fff;
    // border-top: 4px solid #f0f2f5;
    .buttonGroup{
        margin-bottom: 20px;
    }
}

/* 下拉框的宽度 */
.select-drop-down {
    min-width: 194px;
}


/* 设置Descriptions的都宽度，保持美列的宽度一致 */
.ant-descriptions-bordered .ant-descriptions-view > table {
    table-layout: fixed;
}


/* input设置只读样式 */
.readOnly {
    background-color: #fff;
    cursor: auto;
    .ant-input-disabled{
        cursor: auto;
        color:rgba(0, 0, 0, 0.65);
    }
}

/*设置modal标题的颜色*/
.modalTitle{
    .ant-modal-header .ant-modal-title{
        color:#42B5F6;
      }
}

/*设置表格头的颜色*/
.tableBox{
    .ant-table-thead{
        th{
            background: #1890ff;
            font-size: 16px;
        }
    }
}
.button_E{
    background: #ffb800;
    color: white;
    margin: 0 5px 0 5px;
    line-height: 25px;
    height: 22px;
    font-size:12px;
    border: 0;
    padding: 0 5px;
    &:hover{
        background-color: #ffb800;
        color: #fff;
    }
    &:focus{
        background-color: #ffb800;
        color: #fff;
    }
}
.button_D{
    background-color: #ff7875;
    color: #fff;
    margin: 0 5px 0 5px;
    line-height: 25px;
    height: 22px;
    font-size:12px;
    border: 0;
    padding: 0 5px;
    &:hover{
        background-color: #ff7875;
        color: #fff;
    }
    &:focus{
        background-color: #ff7875;
        color: #fff;
    }
}
.button_V{
    color: #fff;
    line-height: 25px;
    height: 22px;
    font-size:12px;
    border:0;
    margin: 0 5px 0 5px;
    padding: 0 5px;
    background-color: #1890ff;
    &:hover{
        background-color: #1890ff;
        color: #fff;
    }
    &:focus{
        background-color: #1890ff;
        color: #fff;
    }
}
.cloudContent{
    display: flex;height: calc(100vh - 96px);margin: 0 16px; overflow-x: auto;
    .cloudRight{min-width: auto;}
}
.cloudRight{
    width: calc(100%);
    overflow-y: auto;
    background-color: #fff;
    min-width: 600px;
}
.uncloudRight{
    height: calc(100vh - 96px);
    overflow: auto;
    margin: 0 16px;
    background-color: #fff;
}

.right-menu{z-index: 2;background:#fff !important;padding: 6px 0;border: none !important;box-shadow: 0 2px 8px #00000026 !important;border-radius: 2px !important;ul li{padding: 0 !important;border-bottom: none !important;font-size: 14px !important;div{span{padding: 2px 0;color: rgba(0, 0, 0, 0.65) !important;}height:100% !important}}}
.right-menu ul li div span:hover{background-color: #f0f2f5 !important;color: rgba(0, 0, 0, 0.8) !important;font-size: 14px !important;}
.ant-modal{width: 608.8px !important;}
.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh);
  }
}
.ant-table-title{padding-top: 0;}
.back-page{height: 100%;min-height: calc(100vh - 136px);margin: 0 16px;}
.back-header{padding:20px;background-color:#fff;display:flex;justify-content:space-between;border-bottom:2px solid #f0f2f5}
.back-content{overflow-y:auto;padding:20px 150px;background-color:#fff;border-top: 2px solid #f0f2f5;}
.ant-spin-nested-loading > div > .ant-spin{max-height: none;}
// .searchform{width: calc(100% - 158px);}
.button-group a.up{display: none;}
.ant-form-inline .ant-form-item{margin-bottom: 20px;}
.ant-spin-blur{opacity: 0.1;}
.full-modal {
    .ant-modal-body {
      flex: 1;
    }
}