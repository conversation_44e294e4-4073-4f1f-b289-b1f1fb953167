import { osLoad, serverLoad, resourceLoad } from "@/api/backend";
import * as echarts from "echarts";
import {ByteFormat} from "@/utils/byteformat";
const toolbox = {
  feature:{
    myTool: {
        show: true,
        title: '导出',
        icon: `M328 576h152V128h64v448h152L512 768 328 576z m568-64h-64v320H192V512h-64v384h768V512z`,
        onclick: async function  (e,b,c,d,f){
          let res;
          let fileName;
        if(e.option.title[0].text=="云平台虚机分布图"){
          res=await serverLoad({})
          fileName = '云平台虚机分布表.xlsx';
        }
        if(e.option.title[0].text=="云平台虚机操作系统分布图"){
          res=await osLoad({})
          fileName = '云平台虚机操作系统分布表.xlsx';
        }
        if(e.option.title[0].text=="云平台资源使用情况"){
          res=await resourceLoad({})
          fileName = '云平台资源使用情况表.xlsx';
        }
        if(res){
          let urldata = res.data ? res.data : res;
          let url=window.URL.createObjectURL(urldata)
          let link = document.createElement("a");
          link.style.display='none'
          link.href=url;
          if(res.headers){
            if(res.headers['Content-disposition'])
              fileName = res.headers['Content-disposition'].split('=')[1];
          }
          link.setAttribute('download',fileName);
          document.body.appendChild(link)
          link.click();
        }
      }
    },
  }
};
// P：The Proportion(比例)”
// HW：Hardware
// WO：Work Order
// STAT：Statistics
export const Options = {
  index_cloudvm_P:{
    title: {
      text: '云平台虚机分布图',
      left: 'left'
    },
    tooltip: {
      trigger: 'item'
    },
    // toolbox,
    series: [
      {
        type: 'pie',
        radius: '56%',
        bottom:'-10%',
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  },
  index_cloudvmOS_P: {
    title: {
      text: '云平台虚机操作系统分布图',
      left: 'left'
    },
    tooltip: {
      trigger: 'item',
      
    },
    toolbox,
    color:['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc','#000'],
    series: [
      {
        type: 'pie',
        radius: '56%',
        bottom:'-10%',
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  },
  index_cloudHW_P: {
    title: {
      text: '云平台资源使用情况',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      top: '13%',
    },
    toolbox,
    grid: {
      left: '3%',
      right: '4%',
      top: '25%',
      bottom:'0',
      containLabel: true
    },
    yAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    xAxis: {
      type: 'category',
      data: []
    },
    series: [
      {
        name: '存储使用占比',
        type: 'bar',
        data: []
      },
      {
        name: 'CPU使用占比',
        type: 'bar',
        data: []
      }
      ,
      {
        name: '内存使用占比',
        type: 'bar',
        data: []
      }
    ]
  },
  index_cloudWO_STAT: {
    title: {
      text: '云平台工单处理统计'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      top:'10%'
    },
    // toolbox,
    grid: {
      left: '3%',
      right: '4%',
      bottom: '1%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '工单数量',
        type: 'bar',
        data: []
      },
      {
        name: '办结工单',
        type: 'bar',
        data: []
      },
      {
        name: '延期工单',
        type: 'bar',
        data: []
      }
    ]
  },
  cloud_1: {
    title: {
      text: '物理机CPU使用率(%)',
      left: 'left',
      textStyle:{
        fontSize:'14px',
        color:'#000000D9'
      }
    },
    legend:{
      show:true,
      right:'0',
      top:'9%',
      type:'scroll'
    },
    grid: {
      left: '0',
      right: '0',
      bottom:'0',
      containLabel: true
    },
    tooltip: {
      show:true,
      trigger: 'axis',
      formatter: function(params){
        let res = params[0].name;
        params.forEach((item,index) => {
          res += '<br/>'+item.marker+' '+item.seriesName+'&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-weight:bold">'+item.data+'</span><span style="font-size:12px">%</span>';
        });
        return res
      }
    },
    xAxis: {
      boundaryGap: false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      },
      data:[]
      // data:['14:00','14:02','14:04','14:06','14:08','14:10','14:12','14:14','14:16','14:18','14:20','14:22','14:24','14:26','14:28','14:30','14:32','14:34','14:36','14:38']
    },
    yAxis: {
      type: 'value',
      show:false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      }
    },
    series: [
      {
        type: 'line',
        data:[],
        name:'-',
        // data: [3,4,3,5,3,2,1,3,5,30,3,5,1,2,4,1,5,2,4,3,1],
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#5470c6'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data:[],
        name:'-',
        // data: [3,4,3,5,3,2,1,3,5,30,3,5,1,2,4,1,5,2,4,3,1],
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#91cc75'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data:[],
        name:'-',
        // data: [3,4,3,5,3,2,1,3,5,30,3,5,1,2,4,1,5,2,4,3,1],
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#fac858'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data:[],
        name:'-',
        // data: [3,4,3,5,3,2,1,3,5,30,3,5,1,2,4,1,5,2,4,3,1],
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#ee6666'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },
    ]
  },
  cloud_2: {
    title: {
      text: '物理机内存负载率(%)',
      left: 'left',
      textStyle:{
        fontSize:'14px',
        color:'#000000D9'
      }
    },
    legend:{
      show:true,
      right:'0',
      top:'9%',
      type:'scroll'
    },
    grid: {
      left: '0',
      right: '0',
      bottom:'0',
      containLabel: true
    },
    tooltip: {
      show:true,
      trigger: 'axis',
      formatter: function(params){
        let res = params[0].name;
        params.forEach((item,index) => {
          res += '<br/>'+item.marker+' '+item.seriesName+'&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-weight:bold">'+item.data+'</span><span style="font-size:12px">%</span>';
        });
        return res
      }
    },
  xAxis: {
      boundaryGap: false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      },
      data:[]
    },
    yAxis: {
      type: 'value',
      show:false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      }
    },
    series: [
      {
        type: 'line',
        data: [],
        name:'-',
        color:'#73c0de',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#73c0de'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#3ba272',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#3ba272'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#fc8452',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#fc8452'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#9a60b4',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#9a60b4'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },
    ]
  },
  cloud_3: {
    title: {
      text: '物理机网络发送量(kb/s)',
      left: 'left',
      textStyle:{
        fontSize:'14px',
        color:'#000000D9'
      }
    },
    legend:{
      show:true,
      right:'0',
      top:'9%',
      type:'scroll'
    },
    grid: {
      left: '0',
      right: '0',
      bottom:'0',
      containLabel: true
    },
    tooltip: {
      show:true,
      trigger: 'axis',
    },
  xAxis: {
      boundaryGap: false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      },
      data:[]
    },
    yAxis: {
      type: 'value',
      show:false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      }
    },
    series: [
      {
        type: 'line',
        data: [],
        name:'-',
        color:'#ea7ccc',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#ea7ccc'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#5470c6',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#5470c6'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#91cc75',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#91cc75'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#fac858',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#fac858'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },
    ]
  },
  cloud_4: {
    title: {
      text: '物理机网络接收量(kb/s)',
      left: 'left',
      textStyle:{
        fontSize:'14px',
        color:'#000000D9'
      }
    },
    legend:{
      show:true,
      right:'0',
      top:'9%',
      type:'scroll'
    },
    grid: {
      left: '0',
      right: '0',
      bottom:'0',
      containLabel: true
    },
    tooltip: {
      show:true,
      trigger: 'axis',
    },
  xAxis: {
      boundaryGap: false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      },
      data:[]
    },
    yAxis: {
      type: 'value',
      show:false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      }
    },
    series: [
      {
        type: 'line',
        data: [],
        name:'-',
        color:'#ee6666',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#ee6666'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#73c0de',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#73c0de'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#3ba272',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#3ba272'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#fc8452',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#fc8452'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },
    ]
  },
  cloud_5: {
    title: {
      text: '物理机磁盘读取量O(kb/s)',
      left: 'left',
      textStyle:{
        fontSize:'14px',
        color:'#000000D9'
      }
    },
    legend:{
      show:true,
      right:0,
      top:'9%',
      type:'scroll'
    },
    grid: {
      left: '0',
      right: '0',
      bottom:'0',
      containLabel: true
    },
    tooltip: {
      show:true,
      trigger: 'axis',
    },
  xAxis: {
      boundaryGap: false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      },
      data:[]
    },
    yAxis: {
      type: 'value',
      show:false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      }
    },
    series: [
      {
        type: 'line',
        data: [],
        name:'-',
        color:'#9a60b4',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#9a60b4'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#ea7ccc',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#ea7ccc'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#5470c6',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#5470c6'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#91cc75',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#91cc75'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },
    ]
  },
  cloud_6: {
    title: {
      text: '物理机磁盘写入量I(kb/s)',
      left: 'left',
      textStyle:{
        fontSize:'14px',
        color:'#000000D9'
      }
    },
    legend:{
      show:true,
      right:0,
      top:'9%',
      type:'scroll'
    },
    grid: {
      left: '0',
      right: '0',
      bottom:'0',
      containLabel: true
    },
    tooltip: {
      show:true,
      trigger: 'axis',
    },
  xAxis: {
      boundaryGap: false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      },
      data:[]
    },
    yAxis: {
      type: 'value',
      show:false,
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      }
    },
    series: [
      {
        type: 'line',
        data: [],
        name:'-',
        color:'#fac858',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#fac858'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#ee6666',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#ee6666'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#73c0de',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#73c0de'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [],
        name:'-',
        color:'#3ba272',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#3ba272'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },
    ]
  },
  server_5: {
    title: {
      text: 'CPU使用率',
      bottom:0,
      left:'center',
      textStyle:{
          fontSize:16
      }
    },
      series: [{
          type: 'liquidFill',
          radius: '72%',
          data: [],
          color:['#91CC75'],
          outline: {
              show: false
          },
          label:{
            color:'#91CC75',
            fontSize:'20px',
            formatter: function(param) {
              if(param.data === null)
                return '无数据';
              else
                return Number((param.data*100).toFixed(2)) + '%'
            },
          }
      }]
  },
  server_6: {
    title: {
      text: 'CPU温度',
      bottom:0,
      left:'center',
      textStyle:{
          fontSize:16
      }
    },
    
      series: [
      {
        type: 'gauge',
        center: ['50%', '63%'],
        startAngle: 200,
        endAngle: -20,
        min: 0,
        max: 100,
        splitNumber: 20,
        itemStyle: {
          color: '#FD7347'
        },
        progress: {
          show: true,
          width: 10
        },
        pointer: {
          show: false
        },
        axisLine: {
          lineStyle: {
            width: 10
          }
        },
        axisTick: {
          distance: -45,
          splitNumber: 5,
          lineStyle: {
            width: 1,
            color: '#999'
          }
        },
        splitLine: {
          distance: -47,
          length: 8,
          lineStyle: {
            width: 1,
            color: '#999'
          }
        },
        axisLabel: {
          distance: -11,
          color: '#999',
          fontSize: 10
        },
        anchor: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          valueAnimation: true,
          width: '60%',
          lineHeight: 40,
          borderRadius: 8,
          offsetCenter: [0, '-15%'],
          fontSize: 20,
          fontWeight: 'bolder',
          formatter: '{value} °C',
          color: 'auto'
        },
        data: [
          {
            value: 39
          }
        ]
      }
    ]
  },
  server_7: {
    title: {
      text: '内存使用率',
      bottom:0,
      left:'center',
      textStyle:{
          fontSize:16
      }
    },
    series: [{
          type: 'liquidFill',
          radius: '72%',
          data: [0],
          color:['#91CC75'],
          outline: {
              show: false
          },
          label:{
            color:'#91CC75',
            fontSize:'20px',
            formatter: function(param) {
              if(param.data === null)
                return '无数据';
              else
                return Number((param.data*100).toFixed(2)) + '%'
            },
          }
      }]
  },
  server_8: {
    title: {
      text: '磁盘使用率',
      bottom:0,
      left:'center',
      textStyle:{
          fontSize:16
      }
    },
    series: [{
          type: 'liquidFill',
          radius: '62%',
          data: [0.9],
          color:['#91CC75'],
          outline: {
              show: false
          },
          label:{
            color:'#91CC75',
            fontSize:'20px'
            }
      }]
  },
  server_14: {
    title: {
      text: 'CPU电压',
      bottom:0,
      left:'center',
      textStyle:{
          fontSize:16
      }
    },
    series: [
      {
        type: 'gauge',
        center: ['50%', '57%'],
        min: 0,
        max: 5,
        splitNumber: 10,
        radius: '80%',
        axisLine: {
          lineStyle: {
            color: [[1, '#f00']],
            width: 3
          }
        },
        splitLine: {
          distance: -13,
          length: 10,
          lineStyle: {
            color: '#f00'
          }
        },
        axisTick: {
          distance: -8,
          length: 5,
          lineStyle: {
            color: '#f00'
          }
        },
        axisLabel: {
          distance: -30,
          color: '#f00',
          fontSize: 14
        },
        pointer: {
          offsetCenter: [0, '10%'],
          icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
          length: '96%',
          itemStyle: {
            color: '#000'
          }
        },
        detail: {
          valueAnimation: true,
          precision: 1
        },
        title: {
          offsetCenter: [0, '-50%']
        },
        data: [
          {
            value: 1.0,
            name: 'V',
            detail:{fontSize:18}
          }
        ]
      },
      {
        type: 'gauge',
        radius: '74%',
        center: ['50%', '57%'],
        splitNumber: 0,
        axisLine: {
          lineStyle: {
            color: [[1, '#000']],
            width: 2
          }
        },
        splitLine: {
          distance: -4,
          length: 0,
          lineStyle: {
            color: '#000'
          }
        },
        anchor: {
          show: true,
          size: 10,
          itemStyle: {
            color: '#000'
          }
        }
      }
    ]
  },
  server_monitor_9: {
    title: {
        text: 'CPU使用情况',
        textStyle:{
            fontSize:14
        },
        subtext:'',
        x: 'left',
        y: 'top'
    },
    label:{
        show:false,
        rotate:45,
    },
    xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    },
    yAxis: {
      name:'(使用率%)',
        type: 'value',
    },
    tooltip:{trigger: 'axis'},
    grid: {
        left: '2%',
        right: '4%',
        top: '25%',
        bottom: '0%',
        containLabel: true
    },
    series: [
        {
        data: ['-', '-', '-', '-', '-', '-', '-'],
        type: 'line',
        // smooth: true,
        symbolSize: 0
        }
    ]
  },
  server_monitor_10: {
    title: {
        text: '内存使用情况',
        textStyle:{
            fontSize:14
        },
        subtext:'',
        x: 'left',
        y: 'top'
    },
    label:{
        show:false,
        rotate:45
    },
    tooltip:{trigger: 'axis'},
    xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    },
    yAxis: {
      name:'(使用率%)',
        type: 'value'
    },
    grid: {
        left: '2%',
        right: '4%',
        top: '25%',
        bottom: '0%',
        containLabel: true
    },
    series: [
        {
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        type: 'line',
        // smooth: true,
        symbolSize: 0
        }
    ]
  },
  server_monitor_11: {
    title: {
      text: 'CPU温度电压监控',
      textStyle:{
          fontSize:14
      },
          subtext:'',
          x: 'left',
          y: 'top'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top:'13%',
      // data: ['CPU温度', 'Union Ads']
    },
    grid: {
      left: '2%',
      right: '4%',
      top: '25%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    },
    yAxis: [
      {
        name:'(CPU温度℃)',
        type: 'value'
      },
      {
        name:'(CPU电压V)',
        type: 'value',
        min:0,
        max:5,
        // splitLine:{lineStyle:{type:'dashed'}}
      },
    ],
    series: [
      {
        name: 'CPU温度',
        type: 'line',
        // stack: 'Total',
        data: [120, 132, 101, 134, 90, 230, 210],
      //   smooth: true,
        symbolSize: 0
      },
      {
        name: 'CPU电压',
        type: 'line',
        // stack: 'Total',
        // yAxisIndex:1,
        data: [0.2, 1.5, 0.9, 0.4, 0, 0.3, 0.3,0,1.1,0.7],
      //   smooth: true,
        symbolSize: 0
      }
    ]
  },
  server_monitor_12: {
    title: {
      text: '网络接收/发送流量（即下行/上行传输速率），KB/秒',
      textStyle:{
          fontSize:14
      },
          subtext:'',
          x: 'left',
          y: 'top'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top:'13%',
      // data: ['Email', 'Union Ads']
    },
    grid: {
      left: '2%',
      right: '4%',
      top: '25%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    },
    yAxis: [
      {
        name:'(KB/秒)',
        type: 'value'
      }
    ],
    series: [
      {
        name: '发送',
        type: 'line',
        // stack: 'Total',
        data: [120, 132, 101, 134, 90, 230, 210],
      //   smooth: true,
        symbolSize: 0
      },
      {
        name: '接收',
        type: 'line',
        // stack: 'Total',
        data: [220, 182, 191, 234, 290, 330, 310],
      //   smooth: true,
        symbolSize: 0
      }
    ]
  },
  server_monitor_13: {
    title: {
      text: '网络接收/发送包，包数/秒',
      textStyle:{
          fontSize:14
      },
          subtext:'',
          x: 'left',
          y: 'top'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top:'13%',
      // data: ['Email', 'Union Ads']
    },
    grid: {
      left: '2%',
      right: '4%',
      top: '25%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    },
    yAxis: [
      {
        name:'(包数/秒)',
        type: 'value'
      }
    ],
    series: [
      {
        name: '发送',
        type: 'line',
        // stack: 'Total',
        data: [120, 132, 101, 134, 90, 230, 210],
      //   smooth: true,
        symbolSize: 0
      },
      {
        name: '接收',
        type: 'line',
        // stack: 'Total',
        data: [2, 1, 9, 2, 0, 3, 3],
      //   smooth: true,
        symbolSize: 0
      }
    ]
  },
  report_1: {
    title: {
        text: '部门项目占用硬件资源使用率'
    },
    tooltip: {
        trigger: 'axis'
    },
    legend: {
        top:'10%',
        data: ['vcpus','内存','存储']
    },
    grid: {
        top:'20%',
        left: '1%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        boundaryGap: false,
        data: []
    },
    yAxis: {
        type: 'value',
        name:'%'
    },
    series: [
        {
        name: 'vcpus',
        key: 'cpu',
        type: 'line',
        data: []
        },
        {
        name: '内存',
        key: 'ram',
        type: 'line',
        data: []
        },
        {
        name: '存储',
        key: 'disk',
        type: 'line',
        data: []
        }
    ]
  },
  report_2: {
    title: {
        text: '节点使用率'
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        },
        formatter: function(params){
            var tip = "";
            if(params != null && params.length > 0){
                tip += params[0].name + '<br/>';
                for(var i = 0; i < params.length; i++){
                    tip += params[i].marker + "<b>" + params[i].seriesName + "</b> " + params[i].value + '%<br/>';
                }
            }
            return tip;
        }
    },
    legend: {right: 'top',top:'5%',},
    grid: {
      top:'14%',
        left: '1%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01]
    },
    yAxis: {
        type: 'category',
        data: []
    },
    series: [
        
    ]
  },
  report_3: {
    title:{
        text:'虚拟资源使用人TOP排行'
    },
    grid: {
      top:'14%',
        left: '1%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        }
    },
    xAxis: {
        type: 'value'
    },
    yAxis: {
        type: 'category',
        data: []
    },
    series: [
        {
        data: [],
        type: 'bar',
        itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
            ])
        },
        }
    ]
  },
  report_monitor_1: {
    title: {
      // text: '物理机内存负载率(%)',
      left: 'left',
      textStyle:{
        fontSize:'14px',
        color:'#000000D9'
      }
    },
    legend:{
      show:true,
      right:'0',
      top:'9%'
    },
    grid: {
      left: '1%',
      right: '4%',
      bottom:'0',
      containLabel: true
    },
    tooltip: {
      show:true,
      trigger: 'axis',
      formatter: function(params){
        let res = params[0].name;
        params.forEach((item,index) => {
          res += '<br/>'+item.marker+' '+item.seriesName+'&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-weight:bold">'+item.data+'</span><span style="font-size:12px">%</span>';
        });
        return res
      }
    },
    xAxis: {
      boundaryGap: false,
      data:['14:00','14:02','14:04','14:06','14:08','14:10','14:12','14:14','14:16','14:18','14:20','14:22','14:24','14:26','14:28','14:30','14:32','14:34','14:36','14:38']
    },
    yAxis: {
      type: 'value',
      show:false
    },
    series: [
      {
        type: 'line',
        data: [3,4,3,5,3,2,1,3,5,20,4,3,2,2,1,4,2,5,0,1,4],
        color:'#73c0de',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#73c0de'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      },{
        type: 'line',
        data: [3,4,3,5,3,2,1,3,5,30,3,5,1,2,4,1,5,2,4,3,1],
        color:'#3ba272',
        symbolSize: 0,
        areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
          {offset:0,color:'#3ba272'},
          {offset:1,color:'#fff'},
        ])},
        lineStyle:{width:1}
      }
    ]
  },
  ceph_capacity_1: {
    title: {
      textAlign:'center',
       x: '28%',
       y: '30%',
       text: 0,
       subtext: '对象',
    },
    
    tooltip: {
      trigger: 'item'
    },
    legend: {
       orient: 'vertical',
       x:'right',
       y:'top',
       itemHeight:8,
       itemWidth:12,
       selectedMode:false
    },
    series: [
      {
        // name: 'Access From',
        type: 'pie',
        radius: ['75%', '90%'],
        center:['30%', '50%'],
        silent:true,
        label: {
            position: 'center',
            show: false,
            formatter:({value,percent,dataIndex}) => {
              if(dataIndex == 0){
                let number;
                let display;
                if(value != 0){
                  number = (value/percent)*100;
                  display = ByteFormat(number,'i','str');
                }else{
                  percent = 0;
                }
                return `${percent}%\nof ${display}`
              }else{
                return ''
              }
              
            },
      lineHeight: 16,
      fontSize: 12,
        },
        labelLine: {
          show: false
        },
         data: [
          { value: 30.3*1024*1024, name: '已使用' },
          { value: 40*1024*1024*1024, name: '空闲',itemStyle: {
          color: "rgba(197, 196, 196, 1)"
        } },
         
        ]
      }
    ]
  },
  ceph_capacity_2: {
    title: {
      textAlign:'center',
       x: '28%',
       y: '30%',
       text: 0,
       subtext: '对象',
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
       orient: 'vertical',
       x:'right',
       y:'top',
       itemHeight:8,
       itemWidth:12,
       selectedMode:false
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['75%', '90%'],
        center:['30%', '50%'],
        silent:true,
        label: {
            position: 'center',
            show: false,
      //       formatter:() => {
      //           let str = '23'+'\n'+ 'objects'
      //           return str
      // },
      // color: '#1E7CE8',
      lineHeight: 16,
      fontSize: 12,
  
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
         data: [
          { value: 100, name: 'Healthy:100%' },
          { value: 0, name: 'Misplaced:0%' },
          { value: 0, name: 'Degraded:0%' },
          { value: 0, name: 'Unfound:0%' },
         
        ]
      }
    ]
  },
  ceph_capacity_3: {
    title: {
      textAlign:'center',
       x: '28%',
       y: '30%',
       text: 0,
       subtext: 'PGs',
    },
    
    tooltip: {
      trigger: 'item'
    },
    legend: {
       orient: 'vertical',
       x:'right',
       y:'top',
       itemHeight:8,
       itemWidth:12,
       selectedMode:false
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['75%', '90%'],
        center:['30%', '50%'],
        silent:true,
        label: {
            position: 'center',
            show: false,
            formatter:() => {
                let str = '193'+'\n'+ 'PGs'
                return str
      },
      // color: '#1E7CE8',
      lineHeight: 16,
      fontSize: 12,
  
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
         data: [
          { value: 0, name: 'Clean:0' },
          { value: 0, name: 'Working:0' },
          { value: 193, name: 'Waming:193' },
          { value: 0, name: 'Unknown:0' },
         
        ]
      }
    ]
  },
  ceph_perform_4: {
    title: {
      textAlign:'center',
       x: '28%',
       y: '30%',
       text: 0,
       subtext: 'IOPs',
    },
    // tooltip: {
    //   trigger: 'item'
    // },
    legend: {
       orient: 'vertical',
       x:'right',
       y:'top',
       itemHeight:8,
       itemWidth:12,
       selectedMode:false
    },
    series: [
      {
        type: 'pie',
        radius: ['75%', '90%'],
        center:['30%', '50%'],
        silent:true,
        label: {
            position: 'center',
            show: false,
            formatter:'{b} {c} /s',
      // color: '#1E7CE8',
      lineHeight: 16,
      fontSize: 12,
  
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
         data: [
          { value: 10, name: '读取' },
          { value: 200, name: '写入' },
         
        ]
      }
    ]
  },
  ceph_perform_5: {
    title: {
      textAlign:'center',
       x: '28%',
       y: '30%',
       text: 0
    },
    // tooltip: {
    //   trigger: 'item'
    // },
    legend: {
       orient: 'vertical',
       x:'right',
       y:'top',
       itemHeight:8,
       itemWidth:12,
       selectedMode:false
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['75%', '90%'],
        center:['30%', '50%'],
        silent:true,
        label: {
            position: 'center',
            show: false,
            formatter:'{b} {c} B/s',
      // color: '#1E7CE8',
      lineHeight: 16,
      fontSize: 12,
  
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
         data: [
          { value: 0, name: '读取' },
          { value: 0, name: '写入' },
         
        ]
      }
    ]
  }
}