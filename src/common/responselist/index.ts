import { select<PERSON>oneList } from "@/api/backend/devops";
import { selectMemberListAPI } from "@/api/backend/devops/balance";
import { selectFlavorList } from "@/api/backend/devops/flavor";
import { selectFloatingipList } from "@/api/backend/devops/floatingip";
import { selectNetworkList, selectSubnetList } from "@/api/backend/devops/network";
import { selectServerList } from "@/api/backend/devops/server";

export async function getZoneCombine(data: any) {
    let res:any = await selectZoneList(data);
    console.log("res",res)
    if(res.code == 0){
        res.data.forEach((item,index)=>{
            item.value = item.id;
            item.label = item.zoneName;
        })
        return res.data;
    }else{
        return false;
    }
}

export async function getNetworkCombine(data: any) {
    let res:any = await selectNetworkList(data);
    if(res.code == 0){
        res.data.forEach((item,index)=>{
            item.value = item.id;
            item.label = item.name;
            item.disabled = 0;
        })
        return res.data;
    }else{
        return false;
    }
}

export async function getSubnetCombine(data: any) {
    let res:any = await selectSubnetList(data);
    if(res.code == 0){
        res.data.forEach((item,index)=>{
            item.value = item.thirdSubnetId;
            item.label = item.subnetName;
            item.disabled = 0;
        })
        return res.data;
    }else{
        return false;
    }
}

export async function getFlavorCombine(data: any) {
    let res:any = await selectFlavorList(data);
    if(res.code == 0){
        res.data.forEach((item,index)=>{
            item.value = item.thirdFlavorId;
            item.label = item.name;
            item.disabled = 0;
        })
        return res.data;
    }else{
        return false;
    }
}

export async function getServerCombine(data: any) {
    let res:any = await selectServerList(data);
    if(res.code == 0){
        res.data.forEach((item,index)=>{
            // item.value = item.id;
            // item.label = item.name;
            item.disabled = 0;
        })
        return res.data;
    }else{
        return false;
    }
}

export async function getFloatipCombine(data: any) {
    let res:any = await selectFloatingipList(data);
    if(res.code == 0){
        res.data.forEach((item,index)=>{
            item.value = item.id;
            item.label = item.name;
            item.disabled = 0;
        })
        return res.data;
    }else{
        return false;
    }
}

export async function getMemberCombine(data: any) {
    let res:any = await selectMemberListAPI(data);
    if(res.code == 0){
        res.data.forEach((item,index)=>{
            // item.value = item.id;
            // item.label = item.name;
            item.disabled = 0;
        })
        return res.data;
    }else{
        return false;
    }
}