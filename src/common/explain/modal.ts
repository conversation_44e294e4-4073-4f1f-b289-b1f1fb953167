export const content = {
    securitygroup_add: `安全组是作用于虚拟机网络接口上的一组IP过滤规则。安全组创建完成后，你可以向其中添加规则。`,
    securitygroup_update: `安全组是作用于虚拟机网络接口上的一组IP过滤规则。`,
    securitygroup_rule: `虚机可以关联安全组，组中的规则定义了允许哪些访问到达被关联的虚机。安全组由以下三个主要组件组成：\n\n规则： 您可以指定期望的规则模板或者使用定制规则，选项有定制TCP规则、定制UDP规则或定制ICMP规则。\n\n打开端口/端口范围： 您选择的TCP和UDP规则可能会打开一个或一组端口.选择"端口范围"，您需要提供开始和结束端口的范围.对于ICMP规则您需要指定ICMP类型和代码.\n\n远程： 您必须指定允许通过该规则的流量来源。可以通过以下两种方式实现：IP地址块(CIDR)或者来源地址组(安全组)。如果选择一个安全组作为来访源地址，则该安全组中的任何虚机都被允许使用该规则访问任一其它虚机。`,
    server_base: `如果您选择一个可用域并且计划在‘启动源’这步使用‘从卷启动’选项，确保您为虚机选择的可用域与您启动卷所在的域相同。\n\n如果您想创建一个使用临时存储的虚机，意味着当虚机被删除时数据会丢失，那么从以下启动项中选择一个：\n\n镜像：本选项使用一个镜像启动虚机。\n虚机快照：此选项使用一个虚机快照来启动虚机。\n\n如果您想创建一个使用持久存储的虚机，意味着当虚机被删除时数据被保存，那么选择从以下启动项中选择一个：\n\n镜像（带有选中“创建新卷”）：此选项使用镜像来启动虚机，并且创建一个新卷来持久化虚机数据。您可以指定卷大小并指定在删除虚机时是否删除虚机。\n卷：这个选项使用一个已存在的卷。它不创建新卷。您可以选择在删除虚机时删除卷。注意：当选择卷时，您只能启动一个虚机。\n卷快照：此选项使用卷快照启动虚机，并且创建一个新卷来持久化虚机数据。您可以选择在删除虚机时删除卷。\n\n您为虚机选择的类型决定计算、存储和内存资源数量，这些资源将会决定虚机配置。\n\n您所选择的虚机类型必须有足够的资源用来支持您想要创建的虚机类型。未能给您的虚机提供足够资源的虚机会被表Available中的一个黄色警告符号标识出。\n\n管理员负责创建和管理类型。可以为您或特定的项目创建一个自定义类型，特定的项目与另外一个项目的用户共享这个类型。如果您需要一个自定义类型，请联系您的管理员。`,
    server_network: `提供者网络是由管理员创建。 这些网络映射到数据中心里现存的物理网络。\n\n项目网络是由用户创建。 这些网络完全隔离，为具体项目专用。\n\n外部网络由管理员创建。 如果您希望虚机与数据中心的外部网络能够通信， 那么可以在您的项目网络和外部网络之间添加一个路由。 然后就可以通过 网络拓扑视图，使用此路由连接这两个网络。\n\n一个浮动IP允许虚机从外部网络寻址。 虚机在创建时浮动IP地址并不会分配，在虚机创建后可以指定。 要附加一个浮动IP，在Instances查看并单击一个虚机的  Actions菜单到右边。 然后，选择 Associate Floating IP 并输入必要的描述。\n\n管理员建立可以绑定到虚机的浮动IP池。\n\n网络特性\n一个子网确定了一个网段，被指定为CIDR格式。 典型的CIDR格式类似192.xxx.x.x/24。\n\n如果网络是共享的，在一个项目中的所有用户都可以访问这个网络。\n\n当网络的Admin State 被设置为Up, 网络将会是可用的。如果您还没有准备让别人访问该网络， 可以把 Admin State 设置为Down。\n\n状态表明网络是否处于活跃连接。\n\n安全组定义了 IP 过滤规则的集合，这些规则决定网络流量如何到达和离开虚机。为了将来为虚机定义访问可选规则，用户可以往现有的安全组添加额外的规则。要创建额外规则，请转至网络 | 安全组视图，然后找到安全组，单击管理规则。\n\n安全组为具体项目专用，不能跨项目共享。\n\n如果虚机创建前没有绑定安全组，在虚机部署后您将只有非常有限的访问权限。您只能从VNC控制台访问虚机。`,
    server_system:`虚机名是必须的，用来帮助您在仪表盘中区分虚机。\n\n有两种方式创建密钥对。从一个Linux系统， 使用ssh-keygen 命令生成密钥对。\n\nssh-keygen -t rsa -f cloud.key\n\n这个命令生成一对密钥：一个私钥 (cloud.key) 和一个公钥(cloud.key.pub)。\n\n从一个Windows系统，您可以使用PuTTYGen生成公/私密钥。 使用PuTTY密钥生成器来创建和保存这些密钥， 然后复制红色高亮框中的公钥到您的 .ssh/authorized_keys文件。`,
    // server_source: `如果您想创建一个使用临时存储的虚机，意味着当虚机被删除时数据会丢失，那么从以下启动项中选择一个：\n\n镜像：本选项使用一个镜像启动虚机。\n虚机快照：此选项使用一个虚机快照来启动虚机。\n\n如果您想创建一个使用持久存储的虚机，意味着当虚机被删除时数据被保存，那么选择从以下启动项中选择一个：\n\n镜像（带有选中“创建新卷”）：此选项使用镜像来启动虚机，并且创建一个新卷来持久化虚机数据。您可以指定卷大小并指定在删除虚机时是否删除虚机。\n卷：这个选项使用一个已存在的卷。它不创建新卷。您可以选择在删除虚机时删除卷。注意：当选择卷时，您只能启动一个虚机。\n卷快照：此选项使用卷快照启动虚机，并且创建一个新卷来持久化虚机数据。您可以选择在删除虚机时删除卷。`,
    // server_flavor: `您为虚机选择的类型决定计算、存储和内存资源数量，这些资源将会决定虚机配置。\n\n您所选择的虚机类型必须有足够的资源用来支持您想要创建的虚机类型。未能给您的虚机提供足够资源的虚机会被表Available中的一个黄色警告符号标识出。\n\n管理员负责创建和管理类型。可以为您或特定的项目创建一个自定义类型，特定的项目与另外一个项目的用户共享这个类型。如果您需要一个自定义类型，请联系您的管理员。`,
    // server_securitygroup: `安全组定义了 IP 过滤规则的集合，这些规则决定网络流量如何到达和离开虚机。为了将来为虚机定义访问可选规则，用户可以往现有的安全组添加额外的规则。要创建额外规则，请转至网络 | 安全组视图，然后找到安全组，单击管理规则。\n\n安全组为具体项目专用，不能跨项目共享。\n\n如果虚机创建前没有绑定安全组，在虚机部署后您将只有非常有限的访问权限。您只能从VNC控制台访问虚机。`,
    // server_keypair: `有两种方式创建密钥对。从一个Linux系统， 使用ssh-keygen 命令生成密钥对。\n\nssh-keygen -t rsa -f cloud.key\n\n这个命令生成一对密钥：一个私钥 (cloud.key) 和一个公钥(cloud.key.pub)。\n\n从一个Windows系统，您可以使用PuTTYGen生成公/私密钥。 使用PuTTY密钥生成器来创建和保存这些密钥， 然后复制红色高亮框中的公钥到您的 .ssh/authorized_keys文件。`,
    server_allocation: `当虚机启动时，自定义脚本被绑定到虚机执行特定的动作。比如，如果您无法在客户机操作系统内安装cloud-init，您可以使用自定义脚本获取一个公钥并把它添加到用户账户中。\n\n直接在自定义脚本输入框中写入您的脚本。如果您的浏览器支持HTML5文件API，您可以选择从文件加载您的脚本。您的脚本的大小不能超过16Kb。\n\n当创建虚机时，一个可用的高级选项是磁盘分区。有两种磁盘分区选项目。选择自动调整磁盘并设置为一个单独的分区。选择手动允许您在一个磁盘上创建多个分区。\n\n如果您想将元数据写入指定的配置驱动器中，请选中配置驱动 复选框。当虚机启动时，就会追加配置驱动并访问元数据。`,
    server_snap: `快照是保存了运行中虚机的磁盘状态的镜像。`,
    server_floatip: `请为选中的虚机或端口选择要绑定的IP地址。`,
    server_cvolume: `连接卷到正在运行的虚机上`,
    server_dvolume: `从正在运行的虚机上分离卷`,
    load_balancer: `负载均衡器占用一个 neutron 网络端口和一个子网分配的 IP 地址。\n\nIP 地址： IP地址必须为格式良好的 IPv4 或 IPv6 地址。 系统尝试为负载均衡器分配提供的 IP 地址。 如果没有提供 IP 地址，则需要你手动分配一个 IP。\n\n子网： 给分配负载均衡器 IP 地址的网络。`,
    balancer_listener:`负载均衡器中的每一个监听的端口独立地配置并与负载均衡器关联。 多个监听器可以关联到同一个负载均衡器上， 但每个都必须使用唯一的端口。\n\n协议： 监听器之间通信的协议。 TERMINATED_HTTPS 协议只有在 key-manager 服务可用 并且你有权限查询证书容器和密钥的情况下才有用。\n\n端口： 用来通信的成员监听端口。必须是 1 至 65536 之间的整数。\n\n连接限制： 该监听器允许的最大连接数。默认值 -1 表示无限制。\n\n默认资源池 ID： 无七层策略匹配时用于该监听器的资源池 ID。\n\n插入报头： 插入到 HTTP 头部的额外报头。 仅支持 "X-Forwarded-For"、"X-Forwarded-Port" 和 "X-Forwarded-Proto"。\n\n客户数据超时时限： 前端客户闲置超时时限，单位是毫秒，默认值：50000。\n\nTCP 监测超时时限： 等待额外 TCP 内容监测包的超时时限，单位是毫秒，默认值：0。\n\n成员连接超时时限： 后端成员连接超时时限，单位是毫秒，默认值：5000。\n\n成员数据超时时限： 后端成员闲置超时时限，单位是毫秒，默认值：50000。\n\nAllowed Cidrs: A newline separated list of cidrs to be allowed to connect to the listener. An empty list means allow from any.\n\nTLS Cipher String: A string of the allowed ciphers using the OpenSSL syntax. The syntax is a colon separated list of the chiphers, ex. "TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256" Note, don't include quotation marks. An empty string sets the default TLS Cipher String configured in Octavia.`,
    balancer_pool:`资源池代表提供负载均衡器的一组成员。\n\n算法： 用于分发消息给资源池成员的负载均衡算法。\n\t· LEAST_CONNECTIONS：分发至最少活动连接次数的虚机。\n\t· ROUND_ROBIN：在多个虚机之间轮询分发。\n\t· SOURCE_IP：来自相同源IP地址的请求连续的发送到同一虚机。\n\n协议： The protocol for which this pool and its members listen. A valid value is HTTP, HTTPS, PROXY, PROXYV2, TCP, UDP or SCTP.\n\n会话持久化： 用于为资源池成员分发消息的会话持久化类型。\n\t· SOURCE_IP：基于源 IP 地址的会话持久化。\n\t· HTTP_COOKIE：基于 HTTP COOKIE 的会话持久化。\n\t· APP_COOKIE：基于应用 COOKIE 的会话持久化。\n\nTLS Enabled: Enable TLS for backend re-encryption, communications between the load balancer and the member servers are encrypted.\n\nTLS Cipher String: A string of the allowed ciphers using the OpenSSL syntax. The syntax is a colon separated list of the chiphers, ex. "TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256" Note, don't include quotation marks. An empty string sets the default TLS Cipher String configured in Octavia.`,
    balancer_member:`成员是在负载均衡器中通信的实际 IP 地址。 每个成员必须有一个唯一关联的 IP 地址和端口。\n\n可用虚机表显示了存在的可以添加为资源池成员的计算虚机。 使用“添加外部成员”按钮可以添加在可用虚机表中找不到的成员。\n\nIP 地址： 用于从负载均衡器中接收消息成员的 IP 地址。 必须使用格式良好的 IPv4 或 IPv6 地址。\n\n子网： 关联成员 IP 地址的网络。\n\n端口： 用来通信的成员监听端口。必须是 1 至 65536 之间的整数。\n\n权重： The weight of a member determines the portion of requests or connections it services compared to the other members of the pool. A value of 0 means the member does not receive new connections but continues to service existing connections. A higher weight means it will receive more traffic. Must be a number from 0 to 256.\n\n健康监控器地址： 用于后端成员健康监控的 IP 地址。 默认是空时监测该成员地址。\n\n健康监控器端口： 用于后端成员健康监控的协议端口。 默认是空时监测该成员协议端口。\n\n备份： 该成员是否为备份？备份成员仅当所有非备份成员不可用时才接收通信。`,
    balancer_health:`健康监控用来确定你的资源池成员的健康状况。 健康检查通常在资源池中与每个成员隔离运行，健康检查的结果通过成员接收到新的连接来确定。 每个资源池只能有一个健康监控。\n\n延迟： 健康检查之间时间间隔，必须大于或等于超时时限。\n\n最大重试次数： 在将成员标记为未活动状态之前，允许连接失败的次数。 必须是 1 至 10 之间的整数。\n\n最大失败重试次数： 在将成员标记为错误之前，允许连接失败的次数。 必须是 1 至 10 之间的整数。默认值是 3。\n\n超时时限： 健康检查请求超时的时间。 必须为大于或等于 0 的整数，并小于或等于间隔时间。\n\nHTTP 方法： 用来进行健康检查的 HTTP 请求方式。\n\n预期的状态码： 健康检查成功返回的期望 HTTP 状态码。 必须为单个数字，逗号分隔的一组数字或一个范围（用连接符分隔的两个数字）。\n\nURL 路径： 成员发送健康检查 HTTP 请求的目标地址，必须为合法的 URL 路径。`,
    balancer_seven:`一个七层策略是关联于监听器的七层规则的集，监听器可能有一个关联的资源池\n\n行为： 七层策略行为。REJECT，REDIRECT_TO_URL，REDIRECT_TO_POOL 其中之一\n\t· REJECT：请求被拒绝并返回合适的状态码，并不被转发给任何资源池\n\t· REDIRECT_TO_URL：请求被作为一个 HTTP 重定向发送到 redirect_url 参数里定义的 URL。\n\t· REDIRECT_TO_POOL：请求被转发到该七层策略关联的资源池。\n\n位置： 该策略在监听器里的位置。位置从一开始。\n\n重定向资源池 ID： 与该策略相匹配的请求将被重定向到由 ID 指定的资源池。仅当行为是 REDIRECT_TO_POOL 时有效。\n\n重定向 URL： 与该策略相匹配的请求将被重定向指定的 URL。仅当行为是 REDIRECT_TO_URL 时有效。`,
    balancer_seven_rule:`一个七层规则是返回是或否的单一简单逻辑测试\n\n类型： 七层规则类型。COOKIE，FILE_TYPE，HEADER，HOST_NAME，PATH 之中之一。\n\t· COOKIE：该规则查找以键参数命名的 COOKIE 并将其与值参数相比较。\n\t· HEADER：该规则查找以键参数命名的报头并将其与值参数相比较。\n\t· FILE_TYPE：该规则将 URI 的后缀与值参数相比较。（例如：“txt”，“jpg”。）\n\t· PATH：该规则将 HTTP URI 的路径部分与值参数相比较。\n\t· HOST_NAME：该规则将请求中的 HTTP/1.1 主机名与值参数相比较。\n\n比较类型： 七层规则的比较类型。CONTAINS，ENDS_WITH，EQUAL_TO，REGEX，STARTS_WITH 其中之一。\n\t· REGEX：Perl 正则表达式匹配。\n\t· STARTS_WITH：字符串前缀。\n\t· ENDS_WITH：字符串后缀。\n\t· 包含：字符串包含。\n\t· EQUAL_TO：字符串相同。\n\n键： 用于比较的键。例如 COOKIE 的名字。\n\n值： 用于比较的值。例如要比较的文件类型。\n\n反转： 为是时该规则将被反转。例如，反转为是时相同将变成不相同。`,
    float_add: `从指定的浮动IP池中分配一个浮动IP。`,
    flavor_add: `虚机类型定义了RAM和磁盘的大小、CPU数，以及其他资源，用户在部署虚机的时候可选用。`,
    host_aggr: `主机聚合通过将主机组合到一起来把可用区域划分成逻辑单元。创建一个主机聚合，然后选择要放里面的主机。`,
    host_aggr_add: `将主机添加到这个聚合。主机可以存在于多个聚合中。`,
    host_aggr_update: `将主机从聚合中删除。主机可以存在于多个聚合中。`,
    image_add: `<b>镜像名称</b><br>镜像名称。<br><b>镜像描述</b><br>镜像的描述信息。<br><b>源类型</b><br>从本地文件或一个指定的 URL 上传镜像。<br><b>位置</b><br>如果通过 HTTP URL 上传镜像，镜像的位置必须可用被镜像服务访问，URL 需要指向镜像的二进制数据。<br><b>镜像格式</b><br>从下拉菜单中选择磁盘的格式。<br><b>内核</b><br>从下拉菜单中选择运行的内核。<br><b>内存盘</b><br>从下拉菜单中选择 Ramdisk。<br><b>架构</b><br>镜像的架构。<br><b>最小磁盘</b><br>引导镜像所需的磁盘空间（以 GB 为单位）<br><b>最低内存</b><br>引导镜像所需的内存数量（以 MB 为单位）<br><b>可见性</b><br>镜像的访问权限。<br><b>受保护的</b><br>如果设为 'Yes'，镜像将不能被删除。`,
    key_add: `密钥对是您在虚机启动后登陆进去的一种方式。 选择一个您易于辨识的密钥对名字， 名字只能由半角的字母、空格、破折号和下划线组成。`,
    network_add: `创建一个新的网络。额外地，网络中的子网可以在向导的下一步中创建。`,
    network_update: `在此可以更新网络的可编辑属性`,
    qos_add: `组类型是在 OpenStack 创建组的时候可以指定的一个类型或标签。它通常映射到这个组所使用的存储后端驱动器的性能指标集合。例如，“Performance”、“SSD”、“Backup”等。这等同于cinder type-create命令。一旦组类型被创建，单击" extra spec"按钮以设置组类型的 extra spec 键值对。`,
    qos_update: `为QoS Spec创建/更新一个新的"spec"键-值对.\n\nQoS spec需要有效的键名。查阅Cinder■文档了解支持的前端键名及后端键名。\n\n前推示例：\n• 键：total_iops_sec 及值：5000\n• 键:total_bytes_sec_max 及值：512000\n• 键：size_iops_sec 及值：16\n\n后端示例：\n• 键：minIOPS及值：20 （数值应小于maxIOPS）\n• 键：maxIOPS及值：5000 （数值应大于minIOPS）\n• 键：burstIOPS 及值：5000 （数值应大于 minIOPS`,
    route_add: `基于特殊参数创建一路由。\n\n仅当设置了外部网络时，启用SNAT才会生效。`,
    route_update: `您可以在这里更新路由的可编辑属性。`,
    volumebackup:`<b>卷备份:</b> 卷备份将使用某个cinder备份驱动 (对象存储服务, Ceph, NFS, 等……)存储。 为了创建备份您需要启用上述的某个服务。<br/><br/>如果在这里选择了一个快照，则只有这个卷选择的快照 将会被备份。<br/><br/>如果不提供容器名字，默认会使用 backups 来作为名字。 备份会跟它们原来的卷大小一样。`,
    ha_add:`<h3 style="font-weight:bold">说明：</h3>Create a failover segment of hypervisor hosts.<br><br><b>恢复类型:</b><br><br>auto: Nova选择新的计算主机来疏散在故障计算主机上运行的实例。<br><br>reserved_host : segment中配置的一个reserved host将用于疏散在故障计算主机上运行的实例。<br><br>auto_priority: 首先它将尝试“auto” recovery method，如果失败，它将尝试使用“reserved_host” recovery method。<br><br>rh_priority: 它与“auto_priority” recovery method完全相反。<br><br><b>请注意：</b> Masakari目前未使用“Service Type”，但它是一个必填字段，因此默认值设置为“compute”，无法更改。`
}