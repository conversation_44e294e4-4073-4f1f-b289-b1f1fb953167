[{"key": "0", "title": "CIM Processor Allocation Setting", "description": "Properties related to the resource allocation settings of a processor (CPU) from Common Information Model (CIM) schema (http://www.dmtf.org/standards/cim). These are properties that identify processor setting data and may be specified to volume, image, host aggregate, flavor and Nova server as scheduler hint. For each property details, please refer to http://schemas.dmtf.org/wbem/cim-html/2/CIM_ProcessorAllocationSettingData.html.", "children": [{"key": "1", "title": "Instruction Set", "label": "CIM_PASD_InstructionSet", "options": ["x86:i386", "x86:i486", "x86:i586", "x86:i686", "x86:64", "IA-64:IA-64", "AS/400:TIMI", "Power:Power_2.03", "Power:Power_2.04", "Power:Power_2.05", "Power:Power_2.06", "S/390:ESA/390", "S/390:z/Architecture", "S/390:z/Architecture_2", "PA-RISC:PA-RISC_1.0", "PA-RISC:PA-RISC_2.0", "ARM:A32", "ARM:A64", "MIPS:MIPS_I", "MIPS:MIPS_II", "MIPS:MIPS_III", "MIPS:MIPS_IV", "MIPS:MIPS_V", "MIPS:MIPS32", "MIPS64:MIPS64", "Alpha:Alpha", "SPARC:SPARC_V7", "SPARC:SPARC_V8", "SPARC:SPARC_V9", "SPARC:SPARC_JPS1", "SPARC:UltraSPARC2005", "SPARC:UltraSPARC2007", "68k:68000", "68k:68010", "68k:68020", "68k:68030", "68k:68040", "68k:68060"], "description": "Identifies the instruction set of the processor within a processor architecture."}, {"key": "2", "title": "Instruction Set Extension", "label": "CIM_PASD_InstructionSetExtensionName", "multiple": true, "options": ["x86:3DNow", "x86:3DNowExt", "x86:ABM", "x86:AES", "x86:AVX", "x86:AVX2", "x86:BMI", "x86:CX16", "x86:F16C", "x86:FSGSBASE", "x86:LWP", "x86:MMX", "x86:PCLMUL", "x86:RDRND", "x86:SSE2", "x86:SSE3", "x86:SSSE3", "x86:SSE4A", "x86:SSE41", "x86:SSE42", "x86:FMA3", "x86:FMA4", "x86:XOP", "x86:TBM", "x86:VT-d", "x86:VT-x", "x86:EPT", "x86:SVM", "PA-RISC:MAX", "PA-RISC:MAX2", "ARM:DSP", "ARM:Jazelle-DBX", "ARM:Thumb", "ARM:Thumb-2", "ARM:ThumbEE)", "ARM:VFP", "ARM:NEON", "ARM:TrustZone", "MIPS:MDMX", "MIPS:MIPS-3D", "Alpha:BWX", "Alpha:FIX", "Alpha:CIX", "Alpha:MVI"], "description": "Identifies the instruction set extensions of the processor within a processor architecture."}, {"key": "3", "title": "Processor Architecture", "label": "CIM_PASD_ProcessorArchitecture", "options": ["x86", "IA-64", "AS/400", "Power", "S/390", "PA-RISC", "ARM", "MIPS", "Alpha", "SPARC", "68k"], "description": "Identifies the processor architecture of the processor."}]}, {"key": "4", "title": "CIM Resource Allocation Setting Data", "description": "Properties from Common Information Model (CIM) schema (http://www.dmtf.org/standards/cim) that represent settings specifically related to an allocated resource that are outside the scope of the CIM class typically used to represent the resource itself. These properties may be specified to volume, host aggregate and flavor. For each property details, please refer to http://schemas.dmtf.org/wbem/cim-html/2/CIM_ResourceAllocationSettingData.html.", "children": [{"key": "5", "title": "Address", "label": "CIM_RASD_Address", "description": "The address of the resource."}, {"key": "6", "title": "Address On Parent", "label": "CIM_RASD_AddressOnParent", "description": "Describes the address of this resource in the context of the Parent."}, {"key": "7", "title": "Allocation Units", "label": "CIM_RASD_AllocationUnits", "description": "This property specifies the units of allocation used by the Reservation and Limit properties."}, {"key": "8", "title": "Automatic Allocation", "label": "CIM_RASD_AutomaticAllocation", "checkbox": true, "description": "This property specifies if the resource will be automatically allocated."}, {"key": "9", "title": "Automatic Deallocation", "label": "CIM_RASD_AutomaticDeallocation", "checkbox": true, "description": "This property specifies if the resource will be automatically de-allocated."}, {"key": "10", "title": "Connection", "label": "CIM_RASD_Connection", "description": "The thing to which this resource is connected."}, {"key": "11", "title": "Consumer Visibility", "label": "CIM_RASD_ConsumerVisibility", "options": ["Unknown", "Passed-Through", "Virtualized", "Not represented", "DMTF reserved", "Vendor Reserved"], "description": "Describes the consumers visibility to the allocated resource."}, {"key": "12", "title": "Host Resource", "label": "CIM_RASD_HostResource", "description": "This property exposes specific assignment of resources."}, {"key": "13", "title": "Limit", "label": "CIM_RASD_Limit", "description": "This property specifies the upper bound, or maximum amount of resource that will be granted for this allocation."}, {"key": "14", "title": "Mapping Behavior", "label": "CIM_RASD_MappingBehavior", "options": ["Unknown", "Not Supported", "Dedicated", "Soft Affinity", "Hard Affinity", "DMTF Reserved", "Vendor Reserved"], "description": "Specifies how this resource maps to underlying resources. If the HostResource array contains any entries, this property reflects how the resource maps to those specific resources."}, {"key": "15", "title": "Other Resource Type", "label": "CIM_RASD_OtherResourceType", "description": "A string that describes the resource type when a well defined value is not available and ResourceType has the value 'Other'."}, {"key": "16", "title": "Parent", "label": "CIM_RASD_Parent", "description": "The Parent of the resource."}, {"key": "17", "title": "Pool ID", "label": "CIM_RASD_PoolID", "description": "This property specifies which ResourcePool the resource is currently allocated from, or which ResourcePool the resource will be allocated from when the allocation occurs."}, {"key": "18", "title": "Reservation", "label": "CIM_RASD_Reservation", "description": "This property specifies the amount of resource guaranteed to be available for this allocation."}, {"key": "19", "title": "Resource Sub Type", "label": "CIM_RASD_ResourceSubType", "description": "A string describing an implementation specific sub-type for this resource."}, {"key": "20", "title": "Resource Type", "label": "CIM_RASD_ResourceType", "options": ["Other", "Computer System", "Processor", "Memory", "IDE Controller", "Parallel SCSI HBA", "FC HBA", "iSCSI HBA", "IB HCA", "Ethernet Adapter", "Other Network Adapter", "I/O Slot", "I/O Device", "Floppy Drive", "CD Drive", "DVD drive", "Disk Drive", "Tape Drive", "Storage Extent", "Other storage device", "Serial port", "Parallel port", "USB Controller", "Graphics controller", "IEEE 1394 Controller", "Partitionable Unit", "Base Partitionable Unit", "Power", "Cooling Capacity", "Ethernet Switch Port", "Logical Disk", "Storage Volume", "Ethernet Connection", "DMTF reserved", "Vendor Reserved"], "description": "The type of resource this allocation setting represents."}, {"key": "21", "title": "Virtual Quantity", "label": "CIM_RASD_VirtualQuantity", "description": "This property specifies the quantity of resources presented to the consumer."}, {"key": "22", "title": "Virtual Quantity Units", "label": "CIM_RASD_VirtualQuantityUnits", "description": "This property specifies the units used by the VirtualQuantity property."}, {"key": "23", "title": "Weight", "label": "CIM_RASD_Weight", "description": "This property specifies a relative priority for this allocation in relation to other allocations from the same ResourcePool."}]}, {"key": "24", "title": "CIM Storage Allocation Setting Data", "description": "Properties related to the allocation of virtual storage from Common Information Model (CIM) schema (http://www.dmtf.org/standards/cim). These properties may be specified to volume, host aggregate and flavor. For each property details, please refer to http://schemas.dmtf.org/wbem/cim-html/2/CIM_StorageAllocationSettingData.html.", "children": [{"key": "25", "title": "Access", "label": "CIM_SASD_Access", "options": ["Unknown", "Readable", "Writeable", "Read/Write Supported", "DMTF Reserved"], "description": "Access describes whether the allocated storage extent is 1 (readable), 2 (writeable), or 3 (both)."}, {"key": "26", "title": "Host Extent Name", "label": "CIM_SASD_HostExtentName", "description": "A unique identifier for the host extent."}, {"key": "27", "title": "Host Extent Name Format", "label": "CIM_SASD_HostExtentNameFormat", "options": ["Unknown", "Other", "SNVM", "NAA", "EUI64", "T10VID", "OS Device Name", "DMTF Reserved"], "description": "The HostExtentNameFormat property identifies the format that is used for the value of the HostExtentName property."}, {"key": "28", "title": "Host Extent Name Namespace", "label": "CIM_SASD_HostExtentNameNamespace", "options": ["Unknown", "Other", "VPD83Type3", "VPD83Type2", "VPD83Type1", "VPD80", "NodeWWN", "SNVM", "OS Device Namespace", "DMTF Reserved"], "description": "If the host extent is a SCSI volume, then the preferred source for SCSI volume names is SCSI VPD Page 83 responses."}, {"key": "29", "title": "Host Extent Starting Address", "label": "CIM_SASD_HostExtentStartingAddress", "description": "The HostExtentStartingAddress property identifies the starting address on the host storage extent identified by the value of the HostExtentName property that is used for the allocation of the virtual storage extent."}, {"key": "30", "title": "Host Resource Block Size", "label": "CIM_SASD_HostResourceBlockSize", "description": "Size in bytes of the blocks that are allocated at the host as the result of this storage resource allocation or storage resource allocation request."}, {"key": "31", "title": "Limit", "label": "CIM_SASD_Limit", "description": "The maximum amount of blocks that will be granted for this storage resource allocation at the host."}, {"key": "32", "title": "Other Host Extent Name Format", "label": "CIM_SASD_OtherHostExtentNameFormat", "description": "A string describing the format of the HostExtentName property if the value of the HostExtentNameFormat property is 1 (Other)."}, {"key": "33", "title": "Other Host Extent Name Namespace", "label": "CIM_SASD_OtherHostExtentNameNamespace", "description": "A string describing the namespace of the HostExtentName property if the value of the HostExtentNameNamespace matches 1 (Other)."}, {"key": "34", "title": "Reservation", "label": "CIM_SASD_Reservation", "description": "The amount of blocks that are guaranteed to be available for this storage resource allocation at the host."}, {"key": "35", "title": "Virtual Quantity", "label": "CIM_SASD_VirtualQuantity", "description": "Number of blocks that are presented to the consumer."}, {"key": "36", "title": "Virtual Quantity Units", "label": "CIM_SASD_VirtualQuantityUnits", "description": "This property specifies the units used by the VirtualQuantity property."}, {"key": "37", "title": "Virtual Resource Block Size", "label": "CIM_SASD_VirtualResourceBlockSize", "description": "Size in bytes of the blocks that are presented to the consumer as the result of this storage resource allocation or storage resource allocation request."}]}, {"key": "38", "title": "CIM Virtual System Setting Data", "description": "A set of virtualization specific properties from Common Information Model (CIM) schema (http://www.dmtf.org/standards/cim), which define the virtual aspects of a virtual system. These properties may be specified to host aggregate and flavor. For each property details, please refer to http://schemas.dmtf.org/wbem/cim-html/2/CIM_VirtualSystemSettingData.html.", "children": [{"key": "39", "title": "Automatic Recovery Action", "label": "CIM_VSSD_AutomaticRecoveryAction", "options": ["None", "<PERSON><PERSON>", "Revert to snapshot", "DMTF Reserved"], "description": "Action to take for the virtual system when the software executed by the virtual system fails."}, {"key": "40", "title": "Automatic Shutdown Action", "label": "CIM_VSSD_AutomaticShutdownAction", "options": ["Turn Off", "Save state", "Shutdown", "DMTF Reserved"], "description": "Action to take for the virtual system when the host is shut down."}, {"key": "41", "title": "Automatic Startup Action", "label": "", "options": ["None", "Restart if previously active", "Always startup", "DMTF Reserved"], "description": "Action to take for the virtual system when the host is started."}, {"key": "42", "title": "Automatic Startup Action Delay", "label": "CIM_VSSD_AutomaticStartupActionDelay", "description": "Delay applicable to startup action."}, {"key": "43", "title": "Automatic Startup Action Sequence Number", "label": "CIM_VSSD_AutomaticStartupActionSequenceNumber", "description": "Number indicating the relative sequence of virtual system activation when the host system is started."}, {"key": "44", "title": "Configuration Data Root", "label": "CIM_VSSD_ConfigurationDataRoot", "description": "Filepath of a directory where information about the virtual system configuration is stored."}, {"key": "45", "title": "Configuration File", "label": "CIM_VSSD_ConfigurationFile", "description": "Filepath of a file where information about the virtual system configuration is stored."}, {"key": "46", "title": "Configuration ID", "label": "CIM_VSSD_ConfigurationID", "description": "Unique id of the virtual system configuration."}, {"key": "47", "title": "Creation Time", "label": "CIM_VSSD_CreationTime", "description": "Time when the virtual system configuration was created."}, {"key": "48", "title": "Log Data Root", "label": "CIM_VSSD_LogDataRoot", "description": "Filepath of a directory where log information about the virtual system is stored."}, {"key": "49", "title": "Notes", "label": "CIM_VSSD_Notes", "description": "End-user supplied notes that are related to the virtual system."}, {"key": "50", "title": "Recovery File", "label": "CIM_VSSD_RecoveryFile", "description": "Filepath of a file where recovery relateded information of the virtual system is stored."}, {"key": "51", "title": "Snapshot Data Root", "label": "CIM_VSSD_SnapshotDataRoot", "description": "Filepath of a directory where information about virtual system snapshots is stored."}, {"key": "52", "title": "Suspend Data Root", "label": "CIM_VSSD_SuspendDataRoot", "description": "Filepath of a directory where suspend related information about the virtual system is stored."}, {"key": "53", "title": "Swap File Data Root", "label": "CIM_VSSD_SwapFileDataRoot", "description": "Filepath of a directory where swapfiles of the virtual system are stored."}, {"key": "54", "title": "Virtual System Identifier", "label": "CIM_VSSD_VirtualSystemIdentifier", "description": "VirtualSystemIdentifier shall reflect a unique name for the system as it is used within the virtualization platform."}, {"key": "55", "title": "Virtual System Type", "label": "CIM_VSSD_VirtualSystemType", "description": "VirtualSystemType shall reflect a particular type of virtual system."}]}, {"key": "56", "title": "CPU Mode", "description": "This provides the preferred CPU Model to be used when booting up a guest VM.", "children": [{"key": "57", "title": "CPU Mode", "label": "hw:cpu_mode", "options": ["none", "host-model", "host-passthrough", "custom"], "description": "Type of CPU Mode."}]}, {"key": "58", "title": "CPU Pinning", "description": "This provides the preferred CPU pinning and CPU thread pinning policy to be used when pinning vCPU of the guest to pCPU of the host. See http://docs.openstack.org/admin-guide/compute-numa-cpu-pinning.html", "children": [{"key": "59", "title": "CPU Pinning policy", "label": "hw:cpu_policy", "options": ["shared", "dedicated"], "description": "Type of CPU pinning policy."}, {"key": "60", "title": "CPU Thread Pinning Policy.", "label": "hw:cpu_thread_policy", "options": ["isolate", "prefer", "require"], "description": "Type of CPU thread pinning policy."}]}, {"key": "61", "title": "Guest <PERSON> Backing", "description": "This provides the preferred backing option for guest RAM. Guest's memory can be backed by hugepages to limit TLB lookups. See also: https://wiki.openstack.org/wiki/VirtDriverGuestCPUMemoryPlacement", "children": [{"key": "62", "title": "Size of memory page", "label": "hw:mem_page_size", "description": "Page size to be used for Guest memory backing. Value can be specified as <number><unit> (i.e.: 2MB, 1GB) or 'any', 'small', 'large'. If this property is set in Image metadata then only 'any' and 'large' values are accepted in Flavor metadata by Nova API."}]}, {"key": "63", "title": "Compute Host Capabilities", "description": "Hardware capabilities provided by the compute host. This provides the ability to fine tune the hardware specification required when an instance is requested. The ComputeCapabilitiesFilter should be enabled in the Nova scheduler to use these properties. When enabled, this filter checks that the capabilities provided by the compute host satisfy any extra specifications requested. Only hosts that can provide the requested capabilities will be eligible for hosting the instance.", "children": [{"key": "64", "title": "Architecture", "label": "capabilities:cpu_info:arch", "options": ["x86", "x86_64", "i686", "ia64", "ARMv8-A", "ARMv7-A"], "description": "Specifies the CPU architecture. Use this property to specify the architecture supported by the hypervisor."}, {"key": "65", "title": "Features", "label": "capabilities:cpu_info:features", "multiple": true, "options": ["fpu", "vme", "de", "pse", "tsc", "msr", "pae", "mce", "cx8", "apic", "sep", "mtrr", "pge", "mca", "cmov", "pat", "pse36", "pn", "clflush", "dts", "acpi", "mmx", "fxsr", "sse", "sse2", "ss", "ht", "tm", "ia64", "pbe", "syscall", "mp", "nx", "mmxext", "fxsr_opt", "pdpe1gb", "rdtscp", "lm", "3dnowext", "3dnow", "arch_perfmon", "pebs", "bts", "rep_good", "nopl", "xtopology", "tsc_reliable", "nonstop_tsc", "extd_apicid", "amd_dcm", "aperfmperf", "eagerfpu", "nonstop_tsc_s3", "pni", "pclmulqdq", "dtes64", "monitor", "ds_cpl", "vmx", "smx", "est", "tm2", "ssse3", "cid", "fma", "cx16", "xtpr", "pdcm", "pcid", "dca", "sse4_1", "sse4_2", "x2apic", "movbe", "popcnt", "tsc_deadline_timer", "aes", "xsave", "avx", "f16c", "rdrand", "hypervisor", "rng", "rng_en", "ace", "ace_en", "ace2", "ace2_en", "phe", "phe_en", "pmm", "pmm_en", "lahf_lm", "cmp_legacy", "svm", "extapic", "cr8_legacy", "abm", "sse4a", "misalignsse", "3dnowprefetch", "osvw", "ibs", "xop", "skinit", "wdt", "lwp", "fma4", "tce", "nodeid_msr", "tbm", "topoext", "perfctr_core", "perfctr_nb", "bpext", "perfctr_l2", "mwaitx", "ida", "arat", "cpb", "epb", "pln", "pts", "dtherm", "hw_pstate", "proc_feedback", "hwp", "hwp_notify", "hwp_act_window", "hwp_epp", "hwp_pkg_req", "intel_pt", "tpr_shadow", "vnmi", "flexpriority", "ept", "vpid", "npt", "lbrv", "svm_lock", "nrip_save", "tsc_scale", "vmcb_clean", "<PERSON><PERSON><PERSON><PERSON>", "decodeassists", "pausefilter", "pfthreshold", "vmmcall", "fsgsbase", "tsc_adjust", "bmi1", "hle", "avx2", "smep", "bmi2", "erms", "invpcid", "rtm", "cqm", "mpx", "avx512f", "rdseed", "adx", "smap", "pcommit", "clflushopt", "clwb", "avx512pf", "avx512er", "avx512cd", "sha_ni", "xsaveopt", "xsavec", "xgetbv1", "xsaves", "cqm_llc", "cqm_occup_llc", "clzero"], "description": "Specifies CPU flags/features. Using this property you can specify the required set of instructions supported by a vm."}, {"key": "66", "title": "Model", "label": "capabilities:cpu_info:model", "options": ["<PERSON><PERSON><PERSON>", "Core2Duo", "Penryn", "Nehalem", "Westmere", "Sandy<PERSON><PERSON>", "IvyBridge", "<PERSON><PERSON>", "<PERSON><PERSON>", "Delhi", "Seoul", "Abu Dhabi", "Interlagos", "<PERSON><PERSON><PERSON>", "Valencia", "Zurich", "Budapest", "Barcelona", "Suzuka", "Shanghai", "Istanbul", "Lisbon", "Magny-Cours", "Cortex-A57", "Cortex-A53", "Cortex-A12", "Cortex-A17", "Cortex-A15", "Coretx-A7", "<PERSON>-<PERSON>"], "description": "Specifies the CPU model. Use this property to ensure that your vm runs on a specific cpu model."}, {"key": "67", "title": "cores", "label": "capabilities:cpu_info:topology:cores", "inputType": "number", "description": "Number of cores."}, {"key": "68", "title": "sockets", "label": "capabilities:cpu_info:topology:sockets", "inputType": "number", "description": "Number of sockets."}, {"key": "69", "title": "threads", "label": "capabilities:cpu_info:topology:threads", "inputType": "number", "description": "Number of threads."}, {"key": "70", "title": "<PERSON><PERSON><PERSON>", "label": "capabilities:cpu_info:vendor", "options": ["Intel", "AMD"], "description": "Specifies the CPU manufacturer."}]}, {"key": "71", "title": "libvirt Driver Options", "description": "The libvirt compute driver options.  \n\nThese are properties that affect the libvirt compute driver and may be specified on flavors and images.  For a list of all hypervisors, see here: https://wiki.openstack.org/wiki/HypervisorSupportMatrix.", "children": [{"key": "72", "title": "Boot Menu", "label": "hw:boot_menu", "options": ["true", "false"], "description": "If true, enables the BIOS bootmenu. In cases where both the image metadata and Extra Spec are set, the Extra Spec setting is used. This allows for flexibility in setting/overriding the default behavior as needed."}, {"key": "73", "title": "Hardware Memory Encryption", "label": "hw:mem_encryption", "options": ["true", "false"], "description": "Enables encryption of guest memory at the hardware level, if there are compute hosts available which support this. See https://docs.openstack.org/nova/latest/admin/configuration/hypervisor-kvm.html#amd-sev-secure-encrypted-virtualization for details."}, {"key": "74", "title": "Serial Port Count", "label": "hw:serial_port_count", "inputType": "number", "description": "Specifies the count of serial ports that should be provided. If hw:serial_port_count is not set in the flavor's extra_specs, then any count is permitted. If hw:serial_port_count is set, then this provides the default serial port count. It is permitted to override the default serial port count, but only with a lower value."}]}, {"key": "75", "title": "<PERSON><PERSON><PERSON>", "description": "Compute drivers may enable quotas on CPUs available to a VM, disk tuning, bandwidth I/O, and instance VIF traffic control.  See: http://docs.openstack.org/admin-guide/compute-flavors.html", "children": [{"key": "76", "title": "CPU Limits", "description": "You can configure the CPU limits with control parameters.", "children": [{"key": "77", "title": "Quota: CPU Shares", "label": "quota:cpu_shares", "description": "Specifies the proportional weighted share for the domain. If this element is omitted, the service defaults to the OS provided defaults. There is no unit for the value; it is a relative measure based on the setting of other VMs. For example, a VM configured with value 2048 gets twice as much CPU time as a VM configured with value 1024."}, {"key": "78", "title": "Quota: CPU Period", "label": "quota:cpu_period", "inputType": "number", "description": "Specifies the enforcement interval (unit: microseconds) for QEMU and LXC hypervisors. Within a period, each VCPU of the domain is not allowed to consume more than the quota worth of runtime. The value should be in range [1000, 1000000]. A period with value 0 means no value.", "min": 1000, "max": 1000000}, {"key": "79", "title": "Quota: CPU Quota", "label": "quota:cpu_quota", "inputType": "number", "description": "Specifies the maximum allowed bandwidth (unit: microseconds). A domain with a negative-value quota indicates that the domain has infinite bandwidth, which means that it is not bandwidth controlled. The value should be in range [1000, 18446744073709551] or less than 0. A quota with value 0 means no value. You can use this feature to ensure that all vCPUs run at the same speed."}]}, {"key": "80", "title": "Disk QoS", "description": "Using disk I/O quotas, you can set maximum disk write to 10 MB per second for a VM user.", "children": [{"key": "81", "title": "Quota: Disk read bytes / sec", "label": "quota:disk_read_bytes_sec", "inputType": "number", "description": "Sets disk I/O quota for disk read bytes / sec."}, {"key": "82", "title": "Quota: Disk read IOPS / sec", "label": "quota:disk_read_iops_sec", "inputType": "number", "description": "Sets disk I/O quota for disk read IOPS / sec."}, {"key": "83", "title": "Quota: Disk Write Bytes / sec", "label": "quota:disk_write_bytes_sec", "inputType": "number", "description": "Sets disk I/O quota for disk write bytes / sec."}, {"key": "84", "title": "Quota: Disk Write IOPS / sec", "label": "quota:disk_write_iops_sec", "inputType": "number", "description": "Sets disk I/O quota for disk write IOPS / sec."}, {"key": "85", "title": "Quota: Disk Total Bytes / sec", "label": "quota:disk_total_bytes_sec", "inputType": "number", "description": "Sets disk I/O quota for total disk bytes / sec."}, {"key": "86", "title": "Quota: Disk Total IOPS / sec", "label": "quota:disk_total_iops_sec", "inputType": "number", "description": "Sets disk I/O quota for disk total IOPS / sec."}]}, {"key": "87", "title": "Virtual Interface QoS", "description": "Bandwidth QoS tuning for instance virtual interfaces (VIFs) may be specified with these properties. Incoming and outgoing traffic can be shaped independently. If not specified, no quality of service (QoS) is applied on that traffic direction. So, if you want to shape only the network's incoming traffic, use inbound only (and vice versa). The OpenStack Networking service abstracts the physical implementation of the network, allowing plugins to configure and manage physical resources. Virtual Interfaces (VIF) in the logical model are analogous to physical network interface cards (NICs). VIFs are typically owned a managed by an external service; for instance when OpenStack Networking is used for building OpenStack networks, VIFs would be created, owned, and managed in Nova. VIFs are connected to OpenStack Networking networks via ports. A port is analogous to a port on a network switch, and it has an administrative state. When a VIF is attached to a port the OpenStack Networking API creates an attachment object, which specifies the fact that a VIF with a given identifier is plugged into the port.", "children": [{"key": "88", "title": "Quota: VIF Inbound Average", "label": "quota:vif_inbound_average", "inputType": "number", "description": "Network Virtual Interface (VIF) inbound average in kilobytes per second. Specifies average bit rate on the interface being shaped."}, {"key": "89", "title": "Quota: VIF Inbound <PERSON><PERSON><PERSON>", "label": "quota:vif_inbound_burst", "inputType": "number", "description": "Network Virtual Interface (VIF) inbound burst in total kilobytes. Specifies the amount of bytes that can be burst at peak speed."}, {"key": "90", "title": "Quota: VIF Inbound Peak", "label": "quota:vif_inbound_peak", "inputType": "number", "description": "Network Virtual Interface (VIF) inbound peak in kilobytes per second. Specifies maximum rate at which an interface can receive data."}, {"key": "91", "title": "Quota: VIF Outbound Average", "label": "quota:vif_outbound_average", "inputType": "number", "description": "Network Virtual Interface (VIF) outbound average in kilobytes per second. Specifies average bit rate on the interface being shaped."}, {"key": "92", "title": "Quota: VIF Outbound <PERSON><PERSON><PERSON>", "label": "quota:vif_outbound_burst", "inputType": "number", "description": "Network Virtual Interface (VIF) outbound burst in total kilobytes. Specifies the amount of bytes that can be burst at peak speed."}, {"key": "93", "title": "Quota: VIF Outbound Peak", "label": "quota:vif_outbound_peak", "inputType": "number", "description": "Network Virtual Interface (VIF) outbound peak in kilobytes per second. Specifies maximum rate at which an interface can send data."}]}]}, {"key": "94", "title": "Random Number Generator", "description": "If a random-number generator device has been added to the instance through its image properties, the device can be enabled and configured.", "children": [{"key": "95", "title": "Random Number Generator Allowed", "label": "hw_rng:allowed", "checkbox": true, "description": ""}, {"key": "96", "title": "Random number generator limits.", "label": "hw_rng:rate_bytes", "inputType": "number", "description": "Allowed amount of bytes that the guest can read from the host's entropy per period."}, {"key": "97", "title": "Random number generator read period.", "label": "hw_rng:rate_period", "inputType": "number", "description": "Duration of the read period in milliseconds."}]}, {"key": "98", "title": "TPM Options", "description": "Configuration options for TPM", "children": [{"key": "99", "title": "TPM model", "label": "hw:tpm_model", "options": ["TIS", "CRB"], "description": "TPM model to use. Option CRB is only valid for TPM version 2.0. Defaults to TIS."}]}, {"key": "100", "title": "Virtual CPU Topology", "description": "This provides the preferred socket/core/thread counts for the virtual CPU instance exposed to guests. This enables the ability to avoid hitting limitations on vCPU topologies that OS vendors place on their products. See also: https://opendev.org/openstack/nova-specs/src/branch/master/specs/juno/implemented/virt-driver-vcpu-topology.rst", "children": [{"key": "101", "title": "vCPU Cores", "label": "hw:cpu_cores", "inputType": "number", "description": "Preferred number of cores to expose to the guest."}, {"key": "102", "title": "Max vCPU Cores", "label": "hw:cpu_max_cores", "inputType": "number", "description": "Maximum number of cores to expose to the guest."}, {"key": "103", "title": "Max vCPU Sockets", "label": "hw:cpu_max_sockets", "inputType": "number", "description": "Maximum number of sockets to expose to the guest."}, {"key": "104", "title": "Max vCPU Threads", "label": "hw:cpu_max_threads", "inputType": "number", "description": "Maximum number of threads to expose to the guest."}, {"key": "105", "title": "vCPU Sockets", "label": "hw:cpu_sockets", "inputType": "number", "description": "Preferred number of sockets to expose to the guest."}, {"key": "106", "title": " vCPU Threads", "label": "hw:cpu_threads", "inputType": "number", "description": "Preferred number of threads to expose to the guest."}]}, {"key": "107", "title": "VMware Driver Options for Flavors", "description": "VMware Driver Options for Flavors may be used to customize and manage Nova Flavors. These are properties specific to VMWare compute drivers and will only have an effect if the VMWare compute driver is enabled in Nova. See: http://docs.openstack.org/admin-guide/compute-flavors.html", "children": [{"key": "108", "title": "VMware Hardware Version", "label": "vmware:hw_version", "options": ["vmx-13", "vmx-11", "vmx-10", "vmx-09", "vmx-08", "vmx-07", "vmx-04", "vmx-03"], "description": "Specifies the hardware version VMware uses to create images. If the hardware version needs to be compatible with a cluster version, for backward compatibility or other circumstances, the vmware:hw_version key specifies a virtual machine hardware version. In the event that a cluster has mixed host version types, the key will enable the vCenter to place the cluster on the correct host."}, {"key": "109", "title": "VMware Storage Policy", "label": "vmware:storage_policy", "description": "Specifies the storage policy to be applied for newly created instance. If not provided, the default storage policy specified in config file will be used. If Storage Policy Based Management (SPBM) is not enabled in config file, this value won't be used."}]}, {"key": "110", "title": "VMware Quota for Flavors", "description": "The VMware compute driver allows various compute quotas to be specified on flavors. When specified, the VMWare driver will ensure that the quota is enforced. These are properties specific to VMWare compute drivers and will only have an effect if the VMWare compute driver is enabled in Nova. For a list of hypervisors, see: https://wiki.openstack.org/wiki/HypervisorSupportMatrix. For flavor customization, see: http://docs.openstack.org/admin-guide/compute-flavors.html", "children": [{"key": "111", "title": "Quota: CPU Limit", "label": "quota:cpu_limit", "inputType": "number", "description": "Specifies the upper limit for CPU allocation in MHz. This parameter ensures that a machine never uses more than the defined amount of CPU time. It can be used to enforce a limit on the machine's CPU performance. The value should be a numerical value in MHz. If zero is supplied then the cpu_limit is unlimited.", "min": 0}, {"key": "112", "title": "Quota: CPU Reservation Limit", "label": "quota:cpu_reservation", "inputType": "number", "description": "Specifies the guaranteed minimum CPU reservation in MHz. This means that if needed, the machine will definitely get allocated the reserved amount of CPU cycles. The value should be a numerical value in MHz.", "min": 0}]}, {"key": "113", "title": "Emulated Virtual TPM", "description": "Configuration options for Emulated Virtual TPM", "children": [{"key": "114", "title": "Virtual TPM Version 1.2 Support", "label": "traits:COMPUTE_SECURITY_TPM_1_2", "options": ["required"], "description": "Enables virtual TPM version 1.2."}, {"key": "115", "title": "Virtual TPM Version 2.0 Support", "label": "traits:COMPUTE_SECURITY_TPM_2_0", "options": ["required"], "description": "Enables virtual TPM version 2.0."}]}, {"key": "116", "title": "Watchdog Behavior", "description": "Compute drivers may enable watchdog behavior over instances.  See: http://docs.openstack.org/admin-guide/compute-flavors.html", "children": [{"key": "117", "title": "Watchdog Action", "label": "hw:watchdog_action", "options": ["disabled", "reset", "poweroff", "pause", "none"], "description": "For the libvirt driver, you can enable and set the behavior of a virtual hardware watchdog device for each flavor. Watchdog devices keep an eye on the guest server, and carry out the configured action, if the server hangs. The watchdog uses the i6300esb device (emulating a PCI Intel 6300ESB). If hw_watchdog_action is not specified, the watchdog is disabled. Watchdog behavior set using a specific image's properties will override behavior set using flavors."}]}]