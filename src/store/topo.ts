import { defineStore } from 'pinia'
export const topoStore = defineStore('topo', {
    persist:true,
    state: () => {
        return {
            isLeaf:0,
            key:0,
            openKeys:[]
        }
    },
    getters: {  },
    actions: { 
        setIsLeaf(data: any){
            this.isLeaf = data;
        },
        setKey(data: any){
            this.key = data;
        },
        setopenKeys(data: any){
            this.openKeys = data;
        }
     }
})