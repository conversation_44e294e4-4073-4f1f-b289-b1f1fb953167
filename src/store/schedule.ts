import { defineStore } from 'pinia'
import { querySchedulePage, queryScheduleLog } from "@/api/backend/systems/schedule";
export const scheduleStore = defineStore('schedule', {
    state: () => {
        return {
            page: 1,
            limit: 10,
            beanName: "",
            schedulelist: [],
            total: 0,
            loglist: [],
            logpage: 1,
            loglimit: 10,
            jobId: "",
            logtotal: 0,
            loading: false
        }
    },
    actions: { 
        async QuerySchedulePage(data:any = {}) {
            return await querySchedulePage(data);
            // if (res) {
            //   if (res.data) {
            //     if (res.data.currPage > res.data.totalPage && res.data.totalPage>0) {
            //       this.set_page(res.data.totalPage);
            //     }
            //     this.schedulelist = res.data.list;
            //     this.total = res.data.totalCount;
            //   }
            // }
          },
          // async SearchSchedule(data: any = {}) {
          //   const res:any = await querySchedulePage(data);
          //   if (res) {
          //     if (res.data) {
          //       if (res.data.currPage > res.data.totalPage && res.data.totalPage>0) {
          //         this.set_page(res.data.totalPage);
          //       }
          //       this.schedulelist = res.data.list;
          //       this.total = res.data.totalCount;
          //     }
          //   }
          // },
          async QueryScheduleLog(data: any = {}) {
            this.loading = true;
            const res:any = await queryScheduleLog({ page: this.logpage, limit: this.loglimit, jobId: this.jobId });
            this.loading = false;
            if (res) {
              if (res.data.currPage > res.data.totalPage && res.data.currPage != 1) {
                this.set_logpage(res.data.totalPage);
              }
              this.loglist = res.data.list;
              this.logtotal = res.data.totalCount;
            }
          },
          set_page(data: any = {}) {
            this.page = data;
            this.QuerySchedulePage();
          },
          set_limit(data: any = {}) {
            this.limit = data.data2;
            this.QuerySchedulePage();
          },
          set_logpage(data: any = {}) {
            this.logpage = data;
            this.QueryScheduleLog();
          },
          set_loglimit(data: any = {}) {
            this.loglimit = data.data2;
            this.QueryScheduleLog();
          }
     }
})