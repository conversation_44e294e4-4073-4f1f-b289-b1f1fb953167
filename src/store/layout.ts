import { defineStore } from 'pinia'
export const layoutStore = defineStore('layout', {
    state: () => {
        return {
            paneLWidth:'256px',
            paneRWidth:'calc(100vw - 263px)'
        }
    },
    getters: { 
        get_paneLWidth:(state) => state.paneLWidth,
        get_paneRWidth:(state) => state.paneRWidth
     },
    actions: { 
        setPaneLWidth(data: any){
            this.paneLWidth = data;
        },
        setPaneRWidth(data: any){
            this.paneRWidth = data;
        }
     }
})