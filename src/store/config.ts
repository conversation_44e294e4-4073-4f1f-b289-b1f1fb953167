import { defineStore } from 'pinia'
import {
    deleteBatchConfig,
    queryPageConfig,
    saveConfig,
    selectInfoConfig,
    updateConfig
  } from "@/api/backend/systems/config";
export const configStore = defineStore('config', {
    state: () => {
        return {
        }
    },
    getters: {  },
    actions: { 
        async DeleteBatchConfig(data: any = {}) {
            return await deleteBatchConfig(data);
          },
          async QueryPageConfig(data: any = {}) {
            return await queryPageConfig(data);
          },
          async SaveConfig(data: any = {}) {
            return await saveConfig(data);
          },
          async SelectInfoConfig(data: any = {}) {
            return await selectInfoConfig(data);
          },
          async UpdateConfig(data: any = {}) {
            return await updateConfig(data);
          }
     }
})