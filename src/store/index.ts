import { defineStore } from 'pinia'
import router from "@/router"
export const indexStore = defineStore('index', {
    persist:true,
    state: () => {
        return {
            collapsed: false,
            routers: [],
            taglist:[{menuId: '首页', name: '首页', url: 'index'}],
            activeIndex:0,
            configs:{}
        }
    },
    getters: { 
        get_collapesd:(state) => state.collapsed,
        get_activeIndex:(state) => state.activeIndex,
        get_taglist:(state) => state.taglist,

     },
    actions: { 
        setRouter(data) {
            this.routers = data;
          },
          set_collaped(data: any){
            this.collapsed = data;
          },
          setIndex(data:any){
            this.activeIndex = data
          },
          setTaglist(data:any=[{menuId: '首页', name: '首页', url: 'index'}]){
            this.taglist=data
          },
          addTag(data: any) {
            let res = this.taglist.some((item,index)=>{
                if(item.menuId == data.menuId){
                  this.taglist.splice(index,1,data)
                    this.activeIndex = index;
                    return true;
                }
                return false;
            })
            if(!res){
                this.taglist.push(data)
                this.activeIndex = this.taglist.length-1
            }
          },
          closeTag(data:any){
            this.taglist.splice(data,1)
            if(this.activeIndex > data){
                this.activeIndex--;
            }else if(this.activeIndex == data){
                if(data == this.taglist.length){
                    this.activeIndex--;
                    router.push('/admin/'+this.taglist[data-1].url)
                }else{
                    router.push('/admin/'+this.taglist[data].url)
                }
            }
          },
          clearTags() {
            this.taglist = [{menuId: '首页', name: '首页', url: 'index'}]
          },
          delTagsItem(index) {
            this.taglist.splice(index, 1);
          },
          closeTagsOther(data) {
            this.taglist = data;
          },
          setConfigs(data){
            this.configs = data;
          }
     }
})