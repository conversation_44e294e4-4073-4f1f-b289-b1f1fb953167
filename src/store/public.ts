import { defineStore } from 'pinia'
import { getUUID, captcha } from "@/api/backend/public";
export const publicStore = defineStore('public', {
    state: () => {
        return {
          loading:false
        }
    },
    getters: { 
      get_loading:(state) => state.loading
     },
    actions: { 
        async GetUUID(data: any = {}) {
            return await getUUID(data);
          },
          async Captcha(data: any = {}) {
            return await captcha(data);
          },
          setLoading(data:any){
            this.loading = data;
          }
     }
})