import { defineStore } from 'pinia'
import { selectNav,selectAllMenuList,selectMenuById,deleteMenuById,saveMenu,updateMenu } from "@/api/backend/systems/menu";
export const menuStore = defineStore('menu', {
  persist:true,
  state: () => {
      return {
        submenuId:0,
        showLeftMenu:false,
        SelectedKeys:[],
        Openkeys:[],
        leftMenuList:[],
        cloudIndex:0,
        domainIndex:0,
        projectIndex:0,
        SubmenuFirst:[],
        CloudMenus:[],
        cloudmenu:[],
        cloudInfo:{},
        projectInfo:{},
        SystemMenus:[]
      }
  },
  getters: { 
    get_submenuId:(state) => state.submenuId,
    get_ShowLeftMenu:(state) => state.showLeftMenu,
   },
  actions: { 
      async SelectNav(data: any={}) {
          return await selectNav(data);
        },
        async SelectAllMenuList(data: any={}) {
          return await selectAllMenuList(data);
        },
        async SelectMenuById(data: any = {}) {
          return await selectMenuById(data);
        },
        async DeleteMenuById(data: any = {}) {
          return await deleteMenuById(data);
        },
        async SaveMenu(data: any = {}) {
          return await saveMenu(data);
        },
        async UpdateMenu(data: any = {}) {
          return await updateMenu(data);
        },
        setMenuId(data:any){
          this.submenuId = data;
        },
        setShowLeftMenu(data:any){
          this.showLeftMenu = data;
        },
        setSelectedKeys(data:any){
          this.SelectedKeys = data;
        },
        setOpenKeys(data:any){
          this.Openkeys = data;
        },
        setLeftMenu(data:any){
          this.leftMenuList = data;
        },
        setCloudIndex(data:any){
          this.cloudIndex = data;
        },
        setDomainIndex(data:any){
          this.domainIndex = data;
        },
        setProjectIndex(data:any){
          this.projectIndex = data;
        },
        setSubmenuFirst(data:any){
          this.SubmenuFirst = data;
        },
        setCloudMenus(data:any){
          this.CloudMenus = data;
        },
        set_cloudMenu(data:any){
          this.cloudmenu = data;
        },
        setCloudInfo(data:any){
          this.cloudInfo = {...data};
        },
        setProjectInfo(data:any){
          this.projectInfo = {...data};
        },
        setSystemMenus(data:any){
          this.SystemMenus = [...data];
        }
    }
})