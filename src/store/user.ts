import { defineStore } from 'pinia'
import {
    login,
    logout,
    info,
    password,
    saveUser,
    selectById,
    selectList,
    updateUser,
    deleteUserById
  } from "@/api/backend/systems/user";
import { selectNav } from "@/api/backend/systems/menu";
import router from "@/router";
import { getToken, setToken, getUsername, setUsername } from "@/utils/auth";
import { message } from "ant-design-vue";
export const userStore = defineStore('user', {
  // 数据持久化，会提交在localStorage，命名为user
  persist:true,
  state: () => {
      return {
          userId:1,
          token: "",
          menulist: [],
          cloudmenu:[],
          permissions: [],
          img:[],
          loginName: "",
          loginErr: false,
          userInfo: {}
      }
  },
  actions: { 
      async Login(data: any = {}) {
          const res:any = await login(data);
          const resObj = JSON.parse(JSON.stringify(res));
          if (resObj.code == 0) {
            this.token = resObj.token;
            setToken(resObj.token);
            setUsername(resObj.data.userName);
            this.loginName = resObj.data.userName;
            this.userId = resObj.data.userId
            this.loginErr = true;
            this.userInfo = resObj.data;
            localStorage.setItem('user',JSON.stringify({
              userId:resObj.data.userId,
              token: resObj.token,
              loginName: resObj.data.userName,
              userInfo: {...resObj.data}
            }))
            // const token = getToken();
            const res1 = await selectNav({});
            const res1Obj = JSON.parse(JSON.stringify(res1));
            
            if (res1Obj.code == 0) {
              this.menulist = res1Obj.data;
              this.permissions = res1Obj.permissions;
              router.push({path:"/admin"}); // 跳转到后台首页
            } else {
              message.error(JSON.parse(JSON.stringify(res1)).msg);
            }
          } else {
            this.loginErr = false;
          }
          return res;
        },
        async Logout(data: any = {}) {
          return await logout(data);
        },
        set_menulist(data:any){
          this.menulist = data;
        },
        set_perm(data:any){
          this.permissions = data;
        },
        set_cloudmenu(data:any){
          this.cloudmenu = data;
        },
        set_userinfo(data:any){
          this.userInfo = data;
        },
        set_img(data:any){
          this.img = data;
        },
        async UserInfo(data: any = {}) {
          return await info(data);
        },
        async SaveUser(data: any = {}) {
          return await saveUser(data);
        },
        async UserPassword(data: any = {}) {
          return await password(data);
        },
        async SelectUserById(data: any = {}) {
          return await selectById(data);
        },
        async SelectUserList(data: any = {}) {
          return await selectList(data);
        },
        async UpdateUser(data: any = {}) {
          return await updateUser(data);
        },
        async DeleteUserById(data: any = {}) {
          return await deleteUserById(data);
        }
    }
})