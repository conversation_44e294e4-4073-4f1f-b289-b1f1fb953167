import { defineStore } from 'pinia'
import { deleteRole, selectRolePage, saveRole, selectById, selectList, updateRole, selectMenuInfo, selectInfo } from "@/api/backend/systems/role";
export const roleStore = defineStore('role', {
    state: () => {
        return {}
    },
    getters: {  },
    actions: { 
        async DeleteRole(data: any = {}) {
            return await deleteRole(data);
          },
          async SelectRolePage(data: any = {}) {
            return await selectRolePage(data);
          },
          async SaveRole(data: any = {}) {
            return await saveRole(data);
          },
          async SelectRoleById(data: any = {}) {
            return await selectById(data);
          },
      
          async SelectMenuInfo(data: any = {}) {
            return await selectMenuInfo(data);
          },
          async SelecRoletList(data: any={}) {
            return await selectList(data);
          },
          async UpdateRole(data: any = {}) {
            return await updateRole(data);
          },
      
          async SelectInfo(data: any = {}) {
            return await selectInfo(data);
          }
     }
})