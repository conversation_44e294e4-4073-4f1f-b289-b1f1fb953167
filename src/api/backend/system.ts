import { getAction, postAction } from "./manage";
//系统管理_部分接口


export const getLdapData= (data:any) => {
    return getAction("/sys/sysldap/queryPage",data);
}

export const saveLdap= (data: any) => {
    return postAction("/sys/sysldap/save", data);
};

export const updateLdap = (data: any) => {
    return postAction("/sys/sysldap/update", data);
};

export const deleteLdap= (data: any) => {
    return postAction("/sys/sysldap/delete",data);
};  

export const getLdapList = (data: any) => {
    return getAction("/sys/sysldap/selectList", data);
  };
