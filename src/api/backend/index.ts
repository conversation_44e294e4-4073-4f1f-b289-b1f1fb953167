import { getAction, postAction,downloadAction } from "@/api/backend/manage";
// 管理员、普通用户的首页、消息中心接口
export const selectIndexCloudList = (data: any) => {
  return getAction("/sys/index/selectCloudList", data);
};

export const selectIndexServerList = (data: any) => {
  return getAction("/sys/index/selectMyServerList", data);
};

// 活跃虚机
export const getServerCount = (data: any) => {
  return getAction("/sys/index/statisticsMyServer", data);
};
// 虚机分布
export const getHostPie = (data: any) => {
  return getAction("/sys/index/selectPieByCloud", data);
};

// 虚机操作系统分布
export const getOsPie = (data: any) => {
  return getAction("/sys/index/selectPieByOS", data);
};

// 云平台资源使用情况
export const SelectResource = (data: any) => {
  return getAction("/sys/index/selectBarbyResource", data);
};
// 云平台预警
export const SelectWarning = (data: any) => {
  return getAction("/sys/index/selectPieByWarning", data);
};
// 工单处理
export const SelectTicket = (data: any) => {
  return getAction("/sys/index/selectLineByTicket", data);
};
// 非图表工单
export const getTicketCount = (data: any) => {
  return getAction("/sys/index/statisticsTicket", data);
};
// 非图表预警
export const getWarningCount = (data: any) => {
  return getAction("/sys/index/statisticsWarning", data);
};

// 消息中心
// GET /sys/sysnoticemessage/queryPage
export const getMessageList = (data: any) => {
  return getAction("/sys/sysnoticemessage/queryPage", data);
};

export const getSysnoticemessagelsit = (data: any) => {
  return getAction("/sys/sysnoticemessage/selectList", data);
};

// GET /sys/sysnoticemessage/selectNumber
export const getRanklist = (data: any) => {
  return getAction("/sys/index/rankingByServer", data);
};
export const selectNumber = (data: any) => {
  return getAction("/sys/sysnoticemessage/selectNumber", data);
};

// POST /sys/sysnoticemessage/transact
export const transactlist = (data: any) => {
  return postAction("/sys/sysnoticemessage/transact", data);
};

export const transactAll = (data: any) => {
  return postAction("/sys/sysnoticemessage/transactAll", data);
};

// GET /sys/sysnoticemessage/info
export const getMessageInfo = (data: any) => {
  return getAction("/sys/sysnoticemessage/info", data);
};

// 下载
// GET /sys/index/cloudOs
export const osLoad = (data: any) => {
  return downloadAction("/sys/index/cloudOs", data);
};

export const resourceLoad = (data: any) => {
  return downloadAction("/sys/index/cloudResource", data);
};


export const serverLoad = (data: any) => {
  return downloadAction("/sys/index/cloudServer", data);
};