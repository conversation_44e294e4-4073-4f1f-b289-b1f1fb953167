import { getAction, postAction } from "@/api/backend/manage";
// 公共接口 无需验证
export const getUUID = (data: any) => {
  return getAction("/sys/public/getUUID", data);
};
export const captcha = (data: any) => {
  return getAction("/sys/public/getCaptcha", data);
};

export const selectSysconfigImg = (data: any) => {
  return getAction("/sys/public/selectSystemConfigAttachmentById", data);
};

export const selectSysconfig = () => {
  return getAction("/sys/public/selectSystemConfig",null);
};

export const checkLicence = (data) => {
  return getAction("/sys/public/checkLicence",data);
};