import { getAction, postAction } from "@/api/backend/manage";

export const statisticsComputeByProject = (data: any) => {
    return getAction("/sys/statistics/statisticsComputeByProject", data);
};
export const statisticsProjectByCloudId = (data: any) => {
    return getAction("/sys/statistics/statisticsProjectByCloudId", data);
};
export const statisticsHost = (data: any) => {
    return getAction("/sys/statistics/statisticsHost", data);
};
export const statisticsProject = (data: any) => {
    return getAction("/sys/statistics/statisticsProject", data);
};

export const emailconfigSave = (data: any) => {
    return postAction("/sys/sysemailconfig/save", data)
}

export const emailconfigSelectList = (data: any) => {
    return getAction("/sys/sysemailconfig/selectList", data);
};