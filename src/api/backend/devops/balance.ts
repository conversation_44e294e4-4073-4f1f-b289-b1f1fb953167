import { getAction, postAction } from "@/api/backend/manage";

export const getBalanceListAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancer/queryPage",params);
};

export const saveBalanceAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancer/save",data);
};

export const updateBalanceAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancer/update",data);
};

export const getBalanceInfoAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancer/info",params);
};

export const deleteBalanceAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancer/delete",data);
};

export const selectMonitorListAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancerlistener/selectList",params);
};

export const getMonitorInfoAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancerlistener/info",params);
};

export const saveMonitorAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerlistener/save",data);
};

export const updateMonitorAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerlistener/update",data);
};

export const deleteMonitorAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerlistener/delete",data);
};

export const selectPoolListAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancerpool/selectList",params);
};

export const getPoolInfoAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancerpool/info",params);
};

export const savePoolAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerpool/save",data);
};

export const updatePoolAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerpool/update",data);
};

export const deletePoolAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerpool/delete",data);
};

export const selectHealthListAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancerhealthmonitor/selectList",params);
};

export const getHealthInfoAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancerhealthmonitor/info",params);
};

export const saveHealthAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerhealthmonitor/save",data);
};

export const updateHealthAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerhealthmonitor/update",data);
};

export const deleteHealthAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerhealthmonitor/delete",data);
};

export const selectMemberListAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancerpoolmember/selectList",params);
};

export const saveMemberAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerpoolmember/save",data);
};

export const updateMemberAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerpoolmember/update",data);
};

export const deleteMemberAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerpoolmember/delete",data);
};

export const selectSevenListAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancerlistenerl7policie/selectList",params);
};

export const saveSevenAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerlistenerl7policie/save",data);
};
export const updateSevenAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerlistenerl7policie/update",data);
};

export const deleteSevenAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerlistenerl7policie/delete",data);
};

export const selectRulesListAPI = (params: any) => {
    return getAction("/sys/sysopenstackloadbalancerlistenerl7policierule/selectList",params);
};

export const saveRulesAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerlistenerl7policierule/save",data);
};
export const updateRulesAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerlistenerl7policierule/update",data);
};

export const deleteRulesAPI = (data: any) => {
    return postAction("/sys/sysopenstackloadbalancerlistenerl7policierule/delete",data);
};