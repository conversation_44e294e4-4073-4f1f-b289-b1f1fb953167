import { getAction, postAction } from "@/api/backend/manage";

export const getNetworkList = (data: any) => {
    return getAction("/sys/sysnetwork/queryPage", data);
  };
  
  export const getNetworkInfo = (data: any) => {
    return getAction("/sys/sysnetwork/info", data);
  }; 

  export const selectNetworkList = (data: any) => {
    return getAction("/sys/sysnetwork/selectList", data);
  };
  
  export const saveNetwork = (data: any) => {
    return postAction("/sys/sysnetwork/save", data);
  };
  
  export const updateNetwork = (data: any) => {
    return postAction("/sys/sysnetwork/update", data);
  };
  
  export const deleteNetwork = (data: any) => {
    return postAction("/sys/sysnetwork/delete",data);
  };

  export const getSubnetList = (data: any) => {
    return getAction("/sys/syssubnet/queryPage", data);
  };

  export const selectSubnetList = (data: any) => {
    return getAction("/sys/syssubnet/selectList", data);
  };
  
  export const saveSubnet = (data: any) => {
    return postAction("/sys/syssubnet/save", data);
  };
  
  export const updateSubnet = (data: any) => {
    return postAction("/sys/syssubnet/update", data);
  };
  
  export const deleteSubnet = (data: any) => {
    return postAction("/sys/syssubnet/delete",data);
  };
  
  export const getPortlist = (data: any) => {
    return getAction("/sys/sysport/queryPage", data);
  };
  export const selectPortList = (data: any) => {
    return getAction("/sys/sysport/selectList", data);
  };
  
  export const selectMapPortList = (data: any) => {
    return getAction("/sys/sysport/selectPortIpList", data);
  };

  export const savePort = (data: any) => {
    return postAction("/sys/sysport/save", data);
  };
  
  export const updatePort = (data: any) => {
    return postAction("/sys/sysport/update", data);
  };
  
  export const deletePort = (data: any) => {
    return postAction("/sys/sysport/delete",data);
  };
  export const selectNetworkTopology = (data: any) => {
    return getAction("/sys/sysnetwork/selectNetworkTopology",data);
  };