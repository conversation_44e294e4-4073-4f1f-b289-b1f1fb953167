import { getAction, postAction } from "@/api/backend/manage";

export const getKeypairList= (data: any) => {
  return getAction("/sys/syskeypair/queryPage", data);
};

export const deleteKeypair = (data: any) => {
  return postAction("/sys/syskeypair/delete",data);
};

export const getKeypairInfo = (data: any) => {
  return getAction("/sys/syskeypair/info", data);
};

export const selectKeypairList= (data: any) => {
  return getAction("/sys/syskeypair/selectList", data);
};

export const saveKeypair = (data: any) => {
  return postAction("/sys/syskeypair/save", data);
};

export const updateKeypair = (data: any) => {
  return postAction("/sys/syskeypair/update", data);
};