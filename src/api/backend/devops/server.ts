import { getAction, postAction,downloadAction } from "@/api/backend/manage";

export const getServerList = (data: any) => {
  return getAction("/sys/sysserver/queryPage", data);
};

export const selectServerList = (data: any) => {
  return getAction("/sys/sysserver/selectList", data);
};

export const selectServerInfo = (data: any) => {
  return getAction("/sys/sysserver/info", data);
};

export const saveServer = (data: any) => {
  return postAction("/sys/sysserver/save", data);
};

export const updateServer = (data: any) => {
  return postAction("/sys/sysserver/update", data);
};

export const rebootServer = (data: any) => {
  return postAction("/sys/sysserver/reboot?serverID="+data.serverID);
};

export const deleteServer = (data: any) => {
  return postAction("/sys/sysserver/delete",data);
};

export const actionServer = (data:any) => {
  return postAction("/sys/sysserver/action?serverID="+data.serverID+"&action="+data.action)
}

export const resizeServer = (data: any) => {
  return postAction("/sys/sysserver/resize?serverID="+data.serverID+'&flavorId='+data.flavorId);
};
export const migrateServer = (data: any) => {
  return postAction("/sys/sysserver/migrate?serverId="+data.serverID);
};
export const liveMigrateServer = (data: any) => {
  return postAction("/sys/sysserver/liveMigrate?serverId="+data.serverID+'&destHost='+data.destHost+'&diskOverCommit='+data.diskOverCommit+'&blockMigration='+data.blockMigration);
};

export const createSnapshot = (data:any) => {
  return postAction("/sys/sysserver/createSnapshot?serverId="+data.serverId+"&snapshotName="+data.snapshotName)
}

export const getConsoleUrl = (data: any) => {
  return postAction("/sys/sysserver/getVNCConsoleURL?id="+data);
};

export const getConsoleoutput = (data: any) => {
  return postAction("/sys/sysserver/getConsoleOutput?serverId="+data.serverId+'&lines='+data.lines);
};


export const changeOwner = (data: any) => {
  return postAction("/sys/sysserver/changeOwner?nextOwnerId="+data.nextOwnerId, data.ids);
};

  export const postaddip= (data: any) => {
    return postAction("/sys/sysserver/addfloatip?serverId="+data.serverId+'&ipId='+data.ipId+'&floatIpId='+data.floatIpId);
  };

  export const postremoveip= (data: any) => {
    return postAction("/sys/sysserver/removefloatip?serverID="+data.serverID+'&remove='+data.remove);
  };

  export const saveVolvneIp= (data: any) => {
    return postAction("/sys/sysserver/bindingVolume?serverId="+data.serverId+'&volumeId='+data.volumeId);
   
  };
 export const saveDevolvneIp= (data: any) => {
    return postAction("/sys/sysserver/detachVolume?serverId="+data.serverId+'&volumeId='+data.volumeId);
   
  };
  
  export const saveEnableConfig= (data: any) => {
    return postAction("/sys/sysserver/updateEnableConfig",null,data);
  };
  
  export const scheduleSnapshot= (data: any) => {
    return postAction("/sys/sysserver/scheduleSnapshot?serverId="+data.serverId+'&corn='+data.corn);
  };

  export const updateScheduleSnapshot= (data: any) => {
    return postAction("/sys/sysserver/updateScheduleSnapshot?serverId="+data.serverId+'&scheduleSnapshot='+data.scheduleSnapshot);
  };
  
  export const selectUSBList = (data: any) => {
    return getAction("/sys/sysusbinfo/selectList", data);
  };
  
  export const saveUSB = (data: any) => {
    return postAction("/sys/sysusbinfo/attachDevice",null,data);
  };

  export const deleteUSB = (data: any) => {
    return postAction("/sys/sysusbinfo/detachDevice",null,data);
  };

  export const refreshDevice = (data: any) => {
    return postAction("/sys/sysusbinfo/refreshDevice",null,data);
  };
  
  export const SyncDevice= (data: any) => {
    return postAction("/sys/sysusbinfo/syncDevice",null,data);
  };
  export const selectExecList = (data: any) => {
    return downloadAction("/sys/sysserver/selectExec", data);
  };

export const rollBackSnap = (data: any) => {
  return postAction("/sys/sysserver/rollBackBySnapshot",null,data);
};

export const setMonitor = (data: any) => {
  return postAction("/sys/sysserver/isMonitor",null,data);
};

export const bindAPI = (data: any) => {
  return postAction("/sys/sysserver/attachPort",null,data);
};

export const unbindAPI = (data: any) => {
  return postAction("/sys/sysserver/detachPort",null,data);
};

export const changeSecurityGroup = (params: any) => {
  return postAction("/sys/sysserver/changeSecurityGroup", null, params);
};

export const testConnection = (data: any) => {
  return postAction("/sys/syssafeserverinspectionsettings/testConnection",data);
};

export const saveInspection = (data: any) => {
  return postAction("/sys/syssafeserverinspectionsettings/save",data);
};

export const updateInspection = (data: any) => {
  return postAction("/sys/syssafeserverinspectionsettings/update",data);
};

export const IsInspection = (data: any) => {
  return postAction("/sys/sysserver/isInspection",null,data);
};

export const selectInspectionInfo = (data: any) => {
  return getAction("/sys/syssafeserverinspectionsettings/info", data);
};

export const selectInspectionList = (data: any) => {
  return getAction("/sys/syssafeserverinspectionsettings/selectList", data);
};

export const deleteInspection = (data: any) => {
  return postAction("/sys/syssafeserverinspectionsettings/delete",data);
};

export const saveServertag = (data: any) => {
  return postAction("/sys/sysopenstackservertag/save",data);
};
export const deleteServertag = (data: any) => {
  return postAction("/sys/sysopenstackservertag/delete",data);
};

export const rebuildServer = (data: any) => {
  return postAction("/sys/sysserver/rebuildServer",null,data);
};

export const sysserver_changeAdminPassword = (data: any) => {
  return postAction("/sys/sysserver/changeAdminPassword",null,data);
};

export const selectServerErrorInfo = (data: any) => {
  return getAction("/sys/sysserver/selectServerErrorInfo", data);
};