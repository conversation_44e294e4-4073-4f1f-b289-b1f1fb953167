import { getAction, postAction } from "@/api/backend/manage";

export const getMonitorline = (data: any) => {
  return postAction("/agent/selectResourceSateByServerIdAndTime",null,data);
};


export const getNowResource = (params: any) => {
  return postAction("/agent/selectNowResourceSateByServerId",null,params);
};

export const getNowResourceCloud = (params: any) => {
  return postAction("/agent/selectNowResourceSateByCloudId",{},params);
};
// 云平台首页-topN
export const getTopFiveCpu = (params: any) => {
  return postAction("/sys/syshostcpumonitor/rankByCloudId",{},params);
};
// 云平台首页-topN
export const getTopFiveMemory = (params: any) => {
  return postAction("/sys/syshostmemerymonitor/rankByCloudId",{},params);
};