import { getAction, postAction } from "@/api/backend/manage";

  //OPENSTACK域管理模块
  export const getOpenstList = (data: any) => {
    return getAction("/sys/sysdomain/queryPage", data);
  };
  
  export const saveOpenst = (data: any) => {
    return postAction("/sys/sysdomain/save", data);
  };
  
  export const updateOpenst = (data: any) => {
    return postAction("/sys/sysdomain/update", data);
  };
  
  export const getDomainInfo = (data: any) => {
    return getAction("/sys/sysdomain/info", data);
  };
  
  export const deleteOpenst = (data: any) => {
    return postAction("/sys/sysdomain/delete",data);
  };

  export const selectOpenstList = (data: any) => {
    return getAction("/sys/sysdomain/selectList",data);
  };
  // 云平台首页-域列表
  export const getDomainCount = (data: any) => {
    return getAction("/sys/openstack/index/countDomain", data);
  };
  