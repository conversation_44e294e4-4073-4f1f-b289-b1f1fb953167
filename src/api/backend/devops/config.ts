import { getAction, postAction } from "@/api/backend/manage";
// 规则配置
export const getRuleservelist= (data: any) => {
  return getAction("/sys/sysconfigrule/queryPage", data);
};

export const selectRuleservelist= (data: any) => {
  return getAction("/sys/sysconfigrule/selectList", data);
};

export const deleteRuleserve = (data: any) => {
  return postAction("/sys/sysconfigrule/delete",data);
};

export const getConfigInfo = (data:any) => {
  return getAction("/sys/sysconfigrule/getConfigByServerId",data);
}

export const saveRuleserve = (data: any) => {
  return postAction("/sys/sysconfigrule/save", data);
};

export const updateRuleserve= (data: any) => {
  return postAction("/sys/sysconfigrule/update", data);
};
// qos
export const getQoSList= (data: any) => {
  return getAction("/sys/sysqosspecs/queryPage", data);
};

export const selectQoSList= (data: any) => {
  return getAction("/sys/sysqosspecs/selectList", data);
};
export const saveQoS = (data: any) => {
  return postAction("/sys/sysqosspecs/save", data);
};

export const updateQoS = (data: any) => {
  return postAction("/sys/sysqosspecs/update", data);
};

export const deleteQOS = (data: any) => {
  return postAction("/sys/sysqosspecs/delete",data);
};

export const setSpecsKey = (data: any) => {
  return postAction("/sys/sysqosspecs/setSpecsKey", data);
};

export const unsetSpecsKey = (params: any) => {
  return postAction("/sys/sysqosspecs/deleteSpecsKey", null, params);
};