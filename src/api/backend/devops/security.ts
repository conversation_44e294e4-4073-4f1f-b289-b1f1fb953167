import { getAction, postAction } from "@/api/backend/manage";

export const getSecugroupList = (data: any) => {
  return getAction("/sys/syssecuritygroup/queryPage", data);
};

export const selectSecugroupList = (data: any) => {
  return getAction("/sys/syssecuritygroup/selectList", data);
};

export const saveSecugroup = (data: any) => {
  return postAction("/sys/syssecuritygroup/save", data);
};

export const updateSecugroup = (data: any) => {
  return postAction("/sys/syssecuritygroup/update", data);
};

export const deleteSecugroup = (data: any) => {
  return postAction("/sys/syssecuritygroup/delete",data);
};

export const securitygroup_selectListByServerId = (data: any) => {
  return getAction("/sys/syssecuritygroup/selectListByServerId", data);
};

export const getSecuruleList = (data: any) => {
  return getAction("/sys/sysrule/queryPage", data);
};

  export const selectSecuruleList = (data: any) => {
    return getAction("/sys/sysrule/selectList", data);
  };
  
  export const saveSecurule = (data: any) => {
    return postAction("/sys/sysrule/save", data);
  };
  
  export const updateSecurule = (data: any) => {
    return postAction("/sys/sysrule/update", data);
  };
  
  export const deleteSecurule = (data: any) => {
    return postAction("/sys/sysrule/delete",data);
  };