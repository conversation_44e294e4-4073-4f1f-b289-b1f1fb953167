import { getAction, postAction } from "@/api/backend/manage";

// 虚拟机图
export const getVisualSatistic = (data:any) => {
    return getAction("/sys/openstack/index/queryHypervisorStatisticsByCloudId",data);
}
// 虚拟机列表 报错
export const getVisualList = (data:any) => {
    return getAction("/sys/openstack/index/queryHypervisorByCloudId",data);
}
// 计算服务 报错
export const getComputeServiceList = (data:any) => {
    return getAction("/sys/openstack/index/queryComputeServiceByCloudId",data);
}
// 块存储

export const getBlockServiceList = (data:any) => {
    return getAction("/sys/openstack/index/queryBlockServiceByCloudId",data);
}
// 聚合列表
export const getHostAggregateList = (data:any) => {
    return getAction("/sys/openstack/index/queryHostAggregateByCloudId",data);
}
// 网络代理
export const getNetworkAgentList = (data:any) => {
    return getAction("/sys/openstack/index/queryNetworkAgentByCloudId",data);
}

export const addPoly = (data:any) => {
    return getAction("/sys/openstack/index/createHostAggregateByCloudId",data);
}

export const updatePoly = (data:any) => {
    return getAction("/sys/openstack/index/updateHostAggregateByCloudId",data);
}

export const deletePoly = (data:any) => {
    return getAction("/sys/openstack/index/deleteHostAggregateByCloudId",data);
}

export const addPolyHost = (data:any) => {
    return getAction("/sys/openstack/index/hostAggregateAddHostByCloudId",data);
}

export const delPolyHost = (data:any) => {
    return getAction("/sys/openstack/index/hostAggregateRemoveHostByCloudId",data);
}

export const supportsMasakari = (data:any) => {
    return getAction("/sys/sysopenstackmasakarisegment/supportsMasakari",data);
}

export const closeComputeServiceByCloudId = (data:any) => {
    return getAction("/sys/openstack/index/closeComputeServiceByCloudId",data);
}

export const startComputeServiceByCloudId = (data:any) => {
    return getAction("/sys/openstack/index/startComputeServiceByCloudId",data);
}