import { getAction, postAction } from "@/api/backend/manage";

  export const getProjectList = (data: any) => {
    return getAction("/sys/sysproject/queryPage", data);
  };
  
  export const getProjectInfo = (data: any) => {
    return getAction("/sys/sysproject/info", data);
  };
  
  export const saveProject = (data: any) => {
    return postAction("/sys/sysproject/save", data);
  };
  
  export const updateProject = (data: any) => {
    return postAction("/sys/sysproject/update", data);
  };
  
  export const deleteProject = (data: any) => {
    return postAction("/sys/sysproject/delete",data);
  };

  export const selectProjectList = (data: any) => {
    return getAction("/sys/sysproject/selectList", data);
  };

  
  export const getComputeQuotaInfo = (data: any) => {
    return getAction("/sys/sysproject/getquotaByProjectId", data);
  };

  export const updateQuota = (data: any) => {
    return postAction("/sys/sysproject/updateQuotaByProjectId", data);
  };

  export const saveComputeQuota = (data: any) => {
    return postAction("/sys/sysprojectcompute/save", data);
  };

  export const updateComputeQuota = (data: any) => {
    return postAction("/sys/sysprojectcompute/update", data);
  };

  export const getNetworkQuotaInfo = (data: any) => {
    return getAction("/sys/sysprojectnetwork/info", data);
  };

  export const saveNetworkQuota = (data: any) => {
    return postAction("/sys/sysprojectnetwork/save", data);
  };

  export const updateNetworkQuota = (data: any) => {
    return postAction("/sys/sysprojectnetwork/update", data);
  };

  export const getVolumeQuotaInfo = (data: any) => {
    return getAction("/sys/sysprojectvolume/info", data);
  };

  export const saveVolumeQuota = (data: any) => {
    return postAction("/sys/sysprojectvolume/save", data);
  };

  export const updateVolumeQuota = (data: any) => {
    return postAction("/sys/sysprojectvolume/update", data);
  };
  
  export const getProjectCount = (data: any) => {
    return postAction("/sys/sysdomain/countProject",null, data);
  };
