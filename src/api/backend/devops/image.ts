import { getAction, postAction } from "@/api/backend/manage";

  export const getImageList = (data: any) => {
    return getAction("/sys/sysimage/queryPage", data);
  };

  export const selectImageList = (data: any) => {
    return getAction("/sys/sysimage/selectList", data);
  };
  
  export const saveImage = (data: any) => {
    return postAction("/sys/sysimage/save", data);
  };
  
  export const updateImage = (data: any) => {
    return postAction("/sys/sysimage/update", data);
  };
  
  export const deleteImage = (data: any) => {
    return postAction("/sys/sysimage/delete",data);
  };