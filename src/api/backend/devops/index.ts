import { getAction, postAction } from "@/api/backend/manage";
// OPENSTACK云平台下 首页、平台管理、计算管理、网络管理、裸金属管理
export const queryResource = (data: any) => {
  return getAction("/sys/openstack/index/queryResourceByCloudId", data);
};

export const projectResource = (data: any) => {
  return getAction("/sys/openstack/index/projectResourceList", data);
};

export const queryHypervisor = (data: any) => {
  return getAction("/sys/openstack/index/queryHypervisorByCloudId", data);
};