import { getAction, postAction } from "@/api/backend/manage";

export const getRouteList= (data: any) => {
  return getAction("/sys/sysroute/queryPage", data);
};

export const getRouteInfo = (data: any) => {
  return getAction("/sys/sysroute/info", data);
};

export const saveRoute= (data: any) => {
  return postAction("/sys/sysroute/save", data);
};

export const updateRoute= (data: any) => {
  return postAction("/sys/sysroute/update", data);
};

export const deleteRoute = (data: any) => {
  return postAction("/sys/sysroute/delete",data);
};

export const setNetwork = (data: any) => {
  return postAction("/sys/sysroute/setNetwork?routeId="+data.routeId+'&networkId='+data.networkId+'&enableSnat='+data.enableSnat);
};

export const clearNetwork = (data: any) => {
  return postAction("/sys/sysroute/clearNetwork?routeId="+data);
};

// 接口
export const getRouteAPIList= (data: any) => {
  return getAction("/sys/sysrouteinterface/queryPage", data);
};

export const selectAPIList= (data: any) => {
  return getAction("/sys/sysrouteinterface/selectList", data);
};

export const saveRouteAPI= (data: any) => {
  return postAction("/sys/sysrouteinterface/save", data);
};

export const deleteRouteAPI = (data: any) => {
  return postAction("/sys/sysrouteinterface/delete",data);
};