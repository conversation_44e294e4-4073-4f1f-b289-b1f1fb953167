import { getAction, postAction } from "@/api/backend/manage";

export const getFloatingipList = (data: any) => {
  return getAction("/sys/sysfloatingip/queryPage", data);
};

export const selectFloatingipList = (data: any) => {
  return getAction("/sys/sysfloatingip/selectList", data);
};

export const saveFloatingip = (params: any) => {
  return postAction("/sys/sysfloatingip/save",null,params);
};

export const updateFloatingip = (data: any) => {
  return postAction("/sys/sysfloatingip/update", data);
};

export const deleteFloatingip = (data: any) => {
  return postAction("/sys/sysfloatingip/delete",data);
};

export const bindFloatingip = (data: any) => {
  return postAction("/sys/sysfloatingip/bind?floatingIpId="+data.floatingIpId+'&serverId='+data.serverId);
};

export const unbindFloatingip = (data: any) => {
  return postAction("/sys/sysfloatingip/unbind?floatingIpId="+data);
};

export const getselectflaotId= (data: any) => {
  return getAction("/sys/sysfloatingip/selectList", data);
};

export const saveFloatingipPort = (data: any) => {
  return postAction("/sys/sysfloatingip/createPortForwarding",data);
};
export const deleteFloatingipPort = (data: any) => {
  return postAction("/sys/sysfloatingip/deletePortForwarding",data);
};


export const getflaotipInfo= (data: any) => {
  return getAction("/sys/sysfloatingip/info", data);
};