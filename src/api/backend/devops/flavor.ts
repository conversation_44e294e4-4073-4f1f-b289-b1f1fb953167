import { getAction, postAction } from "@/api/backend/manage";

export const getFlavorList = (data: any) => {
    return getAction("/sys/sysflavor/queryPage", data);
  };

  export const selectFlavorList = (data: any) => {
    return getAction("/sys/sysflavor/selectList", data);
  };

  export const selectFlavorInfo = (data: any) => {
    return getAction("/sys/sysflavor/info", data);
  };
  
  export const saveFlavor = (data: any) => {
    return postAction("/sys/sysflavor/save", data);
  };
  
  export const updateFlavor = (data: any) => {
    return postAction("/sys/sysflavor/update", data);
  };
  
  export const deleteFlavor = (data: any) => {
    return postAction("/sys/sysflavor/delete",data);
  };
  
  export const setextraSpecs = (data: any) => {
    return postAction("/sys/sysflavor/setextraSpecs?id="+data.id,data.extraSpecsaMap);
  };