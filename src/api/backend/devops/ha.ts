import { getAction, postAction } from "@/api/backend/manage";

export const getSegmentList = (data: any) => {
    return getAction("/sys/sysopenstackmasakarisegment/queryPage", data);
};

export const saveSegment = (data: any) => {
    return postAction("/sys/sysopenstackmasakarisegment/save", data);
};

export const updateSegment = (data: any) => {
    return postAction("/sys/sysopenstackmasakarisegment/update", data);
};

export const deleteSegment = (data: any) => {
    return postAction("/sys/sysopenstackmasakarisegment/delete",data);
};

export const getSegmentInfo = (data: any) => {
    return getAction("/sys/sysopenstackmasakarisegment/info", data);
};

export const getMasakariHostList = (data: any) => {
    return getAction("/sys/sysopenstackmasakarihost/queryPage", data);
};

export const selectMasakariHostList = (data: any) => {
    return getAction("/sys/sysopenstackmasakarihost/selectList", data);
};

export const saveMasakariHost = (data: any) => {
    return postAction("/sys/sysopenstackmasakarihost/save", data);
};

export const updateMasakariHost = (data: any) => {
    return postAction("/sys/sysopenstackmasakarihost/update", data);
};

export const deleteMasakariHost = (data: any) => {
    return postAction("/sys/sysopenstackmasakarihost/delete",data);
};

export const getMasakariHostInfo = (data: any) => {
    return getAction("/sys/sysopenstackmasakarihost/info", data);
};

export const selectHostListByCloudId = (data: any) => {
    return postAction("/sys/sysopenstackmasakarihost/selectHostListByCloudId",null, data);
};