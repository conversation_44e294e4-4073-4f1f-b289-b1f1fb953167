/* eslint-disable @typescript-eslint/no-explicit-any */
import { axios } from "@/utils/request";
// 封装axios请求，统一格式
export function getAction(
  url: any,
  params: any,
  timeout = 900000,
  headers={},
) {
  return axios({
    url,
    method: "GET",
    params,
    headers,
    timeout,
  });
}

// delete
export function deleteAction(
  url: any,
  params: any,
  headers = { "Content-Type": "application/json;" }
) {
  return axios({
    url,
    method: "DELETE",
    params,
    headers,
  });
}

// post
export function postAction(
  url: any={},
  data: any={},
  params: any = {},
  // timeout = 900000,
  cancelToken:any = null,
  headers = { "Content-Type": "application/json;" }
) {
  return axios({
    url,
    method: "POST",
    data,
    params,
    headers,
    cancelToken
    // timeout
  });
}

// put
export function putAction(
  url: any,
  data: any,
  timeout = 900000,
  headers = { "Content-Type": "application/json;" }
) {
  return axios({
    url,
    method: "PUT",
    data,
    headers,
    timeout
  });
}
// upload 文件上传
export function uploadAction(
  url: any,
  data: any,
  timeout = 0,
  headers = { "Content-Type": "multipart/form-data;" }
) {
  return axios({
    url,
    method: "POST",
    data,
    headers,
    timeout
  });
}
// download 二进制流文件下载
export function downloadAction(
  url: any,
  params: any,
  responseType:any = "blob",
  timeout = 0,
  headers = { "Content-Type": "application/json;" }
) {
  return axios({
    url,
    method: "GET",
    params,
    headers,
    timeout,
    responseType
  });
}
