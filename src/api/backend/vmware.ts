import { getAction, postAction } from "@/api/backend/manage";
// vmware云平台下接口
export const selectTotalresource = (data: any) => {
    return getAction("/sys/vm/index/selectTotalResource", data);
};

export const hostInfo = (data: any) => {
    return getAction("/sys/sysvmhostsystem/info", data);
};

export const getvmlist = (data: any) => {
    return getAction("/sys/sysvmvirtualmachine/queryPage", data);
};

export const virtualInfo = (data: any) => {
    return getAction("/sys/sysvmvirtualmachine/info", data);
};