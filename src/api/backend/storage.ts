import { getAction, postAction } from "./manage";
// 卷管理相关接口

// 卷类型
export const getVolumeTypeList = (data: any) => {
    return getAction('/sys/sysvolumetype/queryPage', data)
}

export const selectVolumeTypeList = (data: any) => {
    return getAction('/sys/sysvolumetype/selectList', data)
}

export const saveType = (data: any) => {
    return postAction('/sys/sysvolumetype/save', data)
}

export const updateType = (data: any) => {
    return postAction('/sys/sysvolumetype/update', data)
}

export const deleteType = (data: any) => {
    return postAction('/sys/sysvolumetype/delete', data)
}

// 卷
export const getVolumeList = (data: any) => {
    return getAction('/sys/sysvolume/queryPage', data)
}

export const selectVolumeList = (data: any) => {
    return getAction('/sys/sysvolume/selectList', data)
}

export const saveVolume = (data: any) => {
    return postAction('/sys/sysvolume/save', data)
}

export const updateVolume = (data: any) => {
    return postAction('/sys/sysvolume/update?id='+data.id+'&name='+data.name+'&description='+data.description)
}

export const deleteVolume = (data: any) => {
    return postAction('/sys/sysvolume/delete', data)
}

export const updateBootable = (data: any) => {
    return postAction('/sys/sysvolume/bootable?volumeId='+data.volumeId+'&state='+data.state)
}

export const updateSize = (data: any) => {
    return postAction('/sys/sysvolume/updateVolumeSizes?volumeId='+data.volumeId+'&size='+data.size)
}

export const uploadToImage = (data: any) => {
    return postAction('/sys/sysvolume/uploadToImage',data);
}

// 卷快照
export const getVolumeSnapShotList = (data: any) => {
    return getAction('/sys/sysvolumesnapshot/queryPage', data)
}

export const selectVolumeSnapShotList = (data: any) => {
    return getAction('/sys/sysvolumesnapshot/selectList', data)
}

export const saveSnapshot = (data: any) => {
    return postAction('/sys/sysvolumesnapshot/save', data)
}

export const createSnapshot = (data: any) => {
    return postAction('/sys/sysvolume/createVolumeSnapshot?volumeId='+data.volumeId+'&snapshotName='+data.name+'&description='+data.description)
}

export const updateSnapshot = (data: any) => {
    return postAction('/sys/sysvolumesnapshot/update?id='+data.id+'&name='+data.name+'&description='+data.description)
}

export const deleteSnapshot = (data: any) => {
    return postAction('/sys/sysvolumesnapshot/delete', data)
}

export const createBackup = (data: any) => {
    return postAction('/sys/sysvolume/volumeBackupCreate',data)
}

export const updateBackup = (data: any) => {
    return postAction('/sys/sysvolumebackup/update?volumeId='+data.volumeId+'&snapshotName='+data.name+'&description='+data.description)
}

export const getVolumeBackUpList = (data: any) => {
    return getAction('/sys/sysvolumebackup/queryPage', data)
}

export const deleteBackup = (data: any) => {
    return postAction('/sys/sysvolumebackup/delete', data)
}

export const resumeBackup = (data: any) => {
    return postAction('/sys/sysvolumebackup/restore',null,data)
}

export const rollVolumeSnap = (data: any) => {
    return postAction('/sys/sysvolume/rollBackBySnapshot',null,data)
}
export const bindQOS = (data: any) => {
    return postAction('/sys/sysqosspecs/bindVolumeType',null,data)
}

export const unbindQOS = (data: any) => {
    return postAction('/sys/sysqosspecs/disassociateVolumeType',null,data)
}

export const getVolumeAttachmentList = (data: any) => {
    return getAction('/sys/sysvolumeattachment/selectList', data)
}