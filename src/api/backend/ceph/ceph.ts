import { axios } from "@/utils/request";
const service = axios;
export const auth = (params) => {
    return service({url:'/sys/ceph/health/checkCephAuth',method: "GET",params})
  }
  
  export const logout = (params) => {
    return service({url:'/auth/logout',method: "POST",timeout:900000,params})
  }
  
  export const dashboard = (params) => {
    return service({url:'/sys/ceph/health/minimal',method: "GET",params})
  }
  
  export const getHostList = (params) => {
    return service({url:'/sys/ceph/selectHostList',method: "GET",params})
  }
  
  export const addHostAPI = (data) => {
    return service({url:'/host',method: "POST",data,headers:{'Accept':'application/vnd.ceph.api.v0.1+json'},timeout:900000})
  }
  
  export const editHostAPI = (hostname,data) => {
    return service({url:`/host/${hostname}`,method: "PUT",data,headers:{'Accept':'application/vnd.ceph.api.v0.1+json'},timeout:900000})
  }
  
  export const delHostAPI = (hostname) => {
    return service({url:`/host/${hostname}`,method: "DELETE",headers:{'Accept':'application/vnd.ceph.api.v0.1+json'},timeout:900000})
  }
  
  export const getOSDList = (params) => {
    return service({url:'/sys/ceph/selectOSDList',method: "GET",params})
  }
  
  export const addOSDAPI = (data) => {
    return service({url:'/osd',method: "POST",data,timeout:900000})
  }
  
  export const editOSDAPI = (svc_id,data) => {
    return service({url:`/osd/${svc_id}`,method: "PUT",data,timeout:900000})
  }
  
  export const delOSDAPI = (svc_id) => {
    return service({url:`/osd/${svc_id}`,method: "DELETE",headers:{'Accept':'application/vnd.ceph.api.v0.1+json'},timeout:900000})
  }
  
  export const getPoolList = (params) => {
    return service({url:'/sys/ceph/selectPoolList',method: "GET",params})
  }
  
  export const addPoolAPI = (data) => {
    return service({url:'/pool',method: "POST",data,timeout:900000})
  }
  
  export const editPoolAPI = (pool_name,data) => {
    return service({url:`/pool/${pool_name}`,method: "PUT",data,timeout:900000})
  }