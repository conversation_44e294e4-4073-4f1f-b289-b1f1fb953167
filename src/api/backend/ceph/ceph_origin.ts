import { getCephToken } from "@/utils/auth";
import { handleErr } from "@/utils/tool";
import axios  from "axios";
// ustack-api缺少的，原始接口作为补充
const service = axios.create({
  baseURL: import.meta.env.VITE_CEPH, // url = base url + request url
    // timeout: 900000 // request timeout
  });
  service.interceptors.request.use(
    config => {
      // if(!config.headers["content-type"])
      config.headers["content-type"] = "application/json";
      if(!config.headers["Accept"])
      config.headers["Accept"] = "application/vnd.ceph.api.v1.0+json";
      const tokenStr = getCephToken();
      if (tokenStr !== "" || tokenStr !== null || tokenStr !== undefined) {
        //获取token
        config.headers["token"] = tokenStr;
      }
      return config;
    },
    error => {
      // do something with request error
      return Promise.reject(error);
    }
  );
  
  // response interceptor
  service.interceptors.response.use(
    response => {
      // removePendingRequest(response.config);
      handleErr(response)
      return !response.headers["Content-disposition"] ? response.data : response;
    },
    error => {
      handleErr(error)
      return Promise.reject(error);
    }
  );

export const auth = (data) => {
  return service({url:'/auth',method: "POST",data:JSON.stringify({username:'admin',password:'UnionTech!23'}),timeout:900000})
}

export const logout = () => {
  return service({url:'/auth/logout',method: "POST",timeout:900000})
}

export const dashboard = (params) => {
  return service({url:'/health/minimal',method: "GET",params,timeout:900000})
}

export const getHostList = (params) => {
  return service({url:'/host',method: "GET",params,timeout:900000})
}

export const addHostAPI = (data) => {
  return service({url:'/host',method: "POST",data,headers:{'Accept':'application/vnd.ceph.api.v0.1+json'},timeout:900000})
}

export const editHostAPI = (hostname,data) => {
  return service({url:`/host/${hostname}`,method: "PUT",data,headers:{'Accept':'application/vnd.ceph.api.v0.1+json'},timeout:900000})
}

export const delHostAPI = (hostname) => {
  return service({url:`/host/${hostname}`,method: "DELETE",headers:{'Accept':'application/vnd.ceph.api.v0.1+json'},timeout:900000})
}

export const getOSDList = (params) => {
  return service({url:'/osd',method: "GET",params,timeout:900000})
}

export const addOSDAPI = (data) => {
  return service({url:'/osd',method: "POST",data,timeout:900000})
}

export const editOSDAPI = (svc_id,data) => {
  return service({url:`/osd/${svc_id}`,method: "PUT",data,timeout:900000})
}

export const delOSDAPI = (svc_id) => {
  return service({url:`/osd/${svc_id}`,method: "DELETE",headers:{'Accept':'application/vnd.ceph.api.v0.1+json'},timeout:900000})
}

export const getPoolList = (params) => {
  return service({url:'/pool',method: "GET",params,timeout:900000})
}

export const addPoolAPI = (data) => {
  return service({url:'/pool',method: "POST",data,timeout:900000})
}

export const editPoolAPI = (pool_name,data) => {
  return service({url:`/pool/${pool_name}`,method: "PUT",data,timeout:900000})
}