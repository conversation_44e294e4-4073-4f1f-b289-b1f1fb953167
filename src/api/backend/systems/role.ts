import { getAction, postAction } from "@/api/backend/manage";
// 角色管理
// 删除
export const deleteRole = (data: any) => {
  return postAction("/sys/role/delete", data);
};
// 分页查询角色列表
export const selectRolePage = (data: any) => {
  return getAction("/sys/role/queryPage", data);
};
// 提交角色
export const saveRole = (data: any) => {
  return postAction("/sys/role/save", data);
};
// 获取角色信息
export const selectById = (data: any) => {
  return getAction("/sys/role/selectInfo", data);
};
//新获取角色信息
export const selectMenuInfo = (data: any) => {
  return getAction("/sys/role/selectMenuInfo", data);
};
export const selectInfo = (data: any) => {
  return getAction("/sys/role/selectInfo", data);
};
// 角色列表（全部，不分页）
export const selectList = (data: any) => {
  return getAction("/sys/role/selectList", data);
};
// 修改角色
export const updateRole = (data: any) => {
  return postAction("/sys/role/update", data);
};
