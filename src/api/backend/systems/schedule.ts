import { getAction, postAction, uploadAction } from "@/api/backend/manage";
// 定时任务接口
// 获取任务列表接口
export const querySchedulePage = (data: any) => {
  return getAction("/sys/schedule/queryPage", data);
};

// 获取不分页列表接口
export const selectScheduleList = (data: any) => {
  return getAction("/sys/schedule/list", data);
};

// 新增任务接口
export const saveSchedule = (data: any) => {
  return postAction("/sys/schedule/save", data);
};

// 修改任务接口
export const updateSchedule = (data: any) => {
  return postAction("/sys/schedule/update", data);
};

// 删除任务接口
export const deleteSchedule = (data: any) => {
  return postAction("/sys/schedule/delete", data);
};

// 获取日志列表接口
export const queryScheduleLog = (data: any) => {
  return getAction("/sys/scheduleLog/queryPage", data);
};

// 立即执行任务接口
export const runSchedule = (data: any) => {
  return postAction("/sys/schedule/run", data);
};
// 暂停任务接口
export const pauseSchedule = (data: any) => {
  return postAction("/sys/schedule/pause", data);
};

// 恢复任务接口
export const resumeSchedule = (data: any) => {
  return postAction("/sys/schedule/resume", data);
};