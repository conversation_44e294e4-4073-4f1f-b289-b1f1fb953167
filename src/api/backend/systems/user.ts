import { getAction, postAction, downloadAction } from "@/api/backend/manage";
// 用户管理相关接口（包括登录）
export const login = (data: any) => {
  return postAction("/sys/login", data);
};
export const logout = (data: any) => {
  return postAction("/sys/logout", data);
};

export const selectWorker = (data: any) => {
  return getAction("/sys/user/list", data);
};

export const queryWorker = (data: any) => {
  return getAction("/sys/user/selectList", data);
};

export const resetPwd = (data: any) => {
  return postAction("/sys/user/resetPassword?userId="+data);
};

export const info = (data: any) => {
  return getAction("/sys/user/info", data);
};
export const password = (data: any) => {
  return postAction("/sys/user/password", data);
};
export const saveUser = (data: any) => {
  return postAction("/sys/user/save", data);
};
export const selectById = (data: any) => {
  return getAction("/sys/user/selectById", data);
};
export const selectList = (data: any) => {
  return getAction("/sys/user/selectList", data);
};
export const updateUser = (data: any) => {
  return postAction("/sys/user/updateUser?id="+data.id+'&mobile='+data.mobile+'&email='+data.email);
};
export const update = (data: any) => {
  return postAction("/sys/user/update", data);
};
export const deleteUserById = (data: any) => {
  return postAction("/sys/user/deleteById", data);
};


export const getcode = (data: any) => {
  return getAction("/sys/attachment/getAttachmentCode", data);
};

export const selectAttachmentList = (data: any) => {
  return getAction("/sys/attachment/selectList", data);
};

export const upload = (data: any) => {
  return postAction("/sys/attachment/upload", data);
};

export const updateAvatar = (data: any) => {
  return postAction("/sys/user/updateAvatar?attachmentId="+data.attachmentId);
};

export const download = (data: any) => {
  return downloadAction("/sys/sysserver/selectExec",data);
};

export const deleteAttach = (data: any) => {
  return postAction("/sys/attachment/delete", data);
};

export const getLoginList = (data: any) => {
  return getAction("/sys/user/selectLoginList", data);
};

export const forceLoginOut = (data: any) => {
  return postAction("/sys/user/forceLoginOut",null, data);
};
export const uploadChunk = (data: any,cancelToken) => {
  return postAction("/sys/attachment/uploadChunk", data,null,cancelToken);
};
export const mergeChunks = (data: any) => {
  return postAction("/sys/attachment/mergeChunks",null, data);
};