import { getAction, postAction } from "@/api/backend/manage";
// 部门管理接口（不包括user）
export const getDepartList = (data: any) => {
  return getAction("/sys/sysdepartment/selectList", data);
};

export const saveDepart = (data: any) => {
  return postAction("/sys/sysdepartment/save", data);
};

export const updateDepart = (data: any) => {
  return postAction("/sys/sysdepartment/update", data);
};

export const deleteDepart = (data: any) => {
  return postAction("/sys/sysdepartment/delete",data);
};