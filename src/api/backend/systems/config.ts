import { getAction, postAction } from "@/api/backend/manage";
// 参数管理接口
// 批量删除
export const deleteBatchConfig = (data: any) => {
  return postAction("/sys/config/deleteBatch", data);
};
// 查询列表
export const queryPageConfig = (data: any) => {
  return getAction("/sys/config/queryPage", data);
};
// 提交
export const saveConfig = (data: any) => {
  return postAction("/sys/config/save", data);
};
//详细信息
export const selectInfoConfig = (data: any) => {
  return getAction("/sys/config/selectInfo", data);
};
// 修改
export const updateConfig = (data: any) => {
  return postAction("/sys/config/update", data);
};
