import { getAction, postAction, uploadAction } from "@/api/backend/manage";


export const getFlowList = (data: any) => {
    return getAction("/sys/flowable/queryPage", data);
};
  
export const saveFlow = (data: any) => {
    return postAction("/sys/flowable/save", data);
};

export const updateFlow = (data: any) => {
    return postAction("/sys/flowable/update", data);
};

export const deleteFlow = (data:any) => {
    return postAction("/sys/flowable/delete",data);
}