import { getAction, postAction, uploadAction } from "@/api/backend/manage";
// 字典类型、字典数据接口
//   获取字典数据分页列表接口
export const searchDictList = (data: any) => {
  return getAction("/sys/sysdictdata/queryPage", data);
};

//   获取字典类型分页列表接口
export const searchDicttypeList = (data: any) => {
    return getAction("/sys/sysdicttype/queryPage", data);
  };

  //   获取字典数据全部列表接口
  export const selectDictList = (data: any) => {
    return getAction("/sys/sysdictdata/selectList", data);
  };

  //   获取字典类型全部列表接口
export const selectDicttypeList = (data: any) => {
    return getAction("/sys/sysdicttype/selectList", data);
  };

  // 新增字典数据接口
export const saveDict = (data: any) => {
    return postAction("/sys/sysdictdata/save", data);
  };

// 修改字典数据接口
export const updateDict = (data: any) => {
    return postAction("/sys/sysdictdata/update", data);
  };

// 删除字典数据接口
export const deleteDictByid = (data: any) => {
    return postAction("/sys/sysdictdata/delete", data);
  };

    // 新增字典类型接口
export const saveDicttype = (data: any) => {
    return postAction("/sys/sysdicttype/save", data);
  };

// 修改字典类型接口
export const updateDicttype = (data: any) => {
    return postAction("/sys/sysdicttype/update", data);
  };

// 删除字典类型接口
export const deleteDicttypeByid = (data: any) => {
    return postAction("/sys/sysdicttype/delete", data);
  };