import { getAction, postAction } from "@/api/backend/manage";
// 菜单管理
// 获取系统中所有的菜单
export const selectAllMenuList = (data: any) => {
    return getAction("/sys/menu/selectList", data);
};
// 获取当前登录用户具有权限的菜单
export const selectNav = (data: any) => {
    return getAction("/sys/menu/selectNav", data);
};
export const selectMenuById = (data: any) => {
    return getAction("/sys/menu/selectById", data);
};
export const deleteMenuById = (data: any) => {
    return postAction("/sys/menu/deleteById", data);
};
export const saveMenu = (data: any) => {
    return postAction("/sys/menu/save", data);
};
export const updateMenu = (data: any) => {
    return postAction("/sys/menu/update", data);
};
// 获取云平台下的菜单
export const selectNavByCloud = (data: any) => {
    return getAction("/sys/menu/selectNavByCloud", data);
};