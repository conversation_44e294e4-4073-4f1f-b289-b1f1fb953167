import { getAction, postAction } from "../manage";
//系统管理_部分接口


export const getLdapData= (data:any) => {
    return getAction("/sys/sysldap/queryPage",data);
}

export const saveLdap= (data: any) => {
    return postAction("/sys/sysldap/save", data);
};

export const updateLdap = (data: any) => {
    return postAction("/sys/sysldap/update", data);
};

export const deleteLdap= (data: any) => {
    return postAction("/sys/sysldap/delete",data);
};  

export const getLdapList = (data: any) => {
    return getAction("/sys/sysldap/selectList", data);
};

export const syncData= (data: any) => {
    return postAction("/sys/sysldap/syncData", data);
};
//安全中心
export const getScanResultList= (data:any) => {
    return getAction("sys/safe/queryScanResultList",data);
}

export const getStatisticsList= (data:any) => {
    return getAction("sys/safe/statistics",data);
}

export const getCveHostList= (data:any) => {
    return getAction("sys/safe/queryCVEHost",data);
}

export const postimportCVE = (data: any) => {
    return postAction("/sys/safe/importCVE", data);
};

export const postfixCVE = (data: any,a:any) => {
    return postAction("/sys/safe/fixCVE", data,a);
};


export const getprogress = (data: any) => {
    return getAction("/sys/safe/progress", data);
};

export const getexecuteScan= (data:any) => {
    return getAction("sys/safe/executeScan",data);
}


export const getselectExecuteScanStatus= (data:any) => {
    return getAction("sys/safe/selectExecuteScanStatus",data);
}


// CVE扫描

export const getcveList= (data:any) => {
    return getAction("/sys/safe/queryCVEList",data);
}
// POST /sys/safe/saveHost
export const postsaveHost= (data:any) => {
    return postAction("/sys/safe/saveHost",data);
}

export const postdeletHost= (params:any) => {
    return postAction("/sys/safe/deleteHost",{},params);
}

export const getcveResultState= (data:any) => {
    return getAction("/sys/safe/selectImportCVEState",data);
}