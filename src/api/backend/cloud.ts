import { getAction, postAction } from "@/api/backend/manage";
// 云平台管理接口
export const getCloudList = (data: any) => {
  return getAction("/sys/syscloud/queryPage", data);
};

export const selectCloudList = (data: any) => {
  return getAction("/sys/syscloud/selectList", data);
};

export const selectCloudInfo = (data: any) => {
  return getAction("/sys/syscloud/info", data);
};
// manageUserId参数要求：逗号连接字符串，并前后加上-100，如"-100,1,-100"
export const saveCloud = (data: any) => {
  return postAction("/sys/syscloud/save", data);
};

export const updateCloud = (data: any) => {
  return postAction("/sys/syscloud/update", data);
};

export const getCloudInfo = (data: any) => {
  return getAction("/sys/syscloud/info", data);
}
export const testConnect = (data: any) => {
  return postAction("/sys/syscloud/testConnection", data);
};

export const deleteCloud = (data: any) => {
  return postAction("/sys/syscloud/deleteByStatus",data);
};

export const initCloud = (params: any) => {
  return postAction("/sys/syscloud/initCloud",null,params);
};

export const initCloudState = (params: any) => {
  return getAction("/sys/syscloud/selectInitCloudState",params);
};

export const queryInitHistory = (params: any) => {
  return getAction("/sys/syscloud/queryInitHistory",params);
};

export const getCloudUserlist = (data: any) => {
  return postAction("/sys/syscloud/selectManageUserListByCloudId?id="+data);
};
// 云平台首页-物理资源
export const queryPhysicalSource = (data: any) => {
  return getAction("/sys/openstack/index/queryStatisticByCloudId", data);
}