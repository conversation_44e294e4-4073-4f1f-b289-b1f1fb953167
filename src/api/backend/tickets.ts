import { getAction, postAction } from "@/api/backend/manage";
// 工单管理相关接口
export const getTicketList = (data: any) => {
  return getAction("/sys/systicket/queryPage", data);
};

export const getTicketInfo = (data: any) => {
  return getAction("/sys/systicket/info", data);
};

export const saveTicket = (data: any) => {
  return postAction("/sys/systicket/save", data);
};

export const saveDelayTicket = (data: any) => {
  return postAction("/sys/systicket/postponeTicket", data);
};

export const savePlusTicket = (data: any) => {
  return postAction("/sys/systicket/modifyTicket", data);
};

export const updateTicket = (data: any) => {
  return postAction("/sys/systicket/update", data);
};

export const deleteTicket = (data: any) => {
  return postAction("/sys/systicket/delete",data);
};

export const getTickettaskList = (data: any) => {
  return getAction("/sys/systickettask/queryPage", data);
};

export const getTickettaskInfo = (data: any) => {
  return getAction("/sys/systickettask/info", data);
};

export const saveTickettask = (data: any) => {
  return postAction("/sys/systickettask/save", data);
};

export const updateTickettask = (data: any) => {
  return postAction("/sys/systickettask/update", data);
};

export const deleteTickettask = (data: any) => {
  return postAction("/sys/systickettask/delete",data);
};