import { getAction, postAction, downloadAction } from "@/api/backend/manage";

export const selectCategoryList = (data:any) => {
    return getAction("/sys/safe/selectCategoryList",data);
}

export const createHealthCheck = (data:any) => {
    return postAction("/sys/safe/createHealthCheck",data);
}
// 改为post
export const checkHealth = (data:any) => {
    return postAction("/sys/safe/checkHealth",null, data);
}

export const selectHealthCheckResultByHistoryIdAndCategoryId = (data:any) => {
    return getAction("/sys/safe/selectHealthCheckResultByHistoryIdAndCategoryId",data);
}

export const updateHealthCheckState = (data:any) => {
    return postAction("/sys/safe/updateHealthCheckState",null,data);
}

export const statisticsHealthResult = (data:any) => {
    return getAction("/sys/safe/statisticsHealthResult",data);
}

export const selectHealthCheckResult = (data:any) => {
    return getAction("/sys/safe/selectHealthCheckResult",data);
}

export const selectHealthCheckHistoryList = (data:any) => {
    return getAction("/sys/safe/selectHealthCheckHistoryList",data);
}

export const downloadResult = (data:any) => {
    return downloadAction("/sys/safe/downloadResult",data);
}

export const selectHealthCheckSettingList = (data:any) => {
    return getAction("/sys/safe/selectHealthCheckSettingList",data);
}

export const saveSetings = (data:any,params:any) => {
    return postAction("/sys/safe/saveSetings",data,params);
}