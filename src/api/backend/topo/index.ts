import { getAction, postAction } from "@/api/backend/manage";

export const saveNode = (data:any) => {
    return postAction("/sys/syshost/save",data);
}

export const getSwitchNode = (data:any) => {
    return getAction("/sys/sysnetworkswitch/selectList",data);
}

export const getSwitchNodeInfo = (data:any) => {
    return getAction("/sys/sysnetworkswitch/info",data);
}

export const saveSwitchNode = (data:any) => {
    return postAction("/sys/sysnetworkswitch/save",data);
}

export const updateSwitchNode = (data:any) => {
    return postAction("/sys/sysnetworkswitch/update",data);
}

export const deleteSwitchNode = (data:any) => {
    return postAction("/sys/sysnetworkswitch/delete",data);
}

export const selectInterfaceNodeList = (data:any) => {
    return getAction("/sys/sysnetworkserverinterface/selectList",data);
}

export const getInterfaceNodeInfo = (data:any) => {
    return getAction("/sys/sysnetworkserverinterface/info",data);
}

export const saveInterfaceNode = (data:any) => {
    return postAction("/sys/sysnetworkserverinterface/save",data);
}

export const updateInterfaceNode = (data:any) => {
    return postAction("/sys/sysnetworkserverinterface/update",data);
}

export const deleteInterfaceNode = (data:any) => {
    return postAction("/sys/sysnetworkserverinterface/delete",data);
}

export const selectHostNodeList = (data:any) => {
    return getAction("/sys/sysnetworkhost/selectList",data);
}

export const getHostNodeInfo = (data:any) => {
    return getAction("/sys/sysnetworkhost/info",data);
}

export const saveHostNode = (data:any) => {
    return postAction("/sys/sysnetworkhost/save",data);
}

export const updateHostNode = (data:any) => {
    return postAction("/sys/sysnetworkhost/update",data);
}

export const deleteHostNode = (data:any) => {
    return postAction("/sys/sysnetworkhost/delete",data);
}

export const selectNetworktopology = (data:any) => {
    return getAction("/sys/sysnetworktopology/selectList",data);
}
export const saveNetworktopology = (data:any) => {
    return postAction("/sys/sysnetworktopology/save",data);
}

export const updateNetworktopology = (data:any) => {
    return postAction("/sys/sysnetworktopology/update",data);
}

export const delNetworktopology = (data:any) => {
    return postAction("/sys/sysnetworktopology/delete",data);
}

export const canvasNetworktopology = (data:any) => {
    return postAction("/sys/sysnetworktopology/networktopology",null,data);
}

export const networkTopologyHost = (data:any) => {
    return postAction("/sys/sysnetworktopology/networkTopologyHost",null,data);
}

export const networkTopologyTerminal = (data:any) => {
    return postAction("/sys/sysnetworktopology/networkTopologyTerminal",null,data);
}

export const getNetworkTerminalInfo = (data:any) => {
    return getAction("/sys/sysnetworkterminal/info",data);
}

export const getNetworkTerminalList = (data:any) => {
    return getAction("/sys/sysnetworkterminal/selectList",data);
}

export const saveNetworkTerminal = (data:any) => {
    return postAction("/sys/sysnetworkterminal/save",data);
}

export const updateNetworkTerminal = (data:any) => {
    return postAction("/sys/sysnetworkterminal/update",data);
}

export const delNetworkTerminal = (data:any) => {
    return postAction("/sys/sysnetworkterminal/delete",data);
}