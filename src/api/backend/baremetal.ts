import { getAction, postAction } from "@/api/backend/manage";

// GET /sys/sysbaremetalnode/queryPage
export const getBaremetalnode = (data:any) => {
  return getAction("/sys/sysbaremetalnode/queryPage",data);
}

export const saveBaremetalnode = (data: any) => {
  return postAction("/sys/sysbaremetalnode/save", data);
};

export const updateBaremetalnode = (data: any) => {
  return postAction("/sys/sysbaremetalnode/update", data);
};

// POST /sys/sysbaremetalnode/delete
// 数据库物理删除数据接口
export const deleteBaremetalnode= (data: any) => {
  return postAction("/sys/sysbaremetalnode/delete",data);
};

export const selectPortGroupList = (data: any) => {
  return getAction("/sys/sysbaremetalportgroup/selectList", data);
};

export const selectPortList = (data: any) => {
  return getAction("/sys/sysbaremetalport/selectList", data);
};

export const savePortGroup = (data: any) => {
  return postAction("/sys/sysbaremetalportgroup/save", data);
};

export const updatePortGroup = (data: any) => {
  return postAction("/sys/sysbaremetalportgroup/update", data);
};

export const deletePortG = (data: any) => {
  return postAction("/sys/sysbaremetalportgroup/delete",data);
};

export const savePort = (data: any) => {
  return postAction("/sys/sysbaremetalport/save", data);
};

export const updatePort = (data: any) => {
  return postAction("/sys/sysbaremetalport/update", data);
};

export const deletePort = (data: any) => {
  return postAction("/sys/sysbaremetalport/delete",data);
};

export const setMaintenance = (data: any) => {
  return postAction("/sys/sysbaremetalnode/setMaintenance?id="+data.id+'&reason='+data.reason);
};

export const deleteMaintenance = (data: any) => {
  return postAction("/sys/sysbaremetalnode/deleteMaintenance",data);
};

export const setBootDevice = (data: any) => {
  return postAction("/sys/sysbaremetalnode/setBootDevice?id="+data.id+'&bootDevice='+data.bootDevice+'&persistent='+data.persistent);
};

export const changeNodePower = (data: any) => {
  return postAction("/sys/sysbaremetalnode/changeNodePower?id="+data.id+'&target='+data.target);
};

export const setRaid = (data: any) => {
  return postAction("/sys/sysbaremetalnode/setRaid?id="+data.id,data.raidConfig);
};

export const changeNodeConsoleStatus = (data: any) => {
  return postAction("/sys/sysbaremetalnode/changeNodeConsoleStatus?id="+data.id+'&target='+data.target);
};
export const injectNMI = (data: any) => {
  return postAction("/sys/sysbaremetalnode/injectNMI",data);
};

export const validateNode = (data: any) => {
  return postAction("/sys/sysbaremetalnode/validateNode",data);
};

export const selectDriverList = (data: any) => {
  return getAction("/sys/sysbaremetaldriver/selectList", data);
};