import { getAction, postAction } from "@/api/backend/manage";
// 集群管理相关接口
export const getClusterList = (data: any) => {
  return getAction("/sys/syscluster/queryPage", data);
};

export const saveCluster = (data: any) => {
    return postAction("/sys/syscluster/save", data);
};

export const updateCluster = (data: any) => {
    return postAction("/sys/syscluster/update", data);
};

export const getClusterProfileList = (data: any) => {
    return getAction("/sys/sysclusterprofile/queryPage", data);
};
  
export const selectClusterProfileList = (data: any) => {
    return getAction("/sys/sysclusterprofile/selectList", data);
};
  
export const saveClusterProfile = (data: any) => {
    return postAction("/sys/sysclusterprofile/save", data);
};

export const updateClusterProfile = (data: any) => {
    return postAction("/sys/sysclusterprofile/update", data);
};

export const deleteClusterProfile = (data: any) => {
    return postAction("/sys/sysclusterprofile/delete",data);
  };

export const selectClusterReceiverList = (data: any) => {
    return getAction("/sys/sysclusterreceiver/selectList", data);
};

export const deleteCluster = (data: any) => {
    return postAction("/sys/syscluster/delete",data);
};

export const selectClusteNodeList = (data: any) => {
    return getAction("/sys/sysclusternode/selectList", data);
};

export const saveClusterNode = (data: any) => {
    return postAction("/sys/sysclusternode/save", data);
};

export const updateClusterNode = (data: any) => {
    return postAction("/sys/sysclusternode/update", data);
};

export const deleteClusterNode = (data: any) => {
    return postAction("/sys/sysclusternode/delete",data);
};

export const saveClusterReceiver = (data: any) => {
    return postAction("/sys/sysclusterreceiver/save", data);
};

export const updateClusterReceiver = (data: any) => {
    return postAction("/sys/sysclusterreceiver/update", data);
};

export const deleteClusterReceiver = (data: any) => {
    return postAction("/sys/sysclusterreceiver/delete",data);
};