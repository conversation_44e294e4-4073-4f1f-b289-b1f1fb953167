<template>
<div>
  <a-config-provider :locale="locale">
    <router-view />
  </a-config-provider>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "@vue/runtime-core";
import { selectSysconfig } from "./api/backend/public";
import { getSysconfigInfo } from "./api/backend/systems/sysconfig";
import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN';
import moment from 'moment';//引入moment 
import { Receive } from "./utils/iframe/receive";
moment.locale('zh-cn');//配置moment中文环境
const locale = ref(zhCN);//传值给a-config-provider组件
onMounted(()=>{
  Receive();
})
</script>

<style lang="scss">
* {
  margin: 0;
  padding: 0;
}
body {
  width: 100vw;
  height: 100vh;
}
::-webkit-scrollbar {
        height: 6px;
        width: 8px;
    }
    ::-webkit-scrollbar-thumb {
        background: #dadbdf;
        border-radius: var(--el-border-radius-base);
        border: none;
        box-shadow: none;
        -webkit-box-shadow: none;
    }
    ::-webkit-scrollbar-track {
      background-color: transparent;
    }
    :hover {
        &::-webkit-scrollbar-thumb:hover {
            background: #a9aaad;
        }
    }
    // .ant-modal{
    //   ::-webkit-scrollbar {
    //     height: 5px;
    //     width: 8px;
    // }
    // ::-webkit-scrollbar-thumb {
    //     background: rgba(24, 144, 255, 0.4);
    //     border-radius: var(--el-border-radius-base);
    //     border: none;
    //     box-shadow: none;
    //     -webkit-box-shadow: none;
    // }
    // ::-webkit-scrollbar-track {
    //   background-color: transparent;
    // }
    // :hover {
    //     &::-webkit-scrollbar-thumb:hover {
    //         background: rgba(24, 144, 255, 0.7);
    //     }
    // }
    // }
// ::-webkit-scrollbar {
//   width: 6px;
//   height: 6px;
// }
// ::-webkit-scrollbar-thumb {
//   background: #666;
//   border-bottom: 1px solid #333;
//   border-top: 1px solid #333;
// }
// ::-webkit-scrollbar-track {
//   background: #f0f2f5;
//   border-left: 0.5px solid #dedede;
// }
.bg {
  background: #000923;
  color: #fff;
  padding-top: 40px;
  overflow: auto;
  z-index: 1000;
  position: relative;
  height: calc(100vh - 66px);
}
.bgEchart {
  margin: -62px -16px 24px;
}
.dateClass {
  text-align: center;
  height: 46px;
  line-height: 46px;
  font-size: 20px;
  border-top: 2px solid rgba(56, 148, 255, 100);
}
.leftLine {
  width: 64px;
  border: 1px solid rgba(56, 148, 255, 100);
  transform: rotate(45deg);
  transform-origin: left top;
  float: left;
}
.titleClass {
  font-size: 28px;
  text-align: center;
  border-bottom: 2px solid rgba(56, 148, 255, 100);
  margin: 0 auto;
  width: calc(100% - 88px);
}
.rightLine {
  width: 64px;
  border: 1px solid rgba(56, 148, 255, 100);
  transform: rotate(-45deg);
  transform-origin: top right;
  position: absolute;
  top: 0;
  right: 0;
}
.tabClass {
  height: 46px;
  line-height: 46px;
  font-size: 20px;
  text-align: center;
  border-top: 2px solid rgba(56, 148, 255, 100);
}
.spanClass {
  cursor: pointer;
}
.spanClassactive {
  color: rgba(55, 125, 255, 100);
}
.blockBox1 {
  width: 80%;
  margin: 40px auto;
  margin-top: 70px;
  display: flex;
  justify-content: space-around;

  .box {
    border-radius: 5px;
    background-color: rgba(28, 50, 77, 100);
    border: 1px solid rgba(55, 125, 255, 100);
    font-size: 14px;
    color: #fff;
    display: inline-block;
    width: 220px;
    margin-left: 10px;
    margin-right: 10px;
    .boxTitle {
      color: rgba(55, 125, 255, 100);
      font-size: 20px;
      height: 45px;
      line-height: 45px;
      padding-left: 20px;
    }
    .boxNum {
      font-size: 18px;
      height: 45px;
      line-height: 45px;
      text-align: right;
      padding-right: 20px;
    }
  }
}
</style>
