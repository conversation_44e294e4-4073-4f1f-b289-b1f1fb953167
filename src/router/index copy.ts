import { createRouter, createWebHashHistory, createWebHistory, RouteRecordRaw } from "vue-router";
import Layout from "@/layout/backend/index.vue";
import Layout1 from "@/layout/frontend/index.vue";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/admin/login",
    name: "Login",
    component: () => import("@/views/backend/Login.vue"),
    meta: { title: "登录" }
  },
  {
    path: "/",
    // component: Layout1,
    meta: { title: "" },
    redirect: "/admin",
    children: [
      // {
      //   path: "/index",
      //   component: () => import("@/views/frontend/Index.vue"),
      //   meta: { title: "首页" }
      // }
    ]
  },
  {
    path: "/index",
    // component: () => import("@/views/frontend/Index.vue"),
    redirect: "/admin",
    // meta: { title: "首页" }
  },
  {
    path: "/admin",
    component: Layout,
    meta: { title: "" },
    redirect: "/admin/index",
    children: [
      {
        name: '首页',
        path: "/admin/index",
        component: () => import("@/views/backend/Index.vue"),
        meta: { title: "首页" },
      },
      {
        name: '子网管理',
        path: "/admin/devops/subnet",
        component: () => import("@/views/backend/devops/subnet/index.vue"),
        meta: { title: "子网管理" }
      },
      {
        name:'虚机申请',
        path: "/admin/tickets/my/ticketadd",
        component: () => import("@/views/backend/tickets/my/info.vue"),
        meta: { title: "虚机申请",keepAlive: true }
      },
      {
        name:'虚机延期',
        path: "/admin/tickets/my/hostdelay",
        component: () => import("@/views/backend/tickets/my/delay.vue"),
        meta: { title: "虚机延期",keepAlive: true }
      },
      {
        name:'虚机扩容',
        path: "/admin/tickets/my/hostplus",
        component: () => import("@/views/backend/tickets/my/delay.vue"),
        meta: { title: "虚机扩容",keepAlive: true }
      },
      {
        name: '查看工单',
        path: "/admin/ticketview",
        component: () => import("@/views/backend/tickets/ready/info.vue"),
        meta: { title: "查看工单" }
      },
      {
        name: '工单审批',
        path: "/admin/ticketedit",
        component: () => import("@/views/backend/tickets/ready/info.vue"),
        meta: { title: "工单审批" }
      },
      {
        name:'数据字典',
        path: "/admin/systems/dictionary",
        component: () => import("@/views/backend/systems/dictionary/index.vue"),
        meta: { title: "数据字典" }
      },
      {
        name: '个人中心',
        path: "/admin/systems/PersonalCenter",
        component: () => import("@/views/backend/systems/PersonalCenter/index.vue"),
        meta: { title: "个人中心" }
      },
      {
        name: '消息中心',
        path: "/admin/systems/MessageCenter",
        component: () => import("@/views/backend/systems/MessageCenter/index.vue"),
        meta: { title: "消息中心" }
      },
      {
        name:'云平台管理',
        path: "/admin/cloud",
        component: () => import("@/views/backend/cloud/index.vue"),
        // redirect: "/admin/cloud/index",
        meta: { title: "云平台管理" }
      },
      {
        name:'SQL监控',
        path: "/admin/systems/sql",
        component: () => import("@/views/backend/systems/sql/index.vue"),
        meta: { title: "SQL监控" }
      },
      {
        name:'404',
        path: "/404",
        component: () => import("@/views/errpages/404.vue"),
        meta: { title: "404" }
      },
      {
        name:'403',
        path: "/noPermission",
        component: () => import("@/views/errpages/noPermission.vue"),
        meta: { title: "403" }
      }
    ]
  },
  

  // 访问不存在路由不需要登录直接跳转404，先登录再跳转在permission.ts中
  //   {
  //     path: "/:catchAll(.*)",
  //     redirect: "/404",
  //     meta: { title: "" }
  //   },
  
  
];

const router = createRouter({
    history: createWebHistory(),
  routes
});
export default router;
