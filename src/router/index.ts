import { createRouter, createWebHashHistory, createWebHistory, RouteRecordRaw } from "vue-router";
import Layout from "@/layout/backend/index.vue";
import Layout2 from "@/layout/backend/bottom.vue";
const routes: Array<RouteRecordRaw> = [
  {
    path: "/admin/login",
    name: "Login",
    component: () => import("@/views/backend/Login.vue"),
    meta: { title: "登录" }
  },
  {
    path: "/demo",
    component: () => import("@/views/backend/devops/server/add.vue"),
    meta: { title: "Demo" }
  },
  {
    path: "/login-limit",
    component: () => import("@/views/backend/NotWhite.vue"),
    meta: { title: "Login Limit" }
  },
  {
    path: "/",
    name:'layout1',
    component: Layout,
    meta: { title: "" },
    redirect: "/admin/index",
    children: [
      {
        name: '首页',
        path: "/admin/index",
        component: () => import("@/views/backend/Index.vue"),
        meta: { title: "首页" },
      },
      {
        name: '个人中心',
        path: "/admin/systems/PersonalCenter",
        component: () => import("@/views/backend/systems/PersonalCenter/index.vue"),
        meta: { title: "个人中心" }
      },
      {
        name: '消息中心',
        path: "/admin/systems/MessageCenter",
        component: () => import("@/views/backend/systems/MessageCenter/index.vue"),
        meta: { title: "消息中心" }
      },
      {
        name: '消息详情',
        path: "/admin/systems/MessageDetails",
        component: () => import("@/views/backend/systems/MessageDetails/index.vue"),
        meta: { title: "消息详情" }
      },
        {
          name:'layout',
            path: "/admin",
            component: Layout2,
            meta: { title: "" },
            children: [
              // {
              //   name: '流程管理',
              //   path: "/admin/flow",
              //   component: () => import("@/views/backend/systems/flow/index.vue"),
              //   meta: { title: "流程管理" },
              // },
              // {
              //   name: '子网管理',
              //   path: "/admin/devops/subnet",
              //   component: () => import("@/views/backend/devops/subnet/index.vue"),
              //   meta: { title: "子网管理" }
              // },
              {
                name: '回收站',
                path: "/admin/devops/recyclery",
                component: () => import("@/views/backend/others/recyclery/recyclery.vue"),
                meta: { title: "回收站" }
              },
              {
                name:'虚机申请',
                path: "/admin/tickets/my/ticketadd",
                component: () => import("@/views/backend/tickets/my/info.vue"),
                meta: { title: "虚机申请" }
              },
              {
                name:'虚机延期',
                path: "/admin/tickets/my/hostdelay",
                component: () => import("@/views/backend/tickets/my/delay.vue"),
                meta: { title: "虚机延期" }
              },
              {
                name:'虚机扩容',
                path: "/admin/tickets/my/hostplus",
                component: () => import("@/views/backend/tickets/my/plus.vue"),
                meta: { title: "虚机扩容" }
              },
              {
                name: '查看工单',
                path: "/admin/ticketview",
                component: () => import("@/views/backend/tickets/ready/info_index.vue"),
                meta: { title: "查看工单" }
              },
              {
                name: '工单审批',
                path: "/admin/ticketedit",
                component: () => import("@/views/backend/tickets/ready/info_index.vue"),
                meta: { title: "工单审批" }
              },
              // {
              //   name:'存储管理',
              //   path: "/admin/database",
              //   component: () => import("@/views/backend/database/index.vue"),
              //   meta: { title: "存储管理" }
              // },
              // {
              //   name:'数据字典',
              //   path: "/admin/systems/dictionary",
              //   component: () => import("@/views/backend/systems/dictionary/index.vue"),
              //   meta: { title: "数据字典" }
              // },
              {
                name: "配电网云拓扑 ",
                path: "/admin/networktopology/:id",
                component: () => import("@/views/backend/networktopology/database/index.vue")
              },
              
              
              // {
              //   name:'云平台管理',
              //   path: "/admin/cloud",
              //   component: () => import("@/views/backend/cloud/index.vue"),
              //   // redirect: "/admin/cloud/index",
              //   meta: { title: "云平台管理" }
              // },
              // {
              //   name:'云平台报表',
              //   path: "/admin/report",
              //   component: () => import("@/views/backend/report/index.vue"),
              //   // redirect: "/admin/cloud/index",
              //   meta: { title: "云平台报表" }
              // },
              {
                name:'SQL监控',
                path: "/admin/systems/sql",
                component: () => import("@/views/backend/systems/sql/index.vue"),
                meta: { title: "SQL监控" }
              },
              {
                name: '项目首页',
                path: "/admin/devops/menu/project",
                component: ()=>import("@/views/backend/devops/menu/project.vue"),
                meta: { title: "项目首页" }
              },
              {
                name: '域首页',
                path: "/admin/devops/menu/domain",
                component: ()=>import("@/views/backend/devops/menu/domain.vue"),
                meta: { title: "域首页" }
              },
              {
                name: 'OPENSTACK首页',
                path: "/admin/devops/menu/cloud",
                component: ()=>import("@/views/backend/devops/menu/cloud.vue"),
                meta: { title: "OPENSTACK首页" }
              },
              {
                name: '主机',
                path: "/admin/vmware/host",
                component: ()=>import("@/views/backend/vmware/host/index.vue"),
                meta: { title: "主机"}
              },
              {
                name:'404',
                path: "/404",
                component: () => import("@/views/errpages/404.vue"),
                meta: { title: "404" }
              },
              {
                name:'403',
                path: "/noPermission",
                component: () => import("@/views/errpages/noPermission.vue"),
                meta: { title: "403" }
              }
            ]
          },
    ]
  },
];

const router = createRouter({
    history: createWebHistory(),
  routes
});
export default router;
