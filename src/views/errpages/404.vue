<template>
  <div class='contentPadding notfound'>
      <img src="../../assets/404.gif" alt="">
  </div>
</template>
<script lang="ts">
export default {
  name: '',
  props: {},
  data() {
    return {};
  },
  created(){
  },
  computed: {},
  watch: {},
  methods: {},
  components: {},
  filters: {}
};
</script>
<style lang='scss' scoped>
.notfound{
    display: flex;
    justify-content: center;
    align-items: center;
//     width: 100px;
//     height: 100px;
}
</style>