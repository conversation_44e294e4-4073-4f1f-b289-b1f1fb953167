<template>
  <div class="messageBox">
    <img src="../../assets/noPermission.png" />
    <div>{{ time }}秒后将退出</div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { removeToken, removeUsername, removeUserId } from "@/utils/auth";
import { delAllTokenForCookie } from "@/utils/tool";
import router from "@/router/index";
import { userStore } from "@/store/user"
const user_store = userStore()
const time = ref(3);
const logout = () => {
  user_store.Logout({})
    .then((res:any) => {
      if (res.code === 0) {
        console.log("noPerClear")
        localStorage.clear();
        removeToken();
        removeUsername();
        removeUserId();
        delAllTokenForCookie();
        // 判断是否使用cas单点登录
        if (import.meta.env.VITE_CAS === "cas") {
          let beforeUrl = document.location.href;
          if (beforeUrl.indexOf("/noPermission") !== -1) {
            beforeUrl = beforeUrl.substring(0, beforeUrl.indexOf("/noPermission"));
          }
          const url = res.data + "?url=" + beforeUrl;
          window.location.href = url;
        } else {
          router.push("/admin/login");
        }
      }
    })
};
setInterval(() => {
  time.value = time.value - 1;
}, 1000);
setTimeout(() => {
  logout();
}, 3000);
</script>
<style lang="scss" scoped>
.messageBox {
  text-align: center;
  position: relative;
  img {
    width: 600px;
    margin-top: 5%;
  }
  div {
    position: absolute;
    bottom: 120px;
    color: #6793ef;
    font-size: 18px;
    left: calc(100% - 50% - 60px);
    font-weight: 500;
  }
}
</style>
