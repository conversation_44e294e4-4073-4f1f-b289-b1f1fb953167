.home{
    .header{position: fixed;top: 0;right: 6px;width: 100%;padding: 30px 160px;background: url(../../assets/beijing.png) no-repeat 6px 0;z-index:999;} 
    .logo{height: 36px;width: 175px;background: url(../../assets/logo.png);}}
.prods{
    min-width: 820px;
    width: 820px;
    // height: 850px;
    display: flex;
    // flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    .item{
        width: 378px;
        height: 190px;
        border-radius: 10px;
        // border: 1px dashed #888;
        margin: 0 15px 30px 15px;
        color: #fff;
        // text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        // line-height: 48px;
        padding: 32px 0;
        p{
            font-size: 20px;
            cursor: default;
        }
        span{
            font-size: 24px;
            cursor: default;
        }
        .ant-btn-round{
            font-size: 16px;
            background-color: #003296;
            color: #fff;
            letter-spacing: 1px;
        }
        &:nth-child(7n-6){
            background: url(../../assets/youyue.png);
        }
        &:nth-child(7n-5){
            background: url(../../assets/youyi.png);
        }
        &:nth-child(7n-4){
            background: url(../../assets/youbei.png);
        }
        &:nth-child(7n-3){
            background: url(../../assets/youwo.png);
        }
        &:nth-child(7n-2){
            background: url(../../assets/youyan.png);
        }
        &:nth-child(7n-1){
            background: url(../../assets/youque.png);
        }
        &:nth-child(7n){
            background: url(../../assets/youzhan.png);
        }
    }
}
.video{
    position: fixed;
    top: 214px;
    left: 960px;
    min-width: 915px;
    height: 514px;
    // margin-top: 129px;
    box-shadow: 5px 20px 70px 0px rgba(21,36,136,0.8100);
}
.main{
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin: 140px 28px 88px 76px;
}