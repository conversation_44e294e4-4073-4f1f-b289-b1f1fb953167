<template>
    <div class=''>
        <a-card title="我的虚机" :headStyle="{backgroundColor:'#fafafa'}" :bodyStyle="{padding:'20px 34px',position:'relative',height:'319px'}">
            <template #extra>
                <a-tooltip title="申请"><plus-circle-filled @click.stop="()=>{router.push('/admin/tickets/my/ticketadd');indexStore().addTag({menuId:'虚机申请',name:'虚机申请',url:'ticketadd'})}" style="color:rgb(84, 112, 198);font-size:20px" /></a-tooltip>
            </template>
            <swiper v-if="serverlist && serverlist.length > 0" :slides-per-view="showNum" navigation @resize="onResize">
                <swiper-slide v-for="(item,index) in serverlist" :key="index">
                    <a-card bordered hoverable :headStyle="{backgroundColor:'#5470C6',color:'#fff'}" size="small">
                        <template #title>
                            <a-badge dot :number-style="{ backgroundColor: item.status == 'active' ? '#52c41a' : '' }" >
                                        <DesktopOutlined style="color:#fff;font-size:18px" />
                            </a-badge>
                            <span>&nbsp;&nbsp;{{item.serverName}}</span> 
                            </template>
                            <div style="padding:12px 12px 4px 12px;border-bottom:1px solid #fafafa">
                                <a-form :labelCol="{span:6}" :wrapperCol="{offset:0}">
                                <a-form-item label="云平台">{{item.cloudName}}</a-form-item>
                                <a-form-item label="基础配置">CPU {{item.flavorEntity?.vcpus?item.flavorEntity.vcpus : 0}}核 - 内存 {{item.flavorEntity?.ram?item.flavorEntity.ram/1024 : 0}}GB - 磁盘 {{item.flavorEntity?.totalDisk?item.flavorEntity.totalDisk:0}}GB</a-form-item>
                                <a-form-item label="IP">
                                    <span v-for="(item,index) in item.serverAddressEntityList" :key="index" v-if="item.serverAddressEntityList?.length > 0">{{index == 0 ? item.addr : '/'+item.addr}}</span>
                                    <span v-else> 暂未配置</span>
                                </a-form-item>
                                <a-form-item label="到期时间">{{item.endTime?item.endTime.substr(0,10):'永久'}}</a-form-item>
                                <a-form-item label="虚机状态">{{item.statusText}}</a-form-item>
                            </a-form>
                            </div>
                            <a-row style="font-size:18px;justify-content:space-around;color:rgb(84, 112, 198);padding:12px 0">
                                <div></div>
                                <a-tooltip title="登录"><login-outlined @click.stop="getConsoleURL(item.id)" /></a-tooltip>
                                <a-tooltip title="关机" v-if="item.status != 'shutoff'"><poweroff-outlined style="color:#f81d22" @click="setOptions(item.id, 'STOP','关机',item)" /></a-tooltip>
                                <a-tooltip title="开机" v-if="item.status != 'active'"><poweroff-outlined @click="setOptions(item.id, 'START','开机')" /></a-tooltip>
                                <a-tooltip title="删除"><close-circle-outlined @click="setOptions(item.id, 'FORCEDELETE','删除')" /></a-tooltip>
                                <a-tooltip title="重启"><reload-outlined @click="setOptions(item.id, 'REBOOT','重启')" /></a-tooltip>
                                <a-tooltip title="延期"><history-outlined @click="setOptions(item.id, 'delay','延期')" /></a-tooltip>
                                <a-tooltip title="扩容"><save-outlined @click="setOptions(item.id, 'plus','扩容')" /></a-tooltip>
                                <div></div>
                            </a-row>
                    </a-card>
                </swiper-slide>
            </swiper>
            <div class="host-empty" v-else-if="serverlist && serverlist.length <= 0">
                <img src="@/assets/empty.png" alt="">
                <div class="empty-right">
                    <a-page-header title="您还没有云服务器虚机" sub-title="仅需3步，即可拥有一台虚机，开始搭建您的云上业务！"></a-page-header>
                    <a-steps current="0">
                        <a-step title="创建虚机申请工单"/>
                        <a-step title="工单审批创建虚机" />
                        <a-step title="开始搭建业务" />
                    </a-steps><br>
                    <a-button type="primary"  @click="()=>{router.push('/admin/tickets/my/ticketadd');indexStore().addTag({menuId:'虚机申请',name:'虚机申请',url:'ticketadd'})}">申请我的虚机</a-button>
                </div>
            </div>
        </a-card>
        
        <div class="group4">
            <div class="tickets">
                <a-tabs type="card" v-model:activeKey="activeKey">
                    <template #tabBarExtraContent>
                        <router-link @click="toMore" :to="activeKey=='1'?'/admin/tickets/ready':'/admin/tickets/my'">更多</router-link>
                    </template>
                    <a-tab-pane key="1" tab="待办工单">
                        <Ready :isList="false" />
                    </a-tab-pane>
                    <a-tab-pane key="2" tab="我的工单">
                        <My :isList="false" />
                    </a-tab-pane>
                </a-tabs>
            </div>
            <div class="group4-desc">
                <a-card :bordered="false">
                <a-card-grid><p>全部虚机</p><span class="number">{{serverInfo.all}}</span></a-card-grid>
                <a-card-grid><p>活跃虚机</p><span class="number">{{serverInfo.active}}</span></a-card-grid>
                <a-card-grid><p>即将到期虚机</p><span class="number">{{serverInfo.upExpire}}</span></a-card-grid>
                <a-card-grid @click="()=>{router.push({path:'/admin/systems/MessageCenter',query:{noticeType:1}})}"><p></p><p>预警数量</p><span class="number">{{warningInfo.all?warningInfo.all:'0'}}</span></a-card-grid>
                <a-card-grid @click="()=>{router.push({path:'/admin/systems/MessageCenter',query:{noticeType:1,transactState:1}})}"><p></p><p>已处理预警</p><span class="number">{{warningInfo.done?warningInfo.done:'0'}}</span></a-card-grid>
                <a-card-grid @click="()=>{router.push({path:'/admin/systems/MessageCenter',query:{noticeType:1,transactState:0}})}"><p></p><p>未处理预警</p><span class="number">{{warningInfo.undone?warningInfo.undone:'0'}}</span></a-card-grid>
                </a-card>
            </div>
        </div>
    </div>
</template>
<script lang='ts' setup>

import SwiperCore, { Navigation, Pagination } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/scss';
  import 'swiper/scss/navigation';
  import 'swiper/scss/pagination';
import Ready from "../tickets/ready/index.vue"
import My from "../tickets/my/index.vue"
import { computed, createVNode, getCurrentInstance, h, onMounted, reactive, ref } from 'vue';
import {getServerCount, getWarningCount, selectIndexCloudList, selectIndexServerList} from "@/api/backend/index";
import { getConsoleUrl, actionServer, rebootServer, deleteServer } from '@/api/backend/devops/server'
import { deleteRecycle } from "@/api/backend/devops/recycle";
import { message, Modal, Checkbox } from 'ant-design-vue'
import router from '@/router';
import {indexStore} from "@/store/index";
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { menuStore } from '@/store/menu';
const {proxy} = getCurrentInstance()
SwiperCore.use([Navigation, Pagination])

const activeKey = ref('1')

const serverlist = ref(undefined)
const isPlus = ref(false)
const serverInfo = reactive({})
const warningInfo = ref({})
const options = [
    {"value":"STOP", "label":"关闭", "disabled":false, "status":"stopped","icon":`<poweroff-outlined />`},
    {"value":"FORCEDELETE", "label":"删除", "disabled":false,"status":"","icon":`<close-circle-outlined />`},
    {"value":"REBOOT", "label":"重启", "disabled":false,"status":"","icon":`<reload-outlined />`},
    // {"value":"START", "label":"启动", "disabled":false, "status":"active"},
    // {"value":"SUSPEND", "label":"挂起", "disabled":false, "status":"suspended"},
    // {"value":"PAUSE", "label":"暂停", "disabled":false, "status":"paused"},
    {"value":"delay", "label":"延期", "disabled":false,"status":"","icon":`<history-outlined />`},
    {"value":"plus", "label":"扩容", "disabled":false,"status":"","icon":`<save-outlined />`},
]
const showNum = ref(4)
const isImmediate = ref(false);
const deleteApi = computed(()=>isImmediate.value ? deleteRecycle : deleteServer);
const changeCheck = (e) => {
    isImmediate.value = e.target.checked;
}
const onResize = (e) => {
  if(e.size >= 1373)
  showNum.value = 4
  else if(e.size >= 1029 && e.size < 1373)
  showNum.value = 3
  else if(e.size >= 686 && e.size < 1029)
  showNum.value = 2
  else
  showNum.value = 1
//   console.log('resize',e)
}
const setOptions = async (serverID, action,label,record) => {
    if(action == 'FORCEDELETE'){
        // proxy.$handleDel([serverID],deleteServer,getServerList)
        proxy.$handleDel([serverID],()=>deleteApi.value,()=>{getServerList();isImmediate.value = false;},
            ()=>h('div',[
                    h('p','确认要删除虚机？'),
                    h('p','当计算服务开启回收实例间隔时，删除后云主机会存放在回收站，按对应的时间间隔保留，在此期限内可以选择恢复。恢复成功后的云主机状态为运行中，且相关资源保持不变。'),
                    h(Checkbox,{checked:isImmediate.value,onChange:changeCheck},'立即删除')
                ]
            ),
            null,null,()=>{isImmediate.value = false;}
        )
    }else if(action == 'delay'){
        menuStore().setMenuId('tickets')
        proxy.$mitt.emit('setLeftMenu',true)
        router.push({path:'/admin/tickets/my/hostdelay',query:{param:1,serverID}})
        indexStore().addTag({menuId:'虚机延期',name:'虚机延期',url:'hostdelay',query:{param:1}})
    }else if(action == 'plus'){
        menuStore().setMenuId('tickets')
        proxy.$mitt.emit('setLeftMenu',true)
        router.push({path:'/admin/tickets/my/hostplus',query:{param:2,serverID}})
        indexStore().addTag({menuId:'虚机扩容',name:'虚机扩容',url:'hostplus',query:{param:2}})
    }else if(action == 'REBOOT'){
        handleReboot(serverID)
    }else if(action == 'STOP'){
        Modal.confirm({
            // title: 'Confirm',
            icon: createVNode(ExclamationCircleOutlined),
            content: `确认关闭虚机（虚机名称：${record.serverName})？`,
            okText: '提交',
            cancelText: '取消',
            maskClosable: true,
            getContainer:proxy.modalBindNode,
            async onOk(){
                let res = await actionServer({serverID:[serverID],action})
                if(res.code==0 && res.data== true){
                    message.success(label+"成功")
                    getServerList();
                }else if(res.code == 0){
                    message.warning(label+"失败")
                }
            }
        });
    }else{
        let res = await actionServer({serverID,action});
        if(res.code==0 && res.data== true){
          message.success(label+"成功");
          getServerList();
        }else if(res.code == 0){
            message.warning(label+"失败");
        }
    }
}
const toMore = () => {
    menuStore().setMenuId('tickets')
    menuStore().setShowLeftMenu(true)
    proxy.$mitt.emit('setLeftMenu',true)
}
const handleReboot = async (serverID) => {
    let res = await rebootServer({serverID:[serverID]})
    if(res.code == 0 && res.data !== false){
        message.success('重启成功')
        getServerList();
    }else{
        message.error((!res.msg || res.msg == 'success') ? '重启失败' : res.msg);
    }
}
const getwarningCount = async () => {
    let res = await getWarningCount({noticeType:"1"})
    if(res.code == 0){
        warningInfo.value=res.data
       
    }
}
const getConsoleURL = async (id) => {
    let res = await getConsoleUrl(id);
    if(res){
        if(res.code == 0 && res.data){
            window.open(res.data)
        }else if(res.code == 0){
            message.warning(res.msg)
        }
    }
}
const getServerList = async () => {
    let res = await selectIndexServerList()
    if(res.code == 0){
        isPlus.value = true;
        serverlist.value = res.data;
    }
}
const getserverCount = async () => {
    let res = await getServerCount()
    if(res.code == 0){
        Object.assign(serverInfo,res.data);
    }
}
onMounted(() => {getServerList();getserverCount();getwarningCount();})
</script>
<style lang='scss' scoped>
.group4{
  display: flex;
  margin: 16px 0;
  justify-content: space-between;
}
.group4-desc{width: 49%;height: 100%;padding: 27px 20px;background-color: #fff;
:deep(.ant-card){
  color: #fff;
}
.number{
    font-weight: bold;
    font-size: 30px;
  }
  }
.tickets{
    background-color: #fff;
    padding: 20px;
    width: 50%;
    height: 426px;
    overflow-y: hidden;
}
:deep(.ant-card-grid){
    height: 186px;
  cursor: pointer;
  &:nth-child(3n+1){
    background-color: #73C0DE;
  }
  &:nth-child(3n){
    background-color: #EE6666;
  }
  &:nth-child(3n+2){
    background-color: #91CC75;
  }
}
.host-empty{padding-left: 10px;display: flex;img{flex: 1;}}
.empty-right{margin-left: 40px;flex: 6;}
.plus-card2{.ant-btn{height:287px;}}
:deep(.ant-card-extra){color: #fff;}
.ant-form-item{margin-bottom: 4px;}
.ant-steps{width:80%}
:deep(.ant-card-actions > li){margin: 0;}
:deep(.ant-card-actions > li > span > .anticon){line-height: 16px;}
:deep(.swiper-wrapper){.ant-card-body{padding: 0;}}
</style>