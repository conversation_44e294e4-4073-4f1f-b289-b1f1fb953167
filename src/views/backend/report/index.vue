<template>
    <div class='contentPadding report'>
        <a-page-header class="header-title">
            <template #extra>
                <a-tooltip title="设置"><SettingOutlined @click="handleSetting"/></a-tooltip>
            </template>
        </a-page-header>
        <a-divider></a-divider>
        <a-page-header title="虚拟化资源使用率">
          <br>
            <a-table class="table-first" rowClassName="first-row" :columns="columns1" :data-source="data1" row-key="id" @expand="onExpand" :pagination="false" :loading="loading" bordered>
                <template #usage="{record}">
                    {{`${filterNull(record.vcpuUsed)}/${filterMemory(record.ramUsed)}/${filterGB(record.diskUsed)}`}}
                </template>
                <template #time="{record}">
                    {{((new Date(moment().utcOffset(480).format('YYYY-MM-DD HH:mm:ss')).getTime() - new Date(record.createTime).getTime()) / (60 * 60 * 24 * 1000)).toFixed() + '天'}}
                </template>
                <template #expandedRowRender="{ record,index }">
                  <div>
                    <a-table class="table-expand" :columns="columns2" :data-source="data[index+2]" :pagination="pagination['pagination'+index+1]" @change="(e)=>{changeTable(index, e)}" size="small" :bordered="true">
                        <template #serverName="{record}">
                            <a @click="handleMonitor(record)">{{record.serverName}}</a>
                        </template>
                        <template #vspec="{record}">
                            {{`${filterNull(record.vcpus)}/${filterMemory(record.memory)}/${filterGB(record.disk)}`}}
                        </template>
                        <template #maxCpu="{record}">
                            {{record.maxCpu + '%'}}
                        </template>
                        <template #avgCpu="{record}">
                            {{record.avgCpu + '%'}}
                        </template>
                        <template #maxMemory="{record}">
                            {{record.maxMemory + '%'}}
                        </template>
                        <template #avgMemory="{record}">
                            {{record.avgMemory + '%'}}
                        </template>
                    </a-table>
                  </div>
                </template>
            </a-table>
        </a-page-header>
        <br><br>
        <a-divider></a-divider>
        <a-page-header title="硬件资源使用率">
          <br>
            <a-table :columns="columns3" :data-source="vmlist" row-key="id" :scroll="{y:400,x:1200}" :pagination="false" bordered>
                <template #total="{record}">
                    {{record.cpuinfo.arch}} {{filterNull(record.vcpus)}}/{{filterMemory(record.memory_mb)}}/{{filterGB(record.local_gb)}}
                </template>
                <template #cpu_usage="{record}">
                    {{filterNull(record.vcpus_used)}}/{{filterNull(record.vcpus)}}
                </template>
                <template #memory_usage="{record}">
                    {{filterMemory(record.memory_mb_used)}}/{{filterMemory(record.memory_mb)}}
                </template>
                <template #storage_usage="{record}">
                    {{filterGB(record.local_gb_used)}}/{{filterGB(record.local_gb)}}
                </template>
            </a-table>
        </a-page-header>
        <br><br>
        <a-divider></a-divider>
        <a-page-header title="使用情况分析">
          <br>
            <div class="chart_area">
                <div class="chart_card">
                    <a-card :body-style="{padding:0}" :bordered="false">
                        <a-select
                         v-model:value="currentCloud1" 
                         @change="getHardwarePie" 
                         size="small" 
                         :options="cloudlist" 
                         :bordered="false"
                         :getPopupContainer="triggerNode => triggerNode.parentNode"
                         ></a-select>
                        <div v-show="!chartEmpty" class="echarts" id="chart1" ref="chart1"></div>
                    </a-card>
                    <a-card v-if="chartEmpty" title="项目占用硬件资源使用率" size="small" class="echarts progress" :bordered="false">
                      <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                    </a-card>
                </div>
                <div class="chart_card">
                    <a-card :body-style="{padding:0}" :bordered="false">
                        <a-select
                         v-model:value="currentCloud" 
                         @change="getDiviceNodeUsage" 
                         size="small" 
                         :options="cloudlist" 
                         :bordered="false"
                         :getPopupContainer="triggerNode => triggerNode.parentNode"
                         ></a-select>
                        <div v-show="!chartEmpty" class="echarts" id="chart2" ref="chart2"></div>
                    </a-card>
                    <a-card v-if="chartEmpty" title="节点使用率" size="small" class="echarts progress" :bordered="false">
                      <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                    </a-card>
                </div>
                <!-- <div class="chart_card">
                    <div v-show="!chartEmpty" class="echarts" id="chart3" ref="chart3"></div>
                    <a-card v-if="chartEmpty" title="虚拟资源使用人TOP排行" size="small" class="echarts progress" :bordered="false">
                      <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                    </a-card>
                </div> -->
            </div>
        </a-page-header>
        <br>
        <Monitor ref="monitorRef" :monitorProps="monitorProps"/>
        <Setting ref="setRef" :setProps="setProps" />
    </div>
</template>
<script lang='ts' setup>
import { Empty } from 'ant-design-vue';
import { nextTick, onMounted, reactive, ref } from 'vue';
import Monitor from "./monitor.vue";
import Setting from "./setting.vue";
import * as echarts from "echarts";
import { getVisualList } from '@/api/backend/devops/poly';
import { statisticsComputeByProject, statisticsProjectByCloudId, statisticsProject } from '@/api/backend/report';
import { getRanklist, selectIndexCloudList } from '@/api/backend';
import moment from 'moment';
import { useRoute } from 'vue-router';
import { queryWorker } from '@/api/backend/systems/user';
import { Options } from '@/common/chartoption';
const route = useRoute();
const expandedRowKeys = ref([])
const loading = ref(false)
const searchform = reactive({
    searchform1:{pageIndex:1,pageSize:10},
    searchform2:{pageIndex:1,pageSize:10},
    searchform3:{pageIndex:1,pageSize:10},
})
const pagination = reactive({
    pagination1:{
        // 分页配置器
        pageSize: 10, // 一页的数据限制
        current: 1, // 当前页
        total: 0, // 总数
        hideOnSinglePage: false, // 只有一页时是否隐藏分页器
        showQuickJumper: true, // 是否可以快速跳转至某页
        showSizeChanger: true, // 是否可以改变 pageSize
        pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
        showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
    },
    pagination2:{
        // 分页配置器
        pageSize: 10, // 一页的数据限制
        current: 1, // 当前页
        total: 0, // 总数
        hideOnSinglePage: false, // 只有一页时是否隐藏分页器
        showQuickJumper: true, // 是否可以快速跳转至某页
        showSizeChanger: true, // 是否可以改变 pageSize
        pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
        showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
    },
    pagination3:{
        // 分页配置器
        pageSize: 10, // 一页的数据限制
        current: 1, // 当前页
        total: 0, // 总数
        hideOnSinglePage: false, // 只有一页时是否隐藏分页器
        showQuickJumper: true, // 是否可以快速跳转至某页
        showSizeChanger: true, // 是否可以改变 pageSize
        pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
        showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
    },
})
const changeTable = (index, {pagination, filters, sorter}) => {
    searchform['searchform'+index+1].pageIndex = pagination['pagination'+index+1].current;
    searchform['searchform'+index+1].pageSize = pagination['pagination'+index+1].pageSize;
    getList();
}
const filterNull = (data) => {
    if(!data && data !== 0){
        return '-';
    }else{
        return data + 'C';
    }
}
const filterMemory = (data) => {
    if(!data && data !== 0){
        return '-'
    }else{
        if(data < 1024)
        return data + 'MB';
    if(data >= 1024 && data < 1024 * 1024)
        return (data / 1024).toFixed(1) + 'GB';
    if(data >= 1024 * 1024)
        return (data / (1024 * 1024)).toFixed(2) + 'TB';
    }
}
const filterGB = (data) => {
    if(!data && data !== 0){
        return '-'
    }else{
        if(data < 1024)
            return data + 'GB';
        if(data >= 1024)
            return (data / 1024).toFixed(2) + 'TB';
    }
}
const columns1 = [
    {
        title: '云平台',
        dataIndex: 'cloudName',
        key: 'cloudName',
    },
  {
    title: '项目',
    dataIndex: 'projectName',
    key: 'projectName',
  },
  {
    title: '虚拟机数量',
    dataIndex: 'serverNum',
    key: 'serverNum',
  },
  {
    title: '资源使用情况',
    dataIndex: 'usage',
    key: 'usage',
    slots:{customRender:"usage"}
  },
  {
    title: '运行时间',
    dataIndex: 'time',
    key: 'time',
    slots:{customRender:"time"}
  },
];
const columns2 = [
  {
    title: '虚拟机名称',
    dataIndex: 'serverName',
    key: 'serverName',
    slots:{customRender:'serverName'}
  },
  {
    title: '虚拟机规格',
    dataIndex: 'vspec',
    key: 'vspec',
    slots:{customRender:'vspec'}
  },
  {
    title: 'CPU最高使用率',
    dataIndex: 'maxCpu',
    key: 'maxCpu',
    slots:{customRender:'maxCpu'}
  },
  {
    title: 'CPU平均使用率',
    dataIndex: 'avgCpu',
    key: 'avgCpu',
    slots:{customRender:'avgCpu'}
  },
  {
    title: '内存最高使用率',
    dataIndex: 'maxMemory',
    key: 'maxMemory',
    slots:{customRender:'maxMemory'}
  },
  {
    title: '内存平均使用率',
    dataIndex: 'avgMemory',
    key: 'avgMemory',
    slots:{customRender:'avgMemory'}
  },
//   {
//     title: '数据使用率',
//     dataIndex: 'data_usage',
//     key: 'data_usage',
//   },
  {
    title: '使用时间',
    dataIndex: 'startTime',
    key: 'startTime',
  },
  {
    title: '所属人',
    dataIndex: 'ownerName',
    key: 'ownerName',
  },
];
const preIndex = ref(-1);
const columns3 = [
    {
        title: '云平台',
        dataIndex: 'cloudName',
        key: 'cloudName',
        customRender: ({ text, record, index }) => {
            const obj = {
                children: text,
                props: {} as any,
            };
            if(!cloudObj[record.cloudId].yIndexs.includes(index))
                cloudObj[record.cloudId].yIndexs.push(index);
            if(Math.min(...(cloudObj[record.cloudId].yIndexs)) == index)
                obj.props.rowSpan = cloudObj[record.cloudId].yAxis.length;
            else
                obj.props.colSpan = 0;
            return obj;
        },
    },
    {
        title: '设备名称',
        dataIndex: 'hypervisor_hostname',
        key: 'hypervisor_hostname',
        // width:100
    },
    {
        title: '设备资源情况',
        key: 'total',
        slots:{customRender:'total'},
        // width:200
    },
    {
        // title: 'cpu最高/平均使用情况',
        title: 'cpu使用情况',
        dataIndex: 'cpu_usage',
        key: 'cpu_usage',
        slots:{customRender:'cpu_usage'},
    },
    {
        // title: '内存最高/平均使用情况',
        title: '内存使用情况',
        dataIndex: 'memory_usage',
        key: 'memory_usage',
        slots:{customRender:'memory_usage'},
    },
    {
        title: '存储使用情况',
        dataIndex: 'storage_usage',
        key: 'storage_usage',
        slots:{customRender:'storage_usage'}
    },
    {
        title: '虚拟机个数',
        dataIndex: 'running_vms',
        key: 'running_vms',
    },
]
const data = reactive({2:[],3:[],4:[]})
const data1 = ref([]);

const vmlist = ref([])
const cloudlist = ref([])
const currentCloud = ref('')
const currentCloud1 = ref('')
const cloudObj = reactive({})
const cloudObj1 = reactive({})
const monitorRef = ref()
const monitorProps = reactive({
    visible:false,
    name:''
})
const visible = ref(false);
const value3 = ref([]);
const time = ref([]);
const setRef = ref()
const setProps = reactive({
    visible:false
})
const handleSetting = (isVisible) => {
    setProps.visible = true
    nextTick(()=>{
        setRef.value.setInfo()
    })
}
const handleMonitor = (record) => {
    monitorProps.visible = true;
    monitorProps.name = record.vname;
    nextTick(()=>{
        monitorRef.value.initChart(record.serverId);
    })
}

const getThisList = async (api,clouditem,indexx) => {
    let res = await api({cloudId:clouditem.cloudId})
    if(res.code == 0){
        cloudObj[clouditem.cloudId].yAxis = [];
        cloudObj[clouditem.cloudId].series = [];
        let yAxis = [];
        let series = [{
            name: 'vcpus',
            reskey: 'vcpus',
            type: 'bar',
            data: []
            },
            {
            name: '内存',
            reskey: 'memory_mb',
            type: 'bar',
            data: []
            },
            {
            name: '存储',
            reskey: 'local_gb',
            type: 'bar',
            data: []
            }];
        res.data.forEach((item,index)=>{
            item.cloudId = clouditem.cloudId;
            item.cloudName = clouditem.cloudName;
            series.forEach((t,i)=>{
                if(item[t.reskey])
                    t.data.push(Number((item[t.reskey+'_used']*100/item[t.reskey]).toFixed()))
            })
            yAxis.push(item.hypervisor_hostname);
        })
        cloudObj[clouditem.cloudId].yIndexs = [];
        cloudObj[clouditem.cloudId].yAxis = yAxis;
        cloudObj[clouditem.cloudId].series = series;
        if(indexx == 0){
            setTimeout(()=>{
                getDiviceNodeUsage(currentCloud.value)
            })
        }
        return res.data;
    }else{
        myChart.myChart2.hideLoading();
    }
}
const getHardWare = (item,index) => {
    cloudObj[item.cloudId] = {};
    return getThisList(getVisualList,item,index)
}

const getCloud = () => {
    selectIndexCloudList().then((res)=>{
        if(res.code == 0 && res.data && res.data.length > 0){
            let list = [];
            res.data.forEach(async (item,index)=>{
                if(item.cloudType.toUpperCase() == 'OPENSTACK'){
                    item.label = item.cloudName;
                    item.value = item.cloudId;
                    cloudlist.value.push(item)
                    let res11 = await getHardWare(item,index)
                    list.push(...res11)
                }
                if(index >= res.data.length - 1){
                    vmlist.value = list;
                }
            })
            currentCloud.value = cloudlist.value[0].cloudId;
            currentCloud1.value = cloudlist.value[0].cloudId;
            setTimeout(()=>{
                getHardwarePie(currentCloud1.value)
            })
        }
    });
}

const getRiskUser = async () => {
  let res = await getRanklist()
  let risklist = []
  let yrisklist = []
  res.data.userlist.forEach((item,index)=>{
    if(Object.values(item)[0] > 0){
        risklist.unshift({name:Object.keys(item)[0],value:Object.values(item)[0]})
        yrisklist.unshift(Object.keys(item)[0])
    }
  })
  option.option3.series[0].data = risklist;
  option.option3.yAxis.data = yrisklist;
  myChart.myChart3.setOption(option.option3)
  myChart.myChart3.hideLoading();
}

const getDiviceNodeUsage = (cloudId) => {
    option.option2.yAxis.data = cloudObj[cloudId].yAxis;
    option.option2.series = cloudObj[cloudId].series;
    myChart.myChart2.setOption(option.option2)
    myChart.myChart2.hideLoading();
}

const getFirstTable = () => {
    statisticsProject().then((res)=>{
        if(res.code == 0){
            data1.value = res.data;
            data1.value.forEach((item,index)=>{
                if(!cloudObj1[item.cloudId])
                    cloudObj1[item.cloudId] = 0;
                cloudObj1[item.cloudId]++;
            })
        }
    })
}

const onExpand = (expanded, record) => {
    if(expanded){
        loading.value = true;
        data1.value.forEach((item,index)=>{
            if(item.id == record.id){
                getFirstChildTable(statisticsComputeByProject, item, index)
            }
        })
    }
}
const getFirstChildTable = (api,item,index) => {
    searchform['searchform'+1].projectId = item.id;
    api(searchform['searchform'+1]).then((res)=>{
        loading.value = false;
        if(res.code == 0){
            data[index+2] = res.data.records;
        }
    }).catch(()=>{
        loading.value = false;
    })
}
const getHardwarePie = (cloudId) => {
    statisticsProjectByCloudId({cloudId}).then((res)=>{
        if(res.code == 0 && res.data){
            option.option1.xAxis.data = res.data.projectList.map((item,index)=>{
                return item.projectName;
            });
            // option.option3.series = cloudObj[cloudId].series;
            option.option1.series.forEach((item,index)=>{
                if(Object.keys(res.data).includes(item.key+'Result')){
                    item.data = res.data[item.key+'Result'].map((item,index)=>{
                        return Number(item);
                    });
                }
            })
            myChart.myChart1.setOption(option.option1)
            
        }
        myChart.myChart1.hideLoading();
    }).catch(()=>{
        myChart.myChart1.hideLoading();
    })
}
const chartEmpty = ref(true)
const chart1 = ref()
const chart2 = ref()
const chart3 = ref()
const myChart = {
  myChart1:'',
  myChart2:'',
  myChart3:'',
}
const option = {
    option1:Options.report_1,
    option2:Options.report_2,
    option3:Options.report_3
}
const initChart = (cloudId) => {
  chartEmpty.value = false;
  let chart = [chart1.value,chart2.value];
  let n = 2;
  while(n > 0){
    if(!chart[n-1])
        return;
      echarts.dispose(chart[n-1]);
      option['option'+n].xAxis.data = [];
      option['option'+n].yAxis = {};
    (function(n){
        nextTick(()=>{
            myChart['myChart'+n] = echarts.init(chart[n-1]);
            myChart['myChart'+n].setOption(option['option'+n])
            myChart['myChart'+n].resize()
            window.addEventListener('resize',function(){
                myChart['myChart'+n].resize()
            })
            myChart['myChart'+n].showLoading();
        })
    })(n)
    n--;
  }
} 
onMounted(() => {
    window.parent.postMessage("loaded","*");
    getFirstTable();
    // getFirstOneTable();
    
    setTimeout(()=>{
      initChart()
      getCloud();
    //  getRiskUser()
    })
})
</script>
<style lang='scss' scoped>
.ant-page-header{padding: 0 24px;}
:deep(.ant-page-header-heading){
/*  background-color: #fafafa; */
  .ant-page-header-heading-left{margin: 0;}
}
.ant-page-header:not(.header-title){
  :deep(.ant-page-header-heading){
    ::before{
      content: "";
      display: inline-block;
      width: 4px;
      height: 100%;
      margin-right: 4px;
      vertical-align: text-bottom;
      background-color: #1890ff;
    }
  }
}
.ant-page-header-content{padding-top: 24px;}
.ant-divider{border-width: 16px;border-color: #f0f2f5;margin: 0 0 24px;}
.report{height: calc(100vh - 80px);padding: 0;}
.chart_area{display: flex;justify-content: space-between;}
.chart_card{
    width: 48%;
    border: 1px solid #f0f2f5;
    &:hover {
        border-color: #00000017;
        box-shadow: 0 2px 8px #00000017;
    };
    transition:all .3s;
    .ant-select{position:absolute;right:0;top:0;width:auto;z-index:1;color:rgba(0, 0, 0, 0.25)}
}
.echarts{
    height: 300px;
    padding: 10px 8px;
    // border: 1px solid #f0f2f5;
    // margin-bottom: 20px;
    :deep(.ant-card-head){
        font-weight: 600;font-size: 14px;padding-left: 6px;
        .ant-card-head-title{padding: 0;}
    };
}
// .table-first{box-shadow: 0 0 10px 2px #f0f0f0;}
:deep(.table-first .ant-table){background: rgba($color: #fff, $alpha: 0);}
:deep(.table-first .ant-table-tbody > tr > td){font-size: 16px;}
// background-color:#f0f0f0;
:deep(.table-first .ant-table-thead > tr > th){font-size: 16px;font-weight: bold;}
:deep(.table-expand .ant-table-thead > tr > th){font-size: 14px;font-weight: bold;background-color:transparent;}
:deep(.table-expand .ant-table-tbody > tr > td){font-size: 14px;}
// :deep(.table-expand .ant-table.ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td){background-color: #fafafa;}
// .ant-table-tbody > tr > td
:deep(tr.ant-table-expanded-row){box-shadow: 0 0 15px 5px #f0f0f0 inset;}
:deep(.ant-table-tbody > .ant-table-expanded-row > td){padding: 10px 10px 0 10px;}
:deep(.ant-table-pagination.ant-pagination){margin: 5px 0;}
:deep(.ant-pagination-item){background: rgba($color: #fff, $alpha: 0);}
:deep(.ant-pagination-item-active){background: rgba($color: #fff, $alpha: 0);}
::-webkit-scrollbar-track {
    background-color: #f0f2f5;
}
:deep(.ant-empty){background-color: transparent;}
:deep(.ant-table-placeholder){background-color: transparent;}
</style>