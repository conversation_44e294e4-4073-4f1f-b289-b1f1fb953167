<template>
    <div class=''>
        <a-modal v-model:visible="setProps.visible" title="设置" @ok="handleSave" @cancel="cancel" ok-text="提交" :body-style="{height:'456px',overflowY:'auto'}" centered :getContainer="modalBindNode">
            <a-table :columns="columns" :data-source="data" row-key="id" size="small" :expandedRowKeys="expandedRowKeys" @expand="onExpand" :pagination="false">
                <template #action={record}>
                    <a-select
                     mode="multiple" 
                     v-model:value="record.userIds" 
                     v-if="record.projectName" 
                     size="small"
                     style="width:100%" 
                     :placeholder="`请选择 ${record.projectName} 项目资源报表接收人`"
                     :filter-option="(input, option) => {return option.children[0].children.indexOf(input) !== -1}" 
                     show-search>
                        <a-select-option v-for="(item,index) in userlist" :key="index" :value="item.userId" :label="item.userName">{{item.userName}}</a-select-option>
                    </a-select>
                </template>
            </a-table>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { selectCloudList } from '@/api/backend/cloud';
import { selectProjectList } from '@/api/backend/devops/project';
import { emailconfigSave, emailconfigSelectList } from '@/api/backend/report';
import { queryWorker } from '@/api/backend/systems/user';
import { message } from 'ant-design-vue';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
const {proxy} = getCurrentInstance()
const route = useRoute()
const props = defineProps({
    setProps:{
        type:Object,
        default(){
            return {
                visible:false
            }
        }
    }
})
const columns = [
  {
    title: '项目',
    dataIndex: 'name',
    key: 'name',
    ellipsis:true,
    customCell:(record, rowIndex)=>{
        if(record.projectName)
        return {
            style: {
              'text-align': 'right'
            },
          };
    }
  },
  {title: '接收人(多选可搜索)',dataIndex: 'action', key: 'id', slots: { customRender: 'action' },align:'center', width:300, className:'action-cell' },
];

const userlist = ref([])
const rebuildform = reactive({
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    imageId:0
})
const expandedRowKeys = ref([])
const data = ref([])
const onExpand = (e,b) => {
    if(!e){
        expandedRowKeys.value = expandedRowKeys.value.filter((item,index)=>item != b.id);
    }else{
        expandedRowKeys.value.push(b.id)
    }
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
}
const getProjectlist = async (cloudId) => {
    let res = await selectProjectList({cloudId,enabled:1})
    if(res.code == 0 && res.data){
        res.data.forEach((item,index)=>{
            item.name = item.projectName;
        })
        return res.data;
    }
    else
        return [];
}
const getCloudlist = () => {
    selectCloudList().then(async(res)=>{
        if(res.code == 0 && res.data){
            data.value = res.data;
            let keys = [];
            let userdata = await getUsersinfo();
            console.log("userdata",userdata)
            data.value.forEach(async(item,index)=>{
                keys.push(item.id);
                item.name = item.cloudName;
                item.children = await getProjectlist(item.id);
                handleInfo(item,index,userdata)
            })
            expandedRowKeys.value = keys;
        }
    })
}
const handleSave = () => {
    console.log("data",data.value)
    let subarr = [];
    data.value.forEach((item,index)=>{
        item.children.forEach((t,i)=>{
            let userEntityList = t.userIds?.map((tt,ii)=>{
                return {userId:tt};
            });
            subarr.push({cloudId:item.id,projectId:t.id,userEntityList:userEntityList})
        })
    })
    emailconfigSave(subarr).then((res)=>{
        console.log("res",res)
        if(res.code == 0 && res.data !== false){
            message.success("保存成功");
            cancel();
        }else if(res.code == 0){
            message.error(res.msg && res.msg != 'success' ? res.msg : '保存失败')
        }
    }).catch((err)=>{
        message.success('保存失败')
    })
    
    console.log("subarr",subarr)
}
const cancel = () => {
    setProps.visible = false;
}
const getUsersinfo = async () => {
    let res = await emailconfigSelectList()
    //     let res = {
    //     code:0,
    //     data:[
    //             {
    //                 "cloudId":2,"projectId":3,"userEntityList":[{"userId":2},{"userId":1}]
    //             }
    //         ],
    //     msg:'success'
    // }
    if(res.code == 0 && res.data)
        return res.data;
    else
        return [];
}
const handleInfo = (item,index,userdata) => {

    console.log("item",item)
    
        // data.value.forEach((item,index)=>{
            userdata.forEach((t,i)=>{
                if(item.id == t.cloudId){
                    item.children.forEach((tt,ii)=>{
                        if(tt.id == t.projectId){
                            let userIds = [];
                            t.userEntityList.forEach((it,ni)=>{
                                userIds.push(it.userId);
                            })
                            tt.userIds = userIds
                        }
                    })
                }
            })
        // })
}
const setInfo = () => {
    getCloudlist()
    queryworker()
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>