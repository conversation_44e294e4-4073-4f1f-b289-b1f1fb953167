<template>
    <div class=''>
        <a-modal
        v-model:visible="monitorProps.visible"
        :title="monitorProps.name ? monitorProps.name : '资源监控'"
        width="100% !important"
        :footer="null"
        wrapClassName="full-modal"
        @cancel="cancel"
        >
            <template #footer>
                <a-button type="primary" @click="cancel">关闭</a-button>
            </template>
            <div style="height: calc(100vh - 103px);overflow-y: auto;">
                <a-range-picker
                v-model:value="value2"
                :locale="locale"
                :show-time="{ format: 'HH:mm:ss' }"
                format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
                @change="onChangeTime"
                allow-clear
                />
                <br><br>
                <a-spin :spinning="chartLoading[0]">
                    <div class="echarts" v-if="chartEmpty[0] === true" style="display: flex;justify-content: center;align-items: center;">
                        <a-empty></a-empty>
                    </div>
                    <div id="chart9" class="echarts" ref="chart9" v-else-if="chartEmpty[0] === false"></div>
                    <div class="echarts" v-else></div>
                </a-spin>
                <a-spin :spinning="chartLoading[1]">
                    <div class="echarts" v-if="chartEmpty[1] === true" style="display: flex;justify-content: center;align-items: center;">
                        <a-empty></a-empty>
                    </div>
                    <div id="chart10" class="echarts" ref="chart10" v-else-if="chartEmpty[1] === false"></div>
                    <div class="echarts" v-else></div>
                </a-spin>
            </div>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { nextTick, onMounted, reactive, ref } from 'vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import * as echarts from "echarts"
import { Options } from '@/common/chartoption';
import { setNotopt } from '@/utils/tool';
import { getMonitorline } from '@/api/backend/devops/agent';
import moment from 'moment';
const props = defineProps({
    monitorProps:{
        type:Object,
        default(){
            return {
                visible:false,
                name:''
            }
        }
    }
})
const searchform = reactive({
    serverId:undefined,
    startTime:moment((new Date()).getTime()-1000*60*60*24).utcOffset("+08:00").format('YYYY-MM-DD HH:mm:ss'),
    endTime:moment((new Date()).getTime()).utcOffset("+08:00").format('YYYY-MM-DD HH:mm:ss')
})
const value2 = ref([searchform.startTime,searchform.endTime])
const chartEmpty = ref([undefined,undefined]);
const chartLoading = ref([false,false])
const chart9 = ref()
const chart10 = ref()
var myChart9;
var myChart10;
let option9 = Options.server_monitor_9;
let option10 = Options.server_monitor_10;
const onChangeTime = (a,b,c) => {
    searchform.startTime = b[0];
    searchform.endTime = b[1];
    onOk()
}
const onOk = ()=>{
  if(searchform.startTime && searchform.endTime){
    getMonitorData(searchform.startTime,searchform.endTime);
  }
}
const getMonitorData = (startTime,endTime) => {
  getMonitorLine(startTime,endTime,'cpu',option9,myChart9,0);
  getMonitorLine(startTime,endTime,'memory',option10,myChart10,1);
//   getMonitorLine(startTime,endTime,'byte',option12,myChart12);
//   getMonitorLine(startTime,endTime,'packet',option13,myChart13);
}
const getMonitorLine = async (startTime="",endTime="",type,OPTION,CHART,INDEX) => {
    chartLoading.value[INDEX] = true;
    chartEmpty.value[INDEX] = undefined;
    let res = await getMonitorline({serverId:searchform.serverId,startTime,endTime,type:type.toUpperCase()})
    chartLoading.value[INDEX] = false;
    if(res.code == 0 && res.data){
        chartEmpty.value[INDEX] = false;
        setTimeout(()=>{
            CHART = echarts.init(type == 'cpu' ? chart9.value : chart10.value);
            window.addEventListener('resize',function(){
                CHART.resize()
            })
            OPTION.grid.top = '15%';
            OPTION.xAxis.data=res.data[type+'Map'][type+'TimeList']
            OPTION.series[0].data=res.data[type+'Map'][type+'PercentList'];
            if(OPTION.xAxis.data.length > 20){
                OPTION.label.show = false;
                OPTION.series[0].symbolSize = 0;
            }else{
                OPTION.label.show = true;
                OPTION.series[0].symbolSize = 3;
            }
            if(res.data[type+'Map'][type+'TimeList'] && res.data[type+'Map'][type+'TimeList'].length > 0)
                CHART.setOption(OPTION);
            else{
                let Option9 = JSON.parse(JSON.stringify(OPTION));
                setNotopt(CHART,Option9)
            }
        })
    }else{
        chartEmpty.value[INDEX] = true;
    }
}
const initChart = (serverId) => {
    searchform.serverId = serverId;
    
    getMonitorData(searchform.startTime,searchform.endTime)
}
const cancel = () => {
    props.monitorProps.visible = false;
}
onMounted(() => {})
defineExpose({initChart})
</script>
<style lang='scss' scoped>
// .echarts{width: 100%;height: 500px;}
.echarts{width:100%;
height:400px;
padding: 10px 8px;
border: 1px solid #f0f2f5;
margin-bottom: 20px;
&:hover {
    border-color: #00000017;
    box-shadow: 0 2px 8px #00000017;
}
transition:all .3s;
}
.full-modal {
  .ant-modal-body {
    flex: 1;
  }
}
</style>