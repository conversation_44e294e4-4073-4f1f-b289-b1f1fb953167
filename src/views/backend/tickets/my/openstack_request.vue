<template>
    <div class='orq'>
        <a-row style="min-width:834px">
            <a-col>
                <a-steps v-model:current="current" direction="vertical" @change="changeStep" size="small" style="width:122px">
                    <a-step title="平台选择" sub-title="*" />
                    <a-step title="基本信息" sub-title="*" :disabled="step1" />
                    <a-step title="镜像选择" sub-title="*" :disabled="step1||step2"/>
                    <a-step title="虚机类型" sub-title="*" :disabled="step1||step2||step3" />
                    <a-step title="网络选择" sub-title="*" :disabled="step1||step2||step3||step4" />
                    <a-step title="安全组" :disabled="step1||step2||step3||step4 || step5" />
                    <a-step title="密钥对" :disabled="step1||step2||step3||step4 || step5" />
                    <a-step title="配置" :disabled="step1||step2||step3||step4 || step5" />
                </a-steps>
            </a-col>
            <a-divider type="vertical" style="height:517px;margin-top:-24px"></a-divider>
            <a-col :span="20">
                <a-form :model="serverform" :label-col="{span:4}" ref="serverForm" :rules="rules">
                    <a-form-item label="平台域" name="domainId" v-show="current == 0">
                        <a-select v-model:value="serverform.domainId" @change="onDomainChange" placeholder="请选择" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                            <a-select-option value="" label="全部">全部</a-select-option>
                            <a-select-option v-for="(item,index) in domainlist" :key="index" :value="item.id" :label="item.domainName">{{item.domainName}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="平台项目" name="projectId" v-show="current == 0">
                        <a-select v-model:value="serverform.projectId" @change="onProjectChange" placeholder="请选择" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                            <a-select-option v-for="(item,index) in projectlist" :key="index" :value="item.id" :domainId="item.domainId" :label="item.projectName">{{item.projectName}}</a-select-option>
                        </a-select>
                    </a-form-item>

                    <a-form-item label="名称" name="serverName" v-show="current == 1">
                        <a-input v-model:value="serverform.serverName" @change="changeInfo" placeholder="请输入" allow-clear></a-input>
                    </a-form-item>
                    <a-form-item label="描述" name="description" v-show="current == 1">
                        <a-textarea v-model:value="serverform.description" placeholder="请输入" allow-clear></a-textarea>
                    </a-form-item>
                    <a-form-item label="可用域" name="availabilityZone" v-show="current == 1">
                        <a-select v-model:value="serverform.availabilityZone" @change="onSelectChange" placeholder="请选择" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                            <a-select-option v-for="(item,index) in zonelist" :key="index" :value="item.id" :label="item.zoneName">{{item.zoneName}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="虚机数量" name="serverNumber" v-show="current == 1">
                        <a-input-number v-model:value="serverform.serverNumber" @change="changeInfo" placeholder="请输入正整数" :min="1" :max="100" style="width:100%" allow-clear></a-input-number>
                    </a-form-item>
                    <a-form-item label="所属人" name="ownerId" v-show="current == 1">
                        <a-select v-model:value="serverform.ownerId" @change="onSelectChange" placeholder="请选择" :getPopupContainer="triggerNode => triggerNode.parentNode" :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}" show-search allow-clear>
                            <a-select-option v-for="(item,index) in userlist" :key="index" :value="item.userId">{{item.userName}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-space>
                            <a-form-item label="选择源" :label-col="{style:{width:'56px'}}" v-show="current == 2">
                                <a-select 
                                v-model:value="serverform.source" 
                                @select="sourceChange" 
                                placeholder="请选择源" 
                                :getPopupContainer="triggerNode => triggerNode.parentNode" 
                                style="width:100px"
                                allow-clear>
                                    <a-select-option :value="1" label="image">镜像</a-select-option>
                                    <a-select-option :value="2" label="snapshot">虚机快照</a-select-option>
                                    <a-select-option :value="3">卷</a-select-option>
                                    <a-select-option :value="4">卷快照</a-select-option>
                                </a-select>
                            </a-form-item>
                            <a-form-item label="创建新卷" :label-col="{offset:2,style:{width:'70px'}}" :wrapper-col="{style:{width:'56px'}}" v-show="current == 2 && (serverform.source == 1 || serverform.source == 2)">
                                <a-switch v-model:checked="serverform.volCreate" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                            </a-form-item>
                            <a-form-item label="卷大小(GB)" name="volSize" :label-col="{offset:2,style:{width:'100px'}}" :wrapper-col="{style:{width:'89px'}}" v-show="current == 2 && (serverform.source == 1 || serverform.source == 2) && serverform.volCreate">
                                <a-input-number v-model:value="serverform.volSize" :min="1" :step="1" :precision="0" :disabled="info.isInfo" @change="volSizeChange"></a-input-number>
                            </a-form-item>
                            <a-form-item label="删除虚机时删除卷" :label-col="{offset:2,style:{width:'126px'}}" :wrapper-col="{style:{width:'60px'}}" v-show="current == 2 && serverform.volCreate">
                                <a-switch v-model:checked="serverform.volDeleteOnInstanceDelete" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                            </a-form-item>
                        </a-space>
                    <a-form-item name="imageId" v-show="current == 2" v-if="serverform.source == 1 || serverform.source == 2">
                        <a-table :data-source="imagelist" :columns="columns1" row-key="id" :scroll="{ y: 380 }" :row-selection="{selectedRowKeys:serverform.imageId,onChange:onSelectChange,type:'radio',columnTitle:'单选'}" size="small" :pagination="false" bordered>
                            <template #imageSize="{record}">
                                {{(record.imageSize / (1024 * 1024 * 1024)).toFixed(2)}}
                            </template>
                            <template #status="{record}">
                                {{record.statusText}}
                            </template>
                            <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                                <div style="padding: 8px">
                                    <a-input
                                    ref="searchInput"
                                    :placeholder="`请输入${column.title}搜索`"
                                    :value="selectedKeys[0]"
                                    style="width: 188px; margin-bottom: 8px; display: block"
                                    @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                                    @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
                                    />
                                    <a-button
                                    type="primary"
                                    size="small"
                                    style="width: 90px; margin-right: 8px"
                                    @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
                                    >
                                    <template #icon><SearchOutlined /></template>
                                    搜索
                                    </a-button>
                                    <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                                    重置
                                    </a-button>
                                </div>
                            </template>
                            <template #filterIcon="filtered">
                                <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
                            </template>
                            <template #customRender="{ text, column }">
                                <span v-if="searchText3 && searchedColumn3 === column.dataIndex">
                                    <template
                                    v-for="(fragment, i) in text
                                        .toString()
                                        .split(new RegExp(`(?<=${searchText3})|(?=${searchText3})`, 'i'))"
                                    >
                                        <mark
                                            v-if="fragment.toLowerCase() === searchText3.toLowerCase()"
                                            class="highlight"
                                            :key="i"
                                        >
                                            {{ fragment }}
                                        </mark>
                                        <template v-else>{{ fragment }}</template>
                                    </template>
                                </span>
                                <template v-else>
                                    {{ text }}
                                </template>
                            </template>
                        </a-table>
                    </a-form-item>

                    <a-form-item name="volumeId" v-show="current == 2" v-if="serverform.source == 3">
                        <a-table :data-source="volumelist" :columns="columns6" row-key="id" :scroll="{ y: 380 }" :row-selection="{selectedRowKeys:serverform.volumeId,onChange:onSelectChange3,type:'radio',columnTitle:'单选'}" size="small" :pagination="false" bordered>
                            <template #imageSize="{record}">
                                {{(record.imageSize / (1024 * 1024 * 1024)).toFixed(2)}}
                            </template>
                            <template #status="{record}">
                                {{record.statusText}}
                            </template>
                            <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                                <div style="padding: 8px">
                                    <a-input
                                    ref="searchInput"
                                    :placeholder="`请输入${column.title}搜索`"
                                    :value="selectedKeys[0]"
                                    style="width: 188px; margin-bottom: 8px; display: block"
                                    @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                                    @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
                                    />
                                    <a-button
                                    type="primary"
                                    size="small"
                                    style="width: 90px; margin-right: 8px"
                                    @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
                                    >
                                    <template #icon><SearchOutlined /></template>
                                    搜索
                                    </a-button>
                                    <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                                    重置
                                    </a-button>
                                </div>
                            </template>
                            <template #filterIcon="filtered">
                                <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
                            </template>
                            <template #customRender="{ text, column }">
                                <span v-if="searchText4 && searchedColumn4 === column.dataIndex">
                                    <template
                                    v-for="(fragment, i) in text
                                        .toString()
                                        .split(new RegExp(`(?<=${searchText4})|(?=${searchText4})`, 'i'))"
                                    >
                                        <mark
                                            v-if="fragment.toLowerCase() === searchText4.toLowerCase()"
                                            class="highlight"
                                            :key="i"
                                        >
                                            {{ fragment }}
                                        </mark>
                                        <template v-else>{{ fragment }}</template>
                                    </template>
                                </span>
                                <template v-else>
                                    {{ text }}
                                </template>
                            </template>
                        </a-table>
                    </a-form-item>

                    <a-form-item name="volumeSnapshotId" v-show="current == 2" v-if="serverform.source == 4">
                        <a-table :data-source="volumesnaplist" :columns="columns7" row-key="id" :scroll="{ y: 380 }" :row-selection="{selectedRowKeys:serverform.volumeSnapshotId,onChange:onSelectChange4,type:'radio',columnTitle:'单选'}" size="small" :pagination="false" bordered>
                            <template #imageSize="{record}">
                                {{(record.imageSize / (1024 * 1024 * 1024)).toFixed(2)}}
                            </template>
                            <template #status="{record}">
                                {{record.statusText}}
                            </template>
                            <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                                <div style="padding: 8px">
                                    <a-input
                                    ref="searchInput"
                                    :placeholder="`请输入${column.title}搜索`"
                                    :value="selectedKeys[0]"
                                    style="width: 188px; margin-bottom: 8px; display: block"
                                    @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                                    @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
                                    />
                                    <a-button
                                    type="primary"
                                    size="small"
                                    style="width: 90px; margin-right: 8px"
                                    @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
                                    >
                                    <template #icon><SearchOutlined /></template>
                                    搜索
                                    </a-button>
                                    <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                                    重置
                                    </a-button>
                                </div>
                            </template>
                            <template #filterIcon="filtered">
                                <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
                            </template>
                            <template #customRender="{ text, column }">
                                <span v-if="searchText5 && searchedColumn5 === column.dataIndex">
                                    <template
                                    v-for="(fragment, i) in text
                                        .toString()
                                        .split(new RegExp(`(?<=${searchText5})|(?=${searchText5})`, 'i'))"
                                    >
                                        <mark
                                            v-if="fragment.toLowerCase() === searchText5.toLowerCase()"
                                            class="highlight"
                                            :key="i"
                                        >
                                            {{ fragment }}
                                        </mark>
                                        <template v-else>{{ fragment }}</template>
                                    </template>
                                </span>
                                <template v-else>
                                    {{ text }}
                                </template>
                            </template>
                        </a-table>
                    </a-form-item>

                    <a-form-item name="flavorId" v-show="current == 3" >
                        <a-table :title="()=>'虚机类型'" :data-source="flavorlist" :columns="columns2" row-key="id" :scroll="{ y: 103 }" :row-selection="{selectedRowKeys:serverform.flavorId,onChange:onSelectChange,type:'radio',columnTitle:'单选'}" size="small" :pagination="false" bordered>
                            <template #ram="{record}">
                                {{record.ram >= 1024 ? (record.ram/1024).toFixed() + 'GB' : record.ram + 'MB'}}
                            </template>
                            <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                                <div style="padding: 8px">
                                    <a-input
                                    ref="searchInput"
                                    :placeholder="`请输入${column.title}搜索`"
                                    :value="selectedKeys[0]"
                                    style="width: 188px; margin-bottom: 8px; display: block"
                                    @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                                    @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
                                    />
                                    <a-button
                                    type="primary"
                                    size="small"
                                    style="width: 90px; margin-right: 8px"
                                    @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
                                    >
                                    <template #icon><SearchOutlined /></template>
                                    搜索
                                    </a-button>
                                    <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                                    重置
                                    </a-button>
                                </div>
                            </template>
                            <template #filterIcon="filtered">
                                <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
                            </template>
                            <template #customRender="{ text, column }">
                                <span v-if="searchText && searchedColumn === column.dataIndex">
                                    <template
                                    v-for="(fragment, i) in text
                                        .toString()
                                        .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
                                    >
                                        <mark
                                            v-if="fragment.toLowerCase() === searchText.toLowerCase()"
                                            class="highlight"
                                            :key="i"
                                        >
                                            {{ fragment }}
                                        </mark>
                                        <template v-else>{{ fragment }}</template>
                                    </template>
                                </span>
                                <template v-else>
                                    {{ text }}
                                </template>
                            </template>
                        </a-table><br/>
                        <a-table :title="()=>'最小类型'" :data-source="flavorlist" :columns="columns2" row-key="id" :scroll="{ y: 103 }" :row-selection="{selectedRowKeys:serverform.minFlavorId,onChange:onSelectChange1,type:'radio',columnTitle:'单选'}" size="small" :pagination="false" bordered>
                            <template #ram="{record}">
                                {{record.ram >= 1024 ? (record.ram/1024).toFixed() + 'GB' : record.ram + 'MB'}}
                            </template>
                            <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                                <div style="padding: 8px">
                                    <a-input
                                    ref="searchInput"
                                    :placeholder="`请输入${column.title}搜索`"
                                    :value="selectedKeys[0]"
                                    style="width: 188px; margin-bottom: 8px; display: block"
                                    @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                                    @pressEnter="handleSearch1(selectedKeys, confirm, column.dataIndex)"
                                    />
                                    <a-button
                                    type="primary"
                                    size="small"
                                    style="width: 90px; margin-right: 8px"
                                    @click="handleSearch1(selectedKeys, confirm, column.dataIndex)"
                                    >
                                    <template #icon><SearchOutlined /></template>
                                    搜索
                                    </a-button>
                                    <a-button size="small" style="width: 90px" @click="handleReset1(clearFilters)">
                                    重置
                                    </a-button>
                                </div>
                            </template>
                            <template #filterIcon="filtered">
                                <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
                            </template>
                            <template #customRender="{ text, column }">
                                <span v-if="searchText1 && searchedColumn1 === column.dataIndex">
                                    <template
                                    v-for="(fragment, i) in text
                                        .toString()
                                        .split(new RegExp(`(?<=${searchText1})|(?=${searchText1})`, 'i'))"
                                    >
                                        <mark
                                            v-if="fragment.toLowerCase() === searchText1.toLowerCase()"
                                            class="highlight"
                                            :key="i"
                                        >
                                            {{ fragment }}
                                        </mark>
                                        <template v-else>{{ fragment }}</template>
                                    </template>
                                </span>
                                <template v-else>
                                    {{ text }}
                                </template>
                            </template>
                        </a-table><br/>
                        <a-table :title="()=>'最大类型'" :data-source="flavorlist" :columns="columns2" row-key="id" :scroll="{ y: 103 }" :row-selection="{selectedRowKeys:serverform.maxFlavorId,onChange:onSelectChange2,type:'radio',columnTitle:'单选'}" size="small" :pagination="false" bordered>
                            <template #ram="{record}">
                                {{record.ram >= 1024 ? (record.ram/1024).toFixed() + 'GB' : record.ram + 'MB'}}
                            </template>
                            <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                                <div style="padding: 8px">
                                    <a-input
                                    ref="searchInput"
                                    :placeholder="`请输入${column.title}搜索`"
                                    :value="selectedKeys[0]"
                                    style="width: 188px; margin-bottom: 8px; display: block"
                                    @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                                    @pressEnter="handleSearch2(selectedKeys, confirm, column.dataIndex)"
                                    />
                                    <a-button
                                    type="primary"
                                    size="small"
                                    style="width: 90px; margin-right: 8px"
                                    @click="handleSearch2(selectedKeys, confirm, column.dataIndex)"
                                    >
                                    <template #icon><SearchOutlined /></template>
                                    搜索
                                    </a-button>
                                    <a-button size="small" style="width: 90px" @click="handleReset2(clearFilters)">
                                    重置
                                    </a-button>
                                </div>
                            </template>
                            <template #filterIcon="filtered">
                                <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
                            </template>
                            <template #customRender="{ text, column }">
                                <span v-if="searchText2 && searchedColumn2 === column.dataIndex">
                                    <template
                                    v-for="(fragment, i) in text
                                        .toString()
                                        .split(new RegExp(`(?<=${searchText2})|(?=${searchText2})`, 'i'))"
                                    >
                                        <mark
                                            v-if="fragment.toLowerCase() === searchText2.toLowerCase()"
                                            class="highlight"
                                            :key="i"
                                        >
                                            {{ fragment }}
                                        </mark>
                                        <template v-else>{{ fragment }}</template>
                                    </template>
                                </span>
                                <template v-else>
                                    {{ text }}
                                </template>
                            </template>
                        </a-table>
                    </a-form-item>

                    <a-form-item name="networkId" v-show="current == 4" >
                        <a-table :data-source="networklist" :columns="columns3" row-key="id" :scroll="{ y: 400 }" :row-selection="{selectedRowKeys:serverform.networkId,onChange:onSelectChange,type:'radio',columnTitle:'单选'}" size="small" :pagination="false" bordered></a-table>
                    </a-form-item>
                    <a-form-item name="groupIds" v-show="current ==5" >
                        <a-table :data-source="seculist" :columns="columns4" row-key="id" :scroll="{ y: 400 }" :row-selection="{selectedRowKeys:serverform.groupIds,onChange:onSelectChange,type:'radio',columnTitle:'单选'}" size="small" :pagination="false" bordered></a-table>
                    </a-form-item>

                    <a-form-item name="keyPairId" v-show="current == 6" >
                        <a-table :data-source="keylist" :columns="columns5" row-key="id" :scroll="{ y: 400 }" :row-selection="{selectedRowKeys:serverform.keyPairId,onChange:onSelectChange,type:'radio',columnTitle:'单选'}" size="small" :pagination="false" bordered>
                        </a-table>
                        <!-- </div> -->
                    </a-form-item>

                    <a-form-item label="定制化脚本" name="userData" v-show="current == 7" :help="'内容大小：最大字节数为 16.00 KB，其中文本字节数为'+((getLength(serverform.userData) >= 1024) ? ((getLength(serverform.userData)/1024).toFixed(2)+'KB') : (getLength(serverform.userData)+'字节'))">
                        <a-input type="file" style="border:none" @change="importScript" />
                        <a-textarea v-model:value="serverform.userData" id="textareaNode" :auto-size="{minRows: 16,maxRows: 16}" @change="changeData" showCount allow-clear></a-textarea>
                    </a-form-item>
                </a-form>
            </a-col>
        </a-row>
    </div>
</template>
<script lang='ts' setup>
import { selectZoneList } from '@/api/backend/devops.ts';
import { selectOpenstList } from '@/api/backend/devops/domain';
import { selectFlavorList } from '@/api/backend/devops/flavor';
import { selectFloatingipList } from '@/api/backend/devops/floatingip';
import { selectImageList } from '@/api/backend/devops/image';
import { selectKeypairList } from '@/api/backend/devops/keypair';
import { selectNetworkList } from '@/api/backend/devops/network';
import { selectProjectList } from '@/api/backend/devops/project';
import { selectSecugroupList } from '@/api/backend/devops/security';
import { saveServer, updateServer } from '@/api/backend/devops/server';
import { selectVolumeList, selectVolumeSnapShotList } from '@/api/backend/storage';
import { queryWorker } from '@/api/backend/systems/user';
import { userStore } from '@/store/user';
import { getLength, jsReadFiles } from '@/utils/tool';
import { computed, getCurrentInstance, onMounted, onUpdated, reactive, ref } from 'vue';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['disableChange','currChange']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false,
                isInfo:false
            }
        }
    }
})
const serverForm = ref()
const current = ref(0)
const domainlist = ref([])
const projectlist = ref([])
const zonelist = ref([])
const userlist = ref([])
const imagelist = ref([])
const volumelist = ref([])
const volumesnaplist = ref([])
const flavorlist = ref([])
const networklist = ref([])
const floatiplist = ref([]);
const seculist = ref([])
const keylist = ref([])

const searchText = ref('');
const searchText1 = ref('');
const searchText2 = ref('');
const searchText3 = ref('');
const searchText4 = ref('');
const searchText5 = ref('');
const searchedColumn = ref();
const searchedColumn1 = ref();
const searchedColumn2 = ref();
const searchedColumn3 = ref();
const searchedColumn4 = ref();
const searchedColumn5 = ref();
const searchInput = ref();
const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    if(current.value == 2){
        
        if(serverform.source == 1 || serverform.source == 2){
            searchText3.value = selectedKeys[0];
            searchedColumn3.value = dataIndex;
        }
        if(serverform.source == 3){
            searchText4.value = selectedKeys[0];
            searchedColumn4.value = dataIndex;
        }
        if(serverform.source == 4){
            searchText5.value = selectedKeys[0];
            searchedColumn5.value = dataIndex;
        }
    }
    if(current.value == 3){
        searchText.value = selectedKeys[0];
        searchedColumn.value = dataIndex;
    };
    }
    const handleSearch1 = (selectedKeys, confirm, dataIndex) => {
      confirm();
      searchText1.value = selectedKeys[0];
      searchedColumn1.value = dataIndex;
    };
    const handleSearch2 = (selectedKeys, confirm, dataIndex) => {
      confirm();
      searchText2.value = selectedKeys[0];
      searchedColumn2.value = dataIndex;
    };
    const handleReset = clearFilters => {
      clearFilters();
      if(current.value == 2){
        if(serverform.source == 1 || serverform.source == 2)
        searchText3.value = '';
        if(serverform.source == 3)
        searchText4.value = '';
        if(serverform.source == 4)
        searchText5.value = '';
      }
      
      if(current.value == 3){
      searchText.value = '';  
      }
    };
    const handleReset1 = clearFilters => {
      clearFilters();
      searchText1.value = '';
    };
    const handleReset2 = clearFilters => {
      clearFilters();
      searchText2.value = '';
    };
const defaultform = {
    cloudId:localStorage.getItem('tcloudId'),
    projectId:undefined,
    serverName:'',
    description:'',
    availabilityZone:undefined,
    ownerId:userStore().userId,
    source:1,
    imageId:undefined,
    volumeId:undefined,
    volumeSnapshotId:undefined,
    flavorId:undefined,
    minFlavorId:undefined,
    maxFlavorId:undefined,
    networkId:undefined,
    floatipId:undefined,
    groupIds:undefined,
    keyPairId:undefined,
    serverNumber:1,
    userData:'',
    volCreate:true,
    volDeleteOnInstanceDelete:true,
    volSize:1
}
const serverform = reactive({
    cloudId:localStorage.getItem('tcloudId'),
    projectId:undefined,
    serverName:'',
    description:'',
    availabilityZone:undefined,
    ownerId:userStore().userId,
    source:1,
    imageId:undefined,
    volumeId:undefined,
    volumeSnapshotId:undefined,
    flavorId:undefined,
    minFlavorId:undefined,
    maxFlavorId:undefined,
    networkId:undefined,
    floatipId:undefined,
    groupIds:undefined,
    keyPairId:undefined,
    serverNumber:1,
    userData:'',
    volCreate:true,
    volDeleteOnInstanceDelete:true,
    volSize:1
})
const step1 = computed(()=>!serverform.projectId)
const step2 = computed(()=>!(serverform.serverName && serverform.availabilityZone && serverform.ownerId && serverform.serverNumber))
const step3 = computed(()=>!((serverform.imageId && serverform.source == 1 && serverform.volSize) || (serverform.imageId && serverform.source == 2 && serverform.volSize) || (serverform.volumeId && serverform.source == 3) || (serverform.volumeSnapshotId && serverform.source == 4)))
const step4 = computed(()=>!(serverform.flavorId))
const step5 = computed(()=>!serverform.networkId)
const columns1 = [
    {title: '名称', dataIndex: 'name', key: 'name',align:'left',slots: {
          filterDropdown: 'filterDropdown',
          filterIcon: 'filterIcon',
          customRender: 'customRender',
        },
        onFilter: (value, record) =>
            record.name.toString().toLowerCase().includes(value.toLowerCase()),
                onFilterDropdownVisibleChange: visible => {
                if (visible) {
                    setTimeout(() => {
                    console.log(searchInput.value);
                    searchInput.value.focus();
                    }, 100);
                }
            }},
    {title: '大小(G)', dataIndex: 'imageSize', slots: { customRender: 'imageSize' },　key: 'id',width:70,align:'center'},
    {title: '状态', dataIndex: 'status', key: 'id', slots: { customRender: 'status' },width:60,align:'center'}
];
const columns2 = [
    {title: '名称', dataIndex: 'name', key: 'name' ,align:'left',slots: {
          filterDropdown: 'filterDropdown',
          filterIcon: 'filterIcon',
          customRender: 'customRender',
        },
        onFilter: (value, record) =>
            record.name.toString().toLowerCase().includes(value.toLowerCase()),
                onFilterDropdownVisibleChange: visible => {
                if (visible) {
                    setTimeout(() => {
                    console.log(searchInput.value);
                    searchInput.value.focus();
                    }, 100);
                }
            }},
    {title: '硬盘大小(G)', dataIndex: 'totalDisk', key: 'id', width:100, align:'center'},
    {title: '核数', dataIndex: 'vcpus', key: 'id' ,align:'center'},
    {title: '内存(MB)', dataIndex: 'ram', slots: { customRender: 'ram' }, key: 'id' ,align:'center'},
];
const columns3 = [
    {title: '名称', dataIndex: 'networkName', slots: { customRender: 'networkName' }, key: 'id',align:'left'},
    {title: '类型', dataIndex: 'networkType', key: 'id',align:'center'},
];
const columns4 = [
    {title: '名称', dataIndex: 'groupName', key: 'id',align:'left'},
    {title: '描述', dataIndex: 'description', key: 'id',align:'center'},
];
const columns5 = [
    {title: '名称', dataIndex: 'name', key: 'id',align:'left'},
    {title: '指纹', dataIndex: 'fingerPrint', key: 'id',align:'left'},
];
const columns6 = [
    {title:'名称', dataIndex:'name',key:'id'},
    {title:'类型', dataIndex:'type',key:'id',align:'center'},
    {title:'大小', dataIndex:'size',key:'id',align:'center'},
]
const columns7 = [
    {title:'名称', dataIndex:'name',key:'id'},
    {title:'卷', dataIndex:'volumeName',key:'id'},
    {title:'大小', dataIndex:'size',key:'id',align:'center'},
    {title:'状态', dataIndex:'status',key:'id',align:'center'},
]
const columns8 = [
    {title:'IP地址', dataIndex:'floatingIpAddress',key:'id'},
    {title:'池', dataIndex:'pool',key:'id'},
]
const validateData = (rule, value) => {
  if (getLength(value) > 16*1024) {
    return Promise.reject();
  } else {
    return Promise.resolve();
  }
}
const rules = {
    projectId:[{type:'number',required:true, message:'请选择',trigger:'change'}],
    serverName:[{required:true, message:'请输入',trigger:'change'}],
    serverNumber:[{required:true, type:'number', message:'请输入正整数',trigger:'change'}],
    availabilityZone:[{type:'number',required:true, message:'请选择',trigger:'change'}],
    ownerId:[{type:'number',required:true, message:'请选择',trigger:'change'}],
    userData:[{required:false, validator:validateData,trigger:'change'}],
    volSize:[{type:'number',required:true, message:'请输入',trigger:'change'}],
}
const changeStep = (current) => {
    emit('disableChange');
    emit('currChange',current);
}
const onDomainChange = (e) => {
    let temp = {...defaultform};
    temp.domainId = serverform.domainId;
    Object.assign(serverform, temp);
    selectProjectlist(e)
    emit('disableChange');
    getCurrprojectId()
}
const onProjectChange = (e,option) => {
    if(e){
        serverform.domainId = option.domainId;
    }
    let temp = {...defaultform};
    temp.domainId = serverform.domainId;
    temp.projectId = serverform.projectId;
    console.log('step1',step1.value)
    Object.assign(serverform, temp);
    emit('disableChange');
    getCurrprojectId();
}
const changeInfo = (e) => {
    emit('disableChange');
}
const importScript = async (e) => {
    let res = await jsReadFiles(e.target.files,'textareaNode')
    if(res){
        serverform.userData = res
    }
}
const changeData = (e) => {
    console.log("length",e,getLength(e.target.value))
}
const sourceChange = (e,option) => {
    emit('disableChange');
    serverform.imageId = undefined;
    serverform.volumeId = undefined;
    serverform.volumeSnapshotId = undefined;
    console.log('e',e,option)
    if(e == 1 || e==2){
        selectImagelist(option.label);
    }
}
const volSizeChange = (e) => {
    emit('disableChange');
}
const onSelectChange = (e,b) =>{
    if(current.value == 2){
        serverform.imageId = e
    }
    if(current.value == 3){
        serverform.flavorId = e;
    }
    
    if(current.value == 4){
        serverform.networkId = e
    }
    if(current.value ==5)
    serverform.groupIds = e;
    if(current.value == 6)
    serverform.keyPairId = e;
    emit('disableChange');
}
const onSelectChange1 = (e) => {
    
    serverform.minFlavorId = e;
    emit('disableChange');
}
const onSelectChange2 = (e) => {
    
    serverform.maxFlavorId = e;
    emit('disableChange');
}
const onSelectChange3 = (e) => {
    serverform.volumeId = e;
    emit('disableChange');
}
const onSelectChange4 = (e) => {
    serverform.volumeSnapshotId = e;
    emit('disableChange');
}
const cancel = () => {
    serverForm.value.resetFields();
    Object.assign(serverform, defaultform);
    current.value = 0;
}
const save = () => {
    let serverform1 = {...serverform}
    proxy.$handleSave(serverForm.value, saveServer, updateServer, props, serverform1, ()=>{proxy.$mitt.emit('getlist');cancel();},null,()=>{
        if(serverform1.source == 1 || serverform1.source == 2)
        serverform1.imageId = serverform1.imageId[0];
        else{
            serverform1.volSize = undefined;
            serverform1.volCreate = undefined;
        }
        if(serverform1.volCreate === false){
            volDeleteOnInstanceDelete = undefined;
            serverform1.volSize = undefined;
        }
        if(serverform1.source == 3)
        serverform1.volumeId = serverform1.volumeId[0];
        if(serverform1.source == 4)
        serverform1.volumeSnapshotId = serverform1.volumeSnapshotId[0];
        serverform1.minFlavorId = serverform1.minFlavorId[0];
        serverform1.maxFlavorId = serverform1.maxFlavorId[0];
        serverform1.networkId = serverform1.networkId ? serverform1.networkId[0] : '';
        serverform1.groupIds = serverform1.groupIds ? serverform1.groupIds.join(',') : '';
        serverform1.keyPairId = serverform1.keyPairId ? serverform1.keyPairId.join(',') : '';
    })
}
const selectDomainlist = async () => {
    let res = await selectOpenstList({cloudId:localStorage.getItem("tcloudId")});
    if(res.code == 0){
        domainlist.value = res.data;
    }
}
const selectProjectlist = async (domainId) => {
    let res = await selectProjectList({cloudId:localStorage.getItem("tcloudId"),domainId});
    if(res.code == 0){
        projectlist.value = res.data;
    }
}
const selectZonelist = async () => {
    let res = await selectZoneList({cloudId:localStorage.getItem("tcloudId"),module:'nova'});
    if(res.code == 0){
        zonelist.value = res.data;
    }
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
}
const selectImagelist = async (imageType) => {
    let res = await selectImageList({cloudId:localStorage.getItem("tcloudId"),projectId:serverform.projectId,imageType})
    if(res.code == 0)
    imagelist.value = res.data;
}
const SelectVolumeList = async () => {
    let res = await selectVolumeList({projectId:serverform.projectId,bootable:1,status:'available'})
    if(res.code == 0){
        volumelist.value = res.data;
    }
}
const SelectVolumeSnapShotList = async () => {
    let res = await selectVolumeSnapShotList({projectId:serverform.projectId})
    if(res.code == 0){
        volumesnaplist.value = res.data;
    }
}
const getFlavorlist = async () => {
    let res = await selectFlavorList({cloudId:localStorage.getItem("tcloudId")})
    if(res.code == 0)
    flavorlist.value = res.data
}
const selectNetworklist = async () => {
    let res = await selectNetworkList({cloudId:localStorage.getItem("tcloudId"),projectId:serverform.projectId})
    if(res.code == 0)
    networklist.value = res.data
}
const SelectFloatList = async () => {
    let res = await selectFloatingipList({cloudId:localStorage.getItem("tcloudId"),projectId:serverform.projectId,active:1});
    if(res.code == 0){
        floatiplist.value = res.data;
    }
}
const selectSecugrouplist = async () => {
    let res = await selectSecugroupList({cloudId:localStorage.getItem("tcloudId"),projectId:serverform.projectId})
    if(res.code == 0)
    seculist.value = res.data
}

const selectKeypairlist = async () => {
    let res = await selectKeypairList({cloudId:localStorage.getItem("tcloudId")});
    if(res.code == 0){
        keylist.value = res.data;
    }
}

const getPrecloudId = () => {
    selectDomainlist();
    selectProjectlist();
    selectZonelist();
    getFlavorlist();
    selectKeypairlist();
}
const getCurrprojectId = () => {
    selectImagelist('image');
    SelectVolumeList();
    SelectVolumeSnapShotList();
    selectNetworklist();
    selectSecugrouplist();
}
onMounted(() => {
    
    queryworker();
    
})
defineExpose({current,serverform,defaultform,cancel,getPrecloudId})
</script>
<style lang='scss' scoped>
.ant-row{height: 100%;flex-flow: row nowrap;}
.ant-col{overflow-y: auto;};
:deep(.ant-steps-small .ant-steps-item-description){font-size: 12px;};
:deep(.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description){color: rgba(0, 0, 0, 0.45);}
:deep(.ant-steps-item-subtitle){color: #ff4d4f;}
</style>