<template>
    <!-- <div class='contentPadding'>
        <div class="delay-content"> -->
    <div class="back-page">
        <a-page-header
            class="back-header1"
            title="虚机延期"
            @back="()=>{router.back()}"
        >
            <template #extra>
                <a-button type="primary" @click="save">提交</a-button>
            </template>
        </a-page-header>
        <!-- <div style="padding:20px;background-color:#fff;display:flex;justify-content:space-between;border-bottom:2px solid #f0f2f5">
            <div></div>
            <div>
                <a-button type="primary" @click="save">提交</a-button>
            </div>
        </div> -->
        <div class="delay-content" :style="{height: 'calc(100vh - 170px)',overflowY:'auto'}">
            <a-form :model="ticketform" size="large" :label-col="{style:{width:'109px'}}" ref="delayForm" :rules="rules">
                <a-form-item label="工单标题" name="ticketTitle">
                    <a-input v-model:value="ticketform.ticketTitle" placeholder="请输入" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="工单描述" name="ticketInfo">
                    <a-textarea v-model:value="ticketform.ticketInfo" placeholder="请输入" allow-clear></a-textarea>
                </a-form-item>
                <a-form-item label="延期截止时间" name="endTime">
                    <!-- <a-space direction="horizontal">
                        <a-date-picker
                        :locale="locale"
                        v-model:value="ticketform.startTime"
                        :disabled-date="disabledStartDate"
                        format="YYYY-MM-DD"
                        placeholder="开始时间"
                        /> -->
                        <a-date-picker
                        :locale="locale"
                        v-model:value="ticketform.endTime"
                        format="YYYY-MM-DD"
                        placeholder="截止时间"
                        allow-clear
                        />
                    <!-- </a-space> -->
                </a-form-item>
                <a-form-item label="虚机" name="serverId">
                    <a-select
                        v-model:value="ticketform.serverId"
                        placeholder="请选择"
                        @select="select"
                        @clear="clear"
                        :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}"
                        show-search
                        allowClear>
                        <a-select-option v-for="(item,index) in serverlist" :key="index" :serverInfo="item" :value="Number(item.id)" :label="item.serverName">{{item.serverName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <!-- <a-form-item label="工单处理人" name="transcatUserId" v-if="ticketform.serverId">
                    <a-select v-model:value="ticketform.transcatUserId" :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}" placeholder="请选择" show-search allow-clear>
                        <a-select-option v-for="(item,index) in userlist" :key="index" :value="item.userId" :label="item.userName">{{item.userName}}</a-select-option>
                    </a-select>
                </a-form-item> -->
                <a-form-item label="虚机基本信息" v-if="isSelected">
                    <a-descriptions :column="2" :label-style="{width:'133px',textAlign:'right'}" bordered>
                        <a-descriptions-item label="虚机所属平台">{{serverInfo.cloudName}}</a-descriptions-item>
                        <a-descriptions-item label="磁盘大小">{{serverInfo.flavorEntity?.totalDisk}} G</a-descriptions-item>
                        <a-descriptions-item label="CPU核数">{{serverInfo.flavorEntity?.vcpus}}</a-descriptions-item>
                        <a-descriptions-item label="内存大小">{{(serverInfo.flavorEntity?.ram >= 1024) ? ((serverInfo.flavorEntity?.ram / 1024) + ' G') : (serverInfo.flavorEntity?.ram + ' M') }}</a-descriptions-item>
                    </a-descriptions>
                </a-form-item>

                <!-- <a-form-item label="虚机类型" v-else>
                    <a-select
                        v-model:value="ticketform.flavorId"
                        placeholder="请选择"
                        allowClear>
                        <a-select-option v-for="(item,index) in flavorlist" :key="index" :value="Number(item.id)">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item> -->
                <!-- <a-button class="sub-btn" type="primary" @click="save" :disabled="!ticketform.imageId">提交</a-button> -->
            </a-form>
        </div>
        
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { message } from "ant-design-vue";
import {selectFlavorInfo, selectFlavorList} from "@/api/backend/devops/flavor";
import {selectServerList} from "@/api/backend/devops/server";
import { useRoute } from 'vue-router';
import { getCloudUserlist } from '@/api/backend/cloud';
import { saveDelayTicket, updateTicket } from '@/api/backend/tickets';
import router from '@/router';
import { indexStore } from '@/store';
import { userStore } from '@/store/user';
const {proxy} = getCurrentInstance()
const index_store = indexStore()
const route = useRoute();
const serverlist = ref([]);
const flavorlist = ref([]);
const delayForm = ref();
const isSelected = ref(false);
const serverInfo = reactive({});
const userlist = ref([])
const defaultform = {
    "ticketTitle": "",
    "ticketInfo": "",
    "endTime": null,
    "transcatUserId":undefined,
    "serverId":undefined
}
const ticketform = reactive({
    "ticketTitle": "",
    "ticketInfo": "",
    "endTime": null,
    "transcatUserId":undefined,
    "serverId":undefined
})
const rules = {
    ticketTitle:[{required:true, message:'请输入',trigger:'change'}],
    endTime:[{type: 'object',required:true, message:'请选择',trigger:'change'}],
    transcatUserId:[{type:'number',required:true, message:'请选择',trigger:'change'}],
    serverId:[{type:'number',required:true, message:'请选择',trigger:'change'}],
}

const endOpen = ref<boolean>(false);
// const selectedRowKeys = ref()
const disabledStartDate = (startValue: Moment) => {
      if (!startValue || !ticketform.endTime) {
        return false;
      }
      return startValue.valueOf() > ticketform.endTime.valueOf();
    };

    // const disabledEndDate = (endValue: Moment) => {
    //   if (!endValue || !ticketform.startTime) {
    //     return false;
    //   }
    //   return ticketform.startTime.valueOf() >= endValue.valueOf();
    // };
const flavorInfo = async (res1data) => {
    let res = await selectFlavorInfo({id:res1data.flavorId})
    if(res.code == 0){
        serverInfo.flavorEntity = res.data;
        // ticketform.disk = res.data.totalDisk ? res.data.totalDisk + ' G' : '-';
        // ticketform.vcpus = res.data.vcpus ? res.data.vcpus : '-';
        // ticketform.ram = res.data.ram ? (res.data.ram >= 1024 ? (res.data.ram / 1024).toFixed() + ' G' : res.data.ram + ' M') : '-';
        console.log("flavor",res.data)
    }else{
        serverInfo.flavorEntity.totalDisk = res1data.disk ? res1data.disk + ' G' : '-';
        serverInfo.flavorEntity.vcpus = res1data.vcpus ? res1data.vcpus : '-';
        serverInfo.flavorEntity.ram = res1data.ram ? (res1data.ram >= 1024 ? (res1data.ram / 1024).toFixed() + ' G' : res1data.ram + ' M') : '-';
    }
}
    const select = (a,b) => {
        console.log('a,b',a,b)
        // Object.assign(serverInfo, b.serverInfo)
        serverInfo.cloudName = b.serverInfo.cloudName;
        if(b.serverInfo.flavorId)
            flavorInfo(b.serverInfo)
        else{
            serverInfo.flavorEntity.totalDisk = '-';
            serverInfo.flavorEntity.vcpus = '-';
            serverInfo.flavorEntity.ram = '-';
        }
        isSelected.value = true;
        // if(a && b.serverInfo?.cloudId)
        // queryworker(b.serverInfo.cloudId);
        // else
        // userlist.value = []
    }
    const clear = (e,c) =>{
        isSelected.value = false;
        console.log('c',e,c)
    }
    const cancel = () => {
        clear();
        // let index = index_store.get_activeIndex;
        // index_store.delTagsItem(index)
        router.back()
        Object.assign(ticketform,defaultform)
    }
    const save = () => {
        // ticketform.endTime = ticketform.endTime.format('YYYY-MM-DD')
        console.log('f',ticketform)
        ticketform.cloudId = serverInfo.cloudId;
        console.log("serverInfo.cloudId",serverInfo.cloudId)
        let tempform = {...ticketform}
        proxy.$handleSave(delayForm.value, saveDelayTicket, updateTicket, ticketform.id, tempform, ()=>{cancel();},null,()=>{
            tempform.endTime = tempform.endTime.format('YYYY-MM-DD')
            })
    }
    const getServerList = async () => {
    let res = await selectServerList({ownerId:userStore().userId})
    if(res){
        if(res.code == 0){
            if(route.query.serverID)
            ticketform.serverId = Number(route.query.serverID)
            // Object.assign(ticketform,defaultform);
            // Object.assign(serverlist,res.data);
            serverlist.value = res.data;
            if(ticketform.serverId){
    console.log('ticketform.serverId',ticketform.serverId,serverlist.value)
    serverlist.value.forEach((item)=>{
    if(item.id == ticketform.serverId){
        Object.assign(serverInfo, item)
        isSelected.value = true;
    }
})
//     Object.assign(serverInfo, b.serverInfo)
}
        }
    }
}
const getFlavorList = async () => {
    let res = await selectFlavorList()
    if(res.code == 0){
        flavorlist.value = res.data;
    }
}
const queryworker = async (id) => {
    let res = await getCloudUserlist(id)
    if(res.code == 0)
    userlist.value = res.data;
}
onMounted(() => {getServerList();getFlavorList();
proxy.$mitt.on('delayCancel',cancel);

})
</script>
<style lang='scss' scoped>
// .contentPadding{position: relative;}
// .delay-content{position: absolute;top: 50%;left: 50%;margin-top: -111px;margin-left: -238px;}
.delay-content{padding:20px 150px;background-color:#fff;border-top: 2px solid #f0f2f5;}
.contentPadding{position: relative;}
.sub-btn{
    // margin-top: 50px;
    position: absolute;
    right: 60px;
    top: 600px;
}
.back-header1{background-color: #fff;}
</style>
