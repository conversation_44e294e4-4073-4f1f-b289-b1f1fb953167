<template>
<div class='uncloudRight' v-if="!info.isShow && !info1.isShow && !info2.isShow && !info3.isShow">
  <!-- <div style="min-width:935px"> -->
    <div class="buttonPadding" v-if="isList">
    <a-form layout="inline" :model="searchform" class="searchform">
      <a-form-item label="标题">
        <a-input placeholder="请输入" @pressEnter="onSearch" v-model:value="searchform.ticketTitle" allowClear />
      </a-form-item>
      <a-form-item label="类型">
        <a-select v-model:value="searchform.ticketType" placeholder="请选择" allowClear>
            <a-select-option value="1">虚机申请</a-select-option>
            <a-select-option value="2">虚机延期</a-select-option>
            <a-select-option value="3">虚机扩容</a-select-option>
        </a-select>
        </a-form-item>
        <a-form-item label="状态">
        <a-select v-model:value="searchform.state" placeholder="请选择" @change="stateChange" allowClear>
            <a-select-option value="1">办理中</a-select-option>
            <a-select-option value="2">已办结</a-select-option>
        </a-select>
        </a-form-item>
        <a-form-item label="办结时间">
        <a-select v-model:value="searchform.time" placeholder="请选择" @change="timeChange" allowClear>
            <a-select-option value="1">今天</a-select-option>
            <a-select-option value="2">本周</a-select-option>
            <a-select-option value="3">本月</a-select-option>
        </a-select>
        </a-form-item>
    </a-form>
    <MoreSearch @search="onSearch" @reset="reset" />
  </div>
  <div :class="isList ? 'innerPadding' : 'innerPadding1'">
    <a-row class="buttonGroup" v-if="isList">
      <a-button type="primary" @click="()=>{visible = true;}">新增 </a-button>
    </a-row>
    <a-table :columns="columns" row-key="id" :scroll="{x:true}" :data-source="ticketlist" :pagination="isList ? pagination : false" @change="changeTable">
        <template #index={record,index}>
            {{index+1+(pagination.pageSize * (pagination.current-1))}}
        </template>
        <template #ticketTitle={record}>
            <a @click="handleView(record)">{{record.ticketTitle}}</a>
            <!-- <span v-else>{{record.ticketTitle}}</span> -->
        </template>
        <template #ticketType="{ record }">
      <span v-if="record.ticketType == 2">虚机延期</span>
      <span v-else-if="record.ticketType == 3">虚机扩容</span>
      <span v-else>虚机申请</span>
    </template>
        <template #state={record}>
            <span v-if="record.state == 1">待办理</span>
            <span v-else-if="record.state == 2">已办结</span>
            <span v-else-if="record.state == 0">申请</span>
            <span v-else>无</span>
        </template>
        <template #action={record} v-if="isList">
            <a-button @click="handleView(record)" v-if="$isShowBtn('sys:systicket:list')" class="button_V">查看</a-button>
            <a-button @click="$handleDel([record.id],deleteTicket,getList)" v-if="$isShowBtn('sys:systicket:delete') && record.taskStepCode == 'SQRSP'" class="button_D">删除</a-button>
        </template>
    </a-table>
    <a-modal title="请选择工单申请类型" centered v-model:visible="visible" width: 608.8px; ok-text="确定" cancel-text="取消" :ok-button-props="{ disabled: !(selectedKeys && selectedKeys[0]) }" @ok="handleAdd" @cancel="()=>{selectedKeys = ['0']}">
        <a-menu v-model:selectedKeys="selectedKeys">
            <a-menu-item key="0">虚机申请</a-menu-item>
            <a-menu-item key="1">虚机延期</a-menu-item>
            <a-menu-item key="2">虚机扩容</a-menu-item>
        </a-menu>
    </a-modal>
  </div>
  <!-- </div> -->
</div>
    <Info ref="ticketViewRef" :info="info" v-if="info.isShow" />
    <Delay ref="delayViewRef" :info="info1" @getlist="getList" v-if="info1.isShow" />
    <Plus ref="plusViewRef" :info="info2" @getlist="getList" v-if="info2.isShow" />

    <OInfo ref="oticketViewRef" :info="info3" @getlist="getList" v-if="info3.isShow" />
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "../ready/info.vue";
import Delay from "../ready/delay.vue";
import Plus from "../ready/plus.vue";
import OInfo from "../ready/openstack/info.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { getTicketList, deleteTicket } from "@/api/backend/tickets";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import router from "@/router";
import { userStore } from "@/store/user";
import { indexStore } from "@/store";
import { menuStore } from "@/store/menu";
const user_store = userStore()
const { proxy } = getCurrentInstance();
const props = defineProps({
    isList:{
        type:Object,
        default(){
            return true
        }
    },
})
const selectedKeys = ref(['0'])
const visible = ref<boolean>(false);
const ticketViewRef = ref(null);
const delayViewRef = ref(null);
const plusViewRef = ref(null);
const oticketViewRef = ref(null);
const loading = ref(false);
const ticketlist = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false,
    isView:false
})
const info1 = reactive({
    isAdd:true,
    isShow:false,
    isView:false
})
const info2 = reactive({
    isAdd:true,
    isShow:false,
    isView:false
})
const info3 = reactive({
    isAdd:true,
    isShow:false,
    isView:false
})
const defaultform = {
    createUserId:user_store.userId,
    pageIndex:1,
    pageSize:10,
    state: undefined,
    time:undefined,
    ticketTitle: "",
    ticketType: undefined,
}
const searchform = reactive({
    createUserId:user_store.userId,
    pageIndex:1,
    pageSize:10,
    state: undefined,
    time:undefined,
    ticketTitle: "",
    ticketType: undefined,
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
  {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '标题', dataIndex: 'ticketTitle', slots: { customRender: 'ticketTitle' }, key: 'id',align:'center'},
    { title: "类型", dataIndex: "ticketType", slots: { customRender: "ticketType" }, key: "id", align: "center" },
    {title: '状态', dataIndex: 'state', slots: { customRender: 'state' }, key: 'id',align:'center'},
    {title: '办理人', dataIndex: 'transactUserName', key: 'id',align:'center'},
    {title: '创建时间', dataIndex: 'createTime', key: 'id',align:'center'},
    props.isList ? {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' }, width:130 } : {}
];
// const stateChange = (e)=> {
//   if(e != '2')
//   // searchform.time = '1';
//   // else
//   searchform.time = undefined;
// }
// const timeChange = (e,b) => {
//   if(e)
//   searchform.state = '2';
//   else
//   searchform.state = undefined;
// }
const getList = async () => {
    ticketlist.value = await proxy.$getList(loading, getTicketList, searchform, pagination, getList )
}
const handleAdd = () => {
    let param = selectedKeys.value[0];
    // info.isAdd = info.isShow = true;
    visible.value = false;
    if(param == 0){
        router.push({path:'/admin/tickets/my/ticketadd'})
        indexStore().addTag({menuId:'虚机申请',name:'虚机申请',url:'ticketadd'})
    }else if(param == 1){
        router.push({path:'/admin/tickets/my/hostdelay',query:{param}})
        indexStore().addTag({menuId:'虚机延期',name:'虚机延期',url:'hostdelay',query:{param}})
    }else if(param == 2){
        router.push({path:'/admin/tickets/my/hostplus',query:{param}})
        indexStore().addTag({menuId:'虚机扩容',name:'虚机扩容',url:'hostplus',query:{param}})
    }
    selectedKeys.value = ['0'];
}
const handleView = (record) => {
    // info.isAdd = false;
    // info.isShow = true;
    // // Object.assign(ticketDialog.value.ticketform, record)
    // indexStore().addTag({menuId:'查看工单',name:'查看工单',url:'ticketview',query:{ticketId:record.id}})
    // router.push({path:'/admin/ticketview',query:{ticketId:record.id}})
    if(props.isList){
      if(record.ticketType == 2){
        info1.isView = info1.isShow = true;
        proxy.$nextTick(() => {
          delayViewRef.value.getInfo(record.id);
        });
      }else if(record.ticketType == 3){
        info2.isView = info2.isShow = true;
        proxy.$nextTick(() => {
          plusViewRef.value.getInfo(record.id);
        });
      }else{
        if(record.ticketTaskEntityList?.[0]?.cloudType == 1){
          info3.isShow = true;
          info3.isView = true;
          proxy.$nextTick(() => {
            oticketViewRef.value.getinfo(record.id,null,record);
          });
        }else{
          info.isShow = true;
          info.isView = true;
          proxy.$nextTick(() => {
            ticketViewRef.value.getInfo(record.ticketId, record.id);
          });
        }
      }
    }else{
      menuStore().setMenuId('tickets')
      proxy.$mitt.emit('setLeftMenu',true)
      indexStore().addTag({menuId:'查看工单',name:'查看工单',url:'ticketview',query:{ticketId:record.id,ticketType:record.ticketType}})
      router.push({path:'/admin/ticketview',query:{ticketId:record.id,ticketType:record.ticketType}})
    }
    
    // info.isView = info.isShow = true;
    // proxy.$nextTick(() => {
    // ticketViewRef.value.getInfo(record.id);
    // })
}
const onSearch = () => {
  searchform.pageIndex = 1;
  searchform.pageSize = pagination.pageSize
  getList()
 
};
const reset = () => {
  Object.assign(searchform,defaultform)
  // getList();
};
onMounted(() => {getList();
  nextTick(()=>{
      handleWidth()
  })
})
</script>
<style lang='scss' scoped>
.ant-menu-vertical{border-right: none;}
// .buttonPadding,.innerPadding{min-width: 950px;}
.uncloudRight{overflow-x: auto;}
// :deep(.ant-table td){ white-space: nowrap; }
// :deep(.ant-table th){ white-space: nowrap; }
</style>
