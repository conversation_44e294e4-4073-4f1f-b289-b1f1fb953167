<template>
    <div class="back-page">
        <a-page-header
            class="back-header1"
            title="虚机申请"
            @back="()=>{router.back()}"
        >
            <template #extra>
                <a-button  v-if="current > 0" style="margin-left: 8px" @click="prev">上一步</a-button>
                <a-button type="primary" @click="handleSave" :disabled="current != steps.length - 1">提交</a-button>
                <a-button v-if="current < steps.length - 1" type="primary" @click="next">下一步</a-button>
            </template>
        </a-page-header>
        <div class="back-content">
            <div class="steps-indicator">
                <a-steps :current="current" direction="vertical">
                    <a-step v-for="item in steps" :key="item.title" :title="item.title" />
                </a-steps>
            </div>
            <a-divider type="vertical" style="height:420px"></a-divider>
            <div class="steps-content">
                <StepContent ref="stepcontent" :info="{current:current,total:total}" @set_current="(e)=>{current = e;}" @select_cloud="selectCloud">
                    <template #ticket="{serverform}">
                        <!-- <div v-show="(total - current) == 6"> -->
                            <a-form-item id="serverForm" label="工单标题" name="ticketTitle" autoLink>
                                <a-input v-model:value="serverform.ticketTitle" allow-clear></a-input>
                            </a-form-item>
                            <a-form-item label="工单描述" name="ticketInfo" autoLink>
                                <a-textarea v-model:value="serverform.ticketInfo" allow-clear></a-textarea>
                            </a-form-item>
                            <a-form-item label="工单申请人" >
                                {{getUsername()}}
                            </a-form-item>
                            <a-form-item label="使用时间" name="endTime" autoLink>
                                <a-space direction="horizontal">
                                    <a-date-picker
                                    v-model:value="serverform.startTime"
                                    :disabled-date="(value)=>disabledStartDate(value,serverform.endTime)"
                                    format="YYYY-MM-DD"
                                    placeholder="开始时间"
                                    :allowClear="false"
                                    :getCalendarContainer="triggerNode => triggerNode.parentNode"
                                    />
                                    <a-date-picker
                                    v-model:value="serverform.endTime"
                                    :disabled-date="(value)=>disabledEndDate(value,serverform.startTime)"
                                    format="YYYY-MM-DD"
                                    placeholder="结束时间"
                                    :open="endOpen"
                                    @openChange="(open)=>{if(serverform.startTime){endOpen = open}else{message.warning('请先选择开始时间')}}"
                                    @change="()=>{nextTick(()=>{stepcontent.handleValidate('endTime')})}"
                                    :getCalendarContainer="triggerNode => triggerNode.parentNode"
                                    />
                                </a-space>
                            </a-form-item>
                        <!-- </div> -->
                    </template>
                    <template #cloud="{serverform}">
                        <!-- <div v-show="(total - current) == 5"> -->
                            <a-form-item label="云平台" name="cloudId" :extra="cloudInfo" autoLink>
                                <a-select
                                    v-model:value="serverform.cloudId"
                                    @change="(e,{option})=>changeCloud(e,option,serverform)"
                                    :getPopupContainer="triggerNode => triggerNode.parentNode"
                                    allowClear>
                                    <a-select-option v-for="(item,index) in cloudlist" :key="index" :value="item.id" :option="item">{{item.cloudName}}</a-select-option>
                                </a-select>
                            </a-form-item>
                            <a-form-item label="云平台项目" name="projectId" v-show="serverform.cloudId" autoLink>
                                <a-select v-model:value="serverform.projectId" @change="(e,option)=>onProjectChange(e,option,serverform)" placeholder="请选择" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                                    <a-select-option v-for="(item,index) in projectlist" :key="index" :value="item.id" :domainId="item.domainId" :label="item.projectName">{{item.projectName}}</a-select-option>
                                </a-select>
                            </a-form-item>
                        <!-- </div> -->
                    </template>
                </StepContent>
            </div>
        </div>
    </div>
</template>
<script lang='ts' setup>
import { selectCloudList } from "@/api/backend/cloud";
import { selectProjectList } from "@/api/backend/devops/project";
import StepContent from "@/components/stepcontent/StepContent.vue";
import { getUsername } from '@/utils/auth';
import { message } from "ant-design-vue";
import { getCurrentInstance, nextTick, reactive, ref } from "vue";
import router from '@/router';
import { saveTicket, updateTicket } from "@/api/backend/tickets";
const {proxy} = getCurrentInstance();
const current = ref(0);
const total = ref(6);
const stepcontent = ref();
const steps = [
    {
        title: '基本信息',
        label: 'ticket',
    },
    {
        title: '平台选择',
        label: 'cloud',
    },
    {
        title: '基础配置',
        label: 'base',
    },
    {
        title: '网络配置',
        label: 'network',
    },
    {
        title: '系统配置',
        label: 'system',
    },
    {
        title: '确认配置',
        label: 'confirm',
    }
];
const cloudInfo = ref('');
// const ticketform = reactive({
//     "cloudId": undefined,
//     "ticketTitle": "",
//     "ticketInfo": "",
//     // "transcatUserId": "",
//     "osType":"",
//     "diskSize":"",
//     "ramSize":"",
//     "cpuSize":"",
//     "osRemark":"",
//     "startTime" : null,
//     "endTime": null,
// })
const endOpen = ref<boolean>(false);
const disabledStartDate = (startValue: Moment,endTime) => {
    if (!startValue || !endTime) {
    return false;
    }
    return startValue.valueOf() > endTime.valueOf();
};

const disabledEndDate = (endValue: Moment,startTime) => {
    if (!endValue || !startTime) {
    return false;
    }
    return startTime.valueOf() >= endValue.valueOf();
};
const cloudlist = ref([]);
const projectlist = ref([]);
const selectCloud = () => {
  selectCloudList().then((res)=>{
    cloudlist.value = res.data;
  })
}
const selectProject = async (cloudId) => {
    let res = await selectProjectList({cloudId});
    if(res.code == 0){
        projectlist.value = res.data;
    }
}
const changeCloud = (e,option,serverform) => {
    // console.log("option",option)
    selectProject(e)
    serverform.cloudId = e;
    serverform.ticketOpenstackServerInfoEntity.cloudId = e;
    // localStorage.setItem('tcloudId',e)
    // Object.assign( srequestRef.value.ticketform, srequestRef.value.defaultform)
    // Object.assign( orequestRef.value.serverform, orequestRef.value.defaultform)
    // Object.assign( requestRef.value.ticketform, requestRef.value.defaultform)
        
    if(option){
        serverform.cloudType = option.cloudType;
        // if(option.cloudType == 1){
        //     srequestRef.value.ticketform.cloudId = e;
        //     srequestRef.value.ticketform.cloudName = option.cloudName;
        //     orequestRef.value.serverform.cloudId = e;
        //     orequestRef.value.getPrecloudId();
        //     disableChange()
        //     disabledSChange()
        // }else{
            
        //     requestRef.value.ticketform.cloudId = e;
        //     requestRef.value.ticketform.cloudName = b?.option.cloudName;
        //     disabled2SChange()
        // }
    }
    
    if(e){
        if(option.cloudInfo)
            cloudInfo.value = '注：'+option.cloudInfo;
    }else{
        cloudInfo.value = '';
        serverform.cloudType = undefined;
    }

}
const onProjectChange = (e,option,serverform) => {
    // console.log("option",option)
    serverform.projectName = option.label;
    serverform.ticketOpenstackServerInfoEntity.projectId = e;
    // if(e){
    //     ticketform.domainId = option.domainId;
    // }
    // let temp = {...defaultform};
    // temp.domainId = serverform.domainId;
    // temp.projectId = serverform.projectId;
    // console.log('step1',step1.value)
    // Object.assign(serverform, temp);
    // emit('disableChange');
    // getCurrprojectId();
}
const prev = () => {
    current.value--;
};
const next = () => {
    nextTick(()=>{
        stepcontent.value.next();
    })
};
const cancel = () => {
    nextTick(()=>{
        stepcontent.value.cancel();
    })
}
const handleSave = () => {
    
    nextTick(()=>{
        stepcontent.value.handleSave(
            (serverform,serverForm)=>{
                let tempform = {...serverform}
                tempform.startTime = tempform.startTime.format('YYYY-MM-DD')
                tempform.endTime = tempform.endTime.format('YYYY-MM-DD');

                let serverform1 = {...serverform.ticketOpenstackServerInfoEntity};
                // serverform1.cloudId = ticketform.cloudId;
                if(serverform1.source == 1 || serverform1.source == 2){
                    if(serverform1.volCreate === false){
                        serverform1.volDeleteOnInstanceDelete = undefined;
                        serverform1.volSize = undefined;
                    }else{
                        serverform1.volDeleteOnInstanceDelete = Number(serverform1.volDeleteOnInstanceDelete)
                    }
                    serverform1.imageId = serverform1.imageId[0];
                    serverform1.volCreate = Number(serverform1.volCreate);
                }else{
                    serverform1.volSize = undefined;
                    serverform1.volCreate = undefined;
                    serverform1.volDeleteOnInstanceDelete = Number(serverform1.volDeleteOnInstanceDelete)
                }
                if(serverform1.source == 3)
                serverform1.volumeId = serverform1.volumeId[0];
                if(serverform1.source == 4)
                serverform1.volumeSnapshotId = serverform1.volumeSnapshotId[0];
                serverform1.flavorId = serverform1.flavorId[0];
                serverform1.minFlavorId = serverform1.minFlavorId?serverform1.minFlavorId[0] : undefined;
                serverform1.maxFlavorId = serverform1.maxFlavorId ? serverform1.maxFlavorId[0] : undefined;
                serverform1.networkId = serverform1.networkId ? serverform1.networkId[0] : '';
                serverform1.groupIds = serverform1.groupIds ? serverform1.groupIds.join(',') : '';
                serverform1.keyPairId = serverform1.keyPairId ? serverform1.keyPairId.join(',') : '';
                serverform1.domainId = undefined;
                tempform.ticketOpenstackServerInfoEntity = serverform1;
                proxy.$handleSave(serverForm.value, saveTicket, updateTicket, tempform.id, tempform, ()=>{cancel();})
            });
    })
}
// const init = () => {
//     selectCloud();
// }
defineExpose({selectCloud})
</script>
<style lang='scss' scoped>
.steps-content{flex: 1;height: fit-content;}
:deep(.ant-steps-vertical .ant-steps-item){height: 70px;}
.back-content{height: calc(100vh - 170px);display: flex;}
.back-header1{background-color: #fff;}
</style>
