<template>
    <div class="back-page">
        <a-page-header
            class="back-header1"
            title="虚机申请"
            @back="()=>{router.back()}"
        >
            <template #extra>
                <a-button v-if="ccurrent==2 || ccurrent == 3" @click="prev">上一步</a-button>
                <a-button type="primary" @click="save" :disabled="(ticketform.cloudType == 1 && (disabledSave || disabledSNext)) || (ticketform.cloudType != 1 && disabled2SNext)">提交</a-button>
                <a-button :disabled="!ticketform.cloudType||(ticketform.cloudType && ticketform.cloudType == 1 && (ccurrent == 2 && disabledSNext) || (ccurrent == 3 && disabledNext))" @click="next" v-if="ccurrent == 1 || (ccurrent == 2 && ticketform.cloudType == 1) || (ccurrent == 3 && ticketform.cloudType == 1 && current != 7)">下一步</a-button>
            </template>
        </a-page-header>
        <div class="back-content">
            <a-form :model="ticketform" size="large" :label-col="{style:{width:'95px'}}" ref="ticketformRef" :rules="rules">
                <a-form-item label="云平台" name="cloudId" :extra="cloudInfo" v-if="ccurrent == 1">
                    <a-select
                        v-model:value="ticketform.cloudId"
                        @change="changeCloud"
                        allowClear>
                        <a-select-option v-for="(item,index) in options" :key="index" :value="item.id" :option="item">{{item.cloudName}}</a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
            <Request ref="requestRef" v-show="ccurrent == 2 && ticketform.cloudType != 1" @disabled2SChange="disabled2SChange" />
            <SRequest ref="srequestRef" v-show="ccurrent == 2 && ticketform.cloudType == 1" @disabledSChange="disabledSChange" />
            <ORequest ref="orequestRef" v-show="ccurrent == 3 && ticketform.cloudType == 1" @disableChange="disableChange" @currChange="(e)=>{current = e}" />
        </div>
    </div>
</template>
<script lang='ts' setup>
import Request from "./request.vue";
import SRequest from "./openstack_second.vue";
import ORequest from "./openstack_request.vue";
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { message } from "ant-design-vue";
import {selectFlavorList} from "@/api/backend/devops/flavor";
import {selectServerList} from "@/api/backend/devops/server";
import {saveTicket,updateTicket} from "@/api/backend/tickets";
import { useRoute } from 'vue-router';
import { getUsername } from '@/utils/auth';
import { getCloudUserlist } from '@/api/backend/cloud';
import { selectDictList } from '@/api/backend/systems/dictionary';
import { selectCloudList } from '@/api/backend/cloud';
import { indexStore } from '@/store';
import router from '@/router';
import vm from "@/assets/logo/vm.png";
import ali from "@/assets/logo/ali.png";
const index_store = indexStore()
const {proxy} = getCurrentInstance()
const route = useRoute();
const srequestRef = ref();
const requestRef = ref();
const orequestRef = ref();
const cloudInfo = ref('')
const ticketformRef = ref()
const serverlist = ref([]);
const flavorlist = ref([]);
const isSelected = ref(false);
const serverInfo = reactive({});
const userlist = ref([]);
const current = ref(0)
const ccurrent = ref(1)
const radioOptions = ref([
    {value:'1',className:'OPENSTACK',placeholder:'http://10.10.15.11:5000/v3'},
    {value:'13',className:'vm',url:vm,placeholder:'https://10.12.21.140/sdk'},
    // {value:'2',className:'ali'},
    // {value:'3',className:'tencent'},
    // {value:'4',className:'huawei'},
    // {value:'5',className:'amason'},
    // {value:'6',className:'micro'},
    // {value:'7',className:'jd'},
    // {value:'8',className:'uc'},
    // {value:'9',className:'qc'},
    // {value:'10',className:'baidu'},
    // {value:'11',className:'google'},
    // {value:'12',className:'gc'},
])
const defaultform = {
    "cloudId": undefined,
    "ticketTitle": "",
    "ticketInfo": "",
    // "transcatUserId": "",
    "osType":"",
    "diskSize":"",
    "ramSize":"",
    "cpuSize":"",
    "osRemark":"",
    "startTime" : null,
    "endTime": null,
}
const ticketform = reactive({
    "cloudId": undefined,
    "ticketTitle": "",
    "ticketInfo": "",
    // "transcatUserId": "",
    "osType":"",
    "diskSize":"",
    "ramSize":"",
    "cpuSize":"",
    "osRemark":"",
    "startTime" : null,
    "endTime": null,
})
const validateTime : any = (rule, value) => {
  if (!value) {
    // console.log('value',value)
    return Promise.reject("请选择时间");
  } else {
    // if (value && ticketform.startTime) {
    //       ticketformRef.value.validateFields('startTime');
    //     }
    return Promise.resolve();
  }
}
const rules = {
    cloudId:[{type:'number',required:true, message:'请选择',trigger:'change'}],
    ticketTitle:[{required:true, message:'请输入',trigger:'change'}],
    // transcatUserId:[{required:true, message:'请输入',trigger:'change'}],
    // osType:[{required:true, message:'请输入',trigger:'change'}],
    // diskSize:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    // ramSize:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    // cpuSize:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    endTime:[{required:true, validator:validateTime,trigger:'change'}],
}
const disabledNext = ref(true);
const disabledSNext = ref(true);
const disabled2SNext = ref(true);
const disabledSave = ref(true);
const endOpen = ref<boolean>(false);

const changeRadio = (e) => {
    srequestRef.value.selectCloudlist(ticketform.cloudType);
}
// const selectedRowKeys = ref()
const disabledStartDate = (startValue: Moment) => {
      if (!startValue || !ticketform.endTime) {
        return false;
      }
      return startValue.valueOf() > ticketform.endTime.valueOf();
    };

    const disabledEndDate = (endValue: Moment) => {
      if (!endValue || !ticketform.startTime) {
        return false;
      }
      return ticketform.startTime.valueOf() >= endValue.valueOf();
    };
const changeCloud = (e,b) => {
    localStorage.setItem('tcloudId',e)
    Object.assign( srequestRef.value.ticketform, srequestRef.value.defaultform)
    Object.assign( orequestRef.value.serverform, orequestRef.value.defaultform)
    Object.assign( requestRef.value.ticketform, requestRef.value.defaultform)
        
    if(b){
        ticketform.cloudType = b.option.cloudType;
        if(b.option.cloudType == 1){
            srequestRef.value.ticketform.cloudId = e;
            srequestRef.value.ticketform.cloudName = b.option.cloudName;
            orequestRef.value.serverform.cloudId = e;
            orequestRef.value.getPrecloudId();
            disableChange()
            disabledSChange()
        }else{
            
            requestRef.value.ticketform.cloudId = e;
            requestRef.value.ticketform.cloudName = b?.option.cloudName;
            disabled2SChange()
        }
    }
    
    if(e){
        if(b.option.cloudInfo)
            cloudInfo.value = '注：'+b.option.cloudInfo;
    }else{
        cloudInfo.value = '';
        ticketform.cloudType = undefined;
    }

}
const disableChange = () =>{
    // console.log('orequestRef.value.serverform.serverName',orequestRef.value.serverform.serverName)
    disabledNext.value = (!(orequestRef.value.serverform.domainId && orequestRef.value.serverform.projectId) && orequestRef.value.current == 0)
                    || (!(orequestRef.value.serverform.serverName && orequestRef.value.serverform.availabilityZone && orequestRef.value.serverform.ownerId && orequestRef.value.serverform.serverNumber) && orequestRef.value.current == 1)
                    || (!((orequestRef.value.serverform.imageId && orequestRef.value.serverform.source == 1 && orequestRef.value.serverform.volSize) || (orequestRef.value.serverform.imageId && orequestRef.value.serverform.source == 2 && orequestRef.value.serverform.volSize) || (orequestRef.value.serverform.volumeId && orequestRef.value.serverform.source == 3) || (orequestRef.value.serverform.volumeSnapshotId && orequestRef.value.serverform.source == 4)) && orequestRef.value.current == 2)
                    || (!(orequestRef.value.serverform.flavorId) && orequestRef.value.current == 3)
                    || (!orequestRef.value.serverform.networkId && orequestRef.value.current == 4);
    disabledSave.value = ((!(orequestRef.value.serverform.domainId && orequestRef.value.serverform.projectId))
                    || (!(orequestRef.value.serverform.serverName && orequestRef.value.serverform.availabilityZone && orequestRef.value.serverform.ownerId && orequestRef.value.serverform.serverNumber))
                    || (!((orequestRef.value.serverform.imageId && orequestRef.value.serverform.source == 1 && orequestRef.value.serverform.volSize) || (orequestRef.value.serverform.imageId && orequestRef.value.serverform.source == 2 && orequestRef.value.serverform.volSize) || (orequestRef.value.serverform.volumeId && orequestRef.value.serverform.source == 3) || (orequestRef.value.serverform.volumeSnapshotId && orequestRef.value.serverform.source == 4)))
                    || (!(orequestRef.value.serverform.flavorId))
                    || (!orequestRef.value.serverform.networkId)) && !((ccurrent==3) && ((current==7 || current==6 || current==5 || current==4) || orequestRef.value.serverform.networkId));
}
const disabledSChange = () => {
    disabledSNext.value = !(srequestRef.value.ticketform.cloudId && srequestRef.value.ticketform.ticketTitle && srequestRef.value.ticketform.endTime)
}
const disabled2SChange = () => {
    disabled2SNext.value = !(requestRef.value.ticketform.cloudId && requestRef.value.ticketform.ticketTitle && requestRef.value.ticketform.osType && requestRef.value.ticketform.diskSize && requestRef.value.ticketform.ramSize && requestRef.value.ticketform.cpuSize && requestRef.value.ticketform.endTime)
}
const prev = () => {
    if(ccurrent.value == 2){
        ccurrent.value = 1;
        if(ticketform.cloudType == 1){
            proxy.$nextTick(()=>{
                disabledSChange();
            })
        }else{
            proxy.$nextTick(()=>{
                disabled2SChange();
            })
        }
        
    }
    if(ccurrent.value == 3 && orequestRef.value.current == 0){
        ccurrent.value = 2;
        if(ticketform.cloudType == 1)
        proxy.$nextTick(()=>{
            disabledSChange();
        })
    }
    if(ccurrent.value == 3 && orequestRef.value.current>0){
        orequestRef.value.current--;
        current.value--;
        proxy.$nextTick(()=>{
        disableChange();
    })
    }
}

const next = () => {
    console.log('ccurrent.value',ccurrent.value)
    // console.log('ccurrent.value',ccurrent.value, orequestRef.value.current,orequestRef.value.serverform.serverNumber)
    
    if(ccurrent.value == 1 || ccurrent.value == 2){
        ccurrent.value++;
if(ticketform.cloudType == 1){
            proxy.$nextTick(()=>{
                disabledSChange();
            })
        }else{
            proxy.$nextTick(()=>{
                disabled2SChange();
            })
        }
    }
    
    else{
        orequestRef.value.current++;
        current.value++;
        proxy.$nextTick(()=>{
        disableChange();
    })
    }
    
    
}
    const select = (a,b) => {
        console.log('a,b',a,b)
        Object.assign(serverInfo, b.serverInfo)
        isSelected.value = true;
    }
    const clear = (e,c) =>{
        isSelected.value = false;
        console.log('c',e,c)
    }
    const getServerList = async () => {
    let res = await selectServerList()
    if(res){
        if(res.code == 0){
            // Object.assign(serverlist,res.data);
            serverlist.value = res.data;
        }
    }
}
const getFlavorList = async () => {
    let res = await selectFlavorList()
    if(res.code == 0){
        flavorlist.value = res.data;
    }
}
const queryworker = async () => {
    if(ticketform.cloudId){
        let res = await getCloudUserlist(ticketform.cloudId)
        if(res.code == 0)
        userlist.value = res.data;
    }
    
}
const options4 = ref([])
// 镜像os
const selectOs = async () => {
    let res = await selectDictList({dictType:'IMAGE_OS'})
    if(res.code == 0){
        options4.value = res.data;
    }
}
const save = () => {
    // console.log('ticketform',srequestRef.value.ticketform)
    if(ticketform.cloudType == 1){
        let tempform = {...srequestRef.value.ticketform}
        proxy.$handleSave(srequestRef.value.ticketformRef, saveTicket, updateTicket, tempform.id, tempform, ()=>{cancel();},null,()=>{
            tempform.startTime = tempform.startTime.format('YYYY-MM-DD')
            tempform.endTime = tempform.endTime.format('YYYY-MM-DD');
            let serverform1 = {...orequestRef.value.serverform};
            serverform1.cloudId = ticketform.cloudId;
            if(serverform1.source == 1 || serverform1.source == 2){
                if(serverform1.volCreate === false){
                    serverform1.volDeleteOnInstanceDelete = undefined;
                    serverform1.volSize = undefined;
                }else{
                    serverform1.volDeleteOnInstanceDelete = Number(serverform1.volDeleteOnInstanceDelete)
                }
                serverform1.imageId = serverform1.imageId[0];
                serverform1.volCreate = Number(serverform1.volCreate);
            }else{
                serverform1.volSize = undefined;
                serverform1.volCreate = undefined;
                serverform1.volDeleteOnInstanceDelete = Number(serverform1.volDeleteOnInstanceDelete)
            }
            if(serverform1.source == 3)
            serverform1.volumeId = serverform1.volumeId[0];
            if(serverform1.source == 4)
            serverform1.volumeSnapshotId = serverform1.volumeSnapshotId[0];
            serverform1.flavorId = serverform1.flavorId[0];
            serverform1.minFlavorId = serverform1.minFlavorId?serverform1.minFlavorId[0] : undefined;
            serverform1.maxFlavorId = serverform1.maxFlavorId ? serverform1.maxFlavorId[0] : undefined;
            serverform1.networkId = serverform1.networkId ? serverform1.networkId[0] : '';
            serverform1.groupIds = serverform1.groupIds ? serverform1.groupIds.join(',') : '';
            serverform1.keyPairId = serverform1.keyPairId ? serverform1.keyPairId.join(',') : '';
            serverform1.domainId = undefined;
            tempform.ticketOpenstackServerInfoEntity = serverform1;
        })
    }
    else{
        let tempform = {...requestRef.value.ticketform}
        proxy.$handleSave(requestRef.value.ticketformRef, saveTicket, updateTicket, tempform.id, tempform, ()=>{cancel();},null,()=>{
            tempform.startTime = tempform.startTime.format('YYYY-MM-DD')
            tempform.endTime = tempform.endTime.format('YYYY-MM-DD');
        })
    }
}
const cancel = () => {
    router.back();
    setTimeout(()=>{
        ccurrent.value = 1;
    })
    let index = index_store.get_activeIndex;
    index_store.delTagsItem(index);
    cloudInfo.value = '';
    ticketformRef.value.resetFields();
    Object.assign(ticketform,defaultform)
    // Object.assign(orequestRef.value.serverform,orequestRef.value.defaultform)
    if(ticketform.cloudType == 1)
    orequestRef.value.cancel();
    Object.assign(srequestRef.value.ticketform,srequestRef.value.defaultform)
}
const options = ref([])
const selectCloudlist = () => {
  selectCloudList().then((res)=>{
    options.value = res.data;
  })
}
onMounted(() => {getServerList();getFlavorList();selectOs();selectCloudlist();proxy.$mitt.on('infoCancel',cancel);})
</script>
<style lang='scss' scoped>
.delay-content{padding:20px 150px;background-color:#fff;border-top: 2px solid #f0f2f5;}
.contentPadding{position: relative;}
.sub-btn{
    // margin-top: 50px;
    position: absolute;
    right: 60px;
    top: 600px;
}
:deep(.ant-form-item-extra){color:#ff4d4f}
.ant-radio-button-wrapper{
    width: 162px;
    height: 76px;
    margin: 10px;
    background-position:50%;
    background-repeat:no-repeat;
    
    background-size:132px;
    background-color:#fff;
    border: 1px solid #d9d9d9;
    &.OPENSTACK{
        background-image:url(@/assets/logo/OPENSTACK.jpeg);
    }
    &.amason{
        background-size: 66px;
    }
    &.ant-radio-button-wrapper-checked{
        background-color: rgb(24 144 255 / 8%);
        background-blend-mode: multiply;
        box-shadow: none;
        border-color: rgb(24 144 255);
    }
    &::before{width: 0;}
}
.back-content{height: calc(100vh - 170px);}
.back-header1{background-color: #fff;}
</style>
