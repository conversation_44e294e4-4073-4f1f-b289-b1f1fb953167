<template>
    <!-- <div class="back-page"> -->
        <!-- <div class="back-header"> -->
            <!-- <a-button @click="router.back()" v-show="route.path=='/admin/ticketview'">返回</a-button> -->
            <!-- <div></div>
            <div>
                <a-button type="primary" @click="save">提交</a-button> -->
                <!-- <a-button @click="handleSave('驳回')">驳回</a-button> -->
            <!-- </div>
        </div> -->
        <!-- <div class="back-content"> -->
            <a-form :model="ticketform" size="large" :label-col="{style:{width:'95px'}}" ref="ticketformRef" :rules="rules">
                <a-form-item label="云平台" name="cloudId">
                    {{ticketform.cloudName}}
                </a-form-item>
                <a-form-item label="工单标题" name="ticketTitle">
                    <a-input v-model:value="ticketform.ticketTitle" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="工单描述" name="ticketInfo">
                    <a-textarea v-model:value="ticketform.ticketInfo" allow-clear></a-textarea>
                </a-form-item>
                <!-- 等待 -->
                <a-form-item label="工单申请人" >
                    {{getUsername()}}
                </a-form-item>
                <!-- <a-form-item label="工单处理人" name="transcatUserId" >
                    <a-select v-model:value="ticketform.transcatUserId" :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}" show-search allow-clear>
                        <a-select-option v-for="(item,index) in userlist" :key="index" :value="item.userId+''">{{item.userName}}</a-select-option>
                    </a-select>
                </a-form-item> -->
                <!-- <a-row>
                    <a-col :span="11" :offset="0">
                        <a-form-item label="系统类型" name="osType">
                    <a-select v-model:value="ticketform.osType"> 
                        <a-select-option v-for="(item,index) in options4" :key="index" :value="item.dictValue" >{{item.dictLabel}}</a-select-option>
                    </a-select>
                </a-form-item>
                    </a-col>
                    <a-col :span="11" :offset="2">
                        <a-form-item label="磁盘大小" name="diskSize">
                    <a-input-number v-model:value.trim="ticketform.diskSize" :min="0" style="width:calc(100% - 25px)"></a-input-number> GB
                </a-form-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="11" :offset="0">
                        <a-form-item label="内存大小" name="ramSize">
                    <a-input-number v-model:value.trim="ticketform.ramSize" :min="0" style="width:calc(100% - 25px)"></a-input-number> MB
                </a-form-item>
                    </a-col>
                    <a-col :span="11" :offset="2">
                        <a-form-item label="CPU核数" name="cpuSize">
                    <a-input-number v-model:value.trim="ticketform.cpuSize" :min="0" :step="1" style="width:100%"></a-input-number>
                </a-form-item>
                    </a-col>
                </a-row>
                
                
                
                <a-form-item label="系统备注" name="osRemark">
                    <a-textarea v-model:value="ticketform.osRemark"></a-textarea>
                </a-form-item> -->

                <a-form-item label="使用时间" name="endTime">
                    <a-space direction="horizontal">
                        <a-date-picker
                        :locale="locale"
                        v-model:value="ticketform.startTime"
                        :disabled-date="disabledStartDate"
                        format="YYYY-MM-DD"
                        placeholder="开始时间"
                        :allowClear="false"
                        />
                        <a-date-picker
                        :locale="locale"
                        v-model:value="ticketform.endTime"
                        :disabled-date="disabledEndDate"
                        format="YYYY-MM-DD"
                        placeholder="结束时间"
                        :open="endOpen"
                        @openChange="(open)=>{if(ticketform.startTime){endOpen = open}else{message.warning('请先选择开始时间')}}"
                        />
                    </a-space>
                </a-form-item>


                <!-- <a-form-item label="虚机类型" v-else>
                    <a-select
                        v-model:value="ticketform.flavorId"
                        placeholder="请选择"
                        allowClear>
                        <a-select-option v-for="(item,index) in flavorlist" :key="index" :value="Number(item.id)">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="虚机" name="serverId">
                    <a-select
                        v-model:value="ticketform.serverId"
                        placeholder="请选择"
                        @select="select"
                        @clear="clear"
                        allowClear>
                        <a-select-option v-for="(item,index) in serverlist" :key="index" :serverInfo="item" :value="Number(item.id)">{{item.serverName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item>
                    <a-descriptions title="虚机基本信息" v-if="isSelected">
                        <a-descriptions-item label="虚机名称">{{serverInfo.serverName}}</a-descriptions-item>
                        <a-descriptions-item label="所有者">{{serverInfo.ownerName}}</a-descriptions-item>
                        <a-descriptions-item label="镜像">{{serverInfo.imageEntity?.name}}</a-descriptions-item>
                        <a-descriptions-item label="类型">{{serverInfo.flavorEntity?.name}}</a-descriptions-item>
                        <a-descriptions-item label="ip地址" v-if="serverInfo.serverAddressEntityList">
                         <span v-for="(item,index) in serverInfo.serverAddressEntityList" :key="index">{{item.addr}}<br/></span>
                        </a-descriptions-item>
                        <a-descriptions-item label="状态">{{serverInfo.statusText}}</a-descriptions-item>
                    </a-descriptions>
                </a-form-item> -->
                <!-- <a-button class="sub-btn" type="primary" @click="save">提交</a-button> -->
            </a-form>
        <!-- </div>
        
    </div> -->
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref, watch } from 'vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { message } from "ant-design-vue";
import {selectFlavorList} from "@/api/backend/devops/flavor";
import {selectServerList} from "@/api/backend/devops/server";
import {saveTicket,updateTicket} from "@/api/backend/tickets";
import { useRoute } from 'vue-router';
import { getUsername } from '@/utils/auth';
import { getCloudUserlist } from '@/api/backend/cloud';
import { selectDictList } from '@/api/backend/systems/dictionary';
import { selectCloudList } from '@/api/backend/cloud';
import { indexStore } from '@/store';
import router from '@/router';
const index_store = indexStore()
const {proxy} = getCurrentInstance()
const route = useRoute();
const cloudInfo = ref('')
const ticketformRef = ref()
const serverlist = ref([]);
const flavorlist = ref([]);
const isSelected = ref(false);
const serverInfo = reactive({});
const userlist = ref([]);
const emit = defineEmits(['disabledSChange'])
const defaultform = {
    "cloudId": localStorage.getItem('tcloudId'),
    "ticketTitle": "",
    "ticketInfo": "",
    "transcatUserId": "",
    "startTime" : null,
    "endTime": null,
}
const ticketform = reactive({
    "cloudId": localStorage.getItem('tcloudId'),
    "ticketTitle": "",
    "ticketInfo": "",
    "transcatUserId": "",
    "startTime" : null,
    "endTime": null,
})
const validateTime : any = (rule, value) => {
  if (!value) {
    // console.log('value',value)
    return Promise.reject("请选择时间");
  } else {
    // if (value && ticketform.startTime) {
    //       ticketformRef.value.validateFields('startTime');
    //     }
    return Promise.resolve();
  }
}
const rules = {
    ticketTitle:[{required:true, message:'请输入',trigger:'change'}],
    endTime:[{required:true, validator:validateTime,trigger:'change'}],
}
const endOpen = ref<boolean>(false);
// const selectedRowKeys = ref()
const disabledStartDate = (startValue: Moment) => {
      if (!startValue || !ticketform.endTime) {
        return false;
      }
      return startValue.valueOf() > ticketform.endTime.valueOf();
    };

    const disabledEndDate = (endValue: Moment) => {
      if (!endValue || !ticketform.startTime) {
        return false;
      }
      return ticketform.startTime.valueOf() >= endValue.valueOf();
    };
const changeCloud = (e,b) => {
    console.log('e,v',e,b)
    if(e && b.cloudInfo)
    cloudInfo.value = '注：'+b.cloudInfo;
    else
    cloudInfo.value = ''
    if(e)
    queryworker();
    else
    userlist.value = [];
}
    const select = (a,b) => {
        console.log('a,b',a,b)
        Object.assign(serverInfo, b.serverInfo)
        isSelected.value = true;
    }
    const clear = (e,c) =>{
        isSelected.value = false;
        console.log('c',e,c)
    }
    const getServerList = async () => {
    let res = await selectServerList()
    if(res){
        if(res.code == 0){
            // Object.assign(serverlist,res.data);
            serverlist.value = res.data;
        }
    }
}
const getFlavorList = async () => {
    let res = await selectFlavorList()
    if(res.code == 0){
        flavorlist.value = res.data;
    }
}
const queryworker = async () => {
    if(ticketform.cloudId){
        let res = await getCloudUserlist(ticketform.cloudId)
        if(res.code == 0)
        userlist.value = res.data;
    }
    
}
const options4 = ref([])
// 镜像os
const selectOs = async () => {
    let res = await selectDictList({dictType:'IMAGE_OS'})
    if(res.code == 0){
        options4.value = res.data;
    }
}
const save = () => {
    let tempform = {...ticketform}
    proxy.$handleSave(ticketformRef.value, saveTicket, updateTicket, ticketform.id, tempform, ()=>{cancel();},null,()=>{
        tempform.startTime = tempform.startTime.format('YYYY-MM-DD')
        tempform.endTime = tempform.endTime.format('YYYY-MM-DD')
    })
}
const cancel = () => {
    let index = index_store.get_activeIndex;
    index_store.delTagsItem(index);
    cloudInfo.value = '';
    ticketformRef.value.resetFields();
    router.back()
    Object.assign(ticketform,defaultform)
}
const options = ref([])
const selectCloudlist = (cloudType) => {
  selectCloudList({cloudType}).then((res)=>{
    options.value = res.data;
  })
}
watch(
        // 这种写法不会侦听到 props 中 test 的变化
    	ticketform,
        () => {
            console.log("侦听成功");
            emit('disabledSChange');
            emit('disableChange')
        }
    )
onMounted(() => {getServerList();getFlavorList();selectOs();proxy.$mitt.on('infoCancel',cancel);})
defineExpose({ticketform,defaultform,ticketformRef,selectCloudlist});
</script>
<style lang='scss' scoped>
// .contentPadding{position: relative;}
// .delay-content{position: absolute;top: 50%;left: 50%;margin-top: -111px;margin-left: -238px;}
.delay-content{padding:20px 150px;background-color:#fff;border-top: 2px solid #f0f2f5;}
.contentPadding{position: relative;}
.sub-btn{
    // margin-top: 50px;
    position: absolute;
    right: 60px;
    top: 600px;
}
:deep(.ant-form-item-extra){color:#ff4d4f}
</style>
