<template>
    <div class="back-page">
        <!-- <div v-show="route.path=='/admin/ticketedit'" style="padding:20px;background-color:#fff;display:flex;justify-content:space-between;border-bottom:2px solid #f0f2f5">
            <div></div>
            <div>
                <a-button @click="handleSave('驳回')">驳回</a-button>
                <a-button type="primary" @click="handleSave('提交')">提交</a-button>
            </div>
        </div> -->
        <a-page-header
            class="back-header1"
            :title="ticketform.ticketTitle"
            @back="()=>{router.back()}"
        >
        <template #extra>
            <a-button type="primary" v-show="route.path=='/admin/ticketedit'" @click="handleSave('驳回')">驳回</a-button>
            <a-button type="primary" v-show="route.path=='/admin/ticketedit'" @click="handleSave('提交')">提交</a-button>
        </template>
        </a-page-header>
        <div :style="{height: 'calc(100vh - 170px)',overflowY:'auto'}">
            <div class="delay-content" v-if="route.query.ticketType == 1">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">基础信息</div>
                </div>
            <a-form :model="ticketform" size="large" :label-col="{span:5}">
                <a-form-item label="云平台" name="cloudId">
                        {{ticketform.cloudName}}
                    </a-form-item>
                <a-form-item label="工单标题" name="ticketTitle">
                    {{ticketform.ticketTitle}}
                </a-form-item>
                <a-form-item label="工单描述" name="ticketInfo">
                    {{ticketform.ticketInfo ? ticketform.ticketInfo :'-'}}
                </a-form-item>
                <!-- 等待 -->
                <a-row>
                    <a-col :span="10" :offset="3">
                        <a-form-item label="工单申请人">
                    {{ticketform.createUserName}}
                </a-form-item>
                    </a-col>
                    <a-col :span="9" :offset="0">
                        <a-form-item label="申请时间" >
                    {{ticketform.createTime}}
                </a-form-item>
                    </a-col>
                </a-row>
                
                <a-row>
                    <a-col :span="9" :offset="3">
                        <a-form-item label="系统类型" name="osType">
                    {{ticketform.osTypeText ? ticketform.osTypeText : '-'}}
                </a-form-item>
                    </a-col>
                    <a-col :span="9" :offset="1">
                        <a-form-item label="磁盘大小" name="diskSize">
                    {{ticketform.diskSize ? ticketform.diskSize+'G' : '-'}}
                </a-form-item>
                    </a-col>
                </a-row>
                
                <a-row>
                    <a-col :span="9" :offset="3">
                        <a-form-item label="内存大小" name="ramSize">
                    {{ticketform.ramSize ? ticketform.ramSize+'G' : '-'}}
                </a-form-item>
                    </a-col>
                    <a-col :span="9" :offset="1">
                        <a-form-item label="CPU核数" name="cpuSize">
                    {{ticketform.cpuSize ? ticketform.cpuSize : '-'}}
                </a-form-item>
                    </a-col>
                </a-row>
                
                
                
                <a-form-item label="系统备注" name="osRemark">
                    {{ticketform.osRemark ? ticketform.osRemark : '-'}}
                </a-form-item>
                <a-form-item label="使用时间" name="endTime">
                    {{ticketform.startTime ? ticketform.startTime.substr(0,10) :'-'}} 至 {{ticketform.endTime ? ticketform.endTime.substr(0,10) : '-'}}
                </a-form-item>
                </a-form>
                <a-form :model="readyform" size="large" :label-col="{span:5}" ref="readyformRef" :rules="rules">

                
                <a-form-item label="虚机" name="serverId">
                    <a-select
                        v-if="route.path=='/admin/ticketedit' && ticketTaskEntityList&&ticketTaskEntityList.length>0"
                        v-model:value="readyform.serverId"
                        placeholder="请选择"
                        @select="select"
                        @clear="clear"
                        :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}"
                        show-search
                        allowClear>
                        <a-select-option v-for="(item,index) in serverlist" :key="index" :serverInfo="item" :value="Number(item.id)">{{item.serverName}}</a-select-option>
                    </a-select>
                    <a @click.prevent="getConsoleURL(ticketform.serverId)" v-else>{{ticketform.serverName ? ticketform.serverName : '-'}}</a>
                </a-form-item>
                <a-row v-if="readyform.serverId">
                    <a-col :span="9" :offset="3">
                        <a-form-item label="虚机账号" name="userName">
                            <a-input v-model:value="readyform.userName" placeholder="请输入账号" v-if="route.path=='/admin/ticketedit' && ticketTaskEntityList&&ticketTaskEntityList.length>0" allow-clear></a-input>
                            <span v-else>{{ticketform.userName}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="9" :offset="1">
                        <a-form-item label="密码" name="password" >
                            <a-input v-model:value="readyform.password" placeholder="请输入密码" v-if="route.path=='/admin/ticketedit' && ticketTaskEntityList&&ticketTaskEntityList.length>0" allow-clear/>
                            <span v-else>{{ticketform.password}}</span>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
            </div>
            <div class="delay-content" v-if="route.query.ticketType == 2">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">基础信息</div>
                </div>

                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="工单标题" :span="2">
                        {{ticketform.ticketTitle}}
                    </a-descriptions-item>
                    <a-descriptions-item label="工单描述" :span="2">
                        {{ticketform.ticketInfo ? ticketform.ticketInfo :'-'}}
                    </a-descriptions-item>
                    <a-descriptions-item label="工单申请人" :span="2">
                        {{ticketform.createUserName}}
                    </a-descriptions-item>

                    <a-descriptions-item label="申请时间">
                        {{ticketform.createTime}}
                    </a-descriptions-item>
                    <a-descriptions-item label="截止日期">
                        {{ticketform.endTime?.substr(0,10)}}
                    </a-descriptions-item>

                    <a-descriptions-item label="虚机名称" :span="2">
                        <a @click.prevent="getConsoleURL(ticketform.serverId)">{{ticketform.serverName ? ticketform.serverName : '-'}}</a>
                    </a-descriptions-item>
                    <a-descriptions-item label="虚机基本信息" :span="2">
                        <a-descriptions :column="2" :label-style="{width:'120px'}" :contentStyle="{width:'254px'}" bordered>
                            <a-descriptions-item label="所属云平台">{{ticketform.cloudName}}</a-descriptions-item>
                            <a-descriptions-item label="磁盘大小">{{ticketform.before.disk}}</a-descriptions-item>
                            <a-descriptions-item label="CPU核数">{{ticketform.before.vcpus}}</a-descriptions-item>
                            <a-descriptions-item label="内存大小">{{ticketform.before.ram}}</a-descriptions-item>
                        </a-descriptions>
                    </a-descriptions-item>

                    <!-- <a-descriptions-item label="所属云平台">
                        {{ticketform.cloudName}}
                    </a-descriptions-item>
                    <a-descriptions-item label="磁盘大小">
                        {{ticketform.totalDisk ? ticketform.totalDisk + ' G' : '-'}}
                    </a-descriptions-item>
                    <a-descriptions-item label="CPU核数">
                        {{ticketform.vcpus ? vcpus : '-'}}
                    </a-descriptions-item>
                    <a-descriptions-item label="内存大小">
                        {{ticketform.ram}}
                    </a-descriptions-item> -->
                    
                    
                    
                    
                    <a-descriptions-item label="审批意见" :span="2" v-if="route.path=='/admin/ticketedit' && ticketTaskEntityList&&ticketTaskEntityList.length>0">
                        <a-form :model="readyform" size="large" :label-col="{span:6}" ref="readyformRef" :rules="rules">
                            <a-form-item name="opinion">
                                <a-textarea v-model:value="readyform.opinion" placeholder="请输入审批意见(非必填)" style="width:750px" allow-clear></a-textarea>
                            </a-form-item>
                        </a-form>
                    </a-descriptions-item>
                </a-descriptions>

            <!-- <a-form :model="ticketform" size="large" :label-col="{span:6}">
                    <a-form-item label="虚机名称" name="cloudId">
                        <a @click.prevent="getConsoleURL(ticketform.serverId)">{{ticketform.serverName ? ticketform.serverName : '-'}}</a>
                    </a-form-item>
                <a-row justify="space-around">
                            <a-form-item label="工单标题" :label-col="{style:'width:120px'}" name="ticketTitle">
                                {{ticketform.ticketTitle}}
                            </a-form-item>
                            <a-form-item label="工单申请人" :label-col="{style:'width:120px'}">
                                {{ticketform.createUserName}}
                            </a-form-item>
                    </a-row>
                
                <a-row justify="space-around">
                    <a-form-item label="延期截止时间" :label-col="{style:'width:120px'}" name="endTime">
                        {{ticketform.endTime?.substr(0,10)}}
                    </a-form-item>
                        <a-form-item label="申请时间" :label-col="{style:'width:120px'}">
                    {{ticketform.createTime}}
                </a-form-item>
                </a-row>
                <a-form-item label="工单描述" name="ticketInfo">
                    {{ticketform.ticketInfo ? ticketform.ticketInfo :'-'}}
                </a-form-item>
                </a-form>
                <a-form :model="readyform" size="large" :label-col="{span:6}" ref="readyformRef" :rules="rules" v-if="route.path=='/admin/ticketedit' && ticketTaskEntityList&&ticketTaskEntityList.length>0">
                    <a-form-item label="审批意见" name="opinion">
                        <a-textarea v-model:value="readyform.opinion"></a-textarea>
                    </a-form-item> 
            </a-form> -->
            </div>
            <div class="delay-content" v-if="route.query.ticketType == 3">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">基础信息</div>
                </div>

                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="工单标题" :span="2">
                        {{ticketform.ticketTitle}}
                    </a-descriptions-item>
                    <a-descriptions-item label="工单描述" :span="2">
                        {{ticketform.ticketInfo ? ticketform.ticketInfo :'-'}}
                    </a-descriptions-item>
                    <a-descriptions-item label="工单申请人">
                        {{ticketform.createUserName}}
                    </a-descriptions-item>
                    <a-descriptions-item label="申请时间">
                        {{ticketform.createTime}}
                    </a-descriptions-item>
                    <a-descriptions-item label="虚机名称" :span="2">
                        <a @click.prevent="getConsoleURL(ticketform.serverId)">{{ticketform.serverName ? ticketform.serverName : '-'}}</a>
                    </a-descriptions-item>
                    <a-descriptions-item label="虚机基本信息(扩容前)" :span="2">
                        <a-descriptions :column="2" :label-style="{width:'120px'}" :contentStyle="{width:'254px'}" bordered>
                            <a-descriptions-item label="所属云平台">{{ticketform.cloudName}}</a-descriptions-item>
                            <a-descriptions-item label="磁盘大小">{{ticketform.before.disk}}</a-descriptions-item>
                            <a-descriptions-item label="CPU核数">{{ticketform.before.vcpus}}</a-descriptions-item>
                            <a-descriptions-item label="内存大小">{{ticketform.before.ram}}</a-descriptions-item>
                        </a-descriptions>
                    </a-descriptions-item>
                    <a-descriptions-item label="虚机基本信息(扩容后)" :span="2">
                        <a-descriptions :column="2" :label-style="{width:'120px'}" :contentStyle="{width:'254px'}" bordered>
                            <a-descriptions-item label="所属云平台">{{ticketform.cloudName}}</a-descriptions-item>
                            <a-descriptions-item label="磁盘大小">{{ticketform.after.totalDisk}}</a-descriptions-item>
                            <a-descriptions-item label="CPU核数">{{ticketform.after.vcpus}}</a-descriptions-item>
                            <a-descriptions-item label="内存大小">{{ticketform.after.ram}}</a-descriptions-item>
                        </a-descriptions>
                    </a-descriptions-item>
                    <a-descriptions-item label="审批意见" :span="2" v-if="route.path=='/admin/ticketedit' && ticketTaskEntityList&&ticketTaskEntityList.length>0">
                        <a-form :model="readyform" size="large" :label-col="{span:6}" ref="readyformRef" :rules="rules">
                            <a-form-item name="opinion">
                                <a-textarea v-model:value="readyform.opinion" placeholder="请输入审批意见(非必填)" style="width:750px" allow-clear></a-textarea>
                            </a-form-item> 
                        </a-form>
                    </a-descriptions-item>
                </a-descriptions>

            <!-- <a-form :model="ticketform" size="large" :label-col="{span:6}">
                    <a-form-item label="虚机名称" name="cloudId">
                        <a @click.prevent="getConsoleURL(ticketform.serverId)">{{ticketform.serverName ? ticketform.serverName : '-'}}</a>
                    </a-form-item>
                    <a-descriptions-item label="虚机类型" v-if="ticketform.cloudType == 1">
                        {{ticketform.flavorName ? ticketform.flavorName : '-'}}
                    </a-descriptions-item>
                    <a-form-item label="虚机类型" name="flavorName">
                    {{ticketform.flavorName}}
                </a-form-item>
                <a-form-item label="工单标题" name="ticketTitle">
                    {{ticketform.ticketTitle}}
                </a-form-item>
                <a-form-item label="工单描述" name="ticketInfo">
                    {{ticketform.ticketInfo ? ticketform.ticketInfo :'-'}}
                </a-form-item>
                <a-row>
                    <a-col :span="12" :offset="3">
                        <a-form-item label="工单申请人">
                    {{ticketform.createUserName}}
                </a-form-item>
                    </a-col>
                    <a-col :span="7" :offset="0">
                        <a-form-item label="申请时间" >
                    {{ticketform.createTime}}
                </a-form-item>
                    </a-col>
                </a-row>
                
                <a-row>
                    <a-col :span="12" :offset="3">
                        <a-form-item label="扩容后磁盘大小" name="diskSize" >
                    {{ticketform.diskSize ? ticketform.diskSize+'G' : '-'}}
                </a-form-item>
                    </a-col>
                    <a-col :span="7" :offset="0">
                        <a-form-item label="扩容后内存大小" name="ramSize">
                    {{ticketform.ramSize ? ticketform.ramSize+'G' : '-'}}
                </a-form-item>
                    </a-col>
                </a-row>
                
                <a-row>
                    <a-col :span="12" :offset="3">
                        <a-form-item label="扩容后CPU核数" name="cpuSize">
                    {{ticketform.cpuSize ? ticketform.cpuSize : '-'}}
                </a-form-item>
                    </a-col>
                </a-row>
                </a-form>
                <a-form :model="readyform" size="large" :label-col="{span:6}" ref="readyformRef" :rules="rules" v-if="route.path=='/admin/ticketedit' && ticketTaskEntityList&&ticketTaskEntityList.length>0">
                    <a-form-item label="审批意见" name="opinion">
                        <a-textarea v-model:value="readyform.opinion"></a-textarea>
                    </a-form-item> 
            </a-form> -->
            </div>
            <div class="delay-content" style="border-top: 2px solid #f0f2f5">
                    <div class="ant-descriptions-header">
                        <div class="ant-descriptions-title">审批记录</div>
                    </div>
                    <div class="ready-part">
                        <a-timeline>
                            <template v-for="(item,index) in ticketTaskEntityList" :key="index">
                                <a-timeline-item v-if="item.state==2">{{item.taskStep+'&nbsp;&nbsp;'+item.transactUserName+'&nbsp;&nbsp;'+item.transactTime}}
                                    <p>{{item.opinion}}</p>
                                </a-timeline-item>
                            </template>
                        </a-timeline>
                    </div>
                    
                </div>
        <!-- </div> -->
        </div>
        
        
    </div>
</template>
<script lang='ts' setup>
import delayIndex from "./delay_index.vue";
import { getConsoleUrl, selectServerInfo, selectServerList } from '@/api/backend/devops/server';
import { getTicketInfo, getTickettaskInfo, updateTickettask } from '@/api/backend/tickets';
import router from '@/router';
import { indexStore } from '@/store';
import { userStore } from '@/store/user';
import { message } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { selectCloudInfo } from "@/api/backend/cloud";
import { selectFlavorInfo } from "@/api/backend/devops/flavor";
import emiter from "@/utils/Bus";
const index_store = indexStore()
const route = useRoute();
const readyformRef = ref();
const ticketform = reactive({
    cloudType:1,
    before:{},
    after:{}
});
const defaultform = {
  "serverId": undefined,
  "executeType": 1,
  "id": Number(route.query.id),
  "opinion": "",
  "ticketId": Number(route.query.ticketId)
}
const readyform = reactive({
  "serverId": undefined,
  "executeType": 1,
  "id": Number(route.query.id),
  "opinion": "",
  "ticketId": Number(route.query.ticketId)
})
const rules = {
    serverId:[{type:'number',required:(route.path=='/admin/ticketedit'), message:'请选择',trigger:'change'}],
    userName:[{required:true, message:'请输入',trigger:'change'}],
    password:[{required:true, message:'请输入',trigger:'change'}],
}
const ticketTaskEntityList = ref([])
const serverlist = ref([]);
// const rules = reactive({
//     opinion:[{required:true, message:'请输入',trigger:'blur'}],
// })
const cancel = () => {
    let index = index_store.get_activeIndex
    index_store.closeTag(index)
    Object.assign(readyform,defaultform)
}
const handleSave = (e) => {
    readyformRef.value.validate().then(async ()=>{
        readyform.executeType = e;
        console.log('readyform',readyform)
        let res = await updateTickettask(readyform)
        if(res.code == 0 && res.data !== false){
            if(e == '提交')
            message.success('提交成功')
            if(e == '驳回')
            message.success('驳回成功')
            cancel()
        }else{
            if(e == '提交')
            message.error((!res.msg || res.msg == 'success') ? '提交失败' : res.msg);
            if(e == '驳回')
            message.error((!res.msg || res.msg == 'success') ? '驳回失败' : res.msg);
        }
    })

    // readyform.executeType = e;
    // let res = await updateTickettask(readyform)
    // if(res.code == 0){
    //     if(e == '提交')
    //     message.success('提交成功')
    //     if(e == '驳回')
    //     message.success('驳回成功')
    //     cancel()
    // }
    
}
const getCloudType = async (cloudId) => {
    let res = await selectCloudInfo({id:cloudId})
    if(res.code == 0){
        ticketform.cloudType = res.data.cloudType;
    }
}
const flavorInfo = async (flavorId) => {
    let res = await selectFlavorInfo({id:flavorId})
    if(res.code == 0){
        ticketform.after.totalDisk = res.data.totalDisk ? res.data.totalDisk + ' G' : '-';
        ticketform.after.vcpus = res.data.vcpus ? res.data.vcpus : '-';
        ticketform.after.ram = res.data.ram ? (res.data.ram >= 1024 ? (res.data.ram / 1024).toFixed() + ' G' : res.data.ram + ' M') : '-';
    }
}
// 扩容前
const serverInfoAPI = async (id) => {
    let res = await selectServerInfo({id})
    if(res.code == 0){
        ticketform.before.disk = res.data.disk ? res.data.disk + ' G' : '-';
        ticketform.before.vcpus = res.data.vcpus ? res.data.vcpus : '-';
        ticketform.before.ram = res.data.ram ? (res.data.ram >= 1024 ? (res.data.ram / 1024).toFixed() + ' G' : res.data.ram + ' M') : '-';
    }
}
const getInfo = async () => {
    console.log('info')
    let res = await getTicketInfo({id:route.query.ticketId})
    if(res.code == 0){
        if(res.data.flavorId)
        flavorInfo(res.data.flavorId)
        if(res.data.serverId)
        serverInfoAPI(res.data.serverId)
        if(res.data.cloudId)
        getCloudType(res.data.cloudId)
        Object.assign(ticketform,res.data)
        // ticketlist = res.data;
        // readyform.id = ticketlist.id
        if(res.data.ticketTaskEntityList)
        ticketTaskEntityList.value = res.data.ticketTaskEntityList
        // ticketTaskEntityList.value.splice(0,1)
    }
}
const getConsoleURL = async (id) => {
    let res = await getConsoleUrl(id);
    if(res){
        if(res.code == 0 && res.data){
            window.open(res.data)
        }else if(res.code == 0){
            message.warning(res.msg)
        }
    }
}
const getServerList = async () => {
    let res = await selectServerList({ownerId:userStore().userId})
    if(res){
        if(res.code == 0){
            // Object.assign(serverlist,res.data);
            serverlist.value = res.data;
        }
    }
}
onMounted(() => {getInfo();getServerList()})
</script>
<style lang='scss' scoped>
.ready-part{
    padding:20px 234px;background-color:#fff;
}
:deep(.ant-descriptions-item-label){
    text-align: right;
    // width: 240px;
}
:deep(.ant-descriptions-title){
    // border-bottom: 1px solid #f0f2f5;
    &::before{
    content: '';
    display: inline-block;
    width: 4px;
    height: 17px;
    margin-right: 6px;
    vertical-align: text-bottom;
    background-color: #1890ff;
} 
}
// .part2{
    :deep(.ant-descriptions-item-label){width: 14%;}
    // :deep(.ant-descriptions-bordered .ant-descriptions-item-content){width: 340px;}
// }
.part1{
    // :deep(.ant-descriptions-item-label){background-color: #fff;}
    // :deep(.ant-descriptions-bordered .ant-descriptions-item-content){width: 83%;}
}
.delay-content{min-width: 1300px;padding:20px 150px;background-color:#fff;border-top: 2px solid #f0f2f5;}
.ant-descriptions-header{padding-left: 130px;display: inline-block;width: 74%;}
.back-header1{background-color: #fff;}
:deep(.ant-descriptions-row > td){padding-top: 16px;}
:deep(span.ant-descriptions-item-label){
    font-size: 14px;
}
</style>