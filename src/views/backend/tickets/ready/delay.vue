<template>
    <div class="back-page">
        <a-page-header
            class="back-header1"
            :title="ticketform.ticketTitle"
            @back="()=>{info.isShow = false}"
        >
        <template #extra>
            <a-button v-if="!info.isView" type="primary" @click="handleSave('驳回')">驳回</a-button>
            <a-button v-if="!info.isView" type="primary" @click="handleSave('提交')">提交</a-button>
        </template>
        </a-page-header>
        <div :style="{height: 'calc(100vh - 170px)',overflowY:'auto'}">
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">基础信息</div>
                </div>

                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="工单标题" :span="2">
                        {{ticketform.ticketTitle}}
                    </a-descriptions-item>
                    <a-descriptions-item label="工单描述" :span="2">
                        {{ticketform.ticketInfo ? ticketform.ticketInfo :'-'}}
                    </a-descriptions-item>
                    <a-descriptions-item label="工单申请人" :span="2">
                        {{ticketform.createUserName}}
                    </a-descriptions-item>

                    <a-descriptions-item label="申请时间">
                        {{ticketform.createTime}}
                    </a-descriptions-item>
                    <a-descriptions-item label="截止日期">
                        {{ticketform.endTime?.substr(0,10)}}
                    </a-descriptions-item>

                    <a-descriptions-item label="虚机名称" :span="2">
                        <a @click.prevent="getConsoleURL(ticketform.serverId)">{{ticketform.serverName ? ticketform.serverName : '-'}}</a>
                    </a-descriptions-item>
                    <a-descriptions-item label="虚机基本信息" :span="2">
                        <a-descriptions :column="2" :label-style="{width:'120px'}" :contentStyle="{width:'254px'}" bordered>
                            <a-descriptions-item label="所属云平台">{{ticketform.cloudName}}</a-descriptions-item>
                            <a-descriptions-item label="磁盘大小">{{ticketform.disk}}</a-descriptions-item>
                            <a-descriptions-item label="CPU核数">{{ticketform.vcpus}}</a-descriptions-item>
                            <a-descriptions-item label="内存大小">{{ticketform.ram}}</a-descriptions-item>
                        </a-descriptions>
                    </a-descriptions-item>
                    <a-descriptions-item label="审批意见" :span="2" v-if="!info.isView">
                        <a-form :model="readyform" size="large" :label-col="{span:6}" ref="readyformRef" :rules="rules">
                            <a-form-item name="opinion">
                                <a-textarea v-model:value="readyform.opinion" placeholder="请输入审批意见(非必填)" style="width:750px" allow-clear></a-textarea>
                            </a-form-item>
                        </a-form>
                    </a-descriptions-item>
                </a-descriptions>
            </div>
            <div class="delay-content" style="border-top: 2px solid #f0f2f5">
                    <div class="ant-descriptions-header">
                        <div class="ant-descriptions-title">审批记录</div>
                    </div>
                    <div class="ready-part">
                        <a-timeline>
                            <a-timeline-item v-for="(item,index) in ticketTaskEntityList" :key="index">{{item.taskStep+'&nbsp;&nbsp;'+item.transactUserName+'&nbsp;&nbsp;'+item.transactTime}}
                                <p>{{item.opinion}}</p>
                            </a-timeline-item>
                        </a-timeline>
                    </div>
                    
                </div>
        </div>
        
    </div>
</template>
<!-- 扩容的审批、查看 -->
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { message } from "ant-design-vue";
import {getConsoleUrl, selectServerInfo, selectServerList} from "@/api/backend/devops/server";
import {selectFlavorInfo, selectFlavorList} from "@/api/backend/devops/flavor";
import {getTicketInfo, saveTicket,updateTicket, updateTickettask} from "@/api/backend/tickets";
import { useRoute } from 'vue-router';
import { getUsername } from '@/utils/auth';
import { queryWorker } from '@/api/backend/systems/user';
import { selectDictList } from '@/api/backend/systems/dictionary';
import { selectCloudList } from '@/api/backend/cloud';
import { indexStore } from '@/store';
import emiter from '@/utils/Bus';
const index_store = indexStore()
const {proxy} = getCurrentInstance()
const route = useRoute();
const ticketformRef = ref()
const readyformRef = ref()
const serverlist = ref([]);
const flavorlist = ref([]);
const isSelected = ref(false);
const serverInfo = reactive({});
const userlist = ref([])
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: true,
        isView:false
      };
    }
  },
})
const defaultform = {
    "executeType": 1,
  "serverId": "",
}
const ticketform = reactive({

})
const readyform = reactive({
    "executeType": 1,
  "serverId": undefined,
})
const validateTime : any = (rule, value) => {
  if (!value) {
    // console.log('value',value)
    return Promise.reject("请选择时间");
  } else {
    // if (value && ticketform.startTime) {
    //       ticketformRef.value.validateFields('startTime');
    //     }
    return Promise.resolve();
  }
}
const rules = {
    serverId:[{type:'number',required:!props.info.isView, message:'请选择',trigger:'change'}],
    userName:[{required:!props.info.isView, message:'请输入',trigger:'change'}],
    password:[{required:!props.info.isView, message:'请输入',trigger:'change'}],
}
const endOpen = ref<boolean>(false);
const ticketTaskEntityList = ref([])
// const ticketlist = reactive({});
// const selectedRowKeys = ref()
const disabledStartDate = (startValue: Moment) => {
      if (!startValue || !ticketform.endTime) {
        return false;
      }
      return startValue.valueOf() > ticketform.endTime.valueOf();
    };

    const disabledEndDate = (endValue: Moment) => {
      if (!endValue || !ticketform.startTime) {
        return false;
      }
      return ticketform.startTime.valueOf() >= endValue.valueOf();
    };
    const select = (a,b) => {
        console.log('a,b',a,b)
        Object.assign(serverInfo, b.serverInfo)
        isSelected.value = true;
    }
    const clear = (e,c) =>{
        isSelected.value = false;
        console.log('c',e,c)
    }
    const getConsoleURL = async (id) => {
    let res = await getConsoleUrl(id);
    if(res){
        if(res.code == 0 && res.data){
            window.open(res.data)
        }else if(res.code == 0){
            message.warning(res.msg)
        }
    }
}
    const getServerList = async () => {
    let res = await selectServerList()
    if(res){
        if(res.code == 0){
            // Object.assign(serverlist,res.data);
            serverlist.value = res.data;
        }
    }
}
const flavorInfo = async (res1data) => {
    let res = await selectFlavorInfo({id:res1data.flavorId})
    if(res.code == 0){
        ticketform.disk = res.data.totalDisk ? res.data.totalDisk + ' G' : '-';
        ticketform.vcpus = res.data.vcpus ? res.data.vcpus : '-';
        ticketform.ram = res.data.ram ? (res.data.ram >= 1024 ? (res.data.ram / 1024).toFixed() + ' G' : res.data.ram + ' M') : '-';
        console.log("flavor",res.data)
    }else{
        ticketform.disk = res1data.disk ? res1data.disk + ' G' : '-';
        ticketform.vcpus = res1data.vcpus ? res1data.vcpus : '-';
        ticketform.ram = res1data.ram ? (res1data.ram >= 1024 ? (res1data.ram / 1024).toFixed() + ' G' : res1data.ram + ' M') : '-';
    }
}
// 扩容前
const serverInfoAPI = async (id) => {
    let res = await selectServerInfo({id})
    if(res.code == 0){
        if(res.data.flavorId)
            flavorInfo(res.data)
        else{
            ticketform.disk = res.data.disk ? res.data.disk + ' G' : '-';
        ticketform.vcpus = res.data.vcpus ? res.data.vcpus : '-';
        ticketform.ram = res.data.ram ? (res.data.ram >= 1024 ? (res.data.ram / 1024).toFixed() + ' G' : res.data.ram + ' M') : '-';
        }
        // ticketform.disk = res.data.disk ? res.data.disk + ' G' : '-';
        // ticketform.vcpus = res.data.vcpus ? res.data.vcpus : '-';
        // ticketform.ram = res.data.ram ? (res.data.ram >= 1024 ? (res.data.ram / 1024).toFixed() + ' G' : res.data.ram + ' M') : '-';
    }else{
        ticketform.disk = '-';
        ticketform.vcpus = '-';
        ticketform.ram = '-';
    }
}
const getFlavorList = async () => {
    let res = await selectFlavorList()
    if(res.code == 0){
        flavorlist.value = res.data;
    }
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
}
const options4 = ref([])
// 镜像os
const selectOs = async () => {
    let res = await selectDictList({dictType:'IMAGE_OS'})
    if(res.code == 0){
        options4.value = res.data;
    }
}
const handleSave = (e) => {
    readyformRef.value.validate().then(async ()=>{
        readyform.executeType = e;
        console.log('readyform',readyform)
        let res = await updateTickettask(readyform)
        if(res.code == 0 && res.data !== false){
            if(e == '提交')
            message.success('提交成功')
            if(e == '驳回')
            message.success('驳回成功')
            cancel()
            proxy.$mitt.emit('getlist')
        }else{
            if(e == '提交')
            message.error((!res.msg || res.msg == 'success') ? '提交失败' : res.msg)
            if(e == '驳回')
            message.error((!res.msg || res.msg == 'success') ? '驳回失败' : res.msg)
        }
    })
}
const cancel = () => {
    // let index = index_store.get_activeIndex
    // index_store.closeTag(index)
    
    props.info.isShow = false;
    Object.assign(readyform,defaultform)
}
const options = ref([])
const selectCloudlist = () => {
  selectCloudList().then((res)=>{
    options.value = res.data;
  })
}
const getInfo = async (ticketId,id) => {
    let res = await getTicketInfo({id:ticketId})
    if(res.code == 0){
        // flavorInfo(res.data.flavorId)
        serverInfoAPI(res.data.serverId)
        Object.assign(ticketform,res.data)
        // ticketlist = res.data;
        readyform.id = id
        readyform.ticketId = ticketId
        if(res.data){
            if(res.data.ticketTaskEntityList)
                ticketTaskEntityList.value = res.data.ticketTaskEntityList
                // ticketTaskEntityList.value.splice(0,1)
            ticketTaskEntityList.value = ticketTaskEntityList.value.filter((item)=>item.state == 2)
        }
    }
}
onMounted(() => {getServerList();getFlavorList();
    if(!props.info.isView){
        queryworker();selectOs();selectCloudlist();
    }
})
defineExpose({getInfo})
</script>
<style lang='scss' scoped>
// .contentPadding{position: relative;}
// .delay-content{position: absolute;top: 50%;left: 50%;margin-top: -111px;margin-left: -238px;}
.delay-content{min-width: 1300px;padding:20px 150px;background-color:#fff;border-top: 2px solid #f0f2f5;}
.contentPadding{position: relative;}
.sub-btn{
    // margin-top: 50px;
    position: absolute;
    right: 60px;
    top: 600px;
}
.ready-part{
    padding:20px 234px;background-color:#fff;
}
:deep(.ant-descriptions-title){
    border-bottom: 1px solid #f0f2f5;
    // width: 50%;
    &::before{
    content: '';
    display: inline-block;
    width: 4px;
    height: 17px;
    margin-right: 6px;
    vertical-align: text-bottom;
    background-color: #1890ff;
} 
}
.ant-descriptions-header{padding-left: 130px;display: inline-block;width: 90%;}
.back-header1{background-color: #fff;}
:deep(.ant-descriptions-row > td){padding-top: 16px;}
:deep(span.ant-descriptions-item-label) {
    font-size: 14px;
}
:deep(.ant-descriptions-bordered .ant-descriptions-view){max-width: 750px;}
</style>