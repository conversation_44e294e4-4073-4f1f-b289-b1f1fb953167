<template>
   <div class="uncloudRight" v-if="!info.isShow && !info1.isShow && !info2.isShow && !info3.isShow">
        <!-- <div> -->
          <div class="buttonPadding" v-if="isList">
            <a-form layout="inline" :model="searchform" class="searchform">
              <a-form-item label="标题">
                <a-input placeholder="请输入" @pressEnter="onSearch" v-model:value="searchform.ticketTitle" allowClear />
              </a-form-item>
              <a-form-item label="类型">
                <a-select v-model:value="searchform.ticketType" placeholder="请选择" style="width:223px" allowClear>
                    <a-select-option value="1">虚机申请</a-select-option>
                    <a-select-option value="2">虚机延期</a-select-option>
                    <a-select-option value="3">虚机扩容</a-select-option>
                </a-select>
                </a-form-item>
            </a-form>
            <MoreSearch @search="onSearch" @reset="reset" />
       </div>
        <div :class="isList ? 'innerPadding' : 'innerPadding1'">
            <!-- <a-row class="buttonGroup"> -->
                <!-- <a-button type="primary" class="btnMargin" @click="handleAdd"> <PlusOutlined />新增 </a-button> -->
            <!-- </a-row> -->
            <a-table :columns="columns" row-key="id" :scroll="{x:true}" :data-source="ticketlist" :pagination="isList ? pagination : false" @change="changeTable">
                <template #index={record,index}>
                    {{index+1+(pagination.pageSize * (pagination.current-1))}}
                </template>
                <template #ticketTitle={record}>
                  <a @click="handleEdit(record)">{{record.ticketEntityList?.[0].ticketTitle}}</a>
                <!-- <span v-else>{{record.ticketTitle}}</span> -->
              </template>
              <template #ticketType="{ record }">
                <span v-if="record.ticketEntityList?.[0].ticketType == 2">虚机延期</span>
                <span v-else-if="record.ticketEntityList?.[0].ticketType == 3">虚机扩容</span>
                <span v-else>虚机申请</span>
              </template>
                <template #state={record}>
                    <span v-if="record.state == 1">待办理</span>
                    <span v-else-if="record.state == 2">已办结</span>
                    <span v-else-if="record.state == 0">申请</span>
                    <span v-else>无</span>
                </template>
                <!-- <template #transactUserName={record}>
                  {{record.ticketTaskEntityList?.[0].transactUserName}}s
                </template> -->
                <template #action={record} v-if="isList">
                    <a-button @click="handleView(record)" v-if="$isShowBtn('sys:systickettask:info')" class="button_V">查看</a-button>
                    <a-button @click="handleEdit(record)" v-if="$isShowBtn('sys:systickettask:update')" class="button_E">审批</a-button>
                </template>
            </a-table>
            
        </div>
        <!-- </div> -->
    </div>
    <Info ref="ticketViewRef" :info="info" @getlist="getList" v-if="info.isShow" />
    <Delay ref="delayViewRef" :info="info1" @getlist="getList" v-if="info1.isShow" />
    <Plus ref="plusViewRef" :info="info2" @getlist="getList" v-if="info2.isShow" />

    <OInfo ref="oticketViewRef" :info="info3" @getlist="getList" v-if="info3.isShow" />
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import OInfo from "./openstack/info.vue";
import Delay from "./delay.vue";
import Plus from "./plus.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { getTickettaskList, deleteTickettask, getTicketList } from "@/api/backend/tickets";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import router from "@/router";
import { userStore } from "@/store/user";
import { indexStore } from "@/store";
import { menuStore } from "@/store/menu";
const user_store = userStore()
const { proxy } = getCurrentInstance();
const props = defineProps({
    isList:{
        type:Object,
        default(){
            return true
        }
    },
})
const ticketViewRef = ref(null);
const oticketViewRef = ref(null);
const delayViewRef = ref(null);
const plusViewRef = ref(null);
const loading = ref(false);
const ticketlist = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false,
    isView:false
})
const info1 = reactive({
    isAdd:true,
    isShow:false,
    isView:false
})
const info2 = reactive({
    isAdd:true,
    isShow:false,
    isView:false
})
const info3 = reactive({
    isAdd:true,
    isShow:false,
    isView:false
})
const defaultform = {
    transactUserId:user_store.userId,
    state:1,
    pageIndex:1,
    pageSize:10,
     time:undefined,
    ticketTitle: "",
    ticketType: undefined,
}
const searchform = reactive({
    transactUserId:user_store.userId,
    state:1,
    pageIndex:1,
    pageSize:10,
     time:undefined,
    ticketTitle: "",
    ticketType: undefined,
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
  {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '标题', dataIndex: 'ticketTitle', slots: { customRender: 'ticketTitle' }, key: 'id',align:'center'},
    { title: "类型", dataIndex: "ticketType", slots: { customRender: "ticketType" }, key: "id", align: "center" },
    {title: '状态', dataIndex: 'state', slots: { customRender: 'state' }, key: 'id',align:'center'},
    {title: '申请人', dataIndex: 'ticketCreateUserName', key: 'id',align:'center'},
    {title: '办理人', dataIndex: 'transactUserName', slots: { customRender: 'transactUserName' }, key: 'id',align:'center'},
    {title: '创建时间', dataIndex: 'createTime', key: 'id',align:'center'},
    props.isList ? {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' } } : {}
];
const getList = async () => {
    ticketlist.value = await proxy.$getList(loading, getTickettaskList, searchform, pagination, getList )
}
const handleView = (record) => {
  if(record.ticketEntityList?.[0].ticketType == 2){
    info1.isView = info1.isShow = true;
    proxy.$nextTick(() => {
      delayViewRef.value.getInfo(record.ticketId);
    });
  }else if(record.ticketEntityList?.[0].ticketType == 3){
    info2.isView = info2.isShow = true;
    proxy.$nextTick(() => {
      plusViewRef.value.getInfo(record.ticketId);
    });
    }else{
      if(record.cloudType == 1){
      info3.isShow = true;
      info3.isView = true;
      proxy.$nextTick(() => {
        oticketViewRef.value.getinfo(record.ticketId, record.id);
      });
    }else{
      info.isShow = true;
      info.isView = true;
      proxy.$nextTick(() => {
        ticketViewRef.value.getInfo(record.ticketId, record.id);
      });
    }
    }
    // indexStore().addTag({menuId:'查看工单',name:'查看工单',url:'ticketview',query:{ticketId:record.id}})
    // router.push({path:'/admin/ticketview',query:{ticketId:record.id}})
}
const handleEdit = (record) => {
  if(props.isList){
    if(record.ticketEntityList?.[0].ticketType == 2){
    info1.isAdd = false;
    info1.isShow = true;
    info1.isView = false;
    proxy.$nextTick(() => {
      delayViewRef.value.getInfo(record.ticketId, record.id);
    });
  }else if(record.ticketEntityList?.[0].ticketType == 3){
    info2.isAdd = false;
    info2.isShow = true;
    info2.isView = false;
    proxy.$nextTick(() => {
      plusViewRef.value.getInfo(record.ticketId, record.id);
    });
  }else{
    if(record.cloudType == 1){
      info3.isAdd = false;
      info3.isShow = true;
      info3.isView = false;
      proxy.$nextTick(() => {
        oticketViewRef.value.getinfo(record.ticketId, record.id);
      });
    }else{
      info.isAdd = false;
      info.isShow = true;
      info.isView = false;
      proxy.$nextTick(() => {
        ticketViewRef.value.getInfo(record.ticketId, record.id);
      });
    }
    
  }
  }else{
    menuStore().setMenuId('tickets')
    proxy.$mitt.emit('setLeftMenu',true)
    indexStore().addTag({menuId:'工单审批',name:'工单审批',url:'ticketedit',query:{ticketId:record.ticketId,ticketType:record.ticketEntityList?.[0].ticketType}})
    router.push({path:'/admin/ticketedit',query:{ticketId:record.ticketId,ticketType:record.ticketEntityList?.[0].ticketType,id:record.id}})
  }
  
    
    // Object.assign(ticketDialog.value.ticketform, record)
    // indexStore().addTag({menuId:'工单审批',name:'工单审批',url:'ticketedit',query:{ticketId:record.id}})
    // router.push({path:'/admin/ticketedit',query:{ticketId:record.id}})
}
const onSearch = () => {
  searchform.pageIndex = 1;
  searchform.pageSize = pagination.pageSize
  getList()
 
};
const reset = () => {
  Object.assign(searchform,defaultform)
  // getList();
};
onMounted(() => {
  proxy.$mitt.on('getlist',getList)
  getList()
  nextTick(()=>{
      handleWidth()
  })
})
</script>
<style lang='scss' scoped>
// .buttonPadding,.innerPadding{min-width: 910px;}
.uncloudRight{overflow-x: auto;}
// :deep(.ant-table td){ white-space: nowrap; }
// :deep(.ant-table th){ white-space: nowrap; }
</style>