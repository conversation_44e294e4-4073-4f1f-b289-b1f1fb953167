<template>
    <div class="back-page">
        <a-page-header
          class="back-header1"
          :title="ticketform.ticketTitle"
          @back="()=>{
            info.isShow = false;
            nextTick(()=>{
                resize()
            })
          }"
      >
      <template #extra>
        <a-button v-if="!info.isView" type="primary" @click="handleSave('驳回')">驳回</a-button>
        <a-button v-if="!info.isView" type="primary" @click="handleSave('提交')">提交</a-button>
        <!-- <a-button key="2">Operation</a-button>
        <a-button key="1" type="primary">Primary</a-button> -->
      </template>
      </a-page-header>
        <!-- <div style="padding:20px;background-color:#fff;display:flex;justify-content:space-between;border-bottom:2px solid #f0f2f5">
            <a-button @click="()=>{info.isShow = false}">返回</a-button>
            <div></div>
            <div>
                <a-button v-if="!info.isView" type="primary" @click="handleSave('提交')">提交</a-button>
            </div>
        </div> -->
        <div :style="{height: 'calc(100vh - 170px)',overflowY:'auto'}">
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">基础信息</div>
                </div>
            <a-form :model="ticketform" size="large" :label-col="{span:5}">
                <a-form-item label="云平台" name="cloudId">
                        {{ticketform.cloudName}}
                    </a-form-item>
                <a-form-item label="工单标题" name="ticketTitle">
                    {{ticketform.ticketTitle}}
                </a-form-item>
                <a-form-item label="工单描述" name="ticketInfo">
                    {{ticketform.ticketInfo ? ticketform.ticketInfo :'-'}}
                </a-form-item>
                <!-- 等待 -->
                <a-row>
                    <a-col :span="10" :offset="3">
                        <a-form-item label="工单申请人">
                    {{ticketform.createUserName}}
                </a-form-item>
                    </a-col>
                    <a-col :span="9" :offset="0">
                        <a-form-item label="申请时间" >
                    {{ticketform.createTime}}
                </a-form-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="9" :offset="3">
                        <a-form-item label="系统类型" name="osType">
                    {{ticketform.osTypeText ? ticketform.osTypeText : '-'}}
                </a-form-item>
                    </a-col>
                    <a-col :span="9" :offset="1">
                        <a-form-item label="磁盘大小" name="diskSize">
                    {{ticketform.diskSize ? ticketform.diskSize+'G' : '-'}}
                </a-form-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="9" :offset="3">
                        <a-form-item label="内存大小" name="ramSize">
                    {{ticketform.ramSize ? ticketform.ramSize+'G' : '-'}}
                </a-form-item>
                    </a-col>
                    <a-col :span="9" :offset="1">
                        <a-form-item label="CPU核数" name="cpuSize">
                    {{ticketform.cpuSize ? ticketform.cpuSize : '-'}}
                </a-form-item>
                    </a-col>
                </a-row> 
                <a-form-item label="系统备注" name="osRemark">
                    {{ticketform.osRemark ? ticketform.osRemark : '-'}}
                </a-form-item>
                <a-form-item label="使用时间" name="endTime">
                    {{ticketform.startTime ? ticketform.startTime.substr(0,10) :'-'}} 至 {{ticketform.endTime ? ticketform.endTime.substr(0,10) : '-'}}
                </a-form-item>
                </a-form>
                <a-form :model="readyform" size="large" :label-col="{span:5}" ref="readyformRef" :rules="rules" v-if="!info.isView">
                    <a-form-item label="审批意见" name="opinion">
                        <a-textarea v-model:value="readyform.opinion" placeholder="请输入审批意见(非必填)" allow-clear></a-textarea>
                    </a-form-item> 
            </a-form>
            </div>
            <div class="delay-content" style="border-top: 2px solid #f0f2f5">
                    <div class="ant-descriptions-header">
                        <div class="ant-descriptions-title">审批记录</div>
                    </div>
                    <div class="ready-part">
                        <a-timeline>
                            <a-timeline-item v-for="(item,index) in ticketTaskEntityList" :key="index">{{item.taskStep+'&nbsp;&nbsp;'+item.transactUserName+'&nbsp;&nbsp;'+item.transactTime}}
                                <p>{{item.opinion}}</p>
                            </a-timeline-item>
                        </a-timeline>
                    </div>
                    
                </div>
        </div>
        
    </div>
</template>
<!-- 审批、查看 -->
<script lang='ts' setup>
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { message } from "ant-design-vue";
import {getConsoleUrl, selectServerList} from "@/api/backend/devops/server";
import {selectFlavorList} from "@/api/backend/devops/flavor";
import {getTicketInfo, saveTicket,updateTicket, updateTickettask} from "@/api/backend/tickets";
import { useRoute } from 'vue-router';
import { getUsername } from '@/utils/auth';
import { queryWorker } from '@/api/backend/systems/user';
import { selectDictList } from '@/api/backend/systems/dictionary';
import { selectCloudList } from '@/api/backend/cloud';
import { indexStore } from '@/store';
import { userStore } from '@/store/user';
import emiter from '@/utils/Bus';
import { handleWidth, resize } from '@/utils/moreform';
const index_store = indexStore()
const {proxy} = getCurrentInstance()
const route = useRoute();
const ticketformRef = ref()
const readyformRef = ref()
const serverlist = ref([]);
const flavorlist = ref([]);
const isSelected = ref(false);
const serverInfo = reactive({});
const userlist = ref([])
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: true,
        isView:false
      };
    }
  },
})
const defaultform = {
    "executeType": 1,
  "serverId": "",
}
const ticketform = reactive({

})
const readyform = reactive({
    "executeType": 1,
  "serverId": undefined,
})
const validateTime : any = (rule, value) => {
  if (!value) {
    // console.log('value',value)
    return Promise.reject("请选择时间");
  } else {
    // if (value && ticketform.startTime) {
    //       ticketformRef.value.validateFields('startTime');
    //     }
    return Promise.resolve();
  }
}
const rules = {
    serverId:[{type:'number',required:!props.info.isView, message:'请选择',trigger:'change'}],
    userName:[{required:!props.info.isView, message:'请输入',trigger:'change'}],
    password:[{required:!props.info.isView, message:'请输入',trigger:'change'}],
}
const endOpen = ref<boolean>(false);
const ticketTaskEntityList = ref([])
// const ticketlist = reactive({});
// const selectedRowKeys = ref()
const disabledStartDate = (startValue: Moment) => {
      if (!startValue || !ticketform.endTime) {
        return false;
      }
      return startValue.valueOf() > ticketform.endTime.valueOf();
    };

    const disabledEndDate = (endValue: Moment) => {
      if (!endValue || !ticketform.startTime) {
        return false;
      }
      return ticketform.startTime.valueOf() >= endValue.valueOf();
    };
    const select = (a,b) => {
        console.log('a,b',a,b)
        Object.assign(serverInfo, b.serverInfo)
        isSelected.value = true;
    }
    const clear = (e,c) =>{
        isSelected.value = false;
        console.log('c',e,c)
    }
    const getConsoleURL = async (id) => {
    let res = await getConsoleUrl(id);
    if(res){
        if(res.code == 0 && res.data){
            window.open(res.data)
        }else if(res.code == 0){
            message.warning(res.msg)
        }
    }
}
    const getServerList = async () => {
    let res = await selectServerList({ownerId:userStore().userId})
    if(res){
        if(res.code == 0){
            // Object.assign(serverlist,res.data);
            serverlist.value = res.data;
        }
    }
}
const getFlavorList = async () => {
    let res = await selectFlavorList()
    if(res.code == 0){
        flavorlist.value = res.data;
    }
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
}
const options4 = ref([])
// 镜像os
const selectOs = async () => {
    let res = await selectDictList({dictType:'IMAGE_OS'})
    if(res.code == 0){
        options4.value = res.data;
    }
}
const handleSave = (e) => {
    readyformRef.value.validate().then(async ()=>{
        readyform.executeType = e;
        let res = await updateTickettask(readyform)
        if(res.code == 0 && res.data !== false){
            if(e == '提交')
            message.success('提交成功')
            if(e == '驳回')
            message.success('驳回成功')
            cancel()
            proxy.$mitt.emit('getlist')
        }else{
            if(e == '提交')
            message.error((!res.msg || res.msg == 'success') ? '提交失败' : res.msg);
            if(e == '驳回')
            message.error((!res.msg || res.msg == 'success') ? '驳回失败' : res.msg);
        }
    })
}
const cancel = () => {
    // let index = index_store.get_activeIndex
    // index_store.closeTag(index)
    
    props.info.isShow = false;
    Object.assign(readyform,defaultform)
}
const options = ref([])
const selectCloudlist = () => {
  selectCloudList().then((res)=>{
    options.value = res.data;
  })
}
const getInfo = async (ticketId,id) => {
    let res = await getTicketInfo({id:ticketId})
    if(res.code == 0){
        Object.assign(ticketform,res.data)
        // ticketlist = res.data;
        if(id)
        readyform.id = id
        readyform.ticketId = ticketId
        if(res.data){
            if(res.data.ticketTaskEntityList)
                ticketTaskEntityList.value = res.data.ticketTaskEntityList
                // ticketTaskEntityList.value.splice(0,1)
            ticketTaskEntityList.value = ticketTaskEntityList.value.filter((item)=>item.state == 2)
        }
    }
}
onMounted(() => {getServerList();getFlavorList();queryworker();selectOs();selectCloudlist();})
defineExpose({getInfo})
</script>
<style lang='scss' scoped>
// .contentPadding{position: relative;}
// .delay-content{position: absolute;top: 50%;left: 50%;margin-top: -111px;margin-left: -238px;}
.delay-content{padding:20px 150px;background-color:#fff;border-top: 2px solid #f0f2f5;}
.contentPadding{position: relative;}
.sub-btn{
    // margin-top: 50px;
    position: absolute;
    right: 60px;
    top: 600px;
}
.ready-part{
    padding:20px 234px;background-color:#fff;
}
:deep(.ant-descriptions-title){
    border-bottom: 1px solid #f0f2f5;
    // width: 50%;
    &::before{
    content: '';
    display: inline-block;
    width: 4px;
    height: 17px;
    margin-right: 6px;
    vertical-align: text-bottom;
    background-color: #1890ff;
} 
}
.ant-descriptions-header{padding-left: 130px;display: inline-block;width: 74%;}
.back-header1{background-color: #fff;}
</style>