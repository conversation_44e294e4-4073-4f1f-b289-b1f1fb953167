<template>
    <div class="back-page">
        <a-page-header
          class="back-header1"
          :title="ticketform.ticketTitle"
          @back="()=>{
            info.isShow = false;
            nextTick(()=>{
                resize()
            })
          }"
      >
      <template #extra>
        <a-button v-if="!info.isView" type="primary" @click="handleSave('驳回')">驳回</a-button>
        <a-button v-if="!info.isView" type="primary" @click="handleSave('提交')">提交</a-button>
        <!-- <a-button key="2">Operation</a-button>
        <a-button key="1" type="primary">Primary</a-button> -->
      </template>
      </a-page-header>
        <!-- <div style="padding:20px;background-color:#fff;display:flex;justify-content:space-between;border-bottom:2px solid #f0f2f5">
            <a-button @click="()=>{info.isShow = false}">返回</a-button>
            <div></div>
            <div>
                <a-button v-if="!info.isView" type="primary" @click="handleSave('提交')">提交</a-button>
            </div>
        </div> -->
        <div :style="{height: 'calc(100vh - 170px)',overflowY:'auto', backgroundColor: '#fff'}">
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">基础信息</div>
                </div>

                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="工单名称" :span="2">
                        {{ticketform.ticketTitle ? ticketform.ticketTitle : '-'}}
                    </a-descriptions-item>
                    <a-descriptions-item label="工单描述" :span="2">
                        {{ticketform.ticketInfo ? ticketform.ticketInfo : '-'}}
                    </a-descriptions-item>
                    <a-descriptions-item label="工单申请人">
                        {{ticketform.createUserName ? ticketform.createUserName : '-'}}
                    </a-descriptions-item>
                    <a-descriptions-item label="申请时间">
                        {{ticketform.createTime ? ticketform.createTime : '-'}}
                    </a-descriptions-item>
                    
                </a-descriptions>
            </div>
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">虚机信息</div>
                </div>

                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="虚机名称">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.serverName}}
                    </a-descriptions-item>
                    <a-descriptions-item label="虚机数量">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.serverNumber}}
                    </a-descriptions-item>
                    <a-descriptions-item label="项目">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.projectName}}
                    </a-descriptions-item>
                    <a-descriptions-item label="可用域">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.availabilityZone}}
                    </a-descriptions-item>
                    <!-- <a-descriptions-item label="描述">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.description ? ticketform.ticketOpenstackServerInfoList?.[0]?.description : '-'}}
                    </a-descriptions-item> -->
                </a-descriptions>
            </div>
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">源信息</div>
                </div>

                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item :label="ticketform.ticketOpenstackServerInfoList?.[0]?.source == 1 ? '镜像' : (ticketform.ticketOpenstackServerInfoList?.[0]?.source == 2 ? '虚机快照' : (ticketform.ticketOpenstackServerInfoList?.[0]?.source == 3 ? '卷' : '卷快照'))">
                        {{(ticketform.ticketOpenstackServerInfoList?.[0]?.source == 1 || ticketform.ticketOpenstackServerInfoList?.[0]?.source == 2) ? ticketform.ticketOpenstackServerInfoList?.[0]?.imageName : (ticketform.ticketOpenstackServerInfoList?.[0]?.source == 3 ? ticketform.ticketOpenstackServerInfoList?.[0]?.volumeName : ticketform.ticketOpenstackServerInfoList?.[0]?.volumeSnapshotName) }}
                    </a-descriptions-item>
                </a-descriptions>
            </div>
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">虚机类型</div>
                </div>
                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="虚机类型">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.flavorName}}
                    </a-descriptions-item>
                    <a-descriptions-item label="最小虚机类型">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.minFlavorName}}
                    </a-descriptions-item>
                    <a-descriptions-item label="最大虚机类型">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.maxFlavorName}}
                    </a-descriptions-item>
                </a-descriptions>
            </div>
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">网络信息</div>
                </div>

                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="网络">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.networkName ? ticketform.ticketOpenstackServerInfoList?.[0]?.networkName : '-'}}
                    </a-descriptions-item>
                </a-descriptions>
            </div>
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">安全组信息</div>
                </div>
                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="安全组">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.groupName ? ticketform.ticketOpenstackServerInfoList?.[0]?.groupName : '-'}}
                    </a-descriptions-item>
                </a-descriptions>
            </div>
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">密钥信息</div>
                </div>
                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="密钥对">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.keyPairName ? ticketform.ticketOpenstackServerInfoList?.[0]?.keyPairName : '-'}}
                    </a-descriptions-item>
                </a-descriptions>
            </div>
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">配置信息</div>
                </div>
                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="配置脚本">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.userData ? ticketform.ticketOpenstackServerInfoList?.[0]?.userData : '-'}}
                    </a-descriptions-item>
                </a-descriptions>
            </div>
            <div class="delay-content">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">规则信息</div>
                </div>
                <a-descriptions :column="2" size="default" :label-style="{width:'250px',justifyContent: 'flex-end'}">
                    <a-descriptions-item label="规则配置">
                        {{ticketform.ticketOpenstackServerInfoList?.[0]?.configRoleId ? ticketform.ticketOpenstackServerInfoList?.[0]?.configRoleId : '-'}}
                    </a-descriptions-item>
                </a-descriptions>
            </div>
            <div class="delay-content" v-if="!info.isView">
            <a-form :model="readyform" size="large" :label-col="{span:5}" ref="readyformRef" :rules="rules" style="min-width: 864px;">
                <a-form-item label="审批意见" name="opinion">
                    <a-textarea v-model:value="readyform.opinion" placeholder="请输入审批意见(非必填)" allow-clear></a-textarea>
                </a-form-item> 
            </a-form>
            </div>
            <div class="delay-content" style="border-top: 2px solid #f0f2f5">
                    <div class="ant-descriptions-header">
                        <div class="ant-descriptions-title">审批记录</div>
                    </div>
                    <div class="ready-part">
                        <a-timeline>
                            <a-timeline-item v-for="(item,index) in ticketTaskEntityList" :key="index">{{item.taskStep+'&nbsp;&nbsp;'+item.transactUserName+'&nbsp;&nbsp;'+item.transactTime}}
                                <p>{{item.opinion}}</p>
                            </a-timeline-item>
                        </a-timeline>
                    </div>
                    
                </div>
        </div>
        
    </div>
</template>
<!-- 审批、查看 -->
<script lang='ts' setup>
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { message } from "ant-design-vue";
import {getConsoleUrl, selectServerList} from "@/api/backend/devops/server";
import {selectFlavorList} from "@/api/backend/devops/flavor";
import {getTicketInfo, saveTicket,updateTicket, updateTickettask} from "@/api/backend/tickets";
import { useRoute } from 'vue-router';
import { getUsername } from '@/utils/auth';
import { queryWorker } from '@/api/backend/systems/user';
import { selectDictList } from '@/api/backend/systems/dictionary';
import { selectCloudList } from '@/api/backend/cloud';
import { indexStore } from '@/store';
import { userStore } from '@/store/user';
import { handleWidth, resize } from '@/utils/moreform';
const index_store = indexStore()
const {proxy} = getCurrentInstance()
const route = useRoute();
const ticketformRef = ref()
const readyformRef = ref()
const serverlist = ref([]);
const flavorlist = ref([]);
const isSelected = ref(false);
const serverInfo = reactive({});
const userlist = ref([])
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: true,
        isView:false
      };
    }
  },
})
const defaultform = {
    "executeType": 1,
  "serverId": "",
}
const ticketform = reactive({

})
const readyform = reactive({
    "executeType": 1,
  "serverId": undefined,
})
const validateTime : any = (rule, value) => {
  if (!value) {
    // console.log('value',value)
    return Promise.reject("请选择时间");
  } else {
    // if (value && ticketform.startTime) {
    //       ticketformRef.value.validateFields('startTime');
    //     }
    return Promise.resolve();
  }
}
const rules = {
    serverId:[{type:'number',required:!props.info.isView, message:'请选择',trigger:'change'}],
    userName:[{required:!props.info.isView, message:'请输入',trigger:'change'}],
    password:[{required:!props.info.isView, message:'请输入',trigger:'change'}],
}
const endOpen = ref<boolean>(false);
const ticketTaskEntityList = ref([])
// const ticketlist = reactive({});
// const selectedRowKeys = ref()
const disabledStartDate = (startValue: Moment) => {
      if (!startValue || !ticketform.endTime) {
        return false;
      }
      return startValue.valueOf() > ticketform.endTime.valueOf();
    };

    const disabledEndDate = (endValue: Moment) => {
      if (!endValue || !ticketform.startTime) {
        return false;
      }
      return ticketform.startTime.valueOf() >= endValue.valueOf();
    };
    const select = (a,b) => {
        console.log('a,b',a,b)
        Object.assign(serverInfo, b.serverInfo)
        isSelected.value = true;
    }
    const clear = (e,c) =>{
        isSelected.value = false;
        console.log('c',e,c)
    }
    const getConsoleURL = async (id) => {
    let res = await getConsoleUrl(id);
    if(res){
        if(res.code == 0 && res.data){
            window.open(res.data)
        }else if(res.code == 0){
            message.warning(res.msg)
        }
    }
}
    const getServerList = async () => {
    let res = await selectServerList({ownerId:userStore().userId})
    if(res){
        if(res.code == 0){
            console.log("获取")
            // Object.assign(serverlist,res.data);
            serverlist.value = res.data;
        }
    }
}
const getFlavorList = async () => {
    let res = await selectFlavorList()
    if(res.code == 0){
        flavorlist.value = res.data;
    }
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
}
const options4 = ref([])
// 镜像os
const selectOs = async () => {
    let res = await selectDictList({dictType:'IMAGE_OS'})
    if(res.code == 0){
        options4.value = res.data;
    }
}
const handleSave = (e) => {
    readyformRef.value.validate().then(async ()=>{
        readyform.executeType = e;
        let res = await updateTickettask(readyform)
        if(res.code == 0 && res.data !== false){
            if(e == '提交')
            message.success('提交成功')
            if(e == '驳回')
            message.success('驳回成功')
            cancel()
            proxy.$mitt.emit('getlist')
        }else{
            if(e == '提交')
            message.error((!res.msg || res.msg == 'success') ? '提交失败' : res.msg);
            if(e == '驳回')
            message.error((!res.msg || res.msg == 'success') ? '驳回失败' : res.msg);
        }
    })
}
const cancel = () => {
    // let index = index_store.get_activeIndex
    // index_store.closeTag(index)
    
    props.info.isShow = false;
    Object.assign(readyform,defaultform)
}
const options = ref([])
const selectCloudlist = () => {
  selectCloudList().then((res)=>{
    options.value = res.data;
  })
}
const getinfo = async (ticketId,id,record) => {
    if(!id && record){
        // console.log('list',ticketOpenstackServerInfoList[0])
        Object.assign(ticketform,record)
        if(record.ticketTaskEntityList)
            ticketTaskEntityList.value = record.ticketTaskEntityList
    }else{
        let res = await getTicketInfo({id:ticketId})
        if(res.code == 0){
            Object.assign(ticketform,res.data)
            // ticketlist = res.data;
            readyform.id = id
            readyform.ticketId = ticketId
            if(res.data){
                if(res.data.ticketTaskEntityList)
                    ticketTaskEntityList.value = res.data.ticketTaskEntityList
                    // ticketTaskEntityList.value.splice(0,1)
            }
        }
    }
    ticketTaskEntityList.value = ticketTaskEntityList.value.filter((item)=>item.state == 2)
}
onMounted(() => {
    if(!props.info.isView){
        getServerList();getFlavorList();queryworker();selectOs();selectCloudlist();
    }
})
defineExpose({getinfo})
</script>
<style lang='scss' scoped>
// .contentPadding{position: relative;}
// .delay-content{position: absolute;top: 50%;left: 50%;margin-top: -111px;margin-left: -238px;}
.delay-content{background-color:#fff;width: 1017px;margin: 0 auto;padding-top: 40px;}
.contentPadding{position: relative;}
.sub-btn{
    // margin-top: 50px;
    position: absolute;
    right: 60px;
    top: 600px;
}
.ready-part{
    padding:20px 234px;background-color:#fff;
}
:deep(.ant-descriptions-title){
    border-bottom: 1px solid #f0f2f5;
    // width: 50%;
    &::before{
    content: '';
    display: inline-block;
    width: 4px;
    height: 17px;
    margin-right: 6px;
    vertical-align: text-bottom;
    background-color: #1890ff;
} 
}
.ant-descriptions-header{padding: 0 200px 0 130px;display: inline-block;width:100%;}
.back-header1{background-color: #fff;border-bottom: 2px solid #f0f2f5;}
:deep(.ant-descriptions-row > td){padding-top: 16px;}
:deep(span.ant-descriptions-item-label) {
    font-size: 14px;
}
</style>