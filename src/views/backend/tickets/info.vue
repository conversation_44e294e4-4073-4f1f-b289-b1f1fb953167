<template>
    <div class='contentPadding'>
        <!-- <a-modal centered
            v-model:visible="info.isShow"
            :title="info.isAdd ? '虚机申请' : '修改工单'"
            ok-text="提交"
            cancel-text="取消"
            @ok="save"
            @cancel="cancel"
            > -->
                <a-form :model="ticketform" ref="ticketformRef">
                    <a-form-item label="工单标题" name="ticketTitle">
                    <a-input v-model:value="ticketform.ticketTitle" allow-clear />
                    </a-form-item>
                    <a-form-item label="工单描述" name="ticketInfo">
                        <a-textarea v-model:value="ticketform.ticketInfo" allow-clear></a-textarea>
                    </a-form-item>
                    <a-form-item label="工单类型" name="ticketType">
                        <a-select
                            v-model:value="ticketform.ticketType"
                            placeholder="请选择"
                            allowClear>
                            <a-select-option :value="0">类型</a-select-option>
                            <a-select-option :value="1">类型1</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-form>
            <!-- </a-modal> -->
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { saveTicket, updateTicket } from "@/api/backend/tickets";
const { proxy } = getCurrentInstance()
const emit = defineEmits(['getlist'])
const ticketformRef = ref(null);
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: false
      };
    }
  }
})
const defaultform = {
    "serviceId": 0,
    "ticketInfo": "",
    "ticketTitle": "",
    "ticketType": 0
}
const ticketform = reactive({
  "serviceId": 0,
  "ticketInfo": "",
  "ticketTitle": "",
  "ticketType": 0
})
const save = () => {
    proxy.$handleSave(ticketformRef.value, saveTicket, updateTicket, props, ticketform, ()=>{emit('getlist');cancel();})
}
const cancel = () => {
    Object.assign(ticketform,defaultform)
}
onMounted(() => {})
defineExpose({ticketform})
</script>
<style lang='scss' scoped>
</style>