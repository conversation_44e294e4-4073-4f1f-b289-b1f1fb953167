<template>
    <div id="side" v-if="isShowTerminalLevel">
        <div style="position:absolute;left:30px;top:20px">边侧</div>
          
        <!-- <template v-for="(item,index) in sideArrow" :key="item.id">
            <div class="connet" :style="{left:(item.x+600)+'px'}">
                <img :src="ArrowImg" height="45px" width="45px" alt="">
            </div>
        </template> -->
    </div>
    
</template>
<script setup>
import router from '@/router';
import G6 from '@antv/g6/lib/index.js';
import { onMounted, ref } from 'vue';
import ArrowImg from "@/assets/topo/arrow-both.svg";
import { canvasNetworktopology, getNetworkTerminalList, networkTopologyTerminal } from '@/api/backend/topo';
import { combos_template } from '@/common/dagre/data';
const emit = defineEmits(['setTerminalHeight'])
const defaultdata = {
  "nodes": [
    // {
    // id:'1',
    // label:'边侧',
    //   type: 'line',
    //   startPoint: { x: -650, y: 115 },
    //   endPoint: { x: 900, y: 115 },
    // }
  ],
  "combos":[],
  "edges":[]
}
const isShowTerminalLevel = ref(true);
const sideArrow = ref([]);
const graph = ref();
const lineNode = function (cfg,group) {
  const startPoint = cfg.startPoint;
  const endPoint = cfg.endPoint;
  const style = {
    stroke:'#d7d7d7',
    width:1
  };
 
  const keyShape = group.addShape('path', {
    attrs: {
      path: [
        ['M', startPoint.x, -85],
        ['L', endPoint.x, -85],
      ],
      ...style,
    },
    name: 'line-shape',
  });
  // 自定义文本
    const text = group.addShape('text', {
      attrs: {
        text: cfg.label,
        x: -600,
        y: 0,
        fill: '#000',
        fontSize: 16,
        textAlign: 'center',
        textBaseline: 'middle',
      },
      name: 'text-shape',
    });
 
  return keyShape;
};
const arrowCombo = (cfg,group) => {
  const keyShape = group.addShape('rect', {
        attrs: {
          x: 0,
          y: 0,
          stroke: '#e1e1e1',
          lineWidth: 1,
        },
        name: 'combo-key-shape',
      });
  const size = cfg.size;
  // 添加图片
  const image = group.addShape('image', {
    attrs: {
      x: -25,
      y: -100,
      width: 60,
      height: 60,
      img: ArrowImg
    },
    // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
    name: 'arrow-img'
  });
 
  return keyShape;
}
G6.registerNode('line', {
  draw: lineNode,
  getPath(cfg) {
    const startPoint = cfg.startPoint;
    const endPoint = cfg.endPoint;
    return [
      ['M', startPoint.x, startPoint.y],
      ['L', endPoint.x, endPoint.y],
    ];
  },
});
G6.registerCombo('side-area', {
  draw: arrowCombo
},'rect');
const rigister = () => {
  G6.registerNode('net_terminal', {
    afterDraw(cfg, group) {
      const size = [60,60];
      const width = size[0] * 0.7;
      const height = size[1] * 0.7;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/provide.svg`
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'net_terminal-img'
      });
    }
  },
  'circle');
}
let startNode;
let endNode;
let hasComboObj = {};
const groupObj = {};
const sortNodeItems = (item,data) => {
  item.style = {
    fillOpacity: 0
  }
  item.anchorPoints = [
    [0.5, 0],[0.6, 0],[0.4, 0],[0.7, 0],[0.3, 0],
    [0.5, 1],[0.6, 1],[0.4, 1],[0.7, 1],[0.3, 1]
  ];
  if(item.status){
    item.style.stroke = '#0b8235';
  }else{
    item.style.stroke = '#f81d22';
  }
  if(item.group){
    if(!groupObj[item.group]){
      groupObj[item.group] = {isHas:false,label:1};
    }
    if(!groupObj[item.group].isHas){
      groupObj[item.group].isHas = true;
      defaultdata.combos.push({
        id:'group_'+item.group,
        category:'terminal_area',
        label:'边侧区域'+groupObj[item.group].label++
      });
    }
    item.comboId = 'group_'+item.group;
  }
  
  if(item.category == 'net_terminal'){
    // if(!endNode){
    //   endNode = item.id;
    //   data.edges.push({source:startNode,target:endNode})
    // }
    item.type = 'net_terminal';
    item.size = [60, 60];
    item.style.lineDash = [5,2.5];
    item.labelCfg = {
      position:"top",
      style:{fontSize:0}
    }
  }
  return item;
}
const sortComboItems = (item) => {
  item.style = {
    fillOpacity: 0
  }
  item.anchorPoints = [
      [0.5, 0],[0.6, 0],[0.4, 0],[0.7, 0],[0.3, 0],
      [0.5, 1],[0.6, 1],[0.4, 1],[0.7, 1],[0.3, 1]
    ];
    if(item.category == 'terminal_area'){
      item.type = 'rect';
      item.style.lineDash = [10,5];
    }
  if(item.category == 'level'){
    item.type = 'rect';
    item.labelCfg = {position:'left'};
    // item.fixSize = [3000,400];
    // item.x = 1775;
    // if(item.label == '硬件层')
    // item.y = 365;
    // if(item.label == '虚拟层')
    // item.y = 665;
    // if(item.label == '边侧')
    // item.y = 965;
  }
  return item;
}
const sorted = (data) => {
  let tempdata = {nodes:[]}
  data.nodes.forEach((item,index)=>{
    if(item.category == 'net_terminal' || item.category == 'line'){
      tempdata.nodes.push(item);
      sortNodeItems(item,tempdata.nodes)
    }
  })
  data.nodes = [...tempdata.nodes];
  data.combos = defaultdata.combos;
  if(data.combos)
  data.combos.forEach((item,index)=>{
    sortComboItems(item)
  })
  data.edges = [];
  rigister()
  return data;
}
const draw = (data) => {
  const width = document.getElementById('side').width;
const height = document.getElementById('side').height;
  graph.value = new G6.Graph({
  container: 'side',
  fitView:false,
  fitCenter:true,
  width,
  height,
  groupByTypes:false,
  modes: {
    //  'drag-canvas','zoom-canvas',
    default: ['collapse-expand-combo',
      {
        type: 'tooltip',
        // formatText(model) {
        //   console.log("move")
        //   return model.label;
        // },
        shouldBegin(e, self){

          return (e.item.getModel().type != 'lan');
        },
        offset: 30
      }
    ],
  },
  layout: {
    type: 'dagre',
    rankdir: 'TB', // 可选，默认为图的中心
    // align: 'DL', // 可选 节点对齐方式
    ranksep:35,
    nodesep:30,
    preventOverlap: true, // 开启节点之间的防重叠
    nodeSize: 10,
    sortByCombo:true  // 根据comboId 进行排序，以防止 combo 重叠
  },
  defaultCombo: {
    type: 'rect',
    labelCfg: {
      position: 'top'
    },
  },
});
getData({},data);
}
const removeEdges = (edges) => {
  edges.forEach((item)=>{
    graph.value.removeItem(item.id);
  })
  const data = graph.value.save();
  graph.value.read(data);
}
const setEdge = (edge,callback) => {
  let instance = graph.value.findById(edge.id);
  if(instance){
    let model = instance.getModel()
    if(!(edge.source == model.source && edge.target == model.target)){
      edge.targetAnchor = edgeAnchor[edge.source+edge.target] === undefined ? 0 : edgeAnchor[edge.source+edge.target]++;
      // removeEdges([{...model}])
      graph.value.updateItem(instance, edge);
    }
  }else{
    graph.value.addItem('edge', edge);
  }
  graph.value.layout();
  const data = graph.value.save();
  graph.value.data(data);
  setTimeout(()=>{
    if(callback)
    callback()
  })
}
const addNodes = ({node,callback}) => {
  const data = graph.value.save();
  data.nodes.push({...sortNodeItems(node)});
  graph.value.read(data);
  node.edges.source.forEach((item,index)=>{
    // graph.addItem('edge', {source:item,target:node.id});
    setEdge({source:item,target:node.id})
  })
  node.edges.target.forEach((item,index)=>{
    // graph.addItem('edge', {source:node.id,target:item});
    setEdge({source:node.id,target:item})
  })
  if(callback)
    callback()
  // 自动布局
  // graph.layout();
}
const updateNodes = ({node,callback,deledges}) => {
  if(deledges){
    removeEdges(deledges);
  }
  let model = graph.value.findById(node.id).getModel();
  if(!node.label)
    node.label = node.rawLabel;
  model = {...model,...node}
  graph.value.updateItem(node.id, model);
  const data = graph.value.save();
  graph.value.read(data);
  node.edges.source.forEach((item,index)=>{
    // graph.addItem('edge', {source:item,target:node.id});
    setEdge({source:item,target:node.id})
  })
  node.edges.target.forEach((item,index)=>{
    // graph.addItem('edge', {source:node.id,target:item});
    setEdge({source:node.id,target:item})
  })
  if(callback)
    callback()
}
const addCombos = ({combo,nodes,callback}) => { 
  // 第一个参数为 combo 配置
  graph.value.createCombo({...sortComboItems(combo)}, nodes);
  const data = graph.value.save();
  graph.value.data(data);
  combo.edges.source.forEach((item,index)=>{
    // graph.addItem('edge', {source:item,target:node.id});
    setEdge({source:item,target:combo.id})
  })
  combo.edges.target.forEach((item,index)=>{
    // graph.addItem('edge', {source:node.id,target:item});
    setEdge({source:combo.id,target:item})
  })
  if(callback)
    callback()
}
// 删除combo及其连线的函数
function removeComboAndEdges(comboId) {
  const data = graph.value.save();
  graph.value.read(data);
  const combo = graph.value.findById(comboId);
  if (combo) {
    graph.value.removeItem(combo);
  }
}
const updateCombos = ({combo,nodes,callback,delchild,deledges}) => {
  if(deledges){
    removeEdges(deledges);
  }
  if(nodes.length > 0){
    if(!combo.label)
    combo.label = combo.rawLabel;
    
    // 更新子集
    nodes.forEach((item)=>{
      const node1 = graph.value.findById(item);
      let model = node1.getModel();
      model.comboId = combo.id;
      graph.value.updateItem(node1, model);
    
      const data = graph.value.save();
      graph.value.read(data);
    })
    delchild.forEach((item)=>{
      if(item.comboId)
      delete item.comboId;
      graph.value.updateItem(item.id, item);
      const data = graph.value.save();
      graph.value.read(data);
    })
    graph.value.updateItem(combo.id,combo);
    // graph.updateCombo(combo.id);
    // const data = graph.save();
    // graph.read(data);
    
    combo.edges.source.forEach((item,index)=>{
      // graph.addItem('edge', {source:item,target:node.id});
      setEdge({source:item,target:combo.id})
    })
    combo.edges.target.forEach((item,index)=>{
      // graph.addItem('edge', {source:node.id,target:item});
      setEdge({source:combo.id,target:item})
    })
    
  // 更新 node1 所属的 combo 及其所有祖先 combo 的大小和位置
  // 未改名
    // if(!combo.label)
    // combo.label = combo.rawLabel;
    // // 更新label、父级等
    // graph.updateItem(combo.id,combo);
    // // graph.updateCombo(combo.id);
  }else{
    // 获取combo中的所有nodes
    const nodesInCombo = graph.value.getComboChildren(combo.id).nodes;
    // 移除combo中的nodes
    nodesInCombo.forEach(node => {
      // graph.removeItem(node);
      let model = node.getModel();
      delete model.comboId;
      graph.value.updateItem(node, model);
    });
    // 调用函数删除指定的combo及其连线
    removeComboAndEdges(combo.id);
  }
  const data = graph.value.save();
  graph.value.read(data);
  if(callback)
    callback()
}
const setSideArrow = () => {
    let combos = graph.value.getCombos();
    let sides = [];
    combos.forEach((item)=>{
        let model = item.getModel();
        if(model.category == 'side-area'){
            sides.push(model);
        }
    })
    sideArrow.value = sides;
}
const getData = async ({callback},data) => {
  // getNetworkTerminalList({networkId:33}).then((res)=>{
  //   console.log("TERMINAL_CENTER",res)
  //   let data = {...res.data};
  //   data.nodes = defaultdata.nodes.concat(data.nodes);
  //   data.nodes.forEach((item,index)=>{
  //     item.id = String(item.id);
  //     item.category = item.type;
  //   })
  //   console.log("sorted(data)",data)
  //   graph.value.data(sorted(data));
  //   graph.value.render();
  //   setSideArrow();
  // })
  if(!data){
    let res = await networkTopologyTerminal({networkId:router.currentRoute.value.params.id,cloudId:router.currentRoute.value.query.cloudId})
    data = {...res.data};
  }
  // networkTopologyTerminal({networkId:router.currentRoute.value.params.id,cloudId:router.currentRoute.value.query.cloudId}).then((res)=>{
    // let data = {...res.data};
    isShowTerminalLevel.value = (data.nodes && data.nodes.length > 0);
    //  emit("setTerminalHeight",isShowTerminalLevel.value)
    data.nodes = defaultdata.nodes.concat(data.nodes);
    data.nodes.forEach((item,index)=>{
      item.id = String(item.id);
      if(item.id == '4'){
        item.type = 'net_terminal'
      }
      item.category = item.type;
    })
    graph.value.data(sorted(data));
    graph.value.render();
    // setSideArrow();
  // })
  if(callback)
    callback()
}
onMounted(()=>{
  // draw()
})
defineExpose({getData,draw})
</script>
<style lang='scss' scoped>
#side{
    width: 1535px;position: relative;
    
}
// #cloud,#container{height: 313px;width: 100%;}
</style>