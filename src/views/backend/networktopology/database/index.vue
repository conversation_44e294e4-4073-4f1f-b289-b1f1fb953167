<template>
  <div class='contentPadding' v-if="isShowVirtualLevel || isShowHardwareLevel || isShowTerminalLevel">
    <Options :graph="graph" @getData="getData" @getTerminalData="getTerminalData" />
      <Cloud ref="cloudRef" v-if="router.currentRoute.value.query.type == 'DOMAIN' && isShowVirtualLevel" :style="{height:virtualHeight}" />
      <div id="container" v-if="router.currentRoute.value.query.type != 'TERMINAL_CENTER' && isShowHardwareLevel" 
      :style="router.currentRoute.value.query.type == 'DOMAIN' ? {height: '313px',borderTop: isShowVirtualLevel ? '1px solid #f0f0f0' : 'none',borderBottom: isShowTerminalLevel ? '1px solid #f0f0f0' : 'none'} : {height: '783px'}"
      >
        <div class="connetVir" style="left:750px" v-if="router.currentRoute.value.query.type == 'DOMAIN' && isShowVirtualLevel">
            <img :src="ArrowImg" height="45px" width="45px" alt="">
        </div>
        <div style="position:absolute;left:30px;top:20px">硬件层</div>
        <div class="connet" style="left:750px" v-if="router.currentRoute.value.query.type == 'DOMAIN' && isShowTerminalLevel">
            <img :src="ArrowImg" height="45px" width="45px" alt="">
        </div>
      </div>
      <Side ref="sideRef" v-if="(router.currentRoute.value.query.type == 'DOMAIN' || router.currentRoute.value.query.type == 'TERMINAL_CENTER') && isShowTerminalLevel" :style="{height:terminalHeight}" />
      
  </div>
  <div class='contentPadding' style="display: flex;" v-else>
    <a-empty style="margin: auto;"></a-empty>
  </div>
</template>
<script setup>
import router from '@/router';
import G6 from '@antv/g6/lib/index.js';
import { computed, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import Options from "./options/index.vue";
import Side from "./side/index.vue";
import Cloud from "./cloud/index.vue";
import {combos_template} from "@/common/dagre/data";
import { canvasNetworktopology, getHostNodeInfo, getSwitchNode, getSwitchNodeInfo, networkTopologyHost, networkTopologyTerminal, selectHostNodeList, selectInterfaceNodeList } from '@/api/backend/topo';
import { selectNetworkTopology } from '@/api/backend/devops/network';
import {findSubtree} from "../utils/findSubtree";
import ArrowImg from "@/assets/topo/arrow-both.svg";
const defaultdata = reactive({
  "nodes": [
  //   {
  //   "id": "lan",
  //   "type": "lan",
  //   "label": "局域网",
  //   "anchorPoints": [
  //     [0.5, 0],
  //     [0.5, 1]
  //   ],
    
  // }
  ],
  "combos":[],
  "edges":[]
})
const isShowHardwareLevel = ref(true);
const isShowTerminalLevel = ref(false);
const isShowVirtualLevel = ref(false);
const InterfaceListObj = reactive({});
const globalCombos = ref([]);
const cloudRef = ref();
const sideRef = ref();
const graph = ref();
let hardwarelevel = 0;
const groupObj = {
  "CONTROLLER":{isHas:false,label:'控制服务器'},
  "COMPUTE":{isHas:false,label:'计算服务器'},
  "NETWORK":{isHas:false,label:'网络服务器'},
  "STROGE":{isHas:false,label:'存储服务器'}
}
const virtualHeight = computed(()=>(isShowHardwareLevel.value || isShowTerminalLevel.value) ? '368px' : '783px')
const terminalHeight = computed(()=>(isShowHardwareLevel.value || isShowVirtualLevel.value) && router.currentRoute.value.query.type == 'DOMAIN' ? '156px' : '783px')
// const setVirtualHeight = (e) => {
//   if(!e && !isShowHardwareLevel){
//     terminalHeight.value = '783px';
//   }else{
//     terminalHeight.value = '300px';
//   }
// }
// const setTerminalHeight = (e) => {
//   console.log("e",e,isShowHardwareLevel.value)
//   if(!e && !isShowHardwareLevel.value){
//     virtualHeight.value = '783px';
//   }else{
//     virtualHeight.value = '313px';
//   }
// }
const lineNode = function (cfg,group) {
  let model = graph.value.findById('lan').getModel();
  const startPoint = cfg.startPoint;
  const endPoint = cfg.endPoint;
  const style = {
    stroke:'#d7d7d7',
    width:1
  };
 
  const keyShape = group.addShape('path', {
    attrs: {
      path: [
        ['M', startPoint.x, hardwarelevel],
        ['L', endPoint.x, hardwarelevel],
      ],
      ...style,
    },
    name: 'line-shape',
  });
  // 自定义文本
    const text = group.addShape('text', {
      attrs: {
        text: cfg.label,
        x: -600,
        y: hardwarelevel + 60,
        fill: '#000',
        fontSize: 16,
        textAlign: 'center',
        textBaseline: 'middle',
      },
      name: 'text-shape',
    });

    const vmtext = group.addShape('text', {
      attrs: {
        text: cfg.vmlabel,
        x: -600,
        y: hardwarelevel - 60,
        fill: '#000',
        fontSize: 16,
        textAlign: 'center',
        textBaseline: 'middle',
      },
      name: 'vmtext-shape',
    });
 
  return keyShape;
};
const rigister = () => {
  // G6.registerNode('line', {
  //   draw: lineNode,
  //   getPath(cfg) {
  //     const startPoint = cfg.startPoint;
  //     const endPoint = cfg.endPoint;
  //     return [
  //       ['M', startPoint.x, startPoint.y],
  //       ['L', endPoint.x, endPoint.y],
  //     ];
  //   },
  // });
  G6.registerNode('lan', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = size[0] * 0.8;
      const height = size[1] * 0.8;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/net-work.svg`
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'lan-img'
      });
    }
  },
  // 继承了 rect 节点
  'rect');
  G6.registerNode('net-work', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = size[0] * 0.7;
      const height = size[1] * 0.7;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/v_exchange.svg`
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'net-work-img'
      });
    }
  },
  'circle');
  G6.registerNode('net_host', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = size[0] * 0.7;
      const height = size[1] * 0.7;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/server_node.svg`,
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'server_node-img'
      });
    }
  },
  'circle');
  G6.registerNode('net-server', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = size[0] * 0.8;
      const height = size[1] * 0.8;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/net-server.svg`
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'net-server-img'
      });
    }
  },
  // 继承了 rect 节点
  'rect');
  G6.registerNode('net_terminal', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = size[0] * 0.7;
      const height = size[1] * 0.7;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/provide.svg`
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'net_terminal-img'
      });
    }
  },
  'circle');
  // 使用方法二：自定义边，并带有自定义箭头
  G6.registerEdge('line-arrow', {
    draw(cfg, group) {
      const { startPoint, endPoint } = cfg;
      const keyShape = group.addShape('path', {
        attrs: {
          path: [
            ['M', startPoint.x, startPoint.y],
            ['L', endPoint.x, endPoint.y],
          ],
          stroke: 'steelblue',
          lineWidth: 3,
          startArrow: {
            // 自定义箭头指向(0, 0)，尾部朝向 x 轴正方向的 path
            path: 'M 0,0 L 20,10 L 20,-10 Z',
            // 箭头的偏移量，负值代表向 x 轴正方向移动
            // d: -10,
            // v3.4.1 后支持各样式属性
            fill: '#333',
            stroke: '#666',
            opacity: 0.8,
            // ...
          },
          endArrow: {
            // 自定义箭头指向(0, 0)，尾部朝向 x 轴正方向的 path
            path: 'M 0,0 L 20,10 L 20,-10 Z',
            // 箭头的偏移量，负值代表向 x 轴正方向移动
            // d: -10,
            // v3.4.1 后支持各样式属性
            fill: '#333',
            stroke: '#666',
            opacity: 0.8,
            // ...
          },
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'path-ha',
      });
      return keyShape;
    },
  });

  
}
let levelhistory = {};

let hasComboObj = {};
let startNode;
let endNode;
const sortNodeItems = async (item,data,combos,index) => {
  item.tooltip = false; // 禁用tooltip
  item.style = {
    fillOpacity: 0
  }
  item.anchorPoints = [
    [0.5, 0],[0.6, 0],[0.4, 0],[0.7, 0],[0.3, 0],
    [0.5, 1],[0.6, 1],[0.4, 1],[0.7, 1],[0.3, 1]
  ];
  if(item.status){
    item.style.stroke = '#0b8235';
  }else{
    item.style.stroke = '#f81d22';
  }
  if(!hasComboObj[item.category]){
    if(combos_template[item.category]){
      hasComboObj[item.category] = true;
      defaultdata.combos.push(combos_template[item.category]);
      item.comboId = combos_template[item.category].id;
    }
  }else{
    item.comboId = combos_template[item.category].id;
  }
  if(item.category == 'lan'){
    if(!levelhistory['lan']){
      levelhistory['lan'] = true;
      hardwarelevel += 35;
    }
    item.type = 'lan';
    item.size = [60, 60];
    item.style.lineDash = [5,2.5];
  }
  if(item.category == 'net-work'){
    // if(!levelhistory['net-work']){
    //   levelhistory['net-work'] = true;
    //   hardwarelevel -= 75;
    // }
    defaultdata.edges.push({source:"lan",target:item.id});
    item.type = 'net-work';
    item.size = [60, 60];
    item.style.lineDash = [5,2.5];
    
  }
  if(item.category == 'net-server'){
    // if(!levelhistory['net-server']){
    //   levelhistory['net-server'] = true;
    //   hardwarelevel -= 75;
    // }
    item.type = 'net-server';
    item.size = [60, 60];
    item.style.lineDash = [5,2.5];
  }
  if(item.category == 'net_switch'){
    if(router.currentRoute.value.query.type != 'DOMAIN')
      defaultdata.edges.push({source:item.id,target:"lan"});
    item.type = 'image';
    item.size = [60, 60];
    item.img = '/topoicon/h_exchange.svg';
    hardwarelevel -= 65;
    // item.innerHTML = await getSwitchInfo(item.info);
    // item.label = "-";
    // item.labelCfg = {
    //   style:{fontSize:0}
    // }
  }
  if(item.category == 'net_host'){
    item.comboId = 'group_'+item.group;
    item.tooltip = true;
    if(!startNode)
    startNode = item.id;
    item.type = 'net_host';
    item.size = [60, 60];
    item.style.lineDash = [5,2.5];
    item.labelCfg = {
      position:"top",
      offset: -8,
      style:{fontSize:14}
    }
    // let length = await selectInterfaceListLength(item.id);
    // console.log("length",length)
    // if(length > 0){
    //   item.label = eval('"' + `\\u246${(length - 1)}` + '"');
    //   item.labelCfg = {
    //     position:"top",
    //     offset: -8,
    //     style:{fontSize:14}
    //   }
    //   console.log("label",item.label)
    // }else{
    //   item.hide = true;
    // }
    
  }
  if(item.category == 'net_terminal'){
    // if(!endNode){
    //   endNode = item.id;
    //   data.edges.push({source:startNode,target:endNode})
    // }
    item.type = 'net_terminal';
    item.size = [60, 60];
    item.style.lineDash = [5,2.5];
  }
  // if(index >= data.nodes.length - 1){
  //   setTimeout(()=>{
  //     rigister()
  //     graph.value.data(data);
  //     graph.value.render();
  //     graph.value.find('node', (node) => {
  //       console.log("node.get('model')",node.get('model').hide)
  //       if(node.get('model').hide){
  //         node.hide()
  //       }
  //     });
  //   },10000)
    
  // }
  return item;
}
const sortComboItems = (item) => {
  item.style = {
    fillOpacity: 0
  }
  item.anchorPoints = [
      [0.5, 0],[0.6, 0],[0.4, 0],[0.7, 0],[0.3, 0],
      [0.5, 1],[0.6, 1],[0.4, 1],[0.7, 1],[0.3, 1]
    ];
  if(item.category == 'level'){
    item.type = 'rect';
    item.labelCfg = {position:'left'};
    // item.fixSize = [3000,400];
    // item.x = 1775;
    // if(item.label == '硬件层')
    // item.y = 365;
    // if(item.label == '虚拟层')
    // item.y = 665;
    // if(item.label == '边侧')
    // item.y = 965;
  }
  if(item.category == 'exchange_group' || item.category == 'server_node_group'){
    item.type = 'rect';
    item.style.lineDash = [5,2.5];
  }
  return item;
}
const sortHostNodeRaw = async (hostnodes,tempnodes,tempedges,hostedges) => {
  let nodes = {};
  console.log("hostnodes",hostnodes)
  let arr = [];
  hostnodes.forEach((item,index)=>{
    arr.push(getHostNode(item,nodes,tempedges,hostedges,index,hostnodes,tempnodes))
    
  })
  let res = [];
  let promiseRes = await Promise.all(arr);
  if(promiseRes){
    console.log("promiseRes",promiseRes,nodes,tempedges)
    for(let item in nodes){
      if(nodes[item]){
        if(nodes[item][0] && nodes[item][0].length > 0){
          nodes[item][0][0].innerHTML = `<p>物理服务器</p>
                  <ul>${InterfaceListObj[nodes[item][0][0].id]}</ul>`;
          res.push(nodes[item][0][0]);
        }
        if(nodes[item][1] && nodes[item][1].length > 0){
          nodes[item][1][0].innerHTML = `<p>物理服务器</p>
                  <ul>${InterfaceListObj[nodes[item][1][0].id]}</ul>`;
          res.push(nodes[item][1][0]);
        }
      }
      
    }
    return res;
  }
}
const hostId = (info) => {
  return info.split("host_id:")[1].split(" interface_num:")[0];
}
const interfaceNum = (info) => {
  return info.split("host_id:")[1].split(" interface_num:")[1];
}
const sortSwitchHTML = async (nodes) => {
  let res = await getSwitchNode({cloudId:router.currentRoute.value.query.cloudId,networkId:router.currentRoute.value.params.id});
  if(res.code == 0 && res.data){
    res.data.forEach((item,index)=>{
      nodes.forEach((it,ii)=>{
        if(it.info == item.id){
          it.innerHTML = `<p>${item.interfaceName}</p>
          IP地址：${item.ipAddress ? item.ipAddress : '-'}`
        }
      })
    })
  }
}
const sortHostNode = async (tempedges,hostedges,hostnodes,hostnodesIds) => {
  let nodes = {};
  let res_host = await selectHostNodeList({getInterface:1,getChildren:1,cloudId:router.currentRoute.value.query.cloudId,networkId:router.currentRoute.value.params.id})
  console.log("res",res_host)
  res_host.data.forEach((item,index)=>{
    let node = hostnodes.find((it)=>hostId(it.info) == item.id);
    let num = interfaceNum(node.info);
    if(node.group && !groupObj[node.group].isHas){
      groupObj[node.group].isHas = true;
      defaultdata.combos.push({
        id:'group_'+node.group,
        category:'server_node_group',
        label:groupObj[node.group].label
      });
    }
    if(Number(num)){
      if(!nodes['group_'+node.group]){
        nodes['group_'+node.group] = {0:[],1:[]};
      }
      if(nodes['group_'+node.group][0].length <= 0){
        hostedges.some((it,ii)=>{
          if(it.source == node.id){
            tempedges.push({source:'host_node_group_'+node.group+'_'+0,target:it.target})
          }
        })
        nodes['group_'+node.group][0].push({
          id:'host_node_group_'+node.group+'_'+0,
          comboId: 'group_'+node.group,
          type: 'net_host',
          size: [60, 60],
          style: {lineDash: [5,2.5],stroke:'#0b8235'},
          label:eval('"' + `\\u2460` + '"'),
          length:1,
          labelCfg: {
            position:"top",
            offset: -8,
            style:{fontSize:14}
          }
        })
      }else{
        // eval('"' + `\\u246${(length - 1)}` + '"')
        let nodes0 = nodes['group_'+node.group][0][0];
        nodes0.label = eval('"' + `\\u246${(nodes0.length)}` + '"');
        nodes0.length++;
      }
      formatText(item,'host_node_group_'+node.group+'_'+0)
    }else{
      if(!nodes['group_'+node.group]){
        nodes['group_'+node.group] = {0:[],1:[]};
        
      }
      if(nodes['group_'+node.group][1].length <= 0){
        nodes['group_'+node.group][1].push({
          id:'host_node_group_'+node.group+'_'+1,
          comboId: 'group_'+node.group,
          type: 'net_host',
          size: [60, 60],
          style: {lineDash: [5,2.5],stroke:'#f81d22'},
          label:eval('"' + `\\u2460` + '"'),
          length:1,
          labelCfg: {
            position:"top",
            offset: -8,
            style:{fontSize:14}
          }
        })
      }else{
        let nodes1 = nodes['group_'+node.group][1][0];
        nodes1.label = eval('"' + `\\u246${(nodes1.length)}` + '"');
        nodes1.length++;
      }
      formatText(item,'host_node_group_'+node.group+'_'+1)
    }
  })
  let res = [];
  console.log("nodes",nodes)
  for(let item in nodes){
    if(nodes[item]){
      if(nodes[item][0] && nodes[item][0].length > 0){
        nodes[item][0][0].innerHTML = `<p>物理服务器</p>
                <ul>${InterfaceListObj[nodes[item][0][0].id]}</ul>`;
        res.push(nodes[item][0][0]);
      }
      if(nodes[item][1] && nodes[item][1].length > 0){
        nodes[item][1][0].innerHTML = `<p>物理服务器</p>
                <ul>${InterfaceListObj[nodes[item][1][0].id]}</ul>`;
        res.push(nodes[item][1][0]);
      }
    }
    
  }
  return res;
}
const sorted = async (data) => {
  let combos = [];
  let tempnodes = [];
  let hostnodes = [];
  let hostnodesIds = [];
  let rawedges = [];
  let tempedges = [];
  let hostedges = [];
  data.nodes.forEach((item)=>{
    if(item.type != 'net_terminal'){
      if(item.type == 'net_host'){
        hostnodes.push(item);
        hostnodesIds.push(item.id);
      }else{
        tempnodes.push(item);
      }
    }
  })
  await sortSwitchHTML(tempnodes)
  tempnodes.forEach(async (item,index)=>(function (index){
    sortNodeItems(item,tempnodes,combos,index)
  })(index))
  rawedges = defaultdata.edges.concat(data.edges);
  console.log("rawedges",rawedges)
  rawedges.forEach((item,index)=>{
    item.id = "edge_"+index;
    if(!hostnodesIds.includes(item.source) && !hostnodesIds.includes(item.target)){
      tempedges.push(item);
    }else{
      hostedges.push(item);
    }
  })
  let afternodes = await sortHostNode(tempedges,hostedges,hostnodes,hostnodesIds);
  data.combos = defaultdata.combos;
  if(data.combos)
  data.combos.forEach((item,index)=>{
    sortComboItems(item)
  })
  data.edges = tempedges;
  data.nodes = [...tempnodes,...afternodes];
  console.log("data",data,tempedges)
  rigister()
  return data;
}
const tooltip = new G6.Tooltip({
  offsetX: -20,
  offsetY: -400,
  fixToNode:[1,0.5],
  trigger:"click",
  shouldBegin(e){
    return (e.item.getModel().type != 'lan');
  },
  getContent(e) {
    const outDiv = document.createElement('div');
    outDiv.style.width = '180px';
    if(e.item.getModel().type == 'net_host'){
      let innerHTML = '';
      if(InterfaceListObj[e.item.getModel().id]){
        InterfaceListObj[e.item.getModel().id].forEach((item,index)=>{
          innerHTML += `<li>
            <span>主机名：${item.interfaceName}<br></span>
            <span>网络ip：${item.ipAddress ? item.ipAddress : '-'}<br></span>
            <span>服务器型号：ARM架构<br></span>
            </li>`;
        })
        outDiv.innerHTML = `
          <h4>${e.item.getModel().label}</h4>
          <ul>
            ${innerHTML}
          </ul>`
      }
      
    }else{
      // outDiv.style.display = 'none';
      outDiv.innerHTML = `<div>${e.item.getModel().label}</div>`
    }
    return outDiv
  },
  itemTypes: ['node']
});
const getSwitchInfo = async (id) => {
  let res = await getSwitchNodeInfo({id});
  return `<p>${res.data.interfaceName}</p>
          IP地址：${res.data.ipAddress ? res.data.ipAddress : '-'}`;
}
const getHostInfo = async (id) => {
  let res = await getHostNodeInfo({id});
  if(res.code == 0){
    let length = selectInterfaceListLength(res.data.networkServerInterface,id)
    let label = '';
    if(length > 0){
      label = eval('"' + `\\u246${(length - 1)}` + '"');
    }
    return {innerHTML:formatText(res.data),label};
  }
}
const getHostNode = async (item,nodes,tempedges,hostedges,index,hostnodes,tempnodes) => {
  if(item.group && !groupObj[item.group].isHas){
    groupObj[item.group].isHas = true;
    defaultdata.combos.push({
      id:'group_'+item.group,
      category:'server_node_group',
      label:groupObj[item.group].label
    });
  }
  let res = await getHostNodeInfo({id:item.info.split("host_id:")[1].split(" interface_num:")[0]});
  if(res.code == 0){
    if(res.data.networkServerInterface.length > 0){
      if(!nodes['group_'+item.group]){
        nodes['group_'+item.group] = {0:[],1:[]};
      }
      if(nodes['group_'+item.group][0].length <= 0){
        hostedges.some((it,ii)=>{
          if(it.source == item.id){
            tempedges.push({source:'host_node_group_'+item.group+'_'+0,target:it.target})
          }
        })
        nodes['group_'+item.group][0].push({
          id:'host_node_group_'+item.group+'_'+0,
          comboId: 'group_'+item.group,
          type: 'net_host',
          size: [60, 60],
          style: {lineDash: [5,2.5]},
          label:eval('"' + `\\u2460` + '"'),
          length:1,
          labelCfg: {
            position:"top",
            offset: -8,
            style:{fontSize:14}
          }
        })
      }else{
        // eval('"' + `\\u246${(length - 1)}` + '"')
        let nodes0 = nodes['group_'+item.group][0][0];
        nodes0.label = eval('"' + `\\u246${(nodes0.length)}` + '"');
        nodes0.length++;
      }
      formatText(res.data,'host_node_group_'+item.group+'_'+0)
    }else{
      if(!nodes['group_'+item.group]){
        nodes['group_'+item.group] = {0:[],1:[]};
        
      }
      if(nodes['group_'+item.group][1].length <= 0){
        nodes['group_'+item.group][1].push({
          id:'host_node_group_'+item.group+'_'+1,
          comboId: 'group_'+item.group,
          type: 'net_host',
          size: [60, 60],
          style: {lineDash: [5,2.5]},
          label:eval('"' + `\\u2460` + '"'),
          length:1,
          labelCfg: {
            position:"top",
            offset: -8,
            style:{fontSize:14}
          }
        })
      }else{
        let nodes1 = nodes['group_'+item.group][1][0];
        nodes1.label = eval('"' + `\\u246${(nodes1.length)}` + '"');
        nodes1.length++;
      }
      formatText(res.data,'host_node_group_'+item.group+'_'+1)
    }
      
    // if(index >= hostnodes.length - 1){
      // for(let item in nodes){
      //   tempnodes = tempnodes.concat(nodes[item][0]).concat(nodes[item][1])
      //   console.log("tempnodes",tempnodes)
      // }
      
    // }
  }
}
const formatText = (item,id) => {
  let innerHTML = ``;
  if(!InterfaceListObj[id]){
    InterfaceListObj[id] = ``;
  }
  if(item.networkServerInterface){
    let strarr = item.networkServerInterface.map((it,ii)=>{
      return `<span>${it.interfaceName}：${it.ipAddress ? it.ipAddress : '-'}<br></span>`;
    })
    InterfaceListObj[id] = InterfaceListObj[id] + `<li>
      <span>主机名：${item.hostname}<br></span>
      ${strarr.join('')}
      <span>服务器型号：${item.arch}架构<br></span>
      </li>`;
  }else{
    InterfaceListObj[id] = InterfaceListObj[id] + `<li>
      <span>主机名：${item.hostname}<br></span>
      <span>服务器型号：${item.arch}架构<br></span>
      </li>`;
  }
  
  let outerHTML = `<p>物理服务器</p>
                  <ul>${InterfaceListObj[id]}</ul>`;
  // if(InterfaceListObj[id])
  // return outerHTML;
  // else
  // return '';
}
const draw = () => {
  if(router.currentRoute.value.query.type != 'TERMINAL_CENTER'){
    const width = document.getElementById('container').width;
    const height = document.getElementById('container').height;
    graph.value = new G6.Graph({
      container: 'container',
      fitView:false,
      fitCenter:true,
      width,
      height,
      groupByTypes:false,
      modes: {
        //  'drag-canvas','zoom-canvas',
        default: ['collapse-expand-combo',
          {
            type: 'tooltip',
            shouldBegin(e, self){
              return (e.item.getModel().type != 'lan' && e.item.getModel().innerHTML);
            },
            formatText(model) {
              return model.innerHTML;
            },
            offset: 30,
          }
        ],
      },
      // plugins: [tooltip],
      layout: {
        type: 'dagre',
        rankdir: 'BT', // 可选，默认为图的中心
        // align: 'DL', // 可选 节点对齐方式
        ranksep:35,
        nodesep:30,
        preventOverlap: true, // 开启节点之间的防重叠
        nodeSize: 10,
        sortByCombo:true  // 根据comboId 进行排序，以防止 combo 重叠
      },
      defaultCombo: {
        type: 'rect',
        collapsed: false,
        labelCfg: {
          position: 'top'
        },
      },
    });
  }
  getData({});

  // let centerNodes = graph.value.getNodes().filter((node) => node.getModel().category == 'h_exchange');
// 轮廓
// graph.value.on('afterlayout', () => {
//   const hull1 = graph.value.createHull({
//     id: 'centerNode-hull',
//     type: 'bubble',
//     members: centerNodes,
//     padding: 10,
//   });

//   graph.value.on('afterupdateitem', (e) => {
//     hull1.updateData(hull1.members);
//   });
// });
// graph.addItem('edge', {
//   source:startNode,
//   target:endNode,
//   strokeOpacity:0
// });
}
const selectInterfaceListLength = (data,id) => {
    InterfaceListObj[id] = data;
    return data.length;
}
const createCombos = (subtreeIds) => {
  
  // 第一个参数为 combo ID
  graph.value.createCombo('combo-'+subtreeIds[0], subtreeIds)
  graph.value.layout()
  graph.value.fitCenter()
}
const getVitualData = (cloudId,data) => {
  let temp = {nodes:[],combos:[],edges:[]};
  selectNetworkTopology({cloudId}).then((res)=>{
    res.data.nodes.forEach((item,index)=>{
      if(item.type != 'net-route')
      temp.nodes.push({id:item.type+'_'+item.id,category:item.type,label:''})
      if(item.type == 'net-work'){
        temp.edges.push({source:'lan',target:'net-work_'+item.id});
      }
    })
    res.data.edges.forEach((item,index)=>{
      temp.edges.push({source:'net-work_'+item.fromId,target:'net-server_'+item.toId});
    })
    data.nodes = data.nodes.concat(temp.nodes);
    data.edges = data.edges.concat(temp.edges);
    
  })
}
const getData = ({callback}) => {
  if(router.currentRoute.value.query.type == 'TERMINAL_CENTER'){
    nextTick(()=>{
      sideRef.value.draw();
    })
  }else{
    if(router.currentRoute.value.query.type != 'DOMAIN')
      Object.assign(defaultdata,{
        "nodes": [{
          "id": "lan",
          "type": "lan",
          "label": "局域网",
          "anchorPoints": [
            [0.5, 0],
            [0.5, 1]
          ]
        }],
        "combos":[],
        "edges":[]
      })
    else
      Object.assign(defaultdata,{
        "nodes": [],
        "combos":[],
        "edges":[]
      })
    for(let item in groupObj){
      groupObj[item].isHas = false;
    }
    networkTopologyHost({networkId:router.currentRoute.value.params.id,cloudId:router.currentRoute.value.query.cloudId}).then(async (res)=>{
      let data = {...res.data};
      isShowHardwareLevel.value = (data.nodes && data.nodes.length > 0);
      data.nodes = defaultdata.nodes.concat(data.nodes);
      data.nodes.forEach((item,index)=>{
        item.id = String(item.id);
        item.category = item.type;
        item.info = item.label;
        item.label = undefined;
      })
      data.edges.forEach((item,index)=>{
        item.source = String(item.fromId);
        item.target = String(item.toId);
      })
      G6.Util.processParallelEdges(data.edges);
      // getVitualData(router.currentRoute.value.query.cloudId,data)
      graph.value.read(await sorted(data));
      afterReset()
      if(router.currentRoute.value.query.type == 'DOMAIN'){
        let res_terminal = await networkTopologyTerminal({networkId:router.currentRoute.value.params.id,cloudId:router.currentRoute.value.query.cloudId})
        let terminal_data = {...res_terminal.data};
        isShowTerminalLevel.value = (terminal_data.nodes && terminal_data.nodes.length > 0 && terminal_data.nodes.some((item,index)=>item.type == "net_terminal"));
        //  emit("setTerminalHeight",isShowTerminalLevel.value)
        if(isShowTerminalLevel.value)
        nextTick(()=>{
          sideRef.value.draw(terminal_data);
        })
        let res_virtual = await selectNetworkTopology({cloudId:router.currentRoute.value.query.cloudId})
        let virtual_data = {...res_virtual.data};
        isShowVirtualLevel.value = (virtual_data.nodes && virtual_data.nodes.length > 0);
        // emit("setVirtualHeight",isShowVirtualLevel.value)
        if(isShowVirtualLevel.value)
        nextTick(()=>{
          cloudRef.value.draw(virtual_data);
        })
      }
      
    })
  }
  if(callback)
    callback()
}
const afterReset = () => {
  for(let item in hasComboObj){
    hasComboObj[item] = false;
  }
}
const getTerminalData = (data) => {
  nextTick(()=>{
    sideRef.value.getData(data)
  })
}
onMounted(()=>{
  draw()
})
onUnmounted(()=>{
  isShowVirtualLevel.value = false;
  isShowTerminalLevel.value = false;
})

</script>
<style lang='scss' scoped>
// #side{width: 1535px;}
#container{position: relative;width: 1535px;
  .connetVir{position: absolute;top:-20px;left: 0;}
  .connet{position: absolute;bottom:-20px;left: 0;}
}
:deep(.g6-tooltip) {
  padding: 10px 6px;
  color: #444;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e2e2e2;
  border-radius: 4px;
}
// #cloud{border-bottom: 1px solid #f0f0f0;}
</style>