<template>
    <div class=''>
        <a-modal title="删除设备" v-model:visible="nodeprops.isNodeDelShow" @ok="delNodes" @cancel="cancel" :maskClosable="false" :getContainer="modalBindNode">
            <a-form :model="nodeform" ref="nodeForm" :label-col="{span:6}" :rules="rules">
                <a-form-item name="ids" label="设备">
                    <a-select mode="multiple" v-model:value="nodeform.ids" @change="changeNode" :options="devicelist">
                    </a-select>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { deleteHostNode, deleteInterfaceNode, deleteSwitchNode, delNetworkTerminal, getNetworkTerminalList, getSwitchNode, selectHostNodeList, selectInterfaceNodeList } from '@/api/backend/topo';
import router from '@/router';
import { onMounted, reactive, ref } from 'vue';
const emit = defineEmits(["delNodes","getData","getTerminalData"]);
const props = defineProps({
    nodeprops:Object
})

const devicelist = ref([]);
const emptyLabel = {
    "net_switch":"硬件层-交换机"
}
let graph1;
const nodeOptions = ref();
const defaultform = {
    ids:[]
};
const nodeForm = ref();
const nodeform = reactive({
    ids:[]
});
const options = ref([]);
const rules = {
    ids:[{required:true,message:'请选择设备'}]
}
const cancel = () => {
    props.nodeprops.isNodeDelShow = false;
    nodeForm.value.resetFields();
    Object.assign(nodeform,defaultform);
}
const changeNode = (value,option) => {
    // nodeform.type = option.type;
    options.value = option;
}
const delNodes = () => {
    let ids = {
        net_switch:{api:deleteSwitchNode,ids:[]},
        net_host:{api:deleteHostNode,ids:[]},
        net_server_interface:{api:deleteInterfaceNode,ids:[]},
        net_terminal:{api:delNetworkTerminal,ids:[]},
    };
    options.value.forEach((item,index)=>{
        ids[item.type].ids.push(nodeform.ids[index].split("_")[1]);
    })
    // nodeform.ids.forEach((item,index)=>{
    //     let model = graph1.findById(item).getModel();
    //     ids[model.category].ids.push(Number(item));
    // })
    // console.log("nodeform.ids",nodeform.ids)
    for(let item in ids){
        if(ids[item].ids.length > 0){
            ids[item].api(ids[item].ids).then((res)=>{
                if(item == 'net_terminal')
                emit("getTerminalData",{callback:cancel})
                else
                emit("getData",{callback:cancel})
                
            })
        }
    }
}
const getDevice = async () => {
    devicelist.value = [];
    let res_switch = await getSwitchNode({networkId:router.currentRoute.value.params.id})
    res_switch.data.forEach((item,index)=>{
        devicelist.value.push({value:'switch_'+item.id,type:'net_switch',label:item.interfaceName});
    })
    let res_host = await selectHostNodeList({getInterface:1,cloudId:router.currentRoute.value.query.cloudId,networkId:router.currentRoute.value.params.id})
    res_host.data.forEach(async (item,index)=>{
        devicelist.value.push({value:'host_'+item.id,type:'net_host',label:item.hostname});
        let res_interface = await selectInterfaceNodeList({hostId:item.id});

        // .then((res_interface)=>{
            res_interface.data.forEach((it,ii)=>{
                devicelist.value.push({value:'interface_'+it.id,type:'net_server_interface',label:it.interfaceName});
            })
        // });
    })
    let res_terminal = await getNetworkTerminalList({networkId:router.currentRoute.value.params.id})
    res_terminal.data.forEach((item,index)=>{
        devicelist.value.push({value:'terminal_'+item.id,type:'net_terminal',label:item.name});
    })
    // devicelist.value = devicelist.value;
}
const setInfo = (graph) => {
    graph1 = graph;
    nodeOptions.value = graph.save().nodes;
    getDevice();
}
onMounted(() => {});
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>