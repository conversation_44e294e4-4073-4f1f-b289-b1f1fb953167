<template>
    <div class=''>
        <a-modal :title="nodeprops.isAddNode?'添加设备':'修改设备'" v-model:visible="nodeprops.isNodeShow" @ok="setNodes" @cancel="cancel" :maskClosable="false" :getContainer="modalBindNode">
            <a-form :model="nodeform" ref="nodeForm" :label-col="{span:6}" :rules="rules">
                <a-form-item v-if="nodeprops.isAddNode" label="添加方式">
                    <a-select v-model:value="isShowAdd" @change="changeMethod">
                        <a-select-option :value="false">现有设备</a-select-option>
                        <a-select-option :value="true">新设备</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item name="id" label="设备" v-if="nodeprops.isAddNode && !isShowAdd">
                    <a-select v-model:value="nodeform.id" :options="deviceObj[router.currentRoute.value.query.type].filter(item => item.label != '服务器网卡')">
                        <!--  :dropdownRender="dropdownRender" -->
                        <!-- <template #dropdownRender="{ menuNode: menu }">
                            <v-nodes :vnodes="menu" />
                            <a-divider style="margin: 4px 0" />
                            <div
                                style="padding: 4px 8px; cursor: pointer"
                                @mousedown="e => e.preventDefault()"
                                @click="addItem"
                            >
                                <plus-outlined />
                                新设备
                            </div>
                        </template> -->
                        <!-- <a-select-option v-for="(item,index) in nodeOptions" :key="item.id" :value="item.id" :category="item.category">{{item.label ? item.label : emptyLabel[item.category]+item.id}}</a-select-option> -->
                    </a-select>
                </a-form-item>
                <a-form-item name="category" label="设备类型" v-if="nodeprops.isAddNode && isShowAdd">
                    <a-select v-model:value="nodeform.category" :options="categorysObj[router.currentRoute.value.query.type]" @change="changeCategory">
                    </a-select>
                </a-form-item>
                <a-form-item name="id" label="设备" v-if="!nodeprops.isAddNode">
                    <a-select v-model:value="nodeform.id" @change="changeNode" :options="deviceObj[router.currentRoute.value.query.type]">

                        <!-- <a-select-option v-for="(item,index) in nodeOptions" :key="item.id" :value="item.id" :category="item.category">{{item.label ? item.label : emptyLabel[item.category]+item.id}}</a-select-option> -->
                    </a-select>
                </a-form-item>
                <a-form-item :name="['hostinterfaceform','id']" label="服务器网卡" v-if="nodeform.id == 'net_server_interface'">
                    <a-select v-model:value="nodeform.hostinterfaceform.id" @change="changeNode" :options="switchObj.net_server_interface">

                        <!-- <a-select-option v-for="(item,index) in nodeOptions" :key="item.id" :value="item.id" :category="item.category">{{item.label ? item.label : emptyLabel[item.category]+item.id}}</a-select-option> -->
                    </a-select>
                </a-form-item>
                <template v-if="nodeform.category == 'net_switch'">
                    <!--  v-if="nodeform.category != 'exchange'" -->
                    <a-form-item :name="['hexchangeform','interfaceName']" label="设备名称">
                        <a-input v-model:value="nodeform.hexchangeform.interfaceName"></a-input>
                    </a-form-item>
                    <a-form-item :name="['hexchangeform','isGroup']" label="是否开启高可用">
                        <a-switch v-model:checked="nodeform.hexchangeform.isGroup" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" @change="changeHA" />
                    </a-form-item>
                    <a-form-item :name="['hexchangeform','groupTab']" label="高可用编号" v-if="nodeform.hexchangeform.isGroup">
                        <a-select v-model:value="nodeform.hexchangeform.groupTab" :options="halist">
                        </a-select>
                    </a-form-item>
                    <a-form-item :name="['hexchangeform','ipAddress']" label="IP地址">
                        <a-input v-model:value="nodeform.hexchangeform.ipAddress"></a-input>
                    </a-form-item>
                </template>
                <template v-if="nodeform.category == 'net_host'">
                    <a-form-item :name="['nethostform','hostname']" label="设备名称">
                        <a-input v-model:value="nodeform.nethostform.hostname"></a-input>
                    </a-form-item>
                    <a-form-item :name="['nethostform','type']" label="服务器工作类型">
                        <a-select v-model:value="nodeform.nethostform.type">
                            <a-select-option v-for="(item,index) in hostTypelist" :key="item.id" :value="item.dictValue" :label="item.dictLabel">{{item.dictLabel}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :name="['nethostform','arch']" label="CPU架构">
                        <a-select v-model:value="nodeform.nethostform.arch">
                            <a-select-option v-for="(item,index) in archlist" :key="item.id" :value="item.dictValue" :label="item.dictLabel">{{item.dictLabel}}</a-select-option>
                        </a-select>
                    </a-form-item>
                </template>
                <template v-if="nodeform.category == 'net_server_interface'">
                    <a-form-item :name="['hostinterfaceform','interfaceName']" label="设备名称">
                        <a-input v-model:value="nodeform.hostinterfaceform.interfaceName"></a-input>
                    </a-form-item>
                    <a-form-item :name="['hostinterfaceform','hostId']" label="服务器">
                        <a-select v-model:value="nodeform.hostinterfaceform.hostId" :disabled="!nodeprops.isAddNode">
                            <a-select-option v-for="(item,index) in nethostlist" :value="item.id" :label="item.hostname">{{item.hostname}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :name="['hostinterfaceform','switchId']" label="交换机">
                        <a-select v-model:value="nodeform.hostinterfaceform.switchId" @change="(value,option)=>changeSwitch(value,option,'hostinterfaceform')" allow-clear>
                            <a-select-option v-for="(item,index) in switchlist" :value="item.id" :label="item.interfaceName" :ip="item.ipAddress">{{item.interfaceName}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :name="['hostinterfaceform','ipAddress']" label="IP地址">
                        <a-input v-model:value="nodeform.hostinterfaceform.ipAddress"></a-input>
                    </a-form-item>
                </template>
                <template v-if="nodeform.category == 'net_terminal'">
                    <a-form-item :name="['terminalform','name']" label="设备名称">
                        <a-input v-model:value="nodeform.terminalform.name"></a-input>
                    </a-form-item>
                    <a-form-item :name="['terminalform','switchId']" label="交换机">
                        <a-select v-model:value="nodeform.terminalform.switchId" @change="(value,option)=>changeSwitch(value,option,'terminalform')">
                            <a-select-option v-for="(item,index) in switchlist" :value="item.id" :label="item.interfaceName" :ip="item.ipAddress">{{item.interfaceName}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item :name="['terminalform','ipAddress']" label="IP地址">
                        <a-input v-model:value="nodeform.terminalform.ipAddress"></a-input>
                    </a-form-item>
                </template>
                <!-- <a-form-item :name="['terminalform','name']" label="同步添加到">
                    <a-select v-model:value="nodeform.networkId">
                        <a-select-option v-for="(item,index) in menulist" :value="item.id" :label="item.networkName" :disabled="disableTrans(item)">{{item.networkName}}</a-select-option>
                    </a-select>
                </a-form-item> -->
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { selectDictList } from '@/api/backend/systems/dictionary';
import { getHostNodeInfo, getInterfaceNodeInfo, getNetworkTerminalInfo, getNetworkTerminalList, getSwitchNode, getSwitchNodeInfo, saveHostNode, saveInterfaceNode, saveNetworkTerminal, saveSwitchNode, selectHostNodeList, selectInterfaceNodeList, selectNetworktopology, updateHostNode, updateInterfaceNode, updateNetworkTerminal, updateSwitchNode } from '@/api/backend/topo';
import router from '@/router';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { Divider, Modal } from 'ant-design-vue';
import { computed, createVNode, h, onMounted, reactive, ref } from 'vue';
const emit = defineEmits(["getData","getTerminalData","handleAddNode"]);
const props = defineProps({
    nodeprops:Object
})
const emptyLabel = {
    "net_switch":"硬件层-交换机"
}

let graph1;
const defaultform = {
    id:undefined,
    category:undefined,
    hexchangeform:{
        cloudId: router.currentRoute.value.query.cloudId,
        interfaceName:"",
        networkId:router.currentRoute.value.params.id,
        isGroup:0,
        groupTab:"",
        ipAddress:""
    },
    nethostform:{
        "cloudId": router.currentRoute.value.query.cloudId,
        "type": "",
        "arch": "",
        "hostname": "",
        "networkId": router.currentRoute.value.params.id
    },
    hostinterfaceform:{
        "cloudId": router.currentRoute.value.query.cloudId,
        "hostId": undefined,
        "interfaceName": "",
        "ipAddress": "",
        "macAddress": "",
        "switchId": undefined,
        "switchIp": ""
    },
    terminalform:{
        "cloudId": router.currentRoute.value.query.cloudId,
        "ipAddress": "",
        "name": "",
        "networkId": router.currentRoute.value.params.id,
        "switchId": undefined,
        "switchIp": ""
    }
};
const nodeForm = ref();
const nodeform = reactive({
    id:undefined,
    category:undefined,
    hexchangeform:{
        cloudId: router.currentRoute.value.query.cloudId,
        interfaceName:"",
        networkId:router.currentRoute.value.params.id,
        isGroup:0,
        groupTab:"",
        ipAddress:""
    },
    nethostform:{
        "cloudId": router.currentRoute.value.query.cloudId,
        "type": "",
        "arch": "",
        "hostname": "",
        "networkId": router.currentRoute.value.params.id
    },
    hostinterfaceform:{
        "cloudId": router.currentRoute.value.query.cloudId,
        "hostId": undefined,
        "interfaceName": "",
        "ipAddress": "",
        "macAddress": "",
        "switchId": undefined,
        "switchIp": ""
    },
    terminalform:{
        "cloudId": router.currentRoute.value.query.cloudId,
        "ipAddress": "",
        "name": "",
        "networkId": router.currentRoute.value.params.id,
        "switchId": undefined,
        "switchIp": ""
    }
});
const halist = ref([]);
const nethostlist = ref([]);
const switchlist = ref([]);
const hostTypelist = ref([]);
const archlist = ref([]);
const devicelist = ref([]);
const menulist = ref([]);
const isShowAdd = ref(false);
const menuObj = {
    "TERMINAL_CENTER":["net_terminal"],
    "DATA_CENTER":["net_switch","net_host","net_server_interface"],
    "DATA_STATION":["net_switch","net_host","net_server_interface"]
}
const disableTrans = (item) => {
    if(menuObj[item.type])
    return !menuObj[item.type].includes(nodeform.category)
    else
    return true;
}
const categorysObj = {
    "DOMAIN":[
        // {label:'局域网',value:'net-work'},
        // {
        //     label:'虚拟层',
        //     options:[
        //         {label:'虚拟层-交换机',value:'net-work'},
        //         {label:'虚拟机',value:'net-server'}
        //     ]
        // },
        {
            label:'硬件层',
            options:[
                {label:'硬件层-交换机',value:'net_switch'},
                {label:'服务器',value:'net_host'},
                {label:'服务器网卡',value:'net_server_interface'}
            ]
        },
        {
            label:'边侧',
            options:[
                {label:'边侧终端',value:'net_terminal'}
            ]
        }
    ],
    "DATA_CENTER":[
        {label:'硬件层-交换机',value:'net_switch'},
        {label:'服务器',value:'net_host'},
        {label:'服务器网卡',value:'net_server_interface'}
    ],
    "DATA_STATION":[
        {label:'硬件层-交换机',value:'net_switch'},
        {label:'服务器',value:'net_host'},
        {label:'服务器网卡',value:'net_server_interface'}
    ],
    "TERMINAL_CENTER":[
        {label:'边侧终端',value:'net_terminal'}
    ]
}
const categorys = [
    // {label:'局域网',value:'net-work'},
    // {
    //     label:'虚拟层',
    //     options:[
    //         {label:'虚拟层-交换机',value:'net-work'},
    //         {label:'虚拟机',value:'net-server'}
    //     ]
    // },
    {
        label:'硬件层',
        options:[
            {label:'硬件层-交换机',value:'net_switch'},
            {label:'服务器',value:'net_host'},
            {label:'服务器网卡',value:'net_server_interface'}
        ]
    },
    {
        label:'边侧',
        options:[
            {label:'边侧终端',value:'net_terminal'}
        ]
    }
]
const deviceObj = reactive({
    "DOMAIN":[
        {
            label:'硬件层-交换机',
            options:[]
        },
        {
            label:'服务器',
            options:[]
        },
        {
            label:'服务器网卡',
            options:[]
        },
        {
            label:'边侧终端',
            options:[]
        }
    ],
    "DATA_CENTER":[
        {
            label:'硬件层-交换机',
            options:[]
        },
        {
            label:'服务器',
            options:[]
        },
        {
            label:'服务器网卡',
            options:[]
        }
    ],
    "DATA_STATION":[
        {
            label:'硬件层-交换机',
            options:[]
        },
        {
            label:'服务器',
            options:[]
        },
        {
            label:'服务器网卡',
            options:[]
        }
    ],
    "TERMINAL_CENTER":[
        {
            label:'边侧终端',
            options:[]
        }
    ]
})
const switchObj = reactive({
    "net_server_interface":[]
})
const rules = {
    id:[{required:true,message:'请选择设备'}],
    category:[{required:true,message:'请选择设备类型'}],
    hostinterfaceform:{
        hostId:[{required:true,message:"请选择服务器"}],
    },
    nethostform:{
        type:[{required:true,message:"请选择服务器工作类型"}]
    }
}
const dropdownRender = ({menuNode: menu, props}) => {
    // 创建一个VNode
    const vnode = h('div',{},[
        h(menu),
        createVNode(Divider, { style:{margin: '4px 0'} }),
        h('div',{
            style:{padding: '4px 8px', cursor: 'pointer',textAlign:'center'},
            onClick: addItem
            },[
            h(PlusOutlined),
            '新设备'
        ])
    ])
    
    return vnode;
}
const addItem = () => {
    isShowAdd.value = true;
}
const changeHA = (checked) => {
    if(checked){
        setSwitchNodeList()
    }
}
const changeMethod = (value) => {
    if(value){
        setNethostList();
    }
}
const changeNode = (value,option) => {
    nodeform.category = option.type;
    if(nodeform.category == 'net_switch'){
        getSwitchInfo(value.split("_")[1])
        if(nodeform.hexchangeform.isGroup){
            setSwitchNodeList()
        }
    }else if(nodeform.category == 'net_host'){
        sethostTypelist();
        getHostInfo(value.split("_")[1]);
        setArchlist();
    }else if(nodeform.category == 'net_server_interface'){
        setNethostList()
        setSwitchNodeList()
        getInterfaceInfo(value.split("_")[1])
    }else if(nodeform.category == 'net_terminal'){
        getTerminalInfo(value.split("_")[1]);
        setSwitchNodeList()
    }
    // console.log("model",model)
    
    // comboOptions.value = combotemp.value;
    // let sources = [];
    // let targets = [];
    // graph1.findAll('edge', (edge) => {
    //     let edgemodel = edge.getModel();
    //     if(edgemodel.target == value){
    //         sources.push(edgemodel.source)
    //     }
    //     if(edgemodel.source == value){
    //         targets.push(edgemodel.target)
    //     }
    // });
    // nodeform.edges.source = sources;
    // nodeform.edges.target = targets;
}
const sethostTypelist = () => {
    selectDictList({dictType:'HOST_TYPE'}).then((res)=>{
        hostTypelist.value = res.data;
    })
}
const setArchlist = () => {
    selectDictList({dictType:'ARCHITECTURE'}).then((res)=>{
        archlist.value = res.data;
    })
}
const changeCategory = (value) => {
    if(value == 'net_server_interface'){
        // setNethostList();
        setSwitchNodeList();
    }else if(value == 'net_terminal'){
        setSwitchNodeList();
    }else if(value == 'net_host'){
        sethostTypelist();
        setArchlist();
    }
}
const changeSwitch = (value,option,key) => {
    nodeform[key].switchIp = option.ip;
}
const setNodes = () => {
    console.log("save",nodeform)
    nodeForm.value.validate().then(()=>{
        if(props.nodeprops.isAddNode){
            if(isShowAdd.value){
                // nodeform.id = String(nodeOptions.value.length + comboOptions.value.length + 1);
                if(nodeform.category == 'net_switch'){
                    saveSwitchNode(nodeform.hexchangeform).then((res)=>{
                        emit("getData",{callback:cancel})
                    })
                }else if(nodeform.category == 'net_host'){
                    saveHostNode({...nodeform.nethostform,deleted:1}).then((res)=>{
                        emit("getData",{callback:()=>{
                            cancel();
                            Modal.confirm({
                                title: () => '请前往创建服务网卡',
                                icon: () => createVNode(ExclamationCircleOutlined),
                                onOk() {
                                    // nodeprops.isAddNode = true;
                                    nodeform.category = 'net_server_interface';
                                    emit("handleAddNode");
                                }
                            });
                        }})
                    })
                    
                }else if(nodeform.category == 'net_server_interface'){
                    saveInterfaceNode(nodeform.hostinterfaceform).then((res)=>{
                        emit("getData",{callback:cancel})
                    })
                }else if(nodeform.category == 'net_terminal'){
                    saveNetworkTerminal(nodeform.terminalform).then((res)=>{
                        // emit("getTerminalData",{callback:cancel})
                        emit("getData",{callback:cancel})
                    })
                }
            }else{
                console.log("updateadd",nodeform)
                let id = nodeform.id.split('_')[1];
                let category = nodeform.id.split('_')[0];
                if(category == 'switch'){
                    updateSwitchNode({id,networkId:nodeform.hexchangeform.networkId}).then((res)=>{
                        emit("getData",{callback:cancel})
                    })
                }else if(category == 'host'){
                    updateHostNode({id,networkId:nodeform.nethostform.networkId}).then((res)=>{
                        emit("getData",{callback:cancel})
                    })
                    
                }else if(nodeform.id == 'net_server_interface'){
                    updateInterfaceNode({id:nodeform.hostinterfaceform.id,networkId:nodeform.hostinterfaceform.networkId}).then((res)=>{
                        emit("getData",{callback:cancel})
                    })
                }else if(category == 'terminal'){
                    updateNetworkTerminal({id,networkId:nodeform.terminalform.networkId}).then((res)=>{
                        // emit("getTerminalData",{callback:cancel})
                        emit("getData",{callback:cancel})
                    })
                }
            }
            
        }else{
            if(nodeform.category == 'net_switch'){
                nodeform.hexchangeform.id = nodeform.id.split('_')[1];
                updateSwitchNode(nodeform.hexchangeform).then((res)=>{
                    emit("getData",{callback:cancel})
                })
            }else if(nodeform.category == 'net_host'){
                nodeform.nethostform.id = nodeform.id.split('_')[1];
                updateHostNode(nodeform.nethostform).then((res)=>{
                    emit("getData",{callback:cancel})
                })
                
            }else if(nodeform.category == 'net_server_interface'){
                // nodeform.hostinterfaceform.id = nodeform.id.split('_')[1];
                updateInterfaceNode(nodeform.hostinterfaceform).then((res)=>{
                    emit("getData",{callback:cancel})
                })
            }else if(nodeform.category == 'net_terminal'){
                nodeform.terminalform.id = nodeform.id.split('_')[1];
                updateNetworkTerminal(nodeform.terminalform).then((res)=>{
                    // emit("getTerminalData",{callback:cancel})
                    emit("getData",{callback:cancel})
                })
            }

            // let deledges = [];
            // graph1.findAll('edge', (edge) => {
            //     let edgemodel = edge.getModel();
            //     console.log("edge",edge,nodeform)
            //     if(nodeform.id == edgemodel.target){
            //         if(!nodeform.edges.source.includes(edgemodel.source)){
            //             deledges.push(edgemodel);
            //         }else{
            //             nodeform.edges.source.splice(nodeform.edges.source.indexOf(edgemodel.source),1)
            //         }
            //     }
            //     if(nodeform.id == edgemodel.source){
            //         if(!nodeform.edges.target.includes(edgemodel.target)){
            //             deledges.push(edgemodel);
            //         }else{
            //             nodeform.edges.target.splice(nodeform.edges.target.indexOf(edgemodel.target),1)
            //         }
            //     }
            // })
            // console.log("deledges",deledges)
        }
    })
    
    
}
const setNethostList = () => {
    selectHostNodeList({getInterface:1,cloudId:router.currentRoute.value.query.cloudId,networkId:router.currentRoute.value.params.id}).then((res)=>{
        nethostlist.value = res.data;
        let routerType = router.currentRoute.value.query.type;
        if(routerType != 'TERMINAL_CENTER'){
            if(routerType == 'DOMAIN'){
                categorysObj[routerType][0].options[2].disabled = !res.data || res.data.length <= 0;
            }else{
                categorysObj[routerType][2].disabled = (!res.data || res.data.length <= 0);
            }
        }
    })
}
const setSwitchNodeList = () => {
  let haObj = {};
  halist.value = [];
  getSwitchNode({networkId:router.currentRoute.value.params.id}).then((res)=>{
    switchlist.value = res.data;
    res.data.forEach((item,index)=>{
        if(item.groupTab){
            if(!haObj[item.groupTab])
                haObj[item.groupTab] = [];
            haObj[item.groupTab].push(item.interfaceName);
        }
    })
    for(let item in haObj){
        halist.value.push({value:item,label:haObj[item].join(',')+'('+item+')'})
    }
    // getTerminalList();
  })
}
const getSwitchInfo = (id) => {
    getSwitchNodeInfo({id}).then((res)=>{
        if(res.code == 0 && res.data){
            Object.assign(nodeform.hexchangeform,res.data);
        }
    })
}
const getInterfaceInfo = (id) => {
    getInterfaceNodeInfo({id}).then((res)=>{
        if(res.code == 0 && res.data){
            Object.assign(nodeform.hostinterfaceform,res.data);
        }
    })
}
const getHostInfo = (id) => {
    getHostNodeInfo({id}).then((res)=>{
        if(res.code == 0 && res.data){
            Object.assign(nodeform.nethostform,res.data);
        }
    })
}
const getTerminalInfo = (id) => {
    getNetworkTerminalInfo({id}).then((res)=>{
        if(res.code == 0 && res.data){
            Object.assign(nodeform.terminalform,res.data);
        }
    })
}
const cancel = () => {
    props.nodeprops.isNodeShow = false;
    nodeForm.value.resetFields();
    Object.assign(nodeform,JSON.parse(JSON.stringify(defaultform)));
    nodeform.category = undefined;
    nodeform.id = undefined;
    let type = router.currentRoute.value.query.type;
    deviceObj[type].forEach((item,index)=>{
        item.options = [];
    })
    isShowAdd.value = false;
}
const getTerminalList = () => {
    let tempdata = [];
    switchlist.value.forEach((item,index)=>{
        selectInterfaceNodeList({switchId:item.id}).then((res)=>{
            if(res.code == 0){
                let tempswitch = [...res.data];
                tempswitch.forEach((item,index)=>{
                    item.category = 'net_server_interface';
                    item.type = 'net_server_interface';
                    item.label = item.interfaceName;
                })
                tempdata = tempdata.concat(tempswitch);
            }
        })
    })
}
const getDevice = async (params) => {
    switchObj['net_server_interface'] = [];
    devicelist.value = [];
    let type = router.currentRoute.value.query.type;
    console.log("type",type)
    if(!type.includes('TERMINAL_')){
        let res_switch = await getSwitchNode(params)
        res_switch.data.forEach((item,index)=>{
            deviceObj[type][0].options.push(
                {value:'switch_'+item.id,type:'net_switch',label:item.interfaceName}
            )
            // devicelist.value.push({value:'switch_'+item.id,type:'net_switch',label:item.interfaceName});
        })
        let res_host = await selectHostNodeList(params)
        res_host.data.forEach((item,index)=>{
            deviceObj[type][1].options.push(
                {value:'host_'+item.id,type:'net_host',label:item.hostname}
            )
            // devicelist.value.push({value:'host_'+item.id,type:'net_host',label:item.hostname});
            if(!props.nodeprops.isAddNode){
                switchObj['net_server_interface'].push({label:item.hostname,options:[]})
                item.networkServerInterface.forEach((it,ii)=>{
                    switchObj['net_server_interface'][index].options.push({value:'interface_'+it.id,type:'net_server_interface',label:it.interfaceName})
                    // devicelist.value.push({value:'interface_'+it.id,type:'net_server_interface',label:it.interfaceName});
                })
                if(index >= res_host.data.length - 1){
                    if(switchObj['net_server_interface'].length > 0){
                        switchObj['net_server_interface'].some((it)=>{
                            if(it.options.length > 0){
                                deviceObj[type][2].options.push({label:'服务器网卡',value:'net_server_interface'});
                                return true;
                            }
                        })
                    }
                }
            }
        })
    }
    if(!type.includes('DATA_')){
        let res_terminal = await getNetworkTerminalList(params)
        res_terminal.data.forEach((item,index)=>{
            deviceObj[type][type == 'DOMAIN' ? 3 : 0].options.push(
                {value:'terminal_'+item.id,type:'net_terminal',label:item.name}
            )
            // devicelist.value.push({value:'terminal_'+item.id,type:'net_terminal',label:item.name});
        })
    }
}
const getTopoMenu = (parentId) => {
    selectNetworktopology({parentId}).then((res)=>{
        menulist.value = res.data;
    })
}
const setInfo = (graph) => {
    console.log("修改")
    graph1 = graph;
    // 同步添加到
    // getTopoMenu(router.currentRoute.value.params.id);
    // selectInterfaceNodeList({switchId:8})
    if(props.nodeprops.isAddNode){
        
        getDevice({cloudId:router.currentRoute.value.query.cloudId})
    }else{
        getDevice({getInterface:1,cloudId:router.currentRoute.value.query.cloudId,networkId:router.currentRoute.value.params.id})
    }
}
onMounted(() => {});
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>