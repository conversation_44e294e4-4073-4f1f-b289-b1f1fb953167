<template>
    <div class='options'>
        <a-row justify="space-between">
            <div><a @click="router.back()"><DoubleLeftOutlined />返回</a></div>
            <a-space size="large">
                <b>图例：</b>
                <div v-for="(item,index) in legend" :key="index">
                    <div v-if="item.img" class="image" :style="{backgroundImage:`url(${item.img})`}"></div>
                    <div v-else :class="item.class"></div>
                    <span>{{item.title}}</span>
                </div>
                <!-- <a-dropdown>
                        <a class="ant-dropdown-link" @click.prevent>
                            图例
                            <DownOutlined />
                        </a>
                        <template #overlay>
                            <a-menu>
                                <a-menu-item v-for="(item,index) in legend" :key="index">
                                    <div>
                                        <div v-if="item.img" class="image" :style="{backgroundImage:`url(${item.img})`}"></div>
                                        <div v-else :class="item.class"></div>
                                        <span>{{item.title}}</span>
                                    </div>
                                </a-menu-item>
                            </a-menu>
                        </template>
                    </a-dropdown> -->
                <!-- <span>图例：</span>
                <div>
                <div class="legend"></div>
                    <span>高可用</span>
                </div> -->
                <b>操作：</b>
                <!-- <a-tooltip title="设备"> -->
                    <a-dropdown>
                        <a class="ant-dropdown-link" @click.prevent>
                            <SisternodeOutlined />
                            <DownOutlined />
                        </a>
                        <template #overlay>
                            <a-menu>
                            <a-menu-item>
                                <a href="javascript:;" @click="handleAddNode">添加设备</a>
                            </a-menu-item>
                            <a-menu-item>
                                <a href="javascript:;" @click="handleEditNode">修改设备</a>
                            </a-menu-item>
                            <a-menu-item>
                                <a href="javascript:;" @click="handleDelNode">删除设备</a>
                            </a-menu-item>
                            </a-menu>
                        </template>
                    </a-dropdown>
                <!-- </a-tooltip> -->
            </a-space>
        </a-row>
        <MockNode ref="nodeRef" :nodeprops="nodeprops" :categorys="categorys" @getData="getData" @getTerminalData="getTerminalData" @handleAddNode="handleAddNode" />
        <MockNodeDel ref="nodeDelRef" :nodeprops="nodeprops" @delNodes="removeNodesAndEdges" @getData="getData" @getTerminalData="getTerminalData" />
    </div>
</template>
<script lang='ts' setup>
import { nextTick, onMounted, reactive, ref } from 'vue';
import MockNode from "./nodes/index.vue";
import MockNodeDel from "./nodes/del.vue";
import router from '@/router';
const emit = defineEmits(["getData","getTerminalData"])
const props = defineProps(["graph"]);
const legend = [
    {title:'虚拟机',img:`/topoicon/net-server.svg`},
    {title:'路由',img:`/topoicon/net-route.svg`},
    {title:'交换机',img:`/topoicon/v_exchange.svg`},
    {title:'局域网',img:`/topoicon/net-work.svg`},
    {title:'交换机',img:`/topoicon/h_exchange.svg`},
    {title:'高可用',class:'legend'},
    {title:'物理服务器',img:`/topoicon/server_node.svg`},
    {title:'终端',img:`/topoicon/provide.svg`}
]
const nodeRef = ref();
const nodeDelRef = ref();
const nodeprops = reactive({
    isNodeShow:false,
    isNodeDelShow:false,
    isAddNode:true,
    isEdgeShow:false
});
const categorys = [
    {label:'局域网',value:'lan'},
    {label:'虚拟层-交换机',value:'net-work'},
    {label:'硬件层-交换机',value:'h_exchange'},
    {label:'虚拟机',value:'net-server'},
    {label:'服务器',value:'server_node'},
    {label:'边侧终端',value:'net_terminal'}
]
const handleAddNode = () => {
    nodeprops.isNodeShow = true;
    nodeprops.isAddNode = true;
    nextTick(()=>{
        nodeRef.value.setInfo(props.graph);
    })
}
const handleEditNode = () => {
  nodeprops.isNodeShow = true;
  nodeprops.isAddNode = false;
  nextTick(()=>{
    nodeRef.value.setInfo(props.graph);
  })
}
const handleDelNode = () => {
  nodeprops.isNodeDelShow = true;
  nextTick(()=>{
    nodeDelRef.value.setInfo(props.graph);
  })
}
const getData = (data) => emit("getData",data)
const getTerminalData = (data) => emit("getTerminalData",data)
onMounted(() => {})
</script>
<style lang='scss' scoped>
.options{width: 1535px;border-bottom: 1px solid #EFF7FA;}
.legend{display: inline-block;vertical-align: middle;width: 24px;height: 16px;border-radius: 8px;border: 2px solid #D1D4FD;background-color: #EFF7FA;}
.image{display: inline-block;height: 16px;width: 16px;vertical-align: middle;margin-right: 4px;background-size: cover;}
</style>