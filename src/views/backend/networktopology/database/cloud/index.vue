<template>
    <div id="cloud" v-if="isShowVirtualLevel">
        <div style="position:absolute;left:30px;top:20px">虚拟层</div>
    </div>
</template>
<script setup>
import { selectNetworkTopology } from '@/api/backend/devops/network';
import router from '@/router';
import G6 from '@antv/g6/lib/index.js';
import { onMounted, ref } from 'vue';
const emit = defineEmits(['setVirtualHeight'])
const defaultdata = {
  "nodes": [
  //   {
  //   "id": "lan",
  //   "type": "lan",
  //   "label": "局域网",
  //   "anchorPoints": [
  //     [0.5, 0],
  //     [0.5, 1]
  //   ],
    
  // }
  ],
  "edges":[]
}
const isShowVirtualLevel = ref(true);
const graph = ref();
const rigister = () => {
  G6.registerNode('net-physical', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = size[0] * 0.8;
      const height = size[1] * 0.8;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/net-work.svg`
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'net-physical-img'
      });
    }
  },
  // 继承了 rect 节点
  'rect');
  G6.registerNode('net-work', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = size[0] * 0.7;
      const height = size[1] * 0.7;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/v_exchange.svg`
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'net-work-img'
      });
    }
  },
  'circle');
  G6.registerNode('net-server', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = size[0] * 0.8;
      const height = size[1] * 0.8;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/net-server.svg`
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'net-server-img'
      });
    }
  },
  // 继承了 rect 节点
  'rect');
  G6.registerNode('net-route', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = size[0] * 0.8;
      const height = size[1] * 0.8;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/net-route.svg`
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'net-route-img'
      });
    }
  },
  // 继承了 rect 节点
  'rect');
}
let startNode;
let endNode;
const sortNodeItems = (item,data) => {
  item.labelCfg = {
    style:{fontSize:0}
  }
  item.style = {
    fillOpacity: 0
  }
  if(item.status){
    item.style.stroke = '#0b8235';
  }else{
    item.style.stroke = '#f81d22';
  }
    item.anchorPoints = [
        [0.5, 0],[0.6, 0],[0.4, 0],[0.7, 0],[0.3, 0],
        [0.5, 1],[0.6, 1],[0.4, 1],[0.7, 1],[0.3, 1]
      ];
    if(item.category == 'net-physical'){
      item.type = 'net-physical';
      item.size = [60, 60];
      item.style.lineDash = [5,2.5];
    }
    if(item.category == 'net-work'){
      item.type = 'net-work';
      item.size = [60, 60];
      item.style.lineDash = [5,2.5];
      
    }
    if(item.category == 'net-server'){
      item.type = 'net-server';
      item.size = [60, 60];
      item.style.lineDash = [5,2.5];
    }
    if(item.category == 'net-route'){
      item.type = 'net-route';
      item.size = [60, 60];
      item.style.lineDash = [5,2.5];
    }
    return item;
}
const sortComboItems = (item) => {
  item.style = {
    fillOpacity: 0
  }
  item.anchorPoints = [
      [0.5, 0],[0.6, 0],[0.4, 0],[0.7, 0],[0.3, 0],
      [0.5, 1],[0.6, 1],[0.4, 1],[0.7, 1],[0.3, 1]
    ];
  if(item.category == 'level'){
    item.type = 'rect';
    item.labelCfg = {position:'left'};
    // item.fixSize = [3000,400];
    // item.x = 1775;
    // if(item.label == '硬件层')
    // item.y = 365;
    // if(item.label == '虚拟层')
    // item.y = 665;
    // if(item.label == '边侧')
    // item.y = 965;
  }
  return item;
}
const sorted = (data) => {
  data.nodes.forEach((item,index)=>{
    sortNodeItems(item,data)
  })
  if(data.combos)
  data.combos.forEach((item,index)=>{
    sortComboItems(item)
  })
  data.edges.forEach((item,index)=>{
    item.id = "edge_"+index
  })
  rigister()
  return data;
}
const tooltip = new G6.Tooltip({
  offsetX: -30,
  offsetY: -60,
  fixToNode:[1,0.5],
  trigger:"click",
  shouldBegin(e){
    return (e.item.getModel().type != 'net-physical');
  },
  getContent(e) {
    const outDiv = document.createElement('div');
    outDiv.style.width = '180px';
    outDiv.innerHTML = `<div>${e.item.getModel().label}<div>`
    return outDiv
  },
  itemTypes: ['node']
});
const draw = (data) => {
  let isHideTootip = false;
  const width = document.getElementById('cloud').width;
  const height = document.getElementById('cloud').height;
  graph.value = new G6.Graph({
    container: 'cloud',
    fitView:false,
    fitCenter:true,
    width,
    height,
    groupByTypes:false,
    modes: {
      //  'drag-canvas','zoom-canvas',
      default: [{
          type: 'drag-canvas',
          direction: 'x',
        },
        'collapse-expand-combo',
        {
          type: 'tooltip',
          shouldBegin(e, self){
            console.log("shouldBegin",isHideTootip)
            return (e.item.getModel().type != "net-physical") && !isHideTootip;
          },
          offset: 30,
        }
      ],
    },
    // plugins: [tooltip],
    layout: {
      type: 'dagre',
      rankdir: 'BT', // 可选，默认为图的中心
      // align: 'DL', // 可选 节点对齐方式
      ranksep:35,
      nodesep:30,
      preventOverlap: true, // 开启节点之间的防重叠
      nodeSize: 10,
      sortByCombo:true  // 根据comboId 进行排序，以防止 combo 重叠
    },
    defaultCombo: {
      type: 'rect',
      collapse:false,
      labelCfg: {
        position: 'top'
      },
    },
  });
  graph.value.on('node:mouseover',(e)=>{
    if(e.target.cfg.name == "net-work-img" || e.target.cfg.name == "net-server-img")
      isHideTootip = false;
  })
  // 使用graph的onTooltipChange事件来控制tooltip
  graph.value.on('node:mouseout', (e) => {
    if(e.target.cfg.name == "net-work-img" || e.target.cfg.name == "net-server-img"){
      isHideTootip = true;
    }
  });
  getVitualData(router.currentRoute.value.query.cloudId,defaultdata,data)
}
const removeEdges = (edges) => {
  edges.forEach((item)=>{
    graph.value.removeItem(item.id);
  })
  const data = graph.value.save();
  graph.value.read(data);
}
const setEdge = (edge,callback) => {
  let instance = graph.value.findById(edge.id);
  if(instance){
    let model = instance.getModel()
    if(!(edge.source == model.source && edge.target == model.target)){
      edge.targetAnchor = edgeAnchor[edge.source+edge.target] === undefined ? 0 : edgeAnchor[edge.source+edge.target]++;
      // removeEdges([{...model}])
      graph.value.updateItem(instance, edge);
    }
  }else{
    graph.value.addItem('edge', edge);
  }
  graph.value.layout();
  const data = graph.value.save();
  graph.value.data(data);
  setTimeout(()=>{
    if(callback)
    callback()
  })
}
const addNodes = ({node,callback}) => {
  const data = graph.value.save();
  data.nodes.push({...sortNodeItems(node)});
  graph.value.read(data);
  node.edges.source.forEach((item,index)=>{
    // graph.addItem('edge', {source:item,target:node.id});
    setEdge({source:item,target:node.id})
  })
  node.edges.target.forEach((item,index)=>{
    // graph.addItem('edge', {source:node.id,target:item});
    setEdge({source:node.id,target:item})
  })
  if(callback)
    callback()
  // 自动布局
  // graph.layout();
}
const updateNodes = ({node,callback,deledges}) => {
  if(deledges){
    removeEdges(deledges);
  }
  let model = graph.value.findById(node.id).getModel();
  if(!node.label)
    node.label = node.rawLabel;
  model = {...model,...node}
  graph.value.updateItem(node.id, model);
  const data = graph.value.save();
  graph.value.read(data);
  node.edges.source.forEach((item,index)=>{
    // graph.addItem('edge', {source:item,target:node.id});
    setEdge({source:item,target:node.id})
  })
  node.edges.target.forEach((item,index)=>{
    // graph.addItem('edge', {source:node.id,target:item});
    setEdge({source:node.id,target:item})
  })
  if(callback)
    callback()
}
const addCombos = ({combo,nodes,callback}) => { 
  // 第一个参数为 combo 配置
  graph.value.createCombo({...sortComboItems(combo)}, nodes);
  const data = graph.value.save();
  graph.value.data(data);
  combo.edges.source.forEach((item,index)=>{
    // graph.addItem('edge', {source:item,target:node.id});
    setEdge({source:item,target:combo.id})
  })
  combo.edges.target.forEach((item,index)=>{
    // graph.addItem('edge', {source:node.id,target:item});
    setEdge({source:combo.id,target:item})
  })
  if(callback)
    callback()
}
// 删除combo及其连线的函数
function removeComboAndEdges(comboId) {
  const data = graph.value.save();
  graph.value.read(data);
  const combo = graph.value.findById(comboId);
  if (combo) {
    graph.value.removeItem(combo);
  }
}
const updateCombos = ({combo,nodes,callback,delchild,deledges}) => {
  if(deledges){
    removeEdges(deledges);
  }
  if(nodes.length > 0){
    if(!combo.label)
    combo.label = combo.rawLabel;
    
    // 更新子集
    nodes.forEach((item)=>{
      const node1 = graph.value.findById(item);
      let model = node1.getModel();
      model.comboId = combo.id;
      graph.value.updateItem(node1, model);
    
      const data = graph.value.save();
      graph.value.read(data);
    })
    delchild.forEach((item)=>{
      if(item.comboId)
      delete item.comboId;
      graph.value.updateItem(item.id, item);
      const data = graph.value.save();
      graph.value.read(data);
    })
    graph.value.updateItem(combo.id,combo);
    // graph.updateCombo(combo.id);
    // const data = graph.save();
    // graph.read(data);
    
    combo.edges.source.forEach((item,index)=>{
      // graph.addItem('edge', {source:item,target:node.id});
      setEdge({source:item,target:combo.id})
    })
    combo.edges.target.forEach((item,index)=>{
      // graph.addItem('edge', {source:node.id,target:item});
      setEdge({source:combo.id,target:item})
    })
    
  // 更新 node1 所属的 combo 及其所有祖先 combo 的大小和位置
  // 未改名
    // if(!combo.label)
    // combo.label = combo.rawLabel;
    // // 更新label、父级等
    // graph.updateItem(combo.id,combo);
    // // graph.updateCombo(combo.id);
  }else{
    // 获取combo中的所有nodes
    const nodesInCombo = graph.value.getComboChildren(combo.id).nodes;
    // 移除combo中的nodes
    nodesInCombo.forEach(node => {
      // graph.removeItem(node);
      let model = node.getModel();
      delete model.comboId;
      graph.value.updateItem(node, model);
    });
    // 调用函数删除指定的combo及其连线
    removeComboAndEdges(combo.id);
  }
  const data = graph.value.save();
  graph.value.read(data);
  if(callback)
    callback()
}
const getVitualData = async (cloudId,defaultdataparams,data) => {
  let temp = {nodes:[],combos:[],edges:[]};
  if(!data){
    let res = await selectNetworkTopology({cloudId});
    data = {...res.data};
  }
  // selectNetworkTopology({cloudId}).then((res)=>{
    isShowVirtualLevel.value = (data.nodes && data.nodes.length > 0);
    // emit("setVirtualHeight",isShowVirtualLevel.value)
    data.nodes.forEach((item,index)=>{
      temp.nodes.push({id:String(item.id),category:item.type,label:item.label})
      // if(item.type == 'net-work'){
      //   temp.edges.push({source:'lan',target:String(item.id)});
      // }
    })
    data.edges.forEach((item,index)=>{
      temp.edges.push({source:String(item.fromId),target:String(item.toId)});
    })
    // data.nodes = data.nodes.concat(temp.nodes);
    // data.edges = data.edges.concat(temp.edges);
    // console.log("data.nodes",data.nodes)
    
    graph.value.data(sorted(temp));
    graph.value.render();
  // })
}
onMounted(()=>{
  
})
defineExpose({draw})
</script>
<style lang='scss' scoped>
#cloud{width: 1535px;position: relative;}
</style>