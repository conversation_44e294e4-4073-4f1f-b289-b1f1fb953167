<template>
    <div class='contentPadding'>
        <div id="container"></div>
    </div>
</template>
<script setup>
import router from '@/router';
import G6 from '@antv/g6/lib/index.js';
import { nextTick, onMounted, reactive, ref, watch } from 'vue';
import { canvasNetworktopology, getNetworkTerminalList, getSwitchNode, selectNetworktopology } from "@/api/backend/topo";
import emiter from "@/utils/Bus";
import { AllListTodagre, singleListTodagre } from "./utils/listTodagre";
import { listToTreeTopo } from "@/utils/tool";
const categorys = [
    {label:'广域网',value:'CLOUD'},
    {label:'数据中心',value:'DATA_STATION'},
    {label:'交换机',value:'exchange'},
    {label:'边侧',value:'TERMINAL_STATION'}
]
const comboCategorys = [
    {label:'云主站',value:'DATA_CENTER'}
];
const nodeRef = ref();
const nodeDelRef = ref();
const comboRef = ref();
const edgeRef = ref();
let graph;
const rigister = () => {
  G6.registerNode('DATA_STATION', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = 100 * 0.8;
      const height = 65.22 * 0.8;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/database.svg`,
          cursor:'pointer'
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'DATA_STATION-img'
      });
    }
  },
  // 继承了 rect 节点
  'ellipse');
  G6.registerNode('TERMINAL_STATION', {
    afterDraw(cfg, group) {
      const size = cfg.size;
      const width = size * 0.5;
      const height = size * 0.5;
      // 添加图片
      const image = group.addShape('image', {
        attrs: {
          x: - width / 2,
          y: - height / 2,
          width: width,
          height: height,
          img: `/topoicon/side.svg`
        },
        // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
        name: 'TERMINAL_STATION-img'
      });
    }
  },
  // 继承了 rect 节点
  'diamond');
}
const sortNodeItems = (item) => {
  item.anchorPoints = [
    [0.5, 0], [0.5, 1]
  ];
  item.style = {
      fillOpacity: 0
    }
    if(item.category == 'CLOUD' || item.category == 'exchange'){
      item.type = 'image';
      item.img = `/topoicon/${item.category.toLowerCase()}.svg`;
      item.labelCfg = {
        "position": "center"
      };
      item.size = [164,100];
    }
    if(item.category == 'TERMINAL_STATION'){
      item.type = 'TERMINAL_STATION';
      item.size = 100;
      item.style.lineDash = [10,5];
      item.labelCfg = {
        "position": "top",
        "offset":-20
      };
    }
    if(item.category == 'DATA_STATION'){
      item.type = 'DATA_STATION';
      item.size = [100 * 1.4, 65.22 * 1.4]
      item.style.lineDash = [4,2]
      item.labelCfg = {
        "position": "top",
        "offset":-20
      }
      // item.img = `/topoicon/${item.category}.svg`
    }
    return item;
}
const sortComboItems = (item) => {
  item.anchorPoints = [
    [0.5, 0]
  ]
  item.style = {
    fillOpacity: 0
  }
  if(item.category == 'DATA_CENTER' || item.category == 'TERMINAL_CENTER'){
    item.type = "rect";
    item.style.lineDash = [10,5];
  }
  return item;
}
const edgeAnchor = {};
const sorted = (data) => {
  data.nodes.forEach((item,index)=>{
    sortNodeItems(item);
  })
  if(data.combos)
  data.combos.forEach((item,index)=>{
    sortComboItems(item);
  })
  data.edges.forEach((item,index)=>{
    if(edgeAnchor[item.source+item.target]===undefined){
      edgeAnchor[item.source+item.target] = 0;
      item.targetAnchor = edgeAnchor[item.source+item.target]++;
    }else{
      item.targetAnchor = edgeAnchor[item.source+item.target]++;
    }
  })
  rigister()
  return data;
}
const listenerFUNC = () => {
  if (!graph || graph.get('destroyed')) return
    let container = document.getElementById('container')
    let height = container.offsetHeight
    let width = container.offsetWidth
    if (!width || !height) return
    graph.changeSize(width, height) // 接受两个参数（改变的宽度，高度）
};
const draw = (networkId) => {
  const width = document.getElementById('container').width;
  const height = document.getElementById('container').height - 22;
  graph = new G6.Graph({
    container: 'container',
    width,
    height,
    // translate the graph to align the canvas's center, support by v3.5.1
    fitCenter: true,
    fitView: false,
    // Set groupByTypes to false to get rendering result with reasonable visual zIndex for combos
    groupByTypes:false,
    enabled: false, // 禁止锚点移动
    modes: {
      default: ["drag-canvas","zoom-canvas",'collapse-expand-combo'],
    },
    layout: {
      type: 'dagre',
      sortByCombo: false,
      ranksep: 30,
      nodesep: 30,
    },
    defaultCombo: {
      type: 'rect',
      /* The minimum size of the combo. combo 最小大小 */
      size: [50, 50],
      /* style for the keyShape */
      // style: {
      //   lineWidth: 1,
      // },
      labelCfg: {
        /* label's offset to the keyShape */
        // refY: 10,
        /* label's position, options: center, top, bottom, left, right */
        position: 'top',
        /* label's style */
        // style: {
        //   fontSize: 18,
        // },
      },
    },
    /* styles for different states, there are built-in styles for states: active, inactive, selected, highlight, disable */
    /* you can extend it or override it as you want */
    // comboStateStyles: {
    //   active: {
    //     fill: '#f00',
    //     opacity: 0.5
    //   },
    // },
  });

  getData(networkId);

  graph.on('node:click', (evt) => {
    
    const { item } = evt;
    let model = item.getModel();
    if (model.category == "DATA_STATION") {
      let list = JSON.parse(localStorage.getItem("topomenu"));
      list.forEach((it,index)=>{
        if(it.networkName == model.label && it.id == model.id){
          router.push({path: '/admin/networktopology/'+it.id,query:{cloudId:it.cloudId,type:it.type}});
        }
      })
    }
  });
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', listenerFUNC)
  }
}
const mockfirst = {
  "networkLevel": -1,
  "networkName": "配电网云拓扑",
  "parentId": -2,
  "id":-1
}
const getTopoMenu = () => {
  selectNetworktopology().then((res)=>{
    let rawList = [mockfirst,...res.data];
    let list = listToTreeTopo([mockfirst,...res.data],[],-2);
    localStorage.setItem("topotree",JSON.stringify([...list]));
    emiter.emit("setData",{list,rawList})
  })
}
const getTerminals = (networkId) => {
  let data = {nodes:[],edges:[]};
  return getNetworkTerminalList({networkId})
}
const setTerminalsAPI = (data,networkId) => {
  let temp = [];
  data.nodes.forEach((item,index)=>{
    if(item.category == 'DATA_STATION'){
      temp.push(getTerminals(item.id));
    }
  })
  Promise.all(temp).then((res)=>{
    res.forEach((item,index)=>{
      if(item.data && item.data.length > 0){
        data.nodes.push({id:'switch_'+index,category:'exchange',label:''});
        data.edges.push({source:String(item.data[0].networkId),target:'switch_'+index});
        item.data.forEach((it,ii)=>{
          data.nodes.push({id:String(it.id),category:'TERMINAL_STATION',label:it.name});
          data.edges.push({source:'switch_'+index,target:String(it.id)});
        })
      }
    })
    graph.read(sorted(data),{ updateLayout: false });
  });
}
const getData = (networkId) => {
  let list = JSON.parse(localStorage.getItem("topotree"));
  if(list){
    list.some((item,index)=>{
      if(item.id == networkId){
        let data = singleListTodagre(item.children[0],[],[],[],item)
        data.nodes.push({
            id:String(item.id),
            category:item.type,
            label:'广域网'
        })
        // setTerminalsAPI(data,networkId)
        graph.read(sorted(data),{ updateLayout: false });
      }
      return (item.id == networkId);
    })
  }else{
    emiter.emit("getTopoMenu",{callback:()=>getData(router.currentRoute.value.query.id)})
  }
}
watch(()=>router.currentRoute.value,(to,from)=>{
  if(from && !from.params.id && to.query.id){
    getData(Number(to.query.id))
  }
},{immediate:true,deep:true})
onMounted(()=>{
  if(router.currentRoute.value.query.id){
    draw(Number(router.currentRoute.value.query.id))
  }else{
    draw(-1)
  }
  emiter.on("setTopo",(networkId)=>{
    getData(networkId)
  })
})
onMounted(()=>{
  window.removeEventListener('resize', listenerFUNC);
})

</script>
<style lang='scss' scoped>
#container{min-height: 783px;width: 100%;}
</style>