<template>
  <div class="contentPadding">
    <div>
      <svg class="dagre" width="1500" height="800">
        <g class="container"></g>
      </svg>
      <div class="ant-popover ant-popover-placement-right" style="left: 240px; top: 137px; display: none">
        <div class="ant-popover-content">
          <div class="ant-popover-arrow"><span class="ant-popover-arrow-content"></span></div>
          <div class="ant-popover-inner" role="tooltip">
            <div>
              <div class="ant-popover-title">{{popform.type}}</div>
              <div class="ant-popover-inner-content">
                <p data-v-f71753dd="">{{popform.label}}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <Database ref="databaseRef" v-else @setShowDatabase="(e)=>display_info.isShowDatabase = e" /> -->
  </div>
</template>
 
<script setup lang="ts">
import { ref, onMounted, watch, computed, reactive } from "vue";
import dagreD3 from "dagre-d3";
import * as d3 from "d3";
import $ from "jquery";
import topodata from "./example1.json";
import { transform } from "lodash";
import Database from "./database/index.vue";
import router from "@/router";
const display_info = reactive({isShowDatabase:false});
const popform = reactive({type:'',label:''});
const databaseRef = ref();
const nodeInfo = ref();
const nodes = ref([...topodata.nodes]);
// 节点数组
const edges = ref([...topodata.edges]);
// 连线数组
const draw = () => {
  // 创建 Graph 对象
  const g = new dagreD3.graphlib.Graph({ compound: true })
    .setGraph({
      zoom: 1,
      rankdir: "TB", // 流程图从下向上显示，默认'TB'，可取值'TB'、'BT'、'LR'、'RL'
      //ranker: "network-simplex",//连线算法
      // nodesep: 70, // 节点之间间距
      // ranksep: 100, // 层与层之间的间距
    })
    .setDefaultEdgeLabel(function () {
      return {};
    });
  const releaseClass = (node) => {
    return node == "已发布" ? "ADS" : "DWD";
  };
  const setWildNode = (node) => {
    g.setNode(node.id, {
      ...node,
      title:node.label,
      label: `
      <div style="width:164px;height:100px;display:flex;background-image:url(/topoicon/wild.svg);background-size: cover">
        <span id='${node.id}' class='nodeA' style="margin:auto">${node.label}</span>
      </div>
      `,
      labelType: "html",
      style: "fill-opacity:0;cursor: pointer", //节点样式,可设置节点的颜色填充、节点边框
      labelStyle: "fill-opacity: 0;font-weight:bold;cursor: pointer", //节点标签样式, 可设置节点标签的文本样式（颜色、粗细、大小）
    });
  };
  const setExchangeNode = (node) => {
    g.setNode(node.id, {
      ...node,
      title:node.label,
      label: `
      <div style="width:164px;height:100px;display:flex;background-image:url(/topoicon/wild.svg);background-size: cover">
        <img src="/topoicon/${node.type}.svg" width="50px" height="50px" style="margin:auto">
      </div>
      `,
      labelType: "html",
      style: "fill-opacity:0;cursor: pointer", //节点样式,可设置节点的颜色填充、节点边框
      labelStyle: "fill-opacity: 0;font-weight:bold;cursor: pointer", //节点标签样式, 可设置节点标签的文本样式（颜色、粗细、大小）
    });
  };
  const setSideNode = (node) => {
    g.setNode(node.id, {
      ...node,
      shape: "diamond",
      title:node.label,
      label: `<div id='${node.id}'  style='display:flex;flex-direction:column;width:${
        node.children ? 320 * node.children.length - 20 : 300
      }px; height: 80px'>
          <div id='${node.id}' style="text-align:center">
            <span id='${node.id}'>${node.label}</span>
          </div>
          <img src="/topoicon/${node.type}.svg" width="50px" height="50px" style="margin:auto">
        </div>
        `, //node.nodeName,
      labelType: "html",
      style: "fill-opacity:0;stroke:#a0cfff;stroke-width: 2px;stroke-dasharray:10;cursor: pointer", //节点样式,可设置节点的颜色填充、节点边框
      labelStyle: "fill-opacity:0;font-weight:bold;cursor: pointer", //节点标签样式, 可设置节点标签的文本样式（颜色、粗细、大小）
      rx: 5, // 设置圆角
      ry: 5, // 设置圆角
    });
  };
  const setVirtualNode = (node) => {
    if (node.children.length % 2 == 0) {
      // node.children.splice(node.children.length / 2, 1, 'v'+node.id)
      g.setNode(Math.floor(eval(node.children.join("+")) / node.children.length), {
        id: Math.floor(eval(node.children.join("+")) / node.children.length),
        middleMark: true,
        rank: 1,
        order: 4,
        style: "opacity:0;width:0;height:0;display:none",
        labelStyle: "opacity:0;display:none",
      });
      g.setParent(Math.floor(eval(node.children.join("+")) / node.children.length), node.id);
    }
  };
  const setRectDashNode = (node) => {
    if (node.children) {
      let temp_children = [...node.children];
      node.children = temp_children.map((item, index) => {
        console.log("item", item, Number(item * 10));
        return Number(item * 10);
      });
      console.log("node.children", node.children);
      // node.children = [...temp_children]
    }
    g.setNode(node.id, {
      ...node,
      title:node.label,
      label: `
          <div id='${node.id}' style='height: 150px'>
            <span id='${node.id}' class='nodeA'>${node.label}</span>
          </div>
        `, //node.nodeName,
      labelType: "html",
      style: "fill-opacity:0;stroke:#a0cfff;stroke-width: 2px;stroke-dasharray:10;cursor: pointer", //节点样式,可设置节点的颜色填充、节点边框
      labelStyle: "fill-opacity:0;font-weight:bold;cursor: pointer", //节点标签样式, 可设置节点标签的文本样式（颜色、粗细、大小）
      rx: 5, // 设置圆角
      ry: 5, // 设置圆角
    });
    setVirtualNode(node);
    // node.children.forEach((t,i)=>{
    //   g.setParent(t, node.id);
    // })
  };
  const setDatabaseNode = (node) => {
    g.setNode(node.id, {
      ...node,
      shape: "ellipse",
      title:node.label,
      label: `<div id='${node.id}'  style='display:flex;flex-direction:column;width:100px; height: 80px'>
          <div id='${node.id}' style="text-align:center">
            <span id='${node.id}'>${node.label}</span>
          </div>
          <img src="/topoicon/${node.type}.svg" width="50px" height="50px" style="margin:auto">
        </div>
        `, //node.nodeName,
      labelType: "html",
      style: "fill-opacity:0;stroke:#a0cfff;stroke-width: 2px;stroke-dasharray:5;cursor: pointer", //节点样式,可设置节点的颜色填充、节点边框
      labelStyle: "fill-opacity:0;font-weight:bold;cursor: pointer", //节点标签样式, 可设置节点标签的文本样式（颜色、粗细、大小）
      rx: 5, // 设置圆角
      ry: 5, // 设置圆角
      // transform:`translate(${g.node(node.group).x,g.node(node.group).y})`,
      // x:g.node(node.group).x,
      // y:g.node(node.group).y
    });
    if (node.group) {
      console.log("group", node.group, Number(node.group * 10));
      node.group = Number(node.group * 10);
      g.setParent(node.id, node.group);
    }
    // let inner = d3.select(g.node(node.id).elem)
    // var inner = g.node(node.id).attr("transform",g.node(node.group).transform);
    // g.node(node.id).x = g.node(node.group).x;
    // g.node(node.id).y = g.node(node.group).y;
  };
  nodes.value.forEach((node) => {
    console.log("node", node, Number(node.id * 10));
    // node.id = Number(node.id * 10);
    let node_temp = { ...node };
    node_temp.id = Number(node.id * 10);
    if (node_temp.type == "wild") {
      setWildNode(node_temp);
    } else if (node_temp.type == "exchange") {
      setExchangeNode(node_temp);
    } else if (node_temp.type == "side") {
      setSideNode(node_temp);
    } else if (node_temp.type == "database_group") {
      setRectDashNode(node_temp);
    } else if (node_temp.type == "database") {
      setDatabaseNode(node_temp);
    } else
      g.setNode(node_temp.id, {
        id: node_temp.id,
        title:node.label,
        label: `<div id='${node_temp.id}'  style='width:300px; height: 80px'>
          <div id='${node_temp.id}' class='nodeBox'>
            <span id='${node_temp.id}' class='nodeA'>${node_temp.label}</span>
            <div id='${node_temp.id}' class='${node_temp.type}'>${node_temp.type}</div>
          </div>
          <div id='${node_temp.id}' class='nodeBox' style='margin-top:5px'>
            <span id='${node_temp.id}' class='nodeA'>${node_temp.label}</span>
          </div>
          <div id='${node_temp.id}' style='margin-top:5px'>
          </div>
        </div>
        `, //node.nodeName,
        labelType: "html",
        width: 320,
        height: 86,
        // shape: "rect", //节点形状，可以设置rect(长方形),circle,ellipse(椭圆),diamond(菱形) 四种形状，还可以使用render.shapes()自定义形状
        style: "fill:#fff;stroke:#a0cfff;stroke-width: 2px;cursor: pointer", //节点样式,可设置节点的颜色填充、节点边框
        labelStyle: "fill: #fff;font-weight:bold;cursor: pointer", //节点标签样式, 可设置节点标签的文本样式（颜色、粗细、大小）
        rx: 5, // 设置圆角
        ry: 5, // 设置圆角
        // paddingBottom: 0,
        // paddingLeft: 0,
        // paddingRight: 0,
        // paddingTop: 0,
      });
  });

  const setVirtualEdge = (edge) => {
    if (g.node(edge.toId).children.length % 2 == 0) {
      let middleNode = Math.floor(eval(g.node(edge.toId).children.join("+")) / g.node(edge.toId).children.length);
      // g.node(middleNode).middleMark = true;
      g.setEdge(edge.fromId, middleNode, {
        //curve: d3.curveStepBefore , //d3.curveBasis, // 设置为贝塞尔曲线
        style: "stroke: #a0cfff; fill: none; stroke-width: 2px", // 连线样式
        arrowheadStyle: "fill: #a0cfff;stroke: #a0cfff;", //箭头样式，可以设置箭头颜色
        arrowhead: "vee", //箭头形状，可以设置 normal,vee,undirected 三种样式，默认为 normal
      });
    } else {
      let middleNode = g.node(edge.toId).children[Math.floor(g.node(edge.toId).children.length / 2)];
      g.node(middleNode).middleMark = true;
      g.setEdge(edge.fromId, middleNode, {
        //curve: d3.curveStepBefore , //d3.curveBasis, // 设置为贝塞尔曲线
        style: "stroke: #a0cfff; fill: none; stroke-width: 2px", // 连线样式
        arrowheadStyle: "fill: #a0cfff;stroke: #a0cfff;", //箭头样式，可以设置箭头颜色
        arrowhead: "vee", //箭头形状，可以设置 normal,vee,undirected 三种样式，默认为 normal
      });
    }
  };
  // Graph添加节点之间的连线
  if (nodes.value.length > 1) {
    edges.value.forEach((edge) => {
      console.log("edge", Number(edge.fromId * 10), Number(edge.toId * 10));
      let edge_temp = { ...edge };
      edge_temp.fromId = Number(edge.fromId * 10);
      edge_temp.toId = Number(edge.toId * 10);
      console.log("g.node(edge.toId)", g);
      if (g.node(edge_temp.toId).children) {
        setVirtualEdge(edge_temp);
      } else
        g.setEdge(edge_temp.fromId, edge_temp.toId, {
          //curve: d3.curveStepBefore , //d3.curveBasis, // 设置为贝塞尔曲线
          style: "stroke: #a0cfff; fill: none; stroke-width: 2px", // 连线样式
          arrowheadStyle: "fill: #a0cfff;stroke: #a0cfff;", //箭头样式，可以设置箭头颜色
          arrowhead: "vee", //箭头形状，可以设置 normal,vee,undirected 三种样式，默认为 normal
        });
    });
  }

  // 获取要绘制流程图的绘图容器
  const container = d3.select("svg.dagre").select("g.container");

  // 创建渲染器
  const render = new dagreD3.render();
  // 在绘图容器上运行渲染器绘制流程图
  render(container, g);
  const svg = d3.select("svg.dagre");
  // console.log("svg.selectAll",svg.selectAll(".edgePath").select('path'))
  svg
    .selectAll(".edgePath")
    .select("path")
    .filter((t, i) => g.node(t.w).middleMark)
    .attr("d", (d) => {
      console.log("d", d, g.edge(d));
      let str = "";
      g.edge(d).points.map((item, index) => {
        if (index == 0) {
          str = `M${Object.values(item).join(",")}`;
        } else {
          if (index >= g.edge(d).points.length - 1) return;
          str = str + `L${Object.values(item).join(",")}`;
        }
      });
      return str;
    });

  var zoom = d3
    .zoom() // 缩放支持
    .scaleExtent([0.5, 2]) // 缩放范围
    .on("zoom", function (current) {
      container.attr("transform", current.transform);
      console.log("zoom");
      $(".ant-popover-placement-right").css({
        display: "none",
      });
    });
  svg.call(zoom); // 缩放生效
  let { clientWidth, clientHeight } = svg._groups[0][0];
  let { width, height } = g.graph();
  // g.graph().nodeOrder = [7, 'v3', 8]
  let initScale = 1;
  svg
    .transition()
    .duration(1000) // 1s完成过渡
    .call(
      zoom.transform,
      d3.zoomIdentity // 居中显示
        .translate((clientWidth - width * initScale) / 2, (clientHeight - height * initScale) / 2)
        .scale(initScale) // 默认缩放比例
    );

  const mouseOver = () => {
    let nodeWObject = { wild: 164, exchange: 164, database: 120, side: 452.55 };
    let nodeHObject = { wild: 120, exchange: 120, database: 100, side: 141.42 };
    let popoverHeigh = 92;

    let node = d3.selectAll(".node").on("mouseenter", (e) => {
      if (g.node(e.target.id).type) {
        let scale = d3.zoomTransform(svg.node()).k;
        let containerX = d3.zoomTransform(svg.node()).x;
        let containerY = d3.zoomTransform(svg.node()).y;
        let nodeX = g.node(e.target.id).x;
        let nodeY = g.node(e.target.id).y;
        let nodeW = nodeWObject[g.node(e.target.id).type];
        let nodeH = nodeHObject[g.node(e.target.id).type];
        // console.log("container",scale,containerX,containerY,nodeX,nodeY,nodeW,nodeH,node)
        // console.log("zoom",d3.zoomTransform(svg.node()))
        popform.type = g.node(e.target.id).type;
        popform.label = g.node(e.target.id).title;
        $(".ant-popover-placement-right").css({
          display: "block",
          left: 20 + containerX + (nodeX + nodeW / 2) * scale,
          top: 20 + containerY + nodeY * scale - popoverHeigh / 2,
        });
        node.on("mouseleave", () => {
          console.log("leave");
          $(".ant-popover-placement-right").css({
            display: "none",
          });
        });
        node.on("click", () => {
          console.log("click");
          if (g.node(e.target.id).type == "database") {
            // display_info.isShowDatabase = true;
            router.push('/admin/networktopology/'+Number(e.target.id)/10)
          }
        });
      }
    });
    let cluster = d3.selectAll(".cluster").on("mouseenter", (e) => {
      let scale = d3.zoomTransform(svg.node()).k;
      let containerX = d3.zoomTransform(svg.node()).x;
      let containerY = d3.zoomTransform(svg.node()).y;
      let nodeX = g.node(e.target.id).x;
      let nodeY = g.node(e.target.id).y;
      let nodeW = e.target.firstChild.width.baseVal.value;
      let nodeH = e.target.firstChild.height.baseVal.value;
      console.log("e", e, g.node(e.target.id));
      popform.type = g.node(e.target.id).type;
      popform.label = g.node(e.target.id).title;
      $(".ant-popover-placement-right").css({
        display: "block",
        left: 20 + containerX + (nodeX + nodeW / 2) * scale,
        top: 20 + containerY + nodeY * scale - popoverHeigh / 2,
      });
      cluster.on("mouseleave", () => {
        console.log("leave");
        $(".ant-popover-placement-right").css({
          display: "none",
        });
      });
    });
  };
  mouseOver();
  //   let inner = svg.selectAll('g.node').data(nodes.value);
  //   inner.filter(function(t) { console.log("t",t);return t.type === 'database'; }) // 用实际的节点名称替换'nodeName'
  //   .attr('transform', (d,i)=>`translate(${g.node(d.group).x}, ${g.node(d.group).y})`);
  //  console.log("inner",inner)
  // //  节点点击事件
  // let nowNode; // 标记当前高亮节点
  // container.on(
  //   "click",
  //   (e) => {
  //     if (
  //       Number(e.target.__data__) == nowNode ||
  //       nowNode == Number(e.target.id)
  //     ) {
  //       for (let a in g._nodes) {
  //         g._nodes[a].style =
  //           "fill:#fff;stroke:#a0cfff;stroke-width: 2px;cursor: pointer";
  //       }
  //       edges.value.forEach((edge) => {
  //         g.setEdge(edge.fromId, edge.toId, {
  //           //curve: d3.curveStepBefore , //d3.curveBasis, // 设置为贝塞尔曲线
  //           style: "stroke: #a0cfff; fill: none; stroke-width: 2px", // 连线样式
  //           arrowheadStyle: "fill: #a0cfff;stroke: #a0cfff;", //箭头样式，可以设置箭头颜色
  //           arrowhead: "vee", //箭头形状，可以设置 normal,vee,undirected 三种样式，默认为 normal
  //         });
  //       });
  //       nowNode = "";
  //       render(container, g);
  //       return;
  //     }
  //     nodes.value.forEach((item) => {
  //       console.log(nowNode, item.id, "wwwwwwwww");
  //       if (
  //         item.id == Number(e.target.__data__) ||
  //         item.id == Number(e.target.id)
  //       ) {
  //         nowNode = item.id;
  //         nodeInfo.value = item;
  //         let edgeList = [
  //           ...getUpNode(item.id),
  //           nowNode,
  //           ...getDownNode(item.id),
  //         ];
  //         if (nodes.value.length > 1) {
  //           edges.value.forEach((edge) => {
  //             if (
  //               edgeList.indexOf(edge.fromId) != -1 &&
  //               edgeList.indexOf(edge.toId) != -1
  //             ) {
  //               g.setEdge(edge.fromId, edge.toId, {
  //                 //curve: d3.curveStepBefore , //d3.curveBasis, // 设置为贝塞尔曲线
  //                 style: "stroke: #30a8ff; fill: none; stroke-width: 2px", // 连线样式
  //                 arrowheadStyle: "fill: #30a8ff;stroke: #30a8ff;", //箭头样式，可以设置箭头颜色
  //                 arrowhead: "vee", //箭头形状，可以设置 normal,vee,undirected 三种样式，默认为 normal
  //               });
  //             } else {
  //               g.setEdge(edge.fromId, edge.toId, {
  //                 //curve: d3.curveStepBefore , //d3.curveBasis, // 设置为贝塞尔曲线
  //                 style: "stroke: #a0cfff; fill: none; stroke-width: 2px", // 连线样式
  //                 arrowheadStyle: "fill: #a0cfff;stroke: #a0cfff;", //箭头样式，可以设置箭头颜色
  //                 arrowhead: "vee", //箭头形状，可以设置 normal,vee,undirected 三种样式，默认为 normal
  //               });
  //             }
  //           });
  //         }
  //         for (let a in g._nodes) {
  //           g._nodes[a].style =
  //             "fill:#fff;stroke:#a0cfff;stroke-width: 2px;cursor: pointer";
  //         }
  //         g._nodes[nodeInfo.value.id].style =
  //           "fill: #fff;stroke:#30a8ff;stroke-width: 2px;cursor: pointer";
  //         getUpNode(item.id).forEach((item) => {
  //           g._nodes[item].style =
  //             "fill: #a0cfff;stroke:#a0cfff;stroke-width: 2px;cursor: pointer";
  //         });
  //         getDownNode(item.id).forEach((item) => {
  //           g._nodes[item].style =
  //             "fill: #a3da87;stroke:#a0cfff;stroke-width: 2px;cursor: pointer";
  //         });
  //         console.log(nodeInfo.value, g, 6666);
  //       }
  //     });
  //     render(container, g);
  //   },
  //   true
  // );

  // // 获取上游节点函数
  // const getUpNode = (id) => {
  //   let arr = [];
  //   for (let i = 0; i < edges.value.length; i++) {
  //     if (edges.value[i].toId == Number(id)) {
  //       arr.push(edges.value[i].fromId);
  //       arr.push(...getUpNode(edges.value[i].fromId));
  //     }
  //   }
  //   return arr;
  // };
  // // 获取下游节点函数
  // const getDownNode = (id) => {
  //   let arr = [];
  //   for (let i = 0; i < edges.value.length; i++) {
  //     if (edges.value[i].fromId == Number(id)) {
  //       arr.push(edges.value[i].toId);
  //       arr.push(...getDownNode(edges.value[i].toId));
  //     }
  //   }
  //   return arr;
  // };
};
onMounted(() => {
  draw();
});

console.log(dagreD3, 2222);
</script>
 
<style>
.contentPadding {
  position: relative;
}
.ant-popover {
  position: absolute;
}
.label-container {
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}
.nodeA {
  color: #01579b;
  font-family: HarmonyOS Sans SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
}
.nodeBox {
  display: flex;
  justify-content: space-between;
}
.DWO {
  padding: 0 7px;
  color: #67c23a;
  border-radius: 4px;
  border: 1px solid #e1f3d8;
  background: #f0f9eb;
}
.DIM {
  padding: 0 7px;
  color: #909399;
  border-radius: 4px;
  border: 1px solid #e9e9eb;
  background: #f4f4f5;
}
.DWD {
  padding: 0 7px;
  color: #e6a23c;
  border-radius: 4px;
  border: 1px solid #faecd8;
  background: #fcf6ec;
}
.DWS {
  padding: 0 7px;
  color: #f56c6c;
  border-radius: 4px;
  border: 1px solid #fde2e2;
  background: #fef0f0;
}
.ADS {
  padding: 0 7px;
  color: #409eff;
  border-radius: 4px;
  border: 1px solid #d9ecff;
  background: #ecf5ff;
}
</style>