const listObj = {};
var nodes = [];
var combos = [];
var edges = [];
/*if(item.network_level == 1){
    let temp = {
        id:String(item.id),
        category:item.type,
        label:item.networkName,
        parentId:undefined
    };
    if(isCombo)
        temp.parentId = String(id);
    combos.push(temp);
}else{
    let temp = {
        id:String(item.id),
        category:item.type,
        label:item.networkName,
        comboId:undefined
    };
    if(isCombo)
        temp.comboId = String(id);
    nodes.push(temp);
}
edges.push({source:String(id),target:String(item.id)});
if(nodes.length + combos.length >= list.length){

    listTodagre(list,item.id,item.network_level == 1);
}*/
export const AllListTodagre = (list) => {
    list.forEach((item,index)=>{
        if(!listObj[item.parentId]){
            listObj[item.parentId] = {nodes:[],combos:[]};
        }
        if(item.parentId !== 0){
            if(item.networkLevel == 1){
                let temp = {
                    id:String(item.id),
                    category:item.type,
                    label:item.networkName,
                    parentId:String(item.parentId)
                };
                listObj[item.parentId].combos.push(temp);
            }else{
                let temp = {
                    id:String(item.id),
                    category:item.type,
                    label:item.networkName,
                    comboId:undefined
                };
                if(item.networkLevel == 2)
                    temp.comboId = String(item.parentId);
                listObj[item.parentId].nodes.push(temp);
            }
            
            
        }
    })
    for(let item in listObj){
        nodes = nodes.concat(listObj[item].nodes);
        combos = combos.concat(listObj[item].combos);
        listObj[item].nodes.forEach((it,ii)=>{
            edges.push({source:item,target:it.id})
        })
        listObj[item].combos.forEach((it,ii)=>{
            edges.push({source:item,target:it.id})
        })
    }
}

// let sigleObj = {};
// let sigleNodes = [];
// let sigleCombos = [];
// let sigleEdges = [];
export const singleListTodagre = (list,sigleNodes,sigleCombos,sigleEdges,toplist) => {
    // if(!sigleObj[list.id]){
    //     sigleObj[list.id] = {nodes:[],combos:[]};
    // }
    if(list.children && list.children.length)
    list.children.forEach((item,index)=>{
        if(item.parentId !== -1){
            if(item.networkLevel == 2){
                let temp = {
                    id:String(item.id),
                    category:item.type,
                    label:item.networkName
                };
                if(item.children){
                    sigleCombos.push(temp);
                    sigleEdges.push({source:String(toplist.id),target:String(item.id)})
                }
            }else{
                let temp = {
                    id:String(item.id),
                    category:item.type,
                    label:item.networkName,
                    comboId:undefined
                };
                if(item.type != 'TERMINAL_STATION'){
                    if(item.networkLevel == 3){
                        temp.comboId = String(item.parentId);
                        temp.category = 'DATA_STATION';
                    }else
                        sigleEdges.push({source:String(list.id),target:String(item.id)})
                    sigleNodes.push(temp);
                }else{
                    if(item.networkLevel == 3)
                        temp.comboId = String(item.parentId);
                    sigleNodes.push(temp);
                }
                
            }
            
            
        }
        if(item.children)
            return singleListTodagre(item,sigleNodes,sigleCombos,sigleEdges,toplist)
    })
    // console.log("sigleNodes",sigleNodes,sigleCombos,sigleEdges)
    return {nodes:sigleNodes,combos:sigleCombos,edges:sigleEdges};
}