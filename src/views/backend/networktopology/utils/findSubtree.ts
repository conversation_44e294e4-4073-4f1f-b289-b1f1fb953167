// 查找节点及其所有下级节点
export const findSubtree = (rootId,data) => {
    const allNodes = new Set(data.nodes.map(node => node.id));
    const subtree = [];
    const visited = new Set();
   
    const dfs = (id) => {
      if (visited.has(id)) return;
      visited.add(id);
      subtree.push(id);
      data.edges
        .filter(edge => edge.source === id)
        .forEach(edge => dfs(edge.target));
    };
   
    dfs(rootId);
    return subtree.filter(id => allNodes.has(id));
};