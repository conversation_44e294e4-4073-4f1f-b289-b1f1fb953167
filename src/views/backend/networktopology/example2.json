{"nodes": [{"id": 1, "type": "net-work", "label": "局域网", "group": 47}, {"id": 2, "type": "exchange_group", "label": "交换机", "group": 48, "children": [7, 8]}, {"id": 3, "type": "v_exchange", "label": "交换机", "group": 47}, {"id": 4, "type": "v_exchange", "label": "交换机", "group": 47}, {"id": 5, "type": "v_exchange", "label": "交换机", "group": 47}, {"id": 6, "type": "v_exchange", "label": "交换机", "group": 47}, {"id": 7, "type": "h_exchange", "label": "交换机", "group": 2}, {"id": 8, "type": "h_exchange", "label": "交换机", "group": 2}, {"id": 9, "type": "net-server", "label": "虚机", "group": 47}, {"id": 10, "type": "net-server", "label": "虚机", "group": 47}, {"id": 11, "type": "net-server", "label": "虚机", "group": 47}, {"id": 12, "type": "net-server", "label": "虚机", "group": 47}, {"id": 13, "type": "net-server", "label": "虚机", "group": 47}, {"id": 14, "type": "net-server", "label": "虚机", "group": 47}, {"id": 15, "type": "net-server", "label": "虚机", "group": 47}, {"id": 16, "type": "net-server", "label": "虚机", "group": 47}, {"id": 17, "type": "net-server", "label": "虚机", "group": 47}, {"id": 18, "type": "net-server", "label": "虚机", "group": 47}, {"id": 19, "type": "net-server", "label": "虚机", "group": 47}, {"id": 20, "type": "net-server", "label": "虚机", "group": 47}, {"id": 21, "type": "net-server", "label": "虚机", "group": 47}, {"id": 22, "type": "net-server", "label": "虚机", "group": 47}, {"id": 23, "type": "net-server", "label": "虚机", "group": 47}, {"id": 24, "type": "net-server", "label": "虚机", "group": 47}, {"id": 25, "type": "net-server", "label": "虚机", "group": 47}, {"id": 26, "type": "net-server", "label": "虚机", "group": 47}, {"id": 27, "type": "net-server", "label": "虚机", "group": 47}, {"id": 28, "type": "net-server", "label": "虚机", "group": 47}, {"id": 29, "type": "server_node_group", "label": "网络节点", "group": 48, "children": [33, 34]}, {"id": 30, "type": "server_node_group", "label": "管理节点", "group": 48, "children": [35, 36]}, {"id": 31, "type": "server_node_group", "label": "计算节点", "group": 48, "children": [37, 38]}, {"id": 32, "type": "server_node_group", "label": "存储节点", "group": 48, "children": [39, 40]}, {"id": 33, "type": "server_node", "label": "网络节点", "group": 29}, {"id": 34, "type": "server_node", "label": "网络节点", "group": 29}, {"id": 35, "type": "server_node", "label": "管理节点", "group": 30}, {"id": 36, "type": "server_node", "label": "管理节点", "group": 30}, {"id": 37, "type": "server_node", "label": "计算节点", "group": 31}, {"id": 38, "type": "server_node", "label": "计算节点", "group": 31}, {"id": 39, "type": "server_node", "label": "存储节点", "group": 32}, {"id": 40, "type": "server_node", "label": "存储节点", "group": 32}, {"id": 41, "type": "side-area", "label": "边侧区域1", "group": 49, "children": [43, 44]}, {"id": 42, "type": "side-area", "label": "边侧区域2", "group": 49, "children": [45, 46]}, {"id": 43, "type": "provide", "label": "区域", "group": 41}, {"id": 44, "type": "provide", "label": "区域", "group": 41}, {"id": 45, "type": "provide", "label": "区域", "group": 42}, {"id": 46, "type": "provide", "label": "区域", "group": 42}, {"id": 47, "type": "level", "label": "虚拟层", "children": [3, 4, 5, 6, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28]}, {"id": 48, "type": "level", "label": "硬件层", "children": [2, 29, 30, 31, 32]}, {"id": 49, "type": "level", "label": "边侧", "children": [41, 42]}], "edges": [{"fromId": 9, "toId": 3}, {"fromId": 10, "toId": 3}, {"fromId": 11, "toId": 3}, {"fromId": 12, "toId": 3}, {"fromId": 13, "toId": 3}, {"fromId": 14, "toId": 4}, {"fromId": 15, "toId": 4}, {"fromId": 16, "toId": 4}, {"fromId": 17, "toId": 4}, {"fromId": 18, "toId": 4}, {"fromId": 19, "toId": 5}, {"fromId": 20, "toId": 5}, {"fromId": 21, "toId": 5}, {"fromId": 22, "toId": 5}, {"fromId": 23, "toId": 5}, {"fromId": 24, "toId": 6}, {"fromId": 25, "toId": 6}, {"fromId": 26, "toId": 6}, {"fromId": 27, "toId": 6}, {"fromId": 28, "toId": 6}, {"fromId": 4, "toId": 1}, {"fromId": 5, "toId": 1}, {"fromId": 6, "toId": 1}, {"fromId": 1, "toId": 7}, {"fromId": 1, "toId": 8}, {"fromId": 7, "toId": 33}, {"fromId": 7, "toId": 34}, {"fromId": 7, "toId": 35}, {"fromId": 7, "toId": 36}, {"fromId": 7, "toId": 37}, {"fromId": 7, "toId": 38}, {"fromId": 7, "toId": 39}, {"fromId": 7, "toId": 40}, {"fromId": 8, "toId": 33}, {"fromId": 8, "toId": 34}, {"fromId": 8, "toId": 35}, {"fromId": 8, "toId": 36}, {"fromId": 8, "toId": 37}, {"fromId": 8, "toId": 38}, {"fromId": 8, "toId": 39}, {"fromId": 8, "toId": 40}, {"fromId": 48, "toId": 41}, {"fromId": 48, "toId": 42}]}