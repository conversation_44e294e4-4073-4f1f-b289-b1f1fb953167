{"nodes": [{"id": 1, "type": "net-work", "value": "provider1(VLAN)"}, {"id": 2, "type": "net-work", "value": "private(VXLAN)"}, {"id": 3, "type": "net-work", "value": "cloud_department_ustack_selfservice(VXLAN)"}, {"id": 4, "type": "net-work", "value": "hgy-test(VXLAN)"}, {"id": 5, "type": "net-work", "value": "Network-172(VXLAN)"}, {"id": 6, "type": "net-work", "value": "hgy-test(VXLAN)"}, {"id": 7, "type": "net-route", "value": "wj-route-test"}, {"id": 8, "type": "net-route", "value": "route"}, {"id": 9, "type": "net-server", "value": "wkt-test-uswift-iso"}, {"id": 10, "type": "net-server", "value": "wkt-test-uswift-1"}, {"id": 11, "type": "net-server", "value": "ren-jenkins-kolla-build-arm"}, {"id": 12, "type": "net-server", "value": "ren-jenkins-kolla-build-x86"}, {"id": 13, "type": "net-server", "value": "wkt-jenkins"}, {"id": 14, "type": "net-server", "value": "hgy-test-euler-arm2"}, {"id": 15, "type": "net-server", "value": "hgy-test"}, {"id": 16, "type": "net-server", "value": "wuyadan_test"}, {"id": 17, "type": "net-server", "value": "hgy-create-for-wll-0408-3"}, {"id": 18, "type": "net-server", "value": "yxl-big-op"}, {"id": 19, "type": "net-server", "value": "xyh_test"}, {"id": 20, "type": "net-server", "value": "hgy-openEuler-24.03-alpha"}, {"id": 21, "type": "net-server", "value": "wj-5-1"}, {"id": 22, "type": "net-server", "value": "wj-test1"}, {"id": 23, "type": "net-server", "value": "hgy-cirros"}, {"id": 24, "type": "net-server", "value": "wj-euler-rpm"}, {"id": 25, "type": "net-server", "value": "hgy-ustack-0304-1"}, {"id": 26, "type": "net-server", "value": "hgy-test-ram"}, {"id": 27, "type": "net-server", "value": "hgy_used-by-xyh"}, {"id": 28, "type": "net-server", "value": "hgy-ustack"}, {"id": 29, "type": "net-server", "value": "hgy-open<PERSON>ler-dev"}, {"id": 30, "type": "net-server", "value": "wkt-test-dib"}, {"id": 31, "type": "net-server", "value": "wkt"}, {"id": 32, "type": "net-server", "value": "hgy-JUMP"}, {"id": 33, "type": "net-server", "value": "wj-<PERSON><PERSON><PERSON>-db"}, {"id": 34, "type": "net-server", "value": "wj-kolla-build-arm"}, {"id": 35, "type": "net-server", "value": "wkt-test-dib-arm"}, {"id": 36, "type": "net-server", "value": "wj-java-web"}, {"id": 37, "type": "net-server", "value": "ren-test-build"}, {"id": 38, "type": "net-server", "value": "wl-test"}, {"id": 39, "type": "net-server", "value": "ren-kolla-ansible-v"}, {"id": 40, "type": "net-server", "value": "wj-kolla-build"}], "links": [{"source": 1, "fromNodeType": "net-work", "target": 8, "toNodeType": "net-route"}, {"source": 1, "fromNodeType": "net-work", "target": 8, "toNodeType": "net-route"}, {"source": 2, "fromNodeType": "net-work", "target": 8, "toNodeType": "net-route"}, {"source": 5, "fromNodeType": "net-work", "target": 9, "toNodeType": "net-server"}, {"source": 5, "fromNodeType": "net-work", "target": 10, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 11, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 12, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 13, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 14, "toNodeType": "net-server"}, {"source": 3, "fromNodeType": "net-work", "target": 15, "toNodeType": "net-server"}, {"source": 2, "fromNodeType": "net-work", "target": 16, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 16, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 17, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 18, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 19, "toNodeType": "net-server"}, {"source": 4, "fromNodeType": "net-work", "target": 19, "toNodeType": "net-server"}, {"source": 6, "fromNodeType": "net-work", "target": 19, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 20, "toNodeType": "net-server"}, {"source": 2, "fromNodeType": "net-work", "target": 21, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 21, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 22, "toNodeType": "net-server"}, {"source": 2, "fromNodeType": "net-work", "target": 22, "toNodeType": "net-server"}, {"source": 4, "fromNodeType": "net-work", "target": 22, "toNodeType": "net-server"}, {"source": 6, "fromNodeType": "net-work", "target": 22, "toNodeType": "net-server"}, {"source": 5, "fromNodeType": "net-work", "target": 23, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 24, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 25, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 26, "toNodeType": "net-server"}, {"source": 3, "fromNodeType": "net-work", "target": 27, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 28, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 29, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 30, "toNodeType": "net-server"}, {"source": 5, "fromNodeType": "net-work", "target": 31, "toNodeType": "net-server"}, {"source": 3, "fromNodeType": "net-work", "target": 32, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 33, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 34, "toNodeType": "net-server"}, {"source": 5, "fromNodeType": "net-work", "target": 35, "toNodeType": "net-server"}, {"source": 3, "fromNodeType": "net-work", "target": 36, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 36, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 37, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 38, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 39, "toNodeType": "net-server"}, {"source": 3, "fromNodeType": "net-work", "target": 39, "toNodeType": "net-server"}, {"source": 1, "fromNodeType": "net-work", "target": 40, "toNodeType": "net-server"}]}