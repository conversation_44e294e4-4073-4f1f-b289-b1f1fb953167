<template>
    <div class="cloudContent">
        <div class="cloudRight">
            <!-- <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                <a-form-item label="虚机名称">
                    <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.serverName" allowClear />
                </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div> -->
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button class="btnMargin" @click="handleRestore" :disabled="selectedRowKeys.length <= 0" >还原</a-button>
                    <!-- @click="$handleDel(selectedRowKeys,deleteServer,()=>{selectedRowKeys = [];getList()})" -->
                    <a-button @click="$handleDel(selectedRowKeys,deleteRecycle,()=>{selectedRowKeys = [];getList()})" :disabled="selectedRowKeys.length <= 0">
                        彻底删除
                    </a-button>
                </a-row>
                <a-table 
                :columns="columns" 
                row-key="id" 
                :data-source="serverlist" 
                :pagination="pagination" 
                @change="changeTable"
                :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                :scroll="{x:true}"
               >
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #serverName={record}>
                        <a @click="getConsoleURL(record)">{{record.serverName}}</a>
                    </template>
                    <template #createTime={record}>
                        <span>{{record.createTime.substr(0,10)}}</span>
                    </template>
                    <template #endTime={record}>
                        <span>{{record.endTime ? record.endTime.substr(0,10) : '永久'}}</span>
                    </template>
                    <template #imageEntity={record}>
                        {{record.imageEntity?.name}}
                    </template>
                    <template #flavorEntity={record}>
                        {{record.flavorEntity?.name}}
                    </template>
                    <template #serverAddressEntityList={record}>
                        <span v-for="(item,index) in record.serverAddressEntityList" :key="index">{{item.addr}}<br/></span>
                    </template>
                </a-table>
            </div>
        </div>
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import { ExclamationCircleOutlined, PlusOutlined } from "@ant-design/icons-vue"
import { computed, createVNode, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { queryWorker } from "@/api/backend/systems/user";
import { message, Modal } from "ant-design-vue";
import { useRoute } from "vue-router";
import { getToken} from "@/utils/auth";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { myRecycle, deleteRecycle, restoreRecycle,  } from "@/api/backend/devops/recycle";
import { selectImageList } from "@/api/backend/devops/image";
const token=getToken()
const { proxy } = getCurrentInstance();
const route = useRoute()
const selectedRowKeys = ref([])
const selectedRows = ref([])
const rules1 = {
    nextOwnerId:[{type:'number',required:true,message:'请选择接收人',trigger:'change'}]
}
const serverForm = ref(null);
const loading = ref(false);
const serverlist = ref([]);
const searchform = reactive({
    // cloudId:route.query.cloudId,
    // projectId:route.query.projectId,
    // domainId:route.query.domainId,
    // serverName:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,align:'center'},
    {title: '项目', dataIndex: 'projectName', slots: { customRender: 'projectName' }, key: 'id',align:'left'},
    {title: 'Host', dataIndex: 'host', key: 'id',align:'left'},
    {title: 'Host IP', dataIndex: 'hostIp', key: 'id',align:'left'},
    {title: '虚机名称', dataIndex: 'serverName', slots: { customRender: 'serverName' }, key: 'id',width:160,align:'left'},
    {title: '镜像名称', dataIndex: 'imageEntity',slots: { customRender: 'imageEntity' }, key: 'id',width:318, align:'left'},
    {title: '虚机类型', dataIndex: 'flavorEntity',slots: { customRender: 'flavorEntity' }, key: 'id',align:'left'},
    {title: 'IP地址', dataIndex: 'serverAddressEntityList',slots: { customRender: 'serverAddressEntityList' }, key: 'id',align:'left'},
    {title: '区域', dataIndex: 'availabilityZone', key: 'id',align:'center'},
    {title: '状态', dataIndex: 'statusText', key: 'id',align:'center'},
    {title: '开始日期', dataIndex: 'createTime', slots: { customRender: 'createTime' }, key: 'id',align:'center'},
    {title: '到期日期', dataIndex: 'endTime', slots: { customRender: 'endTime' }, key: 'id',align:'center'},
    {title: '所有者', dataIndex: 'ownerName', key: 'id',align:'center'},
];
let map = new Map();
const onSelectChange = (selectedRowkeys,selectedrows) => {
    selectedRowKeys.value = selectedRowkeys;
    for (let item of selectedRows.value.concat(selectedrows)) {
        if (!map.has(item.id)) {
            map.set(item.id, item);
        };
    };
    selectedRows.value = [...map.values()];
    };
const getList = async () => {
    // tempOption.value = undefined;
    // searchform.cloudId = localStorage.getItem('cloudId');
    // searchform.cloudId = route.query.cloudId;
    // searchform.projectId = route.query.projectId;
    // searchform.domainId = route.query.domainId;
    serverlist.value = await proxy.$getList(loading, myRecycle, searchform, pagination, getList );
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    // serverName: ""
  })
//   getList();
}
const handleRestore = () => {
    Modal.confirm({
        title: "确认还原",
        icon: createVNode(ExclamationCircleOutlined),
        content: '请确认是否继续？',
        okText: "提交",
        cancelText: "取消",
        maskClosable: true,
        onOk() {
            restoreRecycle(selectedRowKeys.value).then((res)=>{
                if(res.code === 0){
                    if(res.data === false)
                        message.error((res.msg == 'success' || !res.msg) ? '还原失败' : res.msg)
                    else{
                        message.success('还原成功')
                        selectedRowKeys.value = [];
                        getList()
                    }
                }
            })
        }
    })
}
onMounted(() => {
    getList();
    proxy.$mitt.on('getserverlist',getList)
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
</style>