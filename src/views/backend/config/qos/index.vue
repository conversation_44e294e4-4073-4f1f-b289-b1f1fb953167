<template>
    <div class="cloudContent">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                    <a-form-item label="规格名称">
                        <a-input placeholder="请输入规格名称" @pressEnter="handleSearch" @change="changeName" v-model:value="searchform.name" allowClear />
                    </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd">新增 </a-button>
                    <a-button @click="$handleDel(selectRowIds,deleteQOS,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table :row-selection="rowSelection" :columns="columns" row-key="id" :data-source="imagelist" :pagination="pagination" @change="changeTable" :scroll="{x:true}">
                    <template #index={index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #specs="{record,index}">
                        <span v-for="(item,index) in Object.keys(JSON5.parse(record.specs?record.specs:`{}`))" :key="index">
                            {{item+'='+JSON.parse(record.specs)[item]}}<br>
                        </span>
                    </template>
                    <template #action={record}>
                        <a-button type="link" class="button_E" @click="handleSpecs(record)">管理规格</a-button>
                        <a-button type="link" class="button_D" @click="$handleDel([record.id],deleteQOS,getList)">删除</a-button>
                    </template>
                </a-table>
                <Info ref="qosDialog" :info="info" @getlist="getList" />
                <Specs ref="specDialog" :info="specinfo" @getlist="getList" />
            </div>
        </div>
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Specs from "./specs.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { deleteQOS, getQoSList} from "@/api/backend/devops/config";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { useRoute } from "vue-router";
import JSON5 from "json5";
import router from "@/router";
const { proxy } = getCurrentInstance();
const route = useRoute()
const qosDialog = ref(null);
const specDialog = ref(null);
const loading = ref(false);
const imagelist = ref([]);
let selectRowIds: string[] = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false
})
const specinfo = reactive({
    isShow:false
})
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id',align:'center'},
    {title: '规格名称', dataIndex: 'name', key: 'id',align:'left'},
    {title: '消费者', dataIndex: 'consumer', key: 'id',align:'center'},
    {title: '规格参数', dataIndex: 'specs', slots: { customRender: 'specs' }, key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
const getList = async () => {
    searchform.cloudId = localStorage.getItem('cloudId');
    imagelist.value = await proxy.$getList(loading, getQoSList, searchform, pagination, getList )
}
const changeName = (value) => {
    // console.log('value',value.)
    if(!searchform.name){
        getList();
    }
}
const handleAllReset = () => {
    searchform.name = '';
    searchform.pageIndex = 1;
    // getList();
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAdd = () => {
    info.isAdd = info.isShow = true;
    info.isInfo = false;
}

const handleSpecs = (record) => {
    specinfo.isShow = true;
    specDialog.value.setInfo(record)
}
const handleEdit = (record) => {
    info.isAdd = false;
    info.isShow = true;
    info.isInfo = false;
    let record1 = {...record}
    qosDialog.value.setInfo(record1)
    // console.log('编辑',qosDialog.value.cloudform)
    // Object.assign(qosDialog.value.projectform, record1)
}
const handleView = (record) => {
    info.isShow = true;
    info.isInfo = true;
    let record1 = {...record}
    qosDialog.value.setInfo(record1)
}

onMounted(() => {
    getList()
    if(route.path == '/admin/config/qos')
        proxy.$mitt.on('getlist',getList)
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
</style>