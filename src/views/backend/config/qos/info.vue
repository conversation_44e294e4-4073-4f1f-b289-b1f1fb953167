<template>
    <div class=''>
        <a-modal 
        v-model:visible="info.isShow"
        @cancel="cancel"
        :maskClosable="info.isInfo"
        centered
        :getContainer="modalBindNode"
        >
            <template #title>
                <a-tooltip :overlayStyle="{minWidth:'500px'}">
                    <template #title>
                    <span>{{content.qos_add}}</span>
                    </template>
                    <span>{{info.isInfo ? '查看' : (info.isAdd ? '新增':'修改')}}</span> <QuestionCircleOutlined />
                </a-tooltip>
            </template>
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="qosform" ref="qosForm" :rules="rules" :labelCol="{span:6}">
                <a-form-item label="规格名称" name="name">
                    <a-input v-model:value.trim="qosform.name" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                  <a-form-item label="消费者" name="consumer" >
                    <a-select v-model:value="qosform.consumer" :options="consumerlist" placeholder="请选择"  :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear> 
                    </a-select>
                </a-form-item>
                <!-- <a-form-item label="规格参数" name="specs">
                     <div style="display:flex" align="baseline">
                        <a-input v-model:value="qosform.specsTemp" :disabled="info.isInfo" @change="changeExtra" style="border-right:none" allow-clear placeholder="请输入" />
                        <a-button type="primary" :disabled="!qosform.specsTemp || isRepeat" @click="addSpecs" style="border-left:none"><PlusOutlined class="text_img" /></a-button>
                    </div>
                </a-form-item>
                <a-space  
                    v-for="(item, index) in qosform.specs1"
                    :key="item.id"
                    style="display: flex; margin-bottom: 8px; "
                    align="baseline"
                >
                    <a-form-item 
                        :name="['specs1', index, 'value']"
                        :label="`${item.key}`"
                        :rules="{
                            required: true,
                            message: '请输入',
                        }"
                    >
                        <a-input v-model:value="item.value"  placeholder="请输入" />
                    </a-form-item>
                    <MinusCircleOutlined @click="removeSpecs(item)" />
                 </a-space> -->
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {saveQoS,updateQoS} from "@/api/backend/devops/config"
import {content} from "@/common/explain/modal"
import router from '@/router';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})

 const consumerlist = ref([{
      value: 'front-end',
      label: '前端',
    }, {
      value: 'back-end',
      label: '后端',
    }, {
      value: 'both',
      label: '前后两端',
     
    }]);
const qosForm = ref();
const serverlist = ref([]);
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    consumer:undefined,
    // specs:'',
    // specs1:[],
    // specsTemp:''
}
const qosform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    consumer:undefined,
    // specs:'',
    // specs1:[],
    // specsTemp:''
})
const rules = {
    name:[{required:true, message:'请输入',trigger:'change'}],
    consumer:[{required:true, message:'请选择',trigger:'change'}]
}
const options3 = ref([])
const isRepeat = ref(false)
const addSpecs = () => {
    if(!isRepeat.value){
        qosform.specs1.push({
        value: '',
        key:qosform.specsTemp
    });
    }
    isRepeat.value = qosform.specs1.some((item)=>{
        return item.key == qosform.specsTemp;
    })
};
const changeExtra = (e) => {
    isRepeat.value = qosform.specs1.some((item)=>{
        // console.log('true',item.key == e.target.value)
        return item.key == e.target.value;
    })
}
const removeSpecs = item => {
    let index = qosform.specs1.indexOf(item);
    if (index !== -1) {
        qosform.specs1.splice(index, 1);
    }
};

const handleSave = () => {
    let qosform1 = {...qosform}
    // qosform1.specs = qosform1.specs1[0].key+'='+qosform1.specs1[0].value;
    // console.log('qosform1',qosform1)
    proxy.$handleSave(qosForm.value, saveQoS, updateQoS, props,qosform1, ()=>{cancel();emit('getlist')},null,()=>{
        // if(qosform1.specs1 && qosform1.specs1.length > 0)
        //     qosform1.specs = qosform1.specs1[0].key+'='+qosform1.specs1[0].value;
        // qosform1.specs1 = undefined;
        // qosform1.specsTemp = undefined;
    })
}
const cancel = () => {

   props.info.isShow = false
    props.info.isInfo = false
    // isInfo.value = false
    qosForm.value.resetFields()
    Object.assign(qosform,defaultform)
}
const setInfo = (record) => {
    // if(record1.specs){
    //     let specs1 = Object.keys(JSON.parse(record1.specs));
    //     specs1.forEach((item,index)=>{
    //         record1.specs1.push({
    //             value: Object.values[index],
    //             key: item
    //         })
    //     })
    // }
    Object.assign(qosform, record1)
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
:deep(.ant-space-align-baseline){.ant-space-item:first-child{width:100%}}
</style>