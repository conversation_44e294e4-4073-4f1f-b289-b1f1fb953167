<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" @cancel="cancel" :bodyStyle="{height:'430px'}" :maskClosable="false" centered :getContainer="modalBindNode">
            <template #title>
                <a-tooltip :overlayStyle="{minWidth:'521px'}">
                    <template #title>
                       <pre style="white-space:pre-wrap;">{{content.qos_update}}</pre>
                    </template>
                    QOS规格 <QuestionCircleOutlined /></a-tooltip>
            </template>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" @click="save" >提交</a-button>
            </template>
            <a-table :columns="columns" :data-source="dataSource" size="small" :scroll="{ y: 302 }" :pagination="false" :loading="loading" bordered>
                <template #keyy="{ text, record }">
                    <a-input v-model:value="record.keyy" v-if="!record.isRaw && record.isEdit" size="small" allow-clear />
                    <span v-else>{{record.keyy}}</span>
                </template>
                <template #value="{ text, record }">
                    <a-input v-model:value="record.value" v-if="record.isEdit" size="small" allow-clear />
                    <span v-else>{{record.value}}</span>
                </template>
                <template #operation="{ record,index }">
                    <div class="editable-row-operations">
                        <span style="display:flex">
                            <a-button type="link" @click="handleSave(record)" v-if="record.isEdit" size="small">保存</a-button>
                            <a-button type="link" @click="handleEdit(record)" v-else size="small">修改</a-button>
                            <a-button type="link" v-if="record.isEdit" @click="handleCancel(record)" size="small">{{(!record.rawKey || !record.rawValue) ? '删除' : '取消'}}</a-button>
                            <a-popconfirm title="确定删除?" ok-text="是" cancel-text="否" @confirm="onDelete(record)" v-if="!record.isEdit">
                                <a-button type="link" size="small">删除</a-button>
                            </a-popconfirm>
                            <!-- <a @click="handleCopy(record)" >克隆</a> -->
                            <!-- <a-button type="link" @click="handleCopy(record)" size="small" :disabled="record.id">克隆</a-button> -->
                        </span>
                    </div>
                </template>
                <template #footer>
                    <div style="text-align:center" @click="handlePlus">
                        <a><PlusOutlined /> 添加</a>
                    </div>
                </template>
            </a-table>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
// import { deleteFloatingipPort, getflaotipInfo, saveFloatingipPort } from '@/api/backend/devops/floatingip';
// import { selectPortList } from '@/api/backend/devops/network';
import { message } from 'ant-design-vue';
import { computed, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import JSON5 from "json5";
import { setSpecsKey, unsetSpecsKey } from '@/api/backend/devops/config';
import {content} from "@/common/explain/modal";
import emiter from '@/utils/Bus';
import router from '@/router';
const route = useRoute()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false
            }
        }
    }
    
})
const columns = reactive([
  {
    title: 'Key',
    dataIndex: 'keyy',
    width:200,
    slots: { customRender: 'keyy' },
  },
  {
    title: 'Value',
    dataIndex: 'value',
    slots: { customRender: 'value' },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width:105,
    align:'center',
    slots: { customRender: 'operation' },
  },
]);
const specform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    id:0,
    specsMap:{}
})
const editableData = reactive({});
const loading = ref(false);
const rawportlist = ref([]);
// const exportList = ref([]);
const interportList = ref([]);
const count = computed(() => dataSource.value.length + 1);
const dataSource = ref([]);
const handlePlus = () => {
    let res = dataSource.value.some((item,index)=>!item.keyy && !item.value)
    let res1 = dataSource.value.some((item,index)=>!item.keyy || !item.value)
    if(res){
        message.warning('存在空行');
        return;
    }else if(res1){
        message.warning('存在未填写的Key或Value');
        return;
    }else{
        const newData = {key:dataSource.value.length,keyy:'',value:'',isRaw:false,isEdit:true,rawKey:'',rawValue:''};
        dataSource.value.push(newData);
    }
}
const handleSave = (record) => {
    if(!(record.keyy && record.value)){
        message.warning('请填写完整后保存')
        return;
    }
    record.isEdit = false;
    record.rawKey = record.keyy;
    record.rawValue = record.value;
}
const handleEdit = (record) => {
    record.isEdit = true;
}
const handleCancel = (record) => {
    record.isEdit = false;
    if(!(record.rawKey && record.rawValue)){
        dataSource.value = dataSource.value.filter(item => item.key !== record.key);
    }else{
        record.keyy = record.rawKey;
        record.value = record.rawValue;
    }
    
}
const onDelete = async (record) => {
    if(record.isRaw){
        let res = await unsetSpecsKey({id:specform.id,key:record.keyy})
        if(res.code == 0 && res.data !== false){
            message.success('删除成功');
            dataSource.value = dataSource.value.filter(item => item.key !== record.key);
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '删除失败' : res.msg);
        }
    }else
        dataSource.value = dataSource.value.filter(item => item.key !== record.key);
};
const handleChange = (value,option) => {
    // interportList.value = rawportlist.value[b.key].portIpEntityList;
    dataSource.value[option.index].internalPortId = option.id;
}
const cancel = () => {
    props.info.isShow = false;
    dataSource.value = [];
}
const save = async () => {
    let dataSource1 = [...dataSource.value];

    console.log('data',dataSource.value)
    let someres = dataSource1.some((item,index)=>{
        if(item.keyy && item.value)
            specform.specsMap[item.keyy] = item.value;
        return (!item.keyy || !item.value);
    })
    if(!someres){
        let res = await setSpecsKey(specform);
        if(res.code == 0 && res.data !== false){
                message.success((!res.msg || res.msg == 'success') ? '操作成功' : res.msg);
                emit('getlist')
                cancel();
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '修改失败' : res.msg);
        }
        return false;
    }else{
        message.warning('请填写完整后提交');
    }
}
const setInfo = (record) => {
    specform.id = record.id;
    let data = [];
    Object.keys(JSON5.parse(record.specs?record.specs:'{}')).forEach((item,index)=>{
        data.push({key:index,keyy:item,value:JSON.parse(record.specs)[item],isRaw:true,rawKey:item,rawValue:JSON.parse(record.specs)[item]});
        if(index >= Object.keys(JSON.parse(record.specs)).length - 1){
            dataSource.value = data;
        }
    })
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
.editable-row-operations a:first-child {
  margin-right: 8px;
}
:deep(.ant-empty-normal){margin: 92px 0;}
:deep(.ant-table.ant-table-bordered .ant-table-footer){padding: 8px;width: calc(100% - 8px);}
</style>