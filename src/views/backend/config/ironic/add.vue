<template>
    <div class=''>
        <a-modal 
        :title="info.isInfo ? '查看' : (info.isAdd ? '新增':'修改')"
        v-model:visible="info.isShow"
        @cancel="cancel"
        centered
        :body-style="{height:'456px',overflowY:'auto'}"
        :getContainer="modalBindNode"
        >
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="projectform" ref="projectForm" :rules="rules" :labelCol="{span:5}">
               
                <a-form-item label="节点名称" name="name">
                    <a-input v-model:value.trim="projectform.name" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                 <a-form-item label="驱动类型" name="driver"  >
                    <!-- <a-select v-model:value="projectform.serverIds" placeholder="请选择"  :disabled="info.isInfo" allow-clear> 
                        <a-select-option v-for="(item,index) in serverlist" :key="index" :value="item.id"  >{{item.serverName}}</a-select-option>
                    </a-select> -->
                     <a-select v-model:value="projectform.driver" placeholder="请选择" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode"> 
                        <a-select-option v-for="(item,index) in driverlist" :key="index" :value="item.name">{{item.name}}</a-select-option>
                         </a-select>
                </a-form-item>
                <a-form-item label="资源类型" name="resourceClass">
                    <a-input v-model:value.trim="projectform.resourceClass" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                
                 <!-- <a-form-item label="驱动详情" name="ruleName3">
                    <a-input v-model:value.trim="projectform.ruleName3" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                 <a-form-item label="驱动接口" name="ruleName4">
                    <a-input v-model:value.trim="projectform.ruleName4" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item> -->
                <a-form-item   align="baseline" label="属性"   >
                <div class="plus_txte" align="baseline">
                     <a-input  class="text_input" v-model:value="projectform.ruleName6" @change="changeProperties" :disabled="info.isInfo" allow-clear placeholder="请输入" />
                     <a-button type="primary" :disabled="projectform.ruleName6=='' || isRepeat"  class="text_button" @click="addUser"><PlusOutlined class="text_img" /></a-button>
                    
                </div>
                  
                </a-form-item>
                 <a-space  
                    v-for="(user, index) in projectform.properties1"
                    :key="user.id"
                    style="display: flex; margin-bottom: 8px; "
                    align="baseline"
                >
                 
                <a-form-item 
                    :name="['properties1', index, 'first']"
                    :label="`${user.lasts}`"
                    :rules="{
                        required: true,
                        message: '请输入',
                    }"
                >
                        <a-input v-model:value="user.first"  placeholder="请输入" />
                </a-form-item>
                
                    <MinusCircleOutlined @click="removeUser(user)" />
                 </a-space>


                  <a-form-item   align="baseline" label="额外属性">
                <div class="plus_txte" align="baseline">
                     <a-input  class="text_input" v-model:value="projectform.ruleName7" @change="changeExtra" :disabled="info.isInfo" allow-clear placeholder="请输入" />
                     <a-button type="primary" :disabled="!projectform.ruleName7 || isRepeat1" class="text_button" @click="addUser1"><PlusOutlined class="text_img" /></a-button>
                    
                </div>
                  
                </a-form-item>
                 <a-space  
                    v-for="(user, index) in projectform.extra1"
                    :key="user.id"
                    style="display: flex; margin-bottom: 8px; "
                    align="baseline"
                >
                 
                <a-form-item 
                    :name="['extra1', index, 'first']"
                    :label="`${user.lasts}`"
                    :rules="{
                        required: true,
                        message: '请输入',
                    }"
                >
                        <a-input v-model:value="user.first"  placeholder="请输入" />
                </a-form-item>
                
                    <MinusCircleOutlined @click="removeUser1(user)" />
                 </a-space>
                
                
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {saveBaremetalnode,updateBaremetalnode } from "@/api/backend/baremetal";
import {selectServerList} from "@/api/backend/devops/server"
import { selectDictList } from '@/api/backend/systems/dictionary';

import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons-vue';
import { selectDriverList } from '@/api/backend/baremetal';
import router from '@/router';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})
const isRepeat = ref(false);
const isRepeat1 = ref(false);
const projectForm = ref();
const serverlist = ref([]);
const driverlist = ref([]);
 const options1 = ref([{
      value: 'rem',
      label: 'REM',
    }]);
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    driver:undefined,
    ruleName6:'',
    ruleName7:'',
    properties: {},
    properties1: [],
    propertiesMap: [],
    extra: {},
    extra1: [],
    extraMap: [],
    
}
const projectform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    driver:undefined,
    ruleName6:'',
    ruleName7:'',
    properties: {},
    properties1: [],
    propertiesMap: [],
    extra: {},
    extra1: [],
    extraMap: [],
   
})
const rules = {
    name:[{required:true, message:'请输入',trigger:'change'}],
    driver:[{required:true,  message:'请选择',trigger:'change'}],
    // resourceClass:[{required:true,  message:'请输入',trigger:'change'}],
    //  maxCpu:[{required:true, type:'number', message:'请选择',trigger:'change'}],

    // maxCpu:[{required:true, type:'number', message:'仅支持输入1-100之间的整数',trigger:'change',transform(value){ 
    //                     if(value){
    //                         // 将输入的值转为数字
    //                         var val = Number(value)
    //                         // 正则表达式校验输入的数字是否在0-100之内并且属于整数
    //                         if(/^(?:[1-9]?\d|100)$/.test(val)) return val
    //                         // 返回false即为校验失败
    //                         return false
    //                     }
    //                 }}],
 
    serverIds:[{required:true, message:'请选择',trigger:'change'}],
   

   
}
const changeProperties = (e) => {
    isRepeat.value = projectform.properties1.some((item)=>{
        return item.lasts == e.target.value;
    })
}

const changeExtra = (e) => {
    isRepeat1.value = projectform.extra1.some((item)=>{
        return item.lasts == e.target.value;
    })
}
const removeUser = item => {
      let index = projectform.properties1.indexOf(item);
      if (index !== -1) {
        projectform.properties1.splice(index, 1);
      }
    };
const addUser = () => {
    
    if(!isRepeat.value){
        projectform.properties1.push({
        first: '',
        lasts:projectform.ruleName6
      });
    }
    isRepeat.value = projectform.properties1.some((item)=>{
        return item.lasts == projectform.ruleName6;
    })
    };
const removeUser1 = item => {
      let index = projectform.extra1.indexOf(item);
      if (index !== -1) {
        projectform.extra1.splice(index, 1);
      }
    };
const addUser1 = () => {
    
    if(!isRepeat1.value){
        projectform.extra1.push({
        first: '',
        last: '',
        lasts:projectform.ruleName7
      });
    }
    isRepeat1.value = projectform.extra1.some((item)=>{
        return item.lasts == projectform.ruleName7;
    })
    };
const options3 = ref([])
const getServerList = async () => {
    let res = await selectServerList()
    if(res){
        if(res.code == 0){
            // Object.assign(serverlist,res.data);
            serverlist.value = res.data;
        }
    }
}

const selectDriverlist = async () => {
    let res = await selectDriverList({cloudId:localStorage.getItem('cloudId')});
    if(res){
        if(res.code == 0){
            // Object.assign(serverlist,res.data);
            driverlist.value = res.data;
        }
    }
}
const handleSave = () => {
    
    let projectform1 = {...projectform};
    proxy.$handleSave(projectForm.value, saveBaremetalnode, updateBaremetalnode, props,projectform1, ()=>{cancel();emit('getlist');},null,()=>{
        if(projectform.extra1 && projectform.extra1.length > 0){
            projectform1.extraMap = {};
            projectform.extra1.forEach((item,index)=>{
                projectform1.extraMap[item.lasts] = item.first
            })
        }else{
            projectform1.extraMap = undefined;
        }
        if(projectform.properties1 && projectform.properties1.length > 0){
            projectform1.propertiesMap = {};
            projectform.properties1.forEach((item,index)=>{
                projectform1.propertiesMap[item.lasts] = item.first
            })
        }else{
            projectform1.propertiesMap = undefined;
        }
        projectform1.extra1 = undefined;
        projectform1.extra = undefined;
        projectform1.properties1 = undefined;
        projectform1.properties = undefined;
        projectform1.ruleName6 = undefined;
        projectform1.ruleName7 = undefined;
    })
}
const cancel = () => {

   props.info.isShow = false
    props.info.isInfo = false
    // isInfo.value = false
    projectForm.value.resetFields()
    Object.assign(projectform,defaultform)
}
onMounted(() => {getServerList();selectDriverlist();})
defineExpose({projectform})
</script>
<style lang='scss' scoped>
.plus_txte{
    display: flex;
}
.text_input{
     border-right:none ;
}
.text_button{
    border-left:none ;
}
:deep(.ant-space-align-baseline){.ant-space-item:first-child{width:100%}}
</style>