<template>
    <div class=''>
        <a-modal :title="info.isInfo ? '查看端口' : (info.isAdd ? '添加端口' : '修改端口')" v-model:visible="info.isShow" @cancel="cancel"
        :body-style="{height:'522px',overflowY:'auto'}" centered>
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="save">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="portform" ref="portForm" :rules="rules" :label-col="{span:5}">
                <a-form-item label="MAC地址" name="macAddress">
                    <a-input v-model:value="portform.macAddress" placeholder="请输入MAC地址" :disabled="info.isInfo"></a-input>
                </a-form-item>
                <a-form-item label="PXE enabled" name="pxeEnabled">
                    <a-switch v-model:checked="portform.pxeEnabled" checked-children="True" un-checked-children="False" :disabled="info.isInfo" />
                </a-form-item>
                <a-form-item label="端口组" name="portgroupId">
                    <a-select v-model:value="portform.portgroupId" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item, index) in portgrouplist" :key="index" :label="item.name" :value="item.id">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-space direction="vertical" align="end" class="connect">
                    <span style="margin-left:24px;margin-bottom:24px">本地链接连接</span>
                    <a-form-item label="port_id" :name="['connectionMap', 'port_id']" :label-col="{span:5}" :wrapper-col="{span:18}">
                        <a-input v-model:value="portform.connectionMap.port_id" />
                    </a-form-item>
                    <a-form-item label="switch_id" :name="['connectionMap', 'switch_id']" :label-col="{span:5}" :wrapper-col="{span:18}">
                        <a-input v-model:value="portform.connectionMap.switch_id" placeholder="MAC地址或OpenFlow datapath ID" />
                    </a-form-item>
                    <a-form-item label="switch_info" :name="['connectionMap', 'switch_info']" :label-col="{span:5}" :wrapper-col="{span:18}">
                        <a-input v-model:value="portform.connectionMap.switch_info" />
                    </a-form-item>
                </a-space>
                <a-form-item label="附加属性">
                    <div style="display:flex" align="baseline">
                        <a-input v-model:value="portform.ruleName6" :disabled="info.isInfo" @change="changeExtra" style="border-right:none" allow-clear placeholder="请输入" />
                        <a-button type="primary" :disabled="!portform.ruleName6 || isRepeat" @click="addUser" style="border-left:none"><PlusOutlined class="text_img" /></a-button>
                    </div>
                </a-form-item>
                <a-space  
                    v-for="(user, index) in portform.extra1"
                    :key="user.id"
                    style="display: flex; margin-bottom: 8px; "
                    align="baseline"
                >
                    <a-form-item 
                        :name="['extra1', index, 'first']"
                        :label="`${user.lasts}`"
                        :rules="{
                            required: true,
                            message: '请输入',
                        }"
                    >
                        <a-input v-model:value="user.first"  placeholder="请输入" />
                    </a-form-item>
                    <MinusCircleOutlined @click="removeUser(user)" />
                 </a-space>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { saveClusterNode, selectClusterProfileList, updateClusterNode } from '@/api/backend/cluster';
import { savePort, selectPortGroupList, updatePort } from '@/api/backend/baremetal';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import router from '@/router';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info: {
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow: false,
                isInfo: false
            }
        }
    }
})
const isRepeat = ref(false);
const portForm = ref();
const profilelist = ref([])
const portgrouplist = ref([])
const defaultform = {
    "cloudId": router.currentRoute.value.query.cloudId,
    "macAddress": "",
    "pxeEnabled": false,
    "portgroupId": "",
    "projectId": router.currentRoute.value.query.projectId,
    "extra1":[],
    "extra":{},
    "connectionMap":{port_id:'',switch_id:'',switch_info:''}
}
const portform = reactive({
    "cloudId": router.currentRoute.value.query.cloudId,
    "macAddress": "",
    "pxeEnabled": false,
    "portgroupId": "",
    "projectId": router.currentRoute.value.query.projectId,
    "extra1":[],
    "extra":{},
    "connectionMap":{port_id:'',switch_id:'',switch_info:''}
})
const rules = {
    name:[{required:true,message:'请输入集群节点名称',trigger:'change'}],
    macAddress:[{required:true,message:'请输入MAC地址',trigger:'change'}],
    pxeEnabled:[{required:true,type:'boolean',message:'请选择',trigger:'change'}],
}
const changeExtra = (e) => {
    isRepeat.value = portform.extra1.some((item)=>{
        return item.lasts == e.target.value;
    })
}
const cancel = () => {
    props.info.isShow = false;
    Object.assign(portform,defaultform)
}
const objToStrMap = (obj) => {
        let strMap = new Map();
        for (let k of Object.keys(obj)) {
            strMap.set(k,obj[k]);
        }
        return strMap;
    }
    /**
     *json转换为map
     */
    const jsonToMap = (jsonStr) => {
        return objToStrMap(JSON.parse(JSON.stringify(jsonStr)));
    }
const save = () => {
    let portform1 = {...portform};
    if(portform.extra1 && portform.extra1.length > 0){
        portform1.extra = {};
        portform.extra1.forEach((item,index)=>{
            portform1.extra[item.lasts] = item.first
        })
        portform1.extra = JSON.stringify(portform1.extra);
    }else{
        portform1.extra = undefined;
    }
    if(portform1.pxeEnabled){
        portform1.pxeEnabled = 1;
    }else{
        portform1.pxeEnabled = 0;
    }
    portform1.ruleName6 = undefined;
    portform1.extra1 = undefined;
    proxy.$handleSave(portForm.value, savePort, updatePort, props, portform1, ()=>{emit('getlist',portform.clusterId);cancel()},null)
}
const selectPortGrouplist = async (nodeId) => {
    let res = await selectPortGroupList({nodeId})
    if(res.code == 0){
        portgrouplist.value = res.data;
    }
}
const setInfo = (record) => {
    let record1 = {...record};
    // let properties1 = Object.keys(JSON.parse(record1.properties));
    // properties1.forEach((item,index)=>{
    //     record1.properties1.push({
    //         first: Object.values[index],
    //         lasts: item
    //     })
    // })
    
    if(record1.localLinkConnection){
        record1.connectionMap = JSON.parse(record1.localLinkConnection);
        // extra1.forEach((item,index)=>{
        //     record1.extra1.push({
        //         first: Object.values[index],
        //         lasts: item
        //     })
        // })
    }
    if(record1.extra){
        let extra1 = Object.keys(JSON.parse(record1.extra));
        extra1.forEach((item,index)=>{
            record1.extra1.push({
                first: Object.values[index],
                lasts: item
            })
        })
    }
    if(record1.pxeEnabled == 1){
        record1.pxeEnabled = true;
    }else{
        record1.pxeEnabled = false;
    }
    
    Object.assign(portform, record1)
}
const removeUser = item => {
    let index = portform.extra1.indexOf(item);
    if (index !== -1) {
        portform.extra1.splice(index, 1);
    }
};
const addUser = () => {
    if(!isRepeat.value){
        portform.extra1.push({
        first: '',
        lasts:portform.ruleName6
    });
    }
    isRepeat.value = portform.extra1.some((item)=>{
        return item.lasts == portform.ruleName6;
    })
};
onMounted(() => {})
defineExpose({portform, setInfo,selectPortGrouplist})
</script>
<style lang='scss' scoped>
:deep(.ant-space-align-baseline){.ant-space-item:first-child{width:100%}}
.connect{background-color:#fafafa;border:1px solid #f0f2f5;width:100%;margin-bottom:24px;padding-top:10px;
:deep(.ant-space-item){width:100%}}

</style>