<template>
    <div class=''>
        <a-modal :title="info.isInfo ? '查看端口组' : (info.isAdd ? '添加端口组' : '修改端口组')" v-model:visible="info.isShow" @cancel="cancel"
        :body-style="{height:'456px',overflowY:'auto'}" centered>
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="save">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="portgform" ref="portgForm" :rules="rules" :label-col="{span:9}">
                <a-form-item label="MAC地址" name="macAddress">
                    <a-input v-model:value="portgform.macAddress" placeholder="请输入 MAC地址" :disabled="info.isInfo"></a-input>
                </a-form-item>
                <a-form-item label="名称" name="name">
                    <a-input v-model:value="portgform.name" placeholder="请输入端口组名称" :disabled="info.isInfo"></a-input>
                </a-form-item>
                <a-form-item label="是否支持独立端口" name="standalonePortsSupported">
                    <a-switch v-model:checked="portgform.standalonePortsSupported" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                </a-form-item>
                <a-form-item label="类型" name="mode">
                    <a-select v-model:value="portgform.mode" :getPopupContainer="triggerNode => triggerNode.parentNode" placeholder="请选择类型">
                        <a-select-option label="balance-rr" value="balance-rr">balance-rr</a-select-option>
                        <a-select-option label="active-backup" value="active-backup">active-backup</a-select-option>
                        <a-select-option label="balance-xcr" value="balance-xcr">balance-xcr</a-select-option>
                        <a-select-option label="broadcast" value="broadcast">broadcast</a-select-option>
                        <a-select-option label="802.3ad" value="802.3ad">802.3ad</a-select-option>
                        <a-select-option label="balance-tlb" value="balance-tlb">balance-tlb</a-select-option>
                        <a-select-option label="balance-alb" value="balance-alb">balance-alb</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="属性">
                    <div style="display:flex" align="baseline">
                        <a-input v-model:value="portgform.ruleName6" :disabled="info.isInfo" @change="changeProperties" style="border-right:none" allow-clear placeholder="请输入" />
                        <a-button type="primary" :disabled="!portgform.ruleName6 || isRepeat" @click="addUser" style="border-left:none"><PlusOutlined class="text_img" /></a-button>
                    </div>
                </a-form-item>
                <a-space  
                    v-for="(user, index) in portgform.properties1"
                    :key="user.id"
                    style="display: flex; margin-bottom: 8px; "
                    align="baseline"
                >
                    <a-form-item 
                        :name="['properties1', index, 'first']"
                        :label="`${user.lasts}`"
                        :rules="{
                            required: true,
                            message: '请输入',
                        }"
                    >
                        <a-input v-model:value="user.first"  placeholder="请输入" />
                    </a-form-item>
                    <MinusCircleOutlined @click="removeUser(user)" />
                 </a-space>
                <a-form-item label="附加属性">
                    <div style="display:flex" align="baseline">
                        <a-input v-model:value="portgform.ruleName7" :disabled="info.isInfo" @change="changeExtra" style="border-right:none" allow-clear placeholder="请输入" />
                        <a-button type="primary" :disabled="!portgform.ruleName7 || isRepeat1" @click="addUser1" style="border-left:none"><PlusOutlined class="text_img" /></a-button>
                    </div>
                </a-form-item>
                <a-space  
                    v-for="(user, index) in portgform.extra1"
                    :key="user.id"
                    style="display: flex; margin-bottom: 8px;"
                    align="baseline"
                >
                    <a-form-item 
                        :name="['extra1', index, 'first']"
                        :label="`${user.lasts}`"
                        :rules="{
                            required: true,
                            message: '请输入',
                        }"
                    >
                        <a-input v-model:value="user.first"  placeholder="请输入" />
                    </a-form-item>
                    <MinusCircleOutlined @click="removeUser1(user)" />
                 </a-space>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { savePortGroup, updatePortGroup } from '@/api/backend/baremetal';
import { saveClusterNode, selectClusterProfileList, updateClusterNode } from '@/api/backend/cluster';
import router from '@/router';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info: {
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow: false,
                isInfo: false
            }
        }
    }
})
const isRepeat = ref(false);
const isRepeat1 = ref(false);
const portgForm = ref();
const profilelist = ref([])
const defaultform = {
    "cloudId": router.currentRoute.value.query.cloudId,
    "nodeId":'',
    "thirdNodeUuid":'',
    "macAddress": "",
    "name": "",
    "standalonePortsSupported": true,
    "mode": "active-backup",
    "properties": {},
    "properties1": [],
    "propertiesMap": [],
    "projectId": router.currentRoute.value.query.projectId,
    "extra":{},
    "extra1":[],
    "extraMap":[],
}
const portgform = reactive({
    "cloudId": router.currentRoute.value.query.cloudId,
    "thirdNodeUuid":'',
    "nodeId":'',
    "macAddress": "",
    "name": "",
    "standalonePortsSupported": true,
    "mode": "active-backup",
    "properties": {},
    "properties1": [],
    "propertiesMap": [],
    "projectId": router.currentRoute.value.query.projectId,
    "extra":{},
    "extra1":[],
    "extraMap":[],
})
const rules = {
    name:[{required:true,message:'请输入集群节点名称',trigger:'change'}],
    macAddress:[{required:true,message:'请输入 MAC地址',trigger:'change'}],
    standalonePortsSupported:[{required:true,type:'boolean',message:'请选择',trigger:'change'}],
    mode:[{required:true,message:'请选择模式',trigger:'change'}],
}
const changeProperties = (e) => {
    isRepeat.value = portgform.properties1.some((item)=>{
        return item.lasts == e.target.value;
    })
}

const changeExtra = (e) => {
    isRepeat1.value = portgform.extra1.some((item)=>{
        return item.lasts == e.target.value;
    })
}
const cancel = () => {
    props.info.isShow = false;
    Object.assign(portgform,defaultform)
}
const objToStrMap = (obj) => {
        let strMap = new Map();
        for (let k of Object.keys(obj)) {
            strMap.set(k,obj[k]);
        }
        return strMap;
    }
    /**
     *json转换为map
     */
    const jsonToMap = (jsonStr) => {
        return objToStrMap(JSON.parse(JSON.stringify(jsonStr)));
    }
const save = () => {
    let portgform1 = {...portgform};
    if(portgform.extra1 && portgform.extra1.length > 0){
        portgform1.extraMap = {};
        portgform.extra1.forEach((item,index)=>{
            portgform1.extraMap[item.lasts] = item.first
        })
    }else{
        portgform1.extraMap = undefined;
    }
    if(portgform.properties1 && portgform.properties1.length > 0){
        portgform1.propertiesMap = {};
        portgform.properties1.forEach((item,index)=>{
            portgform1.propertiesMap[item.lasts] = item.first
        })
    }else{
        portgform1.propertiesMap = undefined;
    }
    if(portgform1.standalonePortsSupported){
        portgform1.standalonePortsSupported = 1;
    }else{
        portgform1.standalonePortsSupported = 0;
    }
        portgform1.extra1 = undefined;
        portgform1.extra = undefined;
        portgform1.properties1 = undefined;
        portgform1.properties = undefined;
        portgform1.ruleName6 = undefined;
        portgform1.ruleName7 = undefined;
    // setTimeout(()=>{
    //     portgform1.extra = jsonToMap(portgform1.extra);
    //     portgform1.properties = jsonToMap(portgform1.properties);
        
    // })
    proxy.$handleSave(portgForm.value, savePortGroup, updatePortGroup, props, portgform1, ()=>{emit('getlist',portgform.clusterId);cancel()},null)
}
const setInfo = (record) => {
    let record1 = {...record};
    if(record1.properties){
        let properties1 = Object.keys(JSON.parse(record1.properties));
    properties1.forEach((item,index)=>{
        record1.properties1.push({
            first: Object.values[index],
            lasts: item
        })
    })
    }
    
    if(record1.extra){
        let extra1 = Object.keys(JSON.parse(record1.extra));
    extra1.forEach((item,index)=>{
        record1.extra1.push({
            first: Object.values[index],
            lasts: item
        })
    })
    }
    if(record1.standalonePortsSupported == 1){
        record1.standalonePortsSupported = true;
    }else{
        record1.standalonePortsSupported = false;
    }
    Object.assign(portgform, record1)
}
const removeUser = item => {
    let index = portgform.properties1.indexOf(item);
    if (index !== -1) {
        portgform.properties1.splice(index, 1);
    }
};
const addUser = () => {
    if(!isRepeat.value){
        portgform.properties1.push({
        first: '',
        lasts:portgform.ruleName6
    });
    }
    isRepeat.value = portgform.properties1.some((item)=>{
        return item.lasts == portgform.ruleName6;
    })
};
const removeUser1 = item => {
    let index = portgform.extra1.indexOf(item);
    if (index !== -1) {
        portgform.extra1.splice(index, 1);
    }
};
const addUser1 = () => {
    if(!isRepeat1.value){
        portgform.extra1.push({
        first: '',
        lasts:portgform.ruleName7
    });
    }
    isRepeat1.value = portgform.extra1.some((item)=>{
        return item.lasts == portgform.ruleName7;
    })
};
onMounted(() => {})
defineExpose({portgform, setInfo})
</script>
<style lang='scss' scoped>
:deep(.ant-space-align-baseline){.ant-space-item:first-child{width:100%}}
</style>