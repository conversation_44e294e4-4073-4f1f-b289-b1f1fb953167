<template>
    <div class='back-page'>
        <a-page-header
          style="background-color:#fff"
          :title="ironicform.name"
          @back="()=>{info.isInfo = info.isShow = false}"
        />
        <a-tabs class="back-content" :animated="false" @change="changeTab">
            <a-tab-pane tab=""></a-tab-pane>
            <a-tab-pane key="2" tab="概览">
                <div class="ironic-info1">
                    <a-descriptions :column="1" :label-style="{justifyContent: 'end',width: '120px'}">
                            <a-descriptions-item label="节点名称">{{ironicform?.name}}</a-descriptions-item>
                            <a-descriptions-item label="节点ID">{{ironicform.thirdNodeId}}</a-descriptions-item>
                            <a-descriptions-item label="虚机">{{ironicform.chassisUuid ? ironicform.chassisUuid : '-'}}</a-descriptions-item>
                            <a-descriptions-item label="资源类">{{ironicform.resourceClass ? ironicform.resourceClass : '-' }}</a-descriptions-item>
                            <a-descriptions-item label="驱动">{{ironicform.driver}}</a-descriptions-item>
                            <a-descriptions-item label="创建时间">{{ironicform.createTime}}</a-descriptions-item>
                        </a-descriptions>
                </div>
            </a-tab-pane>
            <a-tab-pane key="3" tab="端口组">
                <a-button type="primary" @click="handlePortGAdd">新增端口组</a-button><br/><br/>
                <a-table :columns="columns1" :data-source="portgrouplist" rowKey="id">
                    <template #index={record,index}>
                        {{index+1}}
                    </template>
                    <template #action={record}>
                        <!-- <a-button class="button_V" @click="handlePortGInfo(record)">查看</a-button> -->
                        <a-button class="button_E" @click="handlePortGEdit(record)">修改</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deletePortG,()=>{selectPortGrouplist(ironicform.id)})">删除</a-button>
                    </template>
                </a-table>
            </a-tab-pane>
            <a-tab-pane key="4" tab="端口">
                <a-button type="primary" @click="handlePortAdd">新增端口</a-button><br/><br/>
                <a-table :columns="columns2" :data-source="portlist" rowKey="id">
                    <template #index={record,index}>
                        {{index+1}}
                    </template>
                    <template #pxeEnabled={record}>
                        {{record.pxeEnabled == 1 ? 'True':'False'}}
                    </template>
                    <template #action={record}>
                        <!-- <a-button class="button_V" @click="handlePortInfo(record)">查看</a-button> -->
                        <a-button class="button_E" @click="handlePortEdit(record)">修改</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deletePort,()=>{selectPortlist(ironicform.id)})">删除</a-button>
                    </template>
                </a-table>
            </a-tab-pane>
            <a-tab-pane key="5" tab="配置">
                <div class="ironic-info">
                    <a-descriptions :column="1" title="驱动信息" :label-style="{justifyContent: 'end',width: '150px'}">
                            <a-descriptions-item v-for="(item,index) in Object.keys(ironicform.driverInfo)" :label="item">{{Object.values(ironicform.driverInfo)[index]}}</a-descriptions-item>
                            <!-- <a-descriptions-item label="节点ID">{{ironicform.thirdNodeId}}</a-descriptions-item>
                            <a-descriptions-item label="虚机">{{ironicform.chassisUuid ? ironicform.chassisUuid : '-'}}</a-descriptions-item>
                            <a-descriptions-item label="资源类">{{ironicform.resourceClass ? ironicform.resourceClass : '-' }}</a-descriptions-item>
                            <a-descriptions-item label="驱动">{{ironicform.driver}}</a-descriptions-item>
                            <a-descriptions-item label="创建时间">{{ironicform.createTime}}</a-descriptions-item> -->
                        </a-descriptions>
                        <div class="valid" v-if="validlist && validlist.length > 0">
                            <a-table :title="()=>'驱动验证信息'" tableLayout="fixed" :columns="columns" :data-source="validlist" :pagination="false">
                                <template #result={record}>
                                    <CheckOutlined v-if="record.result" style="color:#91CC75" />
                                    <CloseOutlined v-else style="color:#EE6666" />
                                </template>
                            </a-table>
                        </div>
                        <a-descriptions v-else :column="1" title="驱动验证信息" :label-style="{justifyContent: 'end',width: '150px'}"></a-descriptions>
                        <!-- <a-descriptions :column="1" title="驱动验证信息" :label-style="{justifyContent: 'end',width: '120px'}"> -->
                            
                        <!-- </a-descriptions> -->
                        <a-descriptions :column="1" title="属性" :label-style="{justifyContent: 'end',width: '120px'}">
                            <a-descriptions-item v-for="(item,index) in Object.keys(ironicform.properties)" :label="item">{{Object.values(ironicform.properties)[index]}}</a-descriptions-item>
                        </a-descriptions>
                        <a-descriptions :column="1" title="附加属性" :label-style="{justifyContent: 'end',width: '120px'}">
                            <a-descriptions-item v-for="(item,index) in Object.keys(ironicform.extra)" :label="item">{{Object.values(ironicform.extra)[index]}}</a-descriptions-item>
                        </a-descriptions>
                        <a-descriptions :column="1" title="虚机信息" :label-style="{justifyContent: 'end',width: '120px'}">
                            <a-descriptions-item v-for="(item,index) in Object.keys(ironicform.instanceInfo)" :label="item">{{Object.values(ironicform.instanceInfo)[index]}}</a-descriptions-item>
                        </a-descriptions>
                        <a-descriptions :column="1" title="启动设备" :label-style="{justifyContent: 'end',width: '120px'}">
                            <a-descriptions-item v-for="(item,index) in Object.keys(ironicform.bootInterface)" :label="item">{{Object.values(ironicform.bootInterface)[index]}}</a-descriptions-item>
                        </a-descriptions>
                </div>
            </a-tab-pane>
        </a-tabs>
        <PortGroup ref="pgGialog" :info="pginfo" @getlist="selectPortGrouplist" v-if="pginfo.isShow" />
        <Port ref="pGialog" :info="pinfo" @getlist="selectPortlist" v-if="pinfo.isShow" />
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {selectPortGroupList, selectPortList, deletePortG, deletePort, validateNode} from "@/api/backend/baremetal";
import PortGroup from "./portgroup/info.vue";
import Port from "./port/info.vue";
import emiter from '@/utils/Bus';
const {proxy} = getCurrentInstance();
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false,
                isInfo:false
            }
        }
    }
})
const portgrouplist = ref([]);
const portlist = ref([]);
const validlist = ref([]);
const pgGialog = ref();
const pGialog = ref();
const pginfo = reactive({
    isAdd:true,
    isShow:false,
    isInfo:false
})
const pinfo = reactive({
    isAdd:true,
    isShow:false,
    isInfo:false
})
const ironicform = reactive({
    name:'',
    thirdNodeId:'',
    chassisUuid:'',
    resourceClass:'',
    createTime:'',
    driver:'',

})
const columns = [
    // {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '接口', dataIndex: 'interface', key: 'id', width:90, align:'center'},
    {title: '合法性', dataIndex: 'result', slots: { customRender: 'result' }, key: 'id', width:80, align:'center'},
    {title: '当前接口', dataIndex: 'currentInterface', key: 'id', width:100, align:'center'},
    {title: '说明', dataIndex: 'reason', key: 'id', align:'left'},
    // {title: '说明', dataIndex: 'reason', ellipsis:true, key: 'id', align:'center'},
]
const columns1 = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '名称', dataIndex: 'name', key: 'id', align:'center'},
    {title: 'UUID', dataIndex: 'thirdNodeUuid', key: 'id', align:'center'},
    {title: 'MAC地址', dataIndex: 'macAddress', key: 'id', align:'center'},
    // {title: '端口', dataIndex: 'name', key: 'id', align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' } }
]
const columns2 = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: 'MAC地址', dataIndex: 'macAddress', key: 'id', align:'center'},
    {title: 'PXE Enabled', dataIndex: 'pxeEnabled', slots: { customRender: 'pxeEnabled' }, key: 'id', align:'center'},
    {title: 'Portgroup', dataIndex: 'thirdPortgroupUuid', key: 'id', align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' } }
]
const changeTab = (e) => {
    if(e == 3){
        selectPortGrouplist()
    }
    if(e == 4){
        selectPortlist()
    }
    if(e == 5){
        selectValidList()
    }
}
const handlePortGAdd = () => {
    pginfo.isAdd = pginfo.isShow = true;
    pginfo.isInfo = false;
    proxy.$nextTick(()=>{
        pgGialog.value.portgform.nodeId = ironicform.id;
        pgGialog.value.portgform.thirdNodeUuid = ironicform.thirdNodeId;
    })
}
const handlePortGEdit = (record) => {
    pginfo.isAdd = pginfo.isInfo = false;
    pginfo.isShow = true;
    proxy.$nextTick(()=>{
        pgGialog.value.setInfo(record);
    })
}
const handlePortAdd = () => {
    pinfo.isAdd = pinfo.isShow = true;
    pinfo.isInfo = false;
    proxy.$nextTick(()=>{
        pGialog.value.portform.nodeId = ironicform.id;
        pGialog.value.portform.thirdNodeId = ironicform.thirdNodeId;
        pGialog.value.selectPortGrouplist(ironicform.id)
    })
}
const handlePortEdit = (record) => {
    pinfo.isAdd = pinfo.isInfo = false;
    pinfo.isShow = true;
    proxy.$nextTick(()=>{
        pGialog.value.selectPortGrouplist(ironicform.id)
        pGialog.value.setInfo(record);
    })
}
const selectPortGrouplist = async () => {
    let res = await selectPortGroupList({nodeId:ironicform.id})
    if(res.code == 0){
        portgrouplist.value = res.data;
    }
}
const selectPortlist = async () => {
    let res = await selectPortList({nodeId:ironicform.id})
    if(res.code == 0){
        portlist.value = res.data;
    }
}
const selectValidList = async () => {
    let res = await validateNode(ironicform.id)
    if(res.code == 0){
        let firstlist = Object.values(JSON.parse(res.data));
        firstlist.map((item,index)=>{
            item.interface = Object.keys(JSON.parse(res.data))[index];
            item.key = index;
            item.currentInterface = ironicform[Object.keys(JSON.parse(res.data))[index]+'Interface'];
        })
        // console.log('JSON.parse(res.data)',firstlist)
        validlist.value = firstlist;
    }
}
onMounted(() => {})
defineExpose({ironicform})
</script>
<style lang='scss' scoped>
.ironic-info{display: flex;flex-wrap: wrap;justify-content: space-between;
.ant-descriptions{width: calc(50% - 32px);}
}
.back-content{padding: 0;height: 100%;overflow-y: initial;}
:deep(.ant-tabs .ant-tabs-top-content){
    height: calc(100vh - 270px);
    overflow-y: scroll;
    padding: 0 16px 16px;
}

.ant-descriptions{padding: 20px;margin: 0 16px 16px;border: 1px solid #f0f2f5;transition:all .3s;}
.ant-descriptions:hover {
    border-color: #00000017;
    box-shadow: 0 2px 8px #00000017;
}
:deep(.ant-descriptions-title){padding: 5px 10px;background-color: #fafafa;}
.valid{
    width: calc(50% - 32px);
    margin: 0 16px 16px;
    border: 1px solid #f0f2f5;
    padding: 20px;
    transition: all 0.3s;
}
:deep(.ant-table-title){font-size: 16px;font-weight: bold;padding: 5px 10px;margin-bottom: 20px;background-color: #fafafa;}
</style>