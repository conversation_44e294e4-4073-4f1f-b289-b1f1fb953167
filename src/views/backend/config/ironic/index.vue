<template>
    <div class="cloudContent" v-if="!info.isInfo">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <!-- <div class="buttonPadding">
                <a-form layout="inline" :model="searchform">
                <a-form-item label="节点名称">
                    <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.ruleName" allowClear />
                </a-form-item>
                <a-form-item>
                    <a-button type="primary" @click="handleSearch"> {{ $t("m.search") }} </a-button>
                </a-form-item>
                <a-form-item>
                    <a-button @click="handleAllReset"> {{ $t("m.reset") }} </a-button>
                </a-form-item>
                </a-form>
            </div> -->
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd">新增 </a-button>
                </a-row>
                <a-table :columns="columns" row-key="id" :data-source="imagelist" :pagination="pagination" @change="changeTable">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #name={record}>
                        <a href="#" @click="handleView(record)">{{record.name ? record.name : '-'}}</a>
                    </template>
                    <template #instanceUuid={record}>
                        {{(record.instanceUuid && record.instanceUuid != 'null') ? record.instanceUuid : '-'}}
                    </template>
                    <template #powerState={record}>
                        {{(record.powerState && record.powerState != 'null') ? record.powerState : '-'}}
                    </template>
                    <template #properties={record}>
                        -
                    </template>
                    <template #imageSize={record}>
                        {{(record.imageSize/(1024*1024*1024)).toFixed(2)}}
                    </template>
                    <template #action={record}>
                        <a-select ref="selectRef" style="width: 100px" placeholder="其他操作"
                            :getPopupContainer="triggerNode => triggerNode.parentNode"
                            :dropdownMatchSelectWidth="false"
                            @select="(value,option)=>setOptions(record,value,option)"
                        >
                            <a-select-option :value="0">删除</a-select-option>
                            <a-select-option :value="1" v-if="record.maintenance == 0">设置维护模式</a-select-option>
                            <a-select-option :value="2" v-else>取消维护模式</a-select-option>
                            <a-select-option :value="3">设置引导设备</a-select-option>
                            <a-select-option :value="4" v-if="record.powerState?.toLowerCase() == 'power on'">关闭电源</a-select-option>
                            <a-select-option :value="5" v-if="record.powerState?.toLowerCase() == 'power on'">重启电源</a-select-option>
                            <a-select-option :value="6" v-else>开启电源</a-select-option>
                            <a-select-option :value="7">设置RAID配置</a-select-option>
                            <a-select-option :value="8" v-if="record.consoleEnabled == 0">开启console状态</a-select-option>
                            <a-select-option :value="9" v-else>关闭console状态</a-select-option>
                            <a-select-option :value="10">注入NMI</a-select-option>
                        </a-select>
                    </template>
                </a-table>
                <Add ref="imageDialog" :info="info" @getlist="getList" />
                <Raid ref="raidDialog" :info="raidinfo" @getlist="getList" />
                <a-modal title="设置维护模式" v-model:visible="isShowMain" ok-text="提交" cancel-text="取消" @ok="saveMain" @cancel="cancelMain" centered>
                    <a-form :model="mainform" ref="mainForm" :rules="mainRules" :label-col="{span:3}">
                        <a-form-item label="说明" name="reason">
                            <a-input v-model:value="mainform.reason" placeholder="请输入维护reason"></a-input>
                        </a-form-item>
                    </a-form>
                </a-modal>
                <a-modal title="设置引导设备" v-model:visible="isShowDriver" ok-text="提交" cancel-text="取消" @ok="saveDriver" @cancel="cancelDriver" centered>
                    <a-form :model="driverform" ref="driverForm" :rules="driverRules" :label-col="{span:4}">
                        <a-form-item label="重启驱动" name="bootDevice">
                            <a-select v-model:value="driverform.bootDevice" placeholder="请选择设备" :getPopupContainer="triggerNode => triggerNode.parentNode">
                                <a-select-option label="pxe" value="pxe">pxe</a-select-option>
                                <a-select-option label="dist" value="dist">dist</a-select-option>
                                <a-select-option label="cdrom" value="cdrom">cdrom</a-select-option>
                                <a-select-option label="bios" value="bios">bios</a-select-option>
                                <a-select-option label="safe" value="safe">safe</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="是否永久" name="persistent">
                            <a-switch v-model:checked="driverform.persistent" checked-children="是" un-checked-children="否" />
                        </a-form-item>
                    </a-form>
                </a-modal>
                <a-modal title="设置console状态" v-model:visible="isShowConsole" ok-text="提交" cancel-text="取消" @ok="saveConsole" @cancel="cancelConsole" centered>
                    <a-form :model="consoleform" ref="consoleForm" :rules="consoleRules" :label-col="{span:3}">
                        <a-form-item label="target" name="target">
                            <a-input v-model:value="consoleform.target" placeholder="请输入"></a-input>
                        </a-form-item>
                    </a-form>
                </a-modal>
            </div>
        </div>
    </div>
    <Info ref="ironicInfoRef" :info="info" v-else />
</template>
<script lang='ts' setup>
import Info from "./info.vue";
import Add from "./add.vue";
import Raid from "./raid.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { getBaremetalnode, deleteBaremetalnode } from "@/api/backend/baremetal";
import { setMaintenance, deleteMaintenance, setBootDevice, changeNodePower, setRaid, changeNodeConsoleStatus, injectNMI } from "@/api/backend/baremetal";
import { useRoute } from "vue-router";
import { message } from "ant-design-vue";
import router from "@/router";
const { proxy } = getCurrentInstance();
const route = useRoute()
const imageDialog = ref(null);
const raidDialog = ref(null);
const ironicInfoRef = ref(null);
const loading = ref(false);
const imagelist = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false,
    isInfo:false
})
const raidinfo = reactive({
    isShow:false,
})
const isShowMain = ref(false);
const isShowDriver = ref(false);
const isShowConsole = ref(false);
const mainForm = ref();
const driverForm = ref();
const raidForm = ref();
const consoleForm = ref();
const mainform = reactive({
    id:'',
    reason:'',
})
const driverform = reactive({
    id:'',
    bootDevice:'',
    persistent:false
})
const consoleform = reactive({
    id:'',
    target:''
})

const mainRules = {
    reason:[{required:true, message:'请输入维护的reason',trigger:'change'}]
};
const driverRules = {
    bootDevice:[{required:true, message:'请输入设备',trigger:'change'}],
    persistent:[{required:true, type:'boolean', message:'请输入永久性',trigger:'change'}],
};
const consoleRules = {
    target:[{required:true, message:'请输入',trigger:'change'}]
};
const cancelMain = () => {
    isShowMain.value = false;
    mainform.id = '';
    mainform.reason = '';
}
const cancelDriver = () => {
    isShowDriver.value = false;
    driverform.id = '';
    driverform.bootDevice = '';
    driverform.persistent = false;
}
const cancelConsole = () => {
    isShowConsole.value = false;
    consoleform.id = '';
    consoleform.target = '';
}
const saveMain = () => {
    mainForm.value.validate().then(async ()=>{
        // setMaintenance(record.id).then((res)=>{
        //         if(res.code == 0 && res.data !== false){message.success('设置维护状态成功')}
        //         else{message.error('设置维护状态失败')}
        //     })
        let res = await setMaintenance(mainform);
        if(res.code == 0 && res.data !== false){
            message.success('设置维护模式成功');
            getList()
            cancelMain();
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '设置维护模式失败' : res.msg);
        }
    })
}
const saveDriver = () => {
    let driverform1 = {...driverform};
    if(driverform1.persistent)
        driverform1.persistent = 1;
    else
        driverform1.persistent = 0;
    driverForm.value.validate().then(async ()=>{
        let res = await setBootDevice(driverform1);
        if(res.code == 0 && res.data !== false){
            message.success('设置引导设备成功');
            getList()
            cancelDriver();
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '设置引导设备失败' : res.msg);
        }
    })
}

const saveConsole = () => {
    consoleForm.value.validate().then(async ()=>{
        let res = await changeNodeConsoleStatus(consoleform);
        if(res.code == 0 && res.data !== false){
            message.success('设置console状态成功');
            getList()
            cancelConsole();
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '设置console状态失败' : res.msg);
        }
    })
}
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
     {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '节点名称', dataIndex: 'name', slots: { customRender: 'name' }, key: 'id',align:'left'},
    {title: '虚机', dataIndex: 'instanceUuid', slots: { customRender: 'instanceUuid' }, key: 'id',align:'left'},
    {title: '电源状态', dataIndex: 'powerState', slots: { customRender: 'powerState' }, key: 'id',align:'center'},
    {title: '配置状态', dataIndex: 'provisionState', key: 'id',align:'center'},
    {title: '端口', dataIndex: 'properties', slots: { customRender: 'properties' }, key: 'id',align:'center'},
    {title: '驱动', dataIndex: 'driver', key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
const getList = async () => {
    searchform.cloudId = localStorage.getItem('cloudId');
    imagelist.value = await proxy.$getList(loading, getBaremetalnode, searchform, pagination, getList )
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    ruleName: ""
  })
  getList();
}
const setOptions = (record,value,option) => {
    // if(value == 0){
    //     proxy.$handleDel([record.id],deleteBaremetalnode,getList)
    // }
    switch (value) {
        case 0:
            proxy.$handleDel([record.id],deleteBaremetalnode,getList);
            break;
        case 1:
            isShowMain.value = true;
            mainform.id = record.id;
            mainform.reason = record.reason;
            break;
        case 2:
            deleteMaintenance(record.id).then((res)=>{
                if(res.code == 0 && res.data !== false){message.success('取消维护模式成功');getList()}
                else{message.error((!res.msg || res.msg == 'success') ? '取消维护模式失败' : res.msg);}
            })
            break;
        case 3:
            isShowDriver.value = true;
            let record1 = {...record};
            driverform.id = record1.id;
            driverform.bootDevice = record1.bootDevice;
            if(record1.persistent == 1)
                record1.persistent = true;
            else
                record1.persistent = false;
            driverform.persistent = record1.persistent;
            break;
        case 4:
            changeNodePower({id:record.id,target:'power off'}).then((res)=>{
                if(res.code == 0 && res.data !== false){message.success('关闭电源成功');getList()}
                else{message.error((!res.msg || res.msg == 'success') ? '关闭电源失败' : res.msg);}
            })
            break;
        case 5:
            changeNodePower({id:record.id,target:'rebooting'}).then((res)=>{
                if(res.code == 0 && res.data !== false){message.success('重启电源成功');getList()}
                else{message.error((!res.msg || res.msg == 'success') ? '重启电源失败' : res.msg);}
            })
            break;
        case 6:
            changeNodePower({id:record.id,target:'power on'}).then((res)=>{
                if(res.code == 0 && res.data !== false){message.success('打开电源成功');getList()}
                else{message.error((!res.msg || res.msg == 'success') ? '打开电源失败' : res.msg);}
            })
            break;
        case 7:
            raidinfo.isShow = true;
            proxy.$nextTick(()=>{
                raidDialog.value.setinfo(record);
                // Object.assign(raidDialog.value.raidform, record)
            })
           
            break;
        case 8:
            // isShowConsole.value = true;
            // consoleform.id = record.id;
            // consoleform.target = record.target;
            changeNodeConsoleStatus({id:record.id,target:'start'}).then((res)=>{
                if(res.code == 0 && res.data !== false){message.success('开启console状态成功');getList()}
                else{message.error((!res.msg || res.msg == 'success') ? '开启console状态失败' : res.msg);}
            })
            break;
        case 9:
            changeNodeConsoleStatus({id:record.id,target:'stop'}).then((res)=>{
                if(res.code == 0 && res.data !== false){message.success('关闭console状态成功');getList()}
                else{message.error((!res.msg || res.msg == 'success') ? '关闭console状态失败' : res.msg);}
            })
            break;
        case 10:
            injectNMI(record.id).then((res)=>{
                if(res.code == 0 && res.data !== false){message.success('注入NMI成功');getList()}
                else{message.error((!res.msg || res.msg == 'success') ? '注入NMI失败' : res.msg);}
            })
            break;
        default:
            break;
    }
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
    info.isInfo = false;
}

const handleEdit = (record) => {
 info.isAdd = false;
    info.isShow = true;
    info.isInfo = false;
    let record1 = {...record}
    if(record1.allServer == 1)
    record1.allServer = true;
    else
    record1.allServer = false;
    record1.serverIds = undefined;
    if(record1.servers && record1.servers.length > 0){
        record1.serverIds = [];
        record1.servers.forEach((item,index)=>{
            record1.serverIds.push(item.id)
        })
    }
    // console.log('编辑',imageDialog.value.cloudform)

    Object.assign(imageDialog.value.projectform, record1)
}
const handleView = (record) => {
    info.isInfo = true;
    info.isShow = false;
    proxy.$nextTick(()=>{
        let record1 = {...record}
        record1.driverInfo = JSON.parse(record1.driverInfo)
        record1.properties = JSON.parse(record1.properties)
        record1.extra = JSON.parse(record1.extra)
        record1.instanceInfo = JSON.parse(record1.instanceInfo)
        record1.bootInterface = record1.bootInterface ? JSON.parse(record1.bootInterface):{};
        Object.assign(ironicInfoRef.value.ironicform, record1)
    })
}

onMounted(() => {
    getList()
    if(route.path == '/admin/devops/openstack')
    proxy.$mitt.on('getlist',getList)
    })
</script>
<style lang='scss' scoped>
</style>