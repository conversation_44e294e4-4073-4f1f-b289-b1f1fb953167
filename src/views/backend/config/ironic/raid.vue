<template>
    <div class=''>
        <a-modal title="设置RAID配置" v-model:visible="info.isShow" :body-style="{height:'495px'}" @cancel="cancelRaid" centered>
            <template #footer>
                <a-button @click="cancelRaid">取消</a-button>
                <a-button type="primary" @click="saveRaid" :disabled="raidSubmit.raidConfig.logical_disks?.length<=0">提交</a-button>
            </template>
            <a-form :model="raidform" ref="raidForm" :rules="raidRules" :label-col="{span:4}">
                <a-form-item label="大小(GB)">
                    <a-input-number v-model:value.trim="raidform.size_gb" :min="1"  :step="1" :precision="0" style="width:100%" placeholder="请输入正整数" allow-clear></a-input-number>
                </a-form-item>
                <a-form-item label="RAID等级">
                    <a-rate v-model:value="raidform.raid_level" :count="10" :tooltips="[1,2,3,4,5,6,7,8,9,10]" />
                    <span class="ant-rate-text">{{ raidform.raid_level }}</span>
                    <!-- <a-input v-model:value="raidform.raid_level" placeholder="请输入RAID等级"></a-input> -->
                </a-form-item>
                <a-form-item label="Root Volume">
                    <a-switch v-model:checked="raidform.is_root_volume" checked-children="True" un-checked-children="False" />
                </a-form-item>
                <a-form-item>
                    <a-button :disabled="!(raidform.size_gb && raidform.raid_level)" @click="addLogicalDisks">添加逻辑磁盘</a-button>
                </a-form-item>
            </a-form>
            <a-table :data-source="raidSubmit?.raidConfig?.logical_disks" :pagination="false" :scroll="{y:168}" rowKey="size_gb">
                <a-table-column key="size_gb" title="大小(GB)" data-index="size_gb" align="center" />
                <a-table-column key="raid_level" title="RAID等级" data-index="raid_level" align="center" />
                <a-table-column key="is_root_volume" title="Root Volume" data-index="is_root_volume" align="center" >
                    <template v-slot="{record,index}">
                        {{record.is_root_volume ? 'True' : 'False'}}
                    </template>
                </a-table-column>
                <a-table-column key="action" title="操作" align="center">
                    <template v-slot="{record,index}">
                        <span>
                            <a-button type="primary" @click="()=>{raidSubmit.raidConfig.logical_disks.splice(index,1)}" class="button_D">
                            {{ $t("m.del") }}
                            </a-button>
                        </span>
                    </template>
                </a-table-column>
            </a-table>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { setRaid } from '@/api/backend/baremetal';
import { message } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false
            }
        }
    }
});
const raidForm = ref();
const key = ref(0);
const raidform = reactive({
    size_gb:1,
    raid_level:1,
    is_root_volume:false
})
const raidSubmit = reactive({
    id:'',
    raidConfig:{logical_disks:[]}
})
// const raidRules = {
//     raidConfig:[{required:true, message:'请输入RAID配置',trigger:'change'}]
// };
const addLogicalDisks = () => {
    let raidform1 = {...raidform};
    // raidform1.is_root_volume = raidform1.is_root_volume ? 1 : 0;
    raidSubmit.raidConfig.logical_disks.push(raidform1)
}
const setinfo = (record) => {
    raidSubmit.id = record.id;
    if(record.raidConfig)
    raidSubmit.raidConfig = record.raidConfig;
}
const cancelRaid = () => {
    props.info.isShow = false;
    Object.assign(raidform,{
        size_gb:1,
        raid_level:1,
        is_root_volume:false
    })
    raidSubmit.id = '';
    raidSubmit.raidConfig.logical_disks = [];
    // Object.assign(raidSubmit,{
    //     id:'',
    //     raidConfig:{
    //         logical_disks:[]
    //     }
    // })
}
const saveRaid = async () => {
    // raidForm.value.validate().then(async ()=>{
        // raidSubmit.raidConfig = JSON.stringify(raidSubmit.raidConfig)
        let res = await setRaid(raidSubmit);
        if(res.code == 0 && res.data !== false){
            message.success('设置RAID配置成功');
            getList()
            cancelRaid();
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '设置RAID配置失败' : res.msg);
        }
    // })
}
onMounted(() => {})
defineExpose({setinfo})
</script>
<style lang='scss' scoped>
</style>