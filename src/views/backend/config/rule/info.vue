<template>
    <div class=''>
        <a-modal 
        :title="info.isInfo ? '查看' : (info.isAdd ? '新增':'修改')"
        v-model:visible="info.isShow"
        @cancel="cancel"
        :maskClosable="info.isInfo"
        centered
        :getContainer="modalBindNode"
        >
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="projectform" ref="projectForm" :rules="rules" :labelCol="{span:7}">
                <!-- <a-form-item label="域名" name="domainId">
                    <a-select v-model:value="projectform.domainId" placeholder="请选择" :disabled="info.isInfo || !info.isAdd" allow-clear> 
                        <a-select-option v-for="(item,index) in options3" :key="index" :value="item.id+''" >{{item.domainName}}</a-select-option>
                    </a-select>
                </a-form-item> -->
                <a-form-item label="规则名称" name="ruleName">
                    <a-input v-model:value.trim="projectform.ruleName" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="最大CPU使用率" name="maxCpu">
                     <a-input-number v-model:value.trim="projectform.maxCpu" :min="1" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入" :disabled="info.isInfo"></a-input-number>
                </a-form-item>
                 <a-form-item label="最大内存使用率" name="maxMemory">
                     <a-input-number v-model:value.trim="projectform.maxMemory" :min="1" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入" :disabled="info.isInfo"></a-input-number>
                </a-form-item>
                <a-form-item label="最小cpu使用率阈值" name="minCpu">
                     <a-input-number v-model:value.trim="projectform.minCpu" :min="-1" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入" :disabled="info.isInfo"></a-input-number>
                </a-form-item>
                <a-form-item label="最小内存使用率阈值" name="minMemory">
                     <a-input-number v-model:value.trim="projectform.minMemory" :min="-1" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入" :disabled="info.isInfo"></a-input-number>
                </a-form-item>
                <!-- <a-form-item label="是否全部使用" name="allServer">
                    <a-switch v-model:checked="projectform.allServer" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                </a-form-item> -->
                 <a-form-item label="虚机" name="serverIds" >
                    <a-select v-model:value="projectform.serverIds" placeholder="请选择" mode="multiple" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear> 
                        <template v-for="(item,index) in serverlist" :key="index">
                            <a-select-option v-if="item.deleted == 1" :value="item.id" >{{item.serverName}}</a-select-option>
                        </template>
                    </a-select>
                </a-form-item>
                
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {saveRuleserve,updateRuleserve} from "@/api/backend/devops/config"
import {selectServerList} from "@/api/backend/devops/server"
import { selectDictList } from '@/api/backend/systems/dictionary';
import router from '@/router';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})
const projectForm = ref();
const serverlist = ref([]);
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    projectId:router.currentRoute.value.query.projectId,
    ruleName:'',
    serverIds:undefined,
    serverIdList:undefined,
    maxCpu:'',
    maxMemory:'',
    minCpu:-1,
    minMemory:-1,
    description:'',
    source:2
    // allServer:false
}
const projectform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    projectId:router.currentRoute.value.query.projectId,
    ruleName:'',
    serverIds:undefined,
    serverIdList:undefined,
    maxCpu:'',
    maxMemory:'',
    minCpu:-1,
    minMemory:-1,
    source:2
    // allServer:false,
})
const rules = {
    ruleName:[{required:true, message:'请输入',trigger:'change'}],
    maxCpu:[{required:true, type:'number', pattern:/^(?:[1-9]?\d|100)$/, message:'仅支持输入1-100之间的整数',trigger:'change'}],
    maxMemory:[{required:true, type:'number', pattern:/^(?:[1-9]?\d|100)$/, message:'仅支持输入1-100之间的整数',trigger:'change'}],
    minCpu:[{required:true, type:'number', pattern:/^([1-9]?\d|100|-1|0)$/, message:'仅支持输入-1~100之间的整数',trigger:'change'}],
    minMemory:[{required:true, type:'number', pattern:/^([1-9]?\d|100|-1|0)$/, message:'仅支持输入-1~100之间的整数',trigger:'change'}],
}
const options3 = ref([])
const getServerList = async () => {
    let res = await selectServerList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId});
    if(res){
        if(res.code == 0){
            // Object.assign(serverlist,res.data);
            serverlist.value = res.data;
        }
    }
}
const handleSave = () => {
    let projectform1 = {...projectform}
    proxy.$handleSave(projectForm.value, saveRuleserve, saveRuleserve, props,projectform1, ()=>{cancel();emit('getlist');},null,()=>{
        // if(projectform1.allServer == true){
        //   projectform1.allServer = 1;
        // }
       
        // else{
        //   projectform1.allServer = 0;
          projectform1.serverIdList=[];
        if(projectform1.serverIds && projectform1.serverIds.length > 0)
        projectform1.serverIds.forEach((id,index)=>{
            projectform1.serverIdList.push(id)
        })
        // }
      
    })
}
const cancel = () => {

   props.info.isShow = false
    props.info.isInfo = false
    // isInfo.value = false
    projectForm.value.resetFields()
    Object.assign(projectform,defaultform)
}
onMounted(() => {})
defineExpose({projectform,getServerList})
</script>
<style lang='scss' scoped>
</style>