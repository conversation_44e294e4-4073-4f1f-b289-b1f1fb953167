<template>
    <div class="cloudContent">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                    <a-form-item label="规则名称">
                        <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.ruleName" allowClear />
                    </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd">新增 </a-button>
                    <a-button @click="$handleDel(selectRowIds,deleteRuleserve,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table :row-selection="rowSelection" :columns="columns" row-key="id" :data-source="imagelist" :pagination="pagination" @change="changeTable" :scroll="{x:true}">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #imageSize={record}>
                        {{(record.imageSize/(1024*1024*1024)).toFixed(2)}}
                    </template>
                    <template #action={record}>
                         <a-button class="button_V" @click="handleView(record)">查看</a-button>
                        <a-button type="link" class="button_E"   @click="handleEdit(record)">编辑</a-button>
                        <a-button type="link" class="button_D" @click="$handleDel([record.id],deleteRuleserve,getList)">删除</a-button>
                    </template>
                </a-table>
                <Info ref="imageDialog" :info="info" @getlist="getList" />
            </div>
        </div>
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { getRuleservelist, deleteRuleserve } from "@/api/backend/devops/config";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { useRoute } from "vue-router";
import router from "@/router";
const { proxy } = getCurrentInstance();
const route = useRoute()
const imageDialog = ref(null);
const loading = ref(false);
const imagelist = ref([]);
let selectRowIds: string[] = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false
})
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    ruleName:'',
    pageIndex:1,
    pageSize:10,
    source:2
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '规则名称', dataIndex: 'ruleName', key: 'id',align:'left'},
    {title: '最大CPU使用率', dataIndex: 'maxCpu', key: 'id',align:'center'},
    {title: '最大内存使用率', dataIndex: 'maxMemory', key: 'id',align:'center'},
    {title: '最小cpu使用率阈值', dataIndex: 'minCpu', key: 'id',align:'center'},
    {title: '最小内存使用率阈值', dataIndex: 'minMemory', key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
const getList = async () => {
    searchform.cloudId = localStorage.getItem('cloudId');
    imagelist.value = await proxy.$getList(loading, getRuleservelist, searchform, pagination, getList )
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    ruleName: "",
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    source:2
  })
//   getList();
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
    info.isInfo = false;
    nextTick(()=>{
        imageDialog.value.getServerList();
    })
}

const handleEdit = (record) => {
 info.isAdd = false;
    info.isShow = true;
    info.isInfo = false;
    let record1 = {...record}
    // if(record1.allServer == 1)
    // record1.allServer = true;
    // else
    // record1.allServer = false;
    record1.serverIds = undefined;
    if(record1.serverEntityList && record1.serverEntityList.length > 0){
        record1.serverIds = [];
        record1.serverEntityList.forEach((item,index)=>{
            record1.serverIds.push(item.id)
        })
    }
    // console.log('编辑',imageDialog.value.cloudform)
    nextTick(()=>{
        Object.assign(imageDialog.value.projectform, record1)
        imageDialog.value.getServerList();
    })
}
const handleView = (record) => {
    info.isInfo = true;
    info.isShow = true;
    let record1 = {...record}
    if(record1.allServer == 1)
    record1.allServer = true;
    else
    record1.allServer = false;
    record1.serverIds = undefined;
    if(record1.serverEntityList && record1.serverEntityList.length > 0){
        record1.serverIds = [];
        record1.serverEntityList.forEach((item,index)=>{
            record1.serverIds.push(item.id)
        })
    }
    nextTick(()=>{
        Object.assign(imageDialog.value.projectform, record1)
        imageDialog.value.getServerList();
    })
}

onMounted(() => {
    getList()
    if(route.path == '/admin/devops/openstack')
        proxy.$mitt.on('getlist',getList)
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
// .buttonPadding,.innerPadding{min-width: 929px;}
</style>