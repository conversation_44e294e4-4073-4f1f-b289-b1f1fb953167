<template>
  <div class="layoutContent" style="height:calc(100vh - 96px);overflow-y:scroll;margin: 16px 16px;">
    <div v-if="user_store.userInfo.roleIdList&&user_store.userInfo.roleIdList.includes(1)">
      <!-- <a-spin wrapperClassName="index_swiper" :spinning="isPlus"> -->
        <div class="index_swiper">
            
            <a-empty
            :image="false"
              :image-style="{
                height: '125px',
                width: '125px',
                background:'url('+empty+') no-repeat center'
              }"
              v-if="cloudlist===null || (cloudlist && cloudlist.length <= 0)"
            >
              <template #description>
                <a-button type="link" @click="handleAdd">创建第一个云平台</a-button>
              </template>
            </a-empty>
            <a-list :grid="{ gutter: 2, column: 3 }" :data-source="cloudlist" v-else :loading="isPlus">
                <!-- <template #header="{ index }" v-if="index>=9">
                    <router-link to="/admin/cloud">更多 ></router-link>
                </template> -->
                <template v-if="cloudlistRam.length > 9" #loadMore>
                  <div :style="{ textAlign: 'center', marginTop: '12px', height: '32px', lineHeight: '32px' }">
                    <!-- <a-spin v-if="loadingMore" /> -->
                    <a-button type="link" v-if="cloudlistRam.length > 9 && cloudlist.length > 9" @click="loadMore(false)">收起<UpCircleOutlined /></a-button>
                    <a-button type="link" v-else-if="cloudlistRam.length > 9 && cloudlist.length == 9" @click="loadMore(true)">展开<DownCircleOutlined /></a-button>
                  </div>
                </template>
                <template #renderItem="{ item,index }">
                <a-list-item>
                  <a-card bordered hoverable :headStyle="{backgroundColor:'#fafafa'}" @click="()=>goCloud(item,index)">
                    <template #title>
                      <i v-if="item.cloudType=='OPENSTACK'" class="iconfont icon-icon_openstack"></i>
                      <img v-else :src="icolist[item.cloudType]" width="16" height="16" alt="">&nbsp;&nbsp;{{item.cloudName}}
                    </template>
                    <template #extra>
                      <span style="color:#666">{{item.cloudType == 'vm' ? 'VMware' : item.cloudType}}</span>
                    </template>
                    <a-card-grid style="width: 100%; text-align: center;padding:10px" :hoverable="false">
                        <a-descriptions
                        size="small" 
                        :column="{ xxl: 3, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }" 
                        v-if="item.cloudType == 'OPENSTACK'" 
                        :label-style="{whiteSpace:'nowrap'}" 
                        :content-style="{whiteSpace:'nowrap',display:'block',overflow:'hidden',textOverflow:'ellipsis',textAlign: 'left'}"
                        >
                          <a-descriptions-item :label="item.distSize >= 1024 ? '存储(TB)':'存储(GB)'">{{item.distSize >= 1024 ? Number(item.userdDistSize/1024).toFixed(2) : Number(item.userdDistSize).toFixed(1)}}/{{item.distSize >= 1024 ? Number(item.distSize/1024).toFixed(2) : Number(item.distSize).toFixed(1)}}</a-descriptions-item>
                          <a-descriptions-item :label="item.memorySize >= 1024 ? '内存(TB)':'内存(GB)'">{{item.memorySize >= 1024 ? Number(item.userdMemorySize/1024).toFixed(2) : Number(item.userdMemorySize).toFixed(1)}}/{{item.memorySize >= 1024 ? Number(item.memorySize/1024).toFixed(2) : Number(item.memorySize).toFixed(1)}}</a-descriptions-item>
                          <a-descriptions-item label="CPU(个)">{{item.userdVcpu}}/{{item.vcpu}}</a-descriptions-item>
                          <a-descriptions-item label="总数量(个)">{{item.serverNumber}}</a-descriptions-item>
                          <a-descriptions-item label="活跃(个)">{{item.activateServerNumber}}</a-descriptions-item>
                          <a-descriptions-item label="过期(个)">{{item.expireServerNumber}}</a-descriptions-item>
                          <a-descriptions-item label="即将过期(个)">{{item.upExpireServerNumber}}</a-descriptions-item>
                      </a-descriptions>
                      <a-descriptions size="small" :column="{ xxl: 3, xl: 2, lg: 2, md: 2, sm: 1, xs: 1 }" v-else-if="item.cloudType=='vm'" :label-style="{whiteSpace:'nowrap'}" :content-style="{whiteSpace:'nowrap',display:'block',overflow:'hidden',textOverflow:'ellipsis',textAlign: 'left'}">
                          <a-descriptions-item :label="item.distSize >= 1024 ? '存储(TB)':'存储(GB)'">{{item.distSize >= 1024 ? Number(item.userdDistSize/1024).toFixed(2) : Number(item.userdDistSize).toFixed(1)}}/{{item.distSize >= 1024 ? Number(item.distSize/1024).toFixed(2) : Number(item.distSize).toFixed(1)}}</a-descriptions-item>
                          <a-descriptions-item :label="item.memorySize >= 1024 ? '内存(TB)':'内存(GB)'">{{item.memorySize >= 1024 ? Number(item.userdMemorySize/1024).toFixed(2) : Number(item.userdMemorySize).toFixed(1)}}/{{item.memorySize >= 1024 ? Number(item.memorySize/1024).toFixed(2) : Number(item.memorySize).toFixed(1)}}</a-descriptions-item>
                          <a-descriptions-item label="CPU(GHz)">{{item.userdVcpu}}/{{Number(item.vcpu).toFixed()}}</a-descriptions-item>
                          <a-descriptions-item label="总数量(个)">{{item.serverNumber}}</a-descriptions-item>
                          <a-descriptions-item label="活跃(个)">{{item.activateServerNumber}}</a-descriptions-item>
                          <a-descriptions-item label="过期(个)">{{item.expireServerNumber}}</a-descriptions-item>
                          <a-descriptions-item label="即将过期(个)">{{item.upExpireServerNumber}}</a-descriptions-item>
                      </a-descriptions>
                    </a-card-grid>
                  </a-card>
                </a-list-item>
                </template>
            </a-list>
        </div>
      <!-- </a-spin> -->
      <div class="group-2">
        <!-- <div class="chart-area"> -->
          <a-spin wrapperClassName="chart-area" :spinning="chartLoading[1]">
            <div class="charts" id="chart_vm" ref="chart_vm"></div>
          </a-spin>
        <!-- </div> -->
        <!-- <div class="chart-area"> -->
          <a-spin wrapperClassName="chart-area" :spinning="chartLoading[2]">
            <div class="charts" id="chart_vmOS" ref="chart_vmOS"></div>
          </a-spin>
        <!-- </div> -->
        <a-spin wrapperClassName="group-2-desc" :spinning="msgLoading">
<div style="width:100%;height:362px">
          <a-card v-if="!msgLoading" :bordered="false">
              <a-card-grid @click="()=>{
              router.push('/admin/tickets');menuStore().setMenuId('tickets');$mitt.emit('setLeftMenu',true)
              }"><p></p><p>工作总量</p><span class="number">{{ticketInfo.all?ticketInfo.all:'0'}}</span></a-card-grid>
            <a-card-grid @click="()=>{router.push('/admin/tickets?state=1');menuStore().setMenuId('tickets');$mitt.emit('setLeftMenu',true)}"><p></p><p>待办工单</p><span class="number">{{ticketInfo.task?ticketInfo.task:'0'}}</span></a-card-grid>
            <a-card-grid @click="()=>{router.push('/admin/tickets?state=2&time=1');menuStore().setMenuId('tickets');$mitt.emit('setLeftMenu',true)}"><p></p><p>当日办理工单</p><span class="number">{{ticketInfo.done?ticketInfo.done:'0'}}</span></a-card-grid>
            <a-card-grid @click="()=>{router.push({path:'/admin/systems/MessageCenter',query:{noticeType:1}})}"><p></p><p>预警数量</p><span class="number">{{warningInfo.all?warningInfo.all:'0'}}</span></a-card-grid>
            <a-card-grid @click="()=>{router.push({path:'/admin/systems/MessageCenter',query:{noticeType:1,transactState:1}})}"><p></p><p>已处理预警</p><span class="number">{{warningInfo.done?warningInfo.done:'0'}}</span></a-card-grid>
            <a-card-grid @click="()=>{router.push({path:'/admin/systems/MessageCenter',query:{noticeType:1,transactState:0}})}"><p></p><p>未处理预警</p><span class="number">{{warningInfo.undone?warningInfo.undone:'0'}}</span></a-card-grid>
          </a-card>
</div>
        </a-spin>
      </div>
      <div class="group-3">
        <a-spin wrapperClassName="chart-area" :spinning="chartLoading[3]">
          <div class="charts" id="chart_HW" ref="chart_HW"></div>
        </a-spin>
        <a-spin wrapperClassName="chart-area" :spinning="chartLoading[4]">
          <div class="charts" id="chart_WO" ref="chart_WO"></div>
        </a-spin>
          <a-spin wrapperClassName="chart-area chart-area-spin" :spinning="rankLoading">
        <!-- <div class="chart-area chart-area-spin"> -->
          <a-page-header title="排行榜"></a-page-header>
            <a-tabs v-if="!rankLoading">
            <a-tab-pane key="1">
              <template #tab>
                <span>
                  <team-outlined />
                  人员
                </span>
              </template>
                <a-table size="small" :columns="eColumns" :data-source="eRankListLeft" row-key="index" :pagination="false" :rowClassName="()=>'item'" />
              <a-table size="small" v-if="eRankListRight.length >= 1" :columns="eColumns" :data-source="eRankListRight" row-key="index" :pagination="false" />
            </a-tab-pane>
            <a-tab-pane key="2" force-render>
              <template #tab>
                <span>
                  <home-outlined />
                  部门
                </span>
              </template>
              <a-table size="small" :columns="deptColumns" :data-source="deptRankListLeft" row-key="index" :pagination="false" :rowClassName="()=>'item'" />
              <a-table size="small" v-if="deptRankListRight.length >= 1" :columns="deptColumns" :data-source="deptRankListRight" row-key="index" :pagination="false" />
            </a-tab-pane>
          </a-tabs>
        <!-- </div> -->
          </a-spin>
      </div>
    </div>
  <Index v-else />
  <Add ref="cloudDialog" :info="info" @getlist="getAllDisplayData" :options="options" :userlist="userlist" />
  </div>
</template>
<script lang="ts" setup>
import SwiperCore, { Navigation, Pagination } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/scss';
  import 'swiper/scss/navigation';
  import 'swiper/scss/pagination';
import Index from "./index/index.vue";
import Add from "@/views/backend/cloud/add.vue";
import "@/assets/iconfont/iconfont.css"
import "@/assets/iconfont/iconfont.js"
import { getCurrentInstance, onMounted, reactive, ref } from "vue";
import * as echarts from "echarts";
import { userStore } from "@/store/user";
import { indexStore } from "@/store/index";
import { getHostPie, getOsPie, getRanklist, getTicketCount, getWarningCount, selectIndexCloudList, SelectResource, SelectTicket, SelectWarning,osLoad,resourceLoad,serverLoad } from "@/api/backend";
import router from '@/router';
import { selectNavByCloud } from '@/api/backend/systems/menu';
import { listToTreeCloud } from '@/utils/tool';
import { filterRouter } from '@/utils/route';
import { selectDictList } from '@/api/backend/systems/dictionary';
import { useRoute } from 'vue-router';
import vm from "@/assets/ico/vm.ico";
import empty from "@/assets/empty.png";
import { selectCloudInfo } from '@/api/backend/cloud';
import { queryWorker } from '@/api/backend/systems/user';
import emiter from '@/utils/Bus';
import { menuStore } from '@/store/menu';
import { checkLicence } from '@/api/backend/public';
import {Options} from "@/common/chartoption"
import { Modal } from 'ant-design-vue';
const {proxy} = getCurrentInstance()
const route = useRoute()
const user_store = userStore()
const index_store = indexStore()
SwiperCore.use([Navigation, Pagination])
const cloudlist = ref(undefined)
const cloudlistRam = ref([])
const warningInfo = ref({})
const warningNot = ref("")
const warningAaily = ref("")
const ticketInfo = reactive({})
const menuList = ref([])
const isPlus = ref(false)
const chartLoading = ref([false,false,false,false,false])
const msgLoading = ref(false)
const rankLoading = ref(false)
const showNum = ref(5)
const userlist = ref([])
const icolist = ref({
  vm,
});
const loadMore = (open) => {
  if(open){
    cloudlist.value = cloudlistRam.value;
  }else{
    cloudlist.value = cloudlistRam.value.slice(0,9);
  }
}
const onResize = (e) => {
  if(e.size > 1300)
  showNum.value = 5
  else if(e.size >= 705 && e.size <= 1300)
  showNum.value = 4
  else if(e.size >= 508 && e.size < 705)
  showNum.value = 3
  else
  showNum.value = 2
}
const getMenuList = async (cloudId) => {
  let res = await selectNavByCloud({cloudId})
  if(res.code == 0){
    let menulist = res.data
    res.data.forEach((item)=>{
      if("/admin/"+item.url == route.path){
        proxy.$mitt.emit('setopenKeys',[item.parentId.toString()])
      }
    })
    menuList.value = listToTreeCloud(menulist)
    user_store.set_cloudmenu(menuList.value)
  }
}
const first = (cloudId) => {
  getMenuList(cloudId).then(()=>{
    // router.replace('/admin/devops/index')
  })
}
const getCloudInfo = async (id) => {
  let res = await selectCloudInfo({id})
  if(res.code == 0 && res.data){
    localStorage.setItem('openPrometheus',res.data.openPrometheus)
  }
}
const goCloud = (item,index) => {
  if(item.cloudType == 'OPENSTACK'){
    menuStore().setShowLeftMenu(true)
    router.push({path:"/admin/devops/menu/cloud",query:{cloudId:item.cloudId,title:item.cloudName}})
    return;
    emiter.emit('allLoading',true)
    menuStore().setCloudIndex(index)
    proxy.$mitt.emit('SelectCloudList',{isAdd:undefined,key:'devops',isInit:true,cloudId:item.cloudId})
  }
}
const options = ref([])
const cloudDialog = ref()
const info = reactive({
    isShow:false
})
const handleAdd = () => {
    info.isShow = true;
    proxy.$nextTick(()=>{
      queryworker()
      getDictList()
    })
}
const getDictList = async () => {
  let res = await selectDictList({dictType:'CLOUD_TYPE'})
  if(res){
    if(res.code == 0){
      options.value = res.data
      cloudDialog.value.radioOptions = cloudDialog.value.radioOptions.filter((item)=>{
        return options.value.some((t,i)=>{
          return t.dictValue == item.value
        })
      })
    }
  }
}
const toolbox = {
    feature:{
      myTool: {
          show: true,
          title: '导出',
          icon: `M328 576h152V128h64v448h152L512 768 328 576z m568-64h-64v320H192V512h-64v384h768V512z`,
          onclick: async function  (e,b,c,d,f){
            let res;
            let fileName;
          if(e.option.title[0].text=="云平台虚机分布图"){
            res=await serverLoad()
            fileName = '云平台虚机分布表.xlsx';
          }
          if(e.option.title[0].text=="云平台虚机操作系统分布图"){
            res=await osLoad()
            fileName = '云平台虚机操作系统分布表.xlsx';
          }
          if(e.option.title[0].text=="云平台资源使用情况"){
            res=await resourceLoad()
            fileName = '云平台资源使用情况表.xlsx';
          }
          if(res){
            let urldata = res.data ? res.data : res;
            let url=window.URL.createObjectURL(urldata)
            let link = document.createElement("a");
            link.style.display='none'
            link.href=url;
            if(res.headers){
              if(res.headers['Content-disposition'])
                fileName = res.headers['Content-disposition'].split('=')[1];
            }
            link.setAttribute('download',fileName);
            document.body.appendChild(link)
            link.click();
          }
        }
      },
    }
  };
var myChartVm;
var myChartVmOS;
var myChartHW;
var myChartWO;
const chart_vm = ref()
const chart_vmOS = ref()
const chart_HW = ref()
const chart_WO = ref()
let option_vm = reactive(Options.index_cloudvm_P)
let option_vmOS = Options.index_cloudvmOS_P;
let option_HW=Options.index_cloudHW_P;
let option_WO = Options.index_cloudWO_STAT;
const init = () => {
  if (!chart_vm.value || !chart_vmOS.value || !chart_HW.value || !chart_WO.value) {
    return
  }
  echarts.dispose(chart_vm.value);
  echarts.dispose(chart_vmOS.value);
  echarts.dispose(chart_HW.value);
  echarts.dispose(chart_WO.value);
  myChartVm = echarts.init(chart_vm.value, null, {
            renderer: 'svg'
        });
  myChartVmOS = echarts.init(chart_vmOS.value);
  myChartHW = echarts.init(chart_HW.value);
  myChartWO = echarts.init(chart_WO.value);
  window.addEventListener('resize',function(){
    myChartVm.resize()
    myChartVmOS.resize()
    myChartHW.resize()
    myChartWO.resize()
  })
  chartLoading.value[1] = true;
  chartLoading.value[2] = true;
  chartLoading.value[3] = true;
  chartLoading.value[4] = true;
  // myChartVm.showLoading();
  // myChartVmOS.showLoading();
  // myChartHW.showLoading();
  // myChartWO.showLoading();
}
const getCloudList = async () => {
  isPlus.value = true;
    let res = await selectIndexCloudList()
    isPlus.value = false;
    if(res.code == 0){
      if(res.data)
        cloudlistRam.value = res.data;
        menuStore().setCloudMenus(res.data);
        if(res.data&&res.data.length > 9){
          cloudlist.value = cloudlistRam.value.slice(0,9);
        }else{
          cloudlist.value = cloudlistRam.value;
        }
        if(!localStorage.getItem('cloudId')){
          let cloudId = res.data[res.data.length-1]?.cloudId
          let openPrometheus = res.data[res.data.length-1]?.openPrometheus
          localStorage.setItem('cloudId',cloudId)
          localStorage.setItem('openPrometheus',openPrometheus)
        }
        
    }
}
const gethostPie = async () => {
  let res = await getHostPie()
  if(res.code == 0){
    option_vm.series[0].data = res.data;
    myChartVm.setOption(option_vm);
    // myChartVm.hideLoading();
    chartLoading.value[1] = false;
  }
}
const getosPie = async () => {
  let res = await getOsPie()
  if(res.code == 0){
    option_vmOS.series[0].data = res.data;
    myChartVmOS.setOption(option_vmOS);
    // myChartVmOS.hideLoading();
    chartLoading.value[2] = false;
  }
}
const getticketCount = async () => {
    let res = await getTicketCount()
    if(res.code == 0){
        Object.assign(ticketInfo,res.data);
    }
}
const getwarningCount = async () => {
  msgLoading.value = true;
    let res = await getWarningCount({noticeType:"1"})
    msgLoading.value = false;
    if(res.code == 0){
        warningInfo.value=res.data
       
    }
}

const selectResource = async () => {
    let res = await SelectResource();
    if(res.code == 0){
      option_HW.xAxis.data = res.data.barList;
      option_HW.series[0].data = res.data.distList;
      option_HW.series[1].data = res.data.cpuList;
      option_HW.series[2].data = res.data.memoryList;
      myChartHW.setOption(option_HW);
      // myChartHW.hideLoading();
      chartLoading.value[3] = false;
    }
}
const selectTicket = async () => {
  let res = await SelectTicket({userID:user_store.userId});
  if(res.code == 0){
    option_WO.xAxis.data = res.data.lineList;
    option_WO.series[0].data = res.data.ticketList;
    option_WO.series[1].data = res.data.tasklist;
    option_WO.series[2].data = res.data.outTimeList;
    myChartWO.setOption(option_WO);
    // myChartWO.hideLoading();
    chartLoading.value[4] = false;
  }
}
const eColumns = [
  {title:'排名',dataIndex:'index',key:'index',align:'center',width:40,ellipsis:true},
  {title:'姓名',dataIndex:'name',key:'index',align:'center',ellipsis:true},
  {title:'虚机数量',dataIndex:'server',key:'index',align:'center',ellipsis:true},
]
const deptColumns = [
  {title:'排名',dataIndex:'index',key:'index',align:'center',width:40,ellipsis:true},
  {title:'部门',dataIndex:'name',key:'index',align:'center',ellipsis:true},
  {title:'虚机数量',dataIndex:'server',key:'index',align:'center',ellipsis:true},
]
const eRankListLeft = ref([])
const eRankListRight = ref([])
const deptRankListLeft = ref([])
const deptRankListRight = ref([])
const getRankList = async () => {
  rankLoading.value = true;
  let res = await getRanklist();
  rankLoading.value = false;
  if(res.code == 0){
    res.data.userlist.map((item,index)=>{
      item.index = index+1;
      item.name = Object.keys(item)[0];
      item.server = Object.values(item)[0];
    })
    eRankListLeft.value = res.data.userlist.slice(0,5);
    eRankListRight.value = res.data.userlist.slice(5,10);
    res.data.deplist.map((item,index)=>{
      item.index = index+1;
      item.name = Object.keys(item)[0];
      item.server = Object.values(item)[0];
    })
    deptRankListLeft.value = res.data.deplist.slice(0,5);
    deptRankListRight.value = res.data.deplist.slice(5,10);
  }
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
    // cloudDialog.value.userlist.value = res.data.list;
}
const getAllDisplayData = () => {
  getRankList()
  if(user_store.userInfo.roleIdList&&user_store.userInfo.roleIdList.includes(1)){
    getCloudList();
    getticketCount();
    getwarningCount();
    setTimeout(()=>{
      init();
      gethostPie();
    getosPie();
    selectResource();
    selectTicket();
    },0)
  }
};
const showCheckModal = () => {
  Modal.info({
    title: () => '您的Licence已过期',
    maskClosable: true,
    centered:true
  })
}
const checklicense = async () => {
  let res = await checkLicence();
  if(res.code != 0){
    showCheckModal()
  }
};
onMounted(()=>{
  emiter.on("handleAdd",handleAdd)
    checklicense()
  getAllDisplayData()
})
</script>
<style lang='scss'>
.ant-empty{
  background-color: #fff;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}
.group-2,.group-3{
  overflow: hidden;
  display: flex;
  margin-top: 10px;
  justify-content: space-between;
  .ant-spin-container{height: 100%;}
}
.group-2-desc{
  width: 32.6%;
  height: 100%;
  background-color: #fff;
  color: #fff;
  .ant-card{
  color: #fff;

}
  .number{
    font-weight: bold;
    font-size: 30px;
  }
  .ant-card-grid{
  width: 30%; text-align: center;margin: 9px 1.6%;height: 163px;cursor: pointer;padding: 24px 0;
  p{text-overflow: ellipsis;overflow: hidden;white-space: nowrap;}
  &:nth-child(3n+1){
    background-color: #73C0DE;
  }
  &:nth-child(3n){
    background-color: #EE6666;
  }
  &:nth-child(3n+2){
    background-color: #91CC75;
  }
}
}
.chart-area{
  background-color: #fff;
  padding: 20px;
  width: 32.6%;
  height: 360px;
  cursor: pointer;
  transition: all .2s cubic-bezier(.645,.045,.355,1);
	-webkit-transition: all .2s cubic-bezier(.645,.045,.355,1);
}
.chart-area:hover{
  box-shadow: 0 2px 8px #00000026;
}
.chart-area-spin{padding: 0;}
.charts{width:100%;height: 100%;}

::v-deep .swiper{position: static;}
.swiper-container{
  padding: 0 27px;
  overflow: hidden;
}
::v-deep .swiper-button-prev{
  left: 0px;
  height: 100%;
  top: 0;
  background-color: #fff;
  margin-top: 0;
}
::v-deep .swiper-button-next{
  right: 0px;
  height: 100%;
  top: 0;
  background-color: #fff;
  margin-top: 0;
}
.ant-card-hoverable{margin: 0 10px;}
.plus-card{border:none;.ant-card-body{padding:0}.ant-btn{width: 100%;font-size:30px;}}
.plus-card1{.ant-btn{height:359px;}}
.swiper-button-next.swiper-button-disabled{pointer-events:auto}
.group-3{
  .ant-page-header{padding: 14px 20px 0 20px;.ant-page-header-heading-title{font-size: 18px;}}
.ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane{display: flex;justify-content: space-evenly;}
.ant-table-thead > tr > th{border: none;background-color: #fff;font-size: 12px;color: #999;}
.ant-table-tbody > tr{background-image:  radial-gradient(at right, #F5F7FB, #fff);}
.ant-table-tbody > tr > td{border: none;}
.ant-table-tbody > tr > td:first-child{font-weight: 1000;color: #8D8FA8;}
.ant-table-tbody > tr > td:last-child{font-weight: bold;}
.item:first-child:hover{td:first-child{background: url(@/assets/index/gold.png) no-repeat bottom;background-size:70% 100%;}}
.item:nth-child(2):hover{td:first-child{background: url(@/assets/index/silver.png) no-repeat bottom;background-size:70% 100%;}}
.item:nth-child(3):hover{td:first-child{background: url(@/assets/index/copper.png) no-repeat bottom;background-size:70% 100%;}}
.item:first-child{background-image: radial-gradient(at right, #FCF0B9, #fff) ;vertical-align:top;td:first-child{background: url(@/assets/index/gold.png) no-repeat bottom;background-size:70% 100%;color: #fff;padding-top: 2px !important;}}
.item:nth-child(2){background-image: radial-gradient(at right, #DAE2E8, #fff);vertical-align:top;td:first-child{background: url(@/assets/index/silver.png) no-repeat bottom;background-size:70% 100%;color: #fff;padding-top: 2px !important;}}
.item:nth-child(3){background-image: radial-gradient(at right, #F7EDDD, #fff);vertical-align:top;td:first-child{background: url(@/assets/index/copper.png) no-repeat bottom;background-size:70% 100%;color: #fff;padding-top: 2px !important;}}
}
.index_swiper{position: relative;min-height: 215px;padding: 20px 10px;background-color:#fff;.ant-form-item{margin-bottom: 4px;}
.ant-list-header{padding-top: 0;}
.ant-list-split .ant-list-header{border: none;text-align: right;}
.ant-avatar{
        width: auto;
    height: auto;
    line-height: 32px;
    border-radius: 0;
}
.ant-card-meta-avatar{
    float: none;
    padding-right:0
}
.ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-label{
    padding: 0;
}}
.ant-descriptions-item-container{
  align-items: baseline;
}
span.ant-descriptions-item-label {
    font-size: 12px;
}
.host-empty{padding-left: 10px;display: flex;img{flex: 1;}}
.empty-right{margin-left: 40px;flex: 6;}
.index_swiper .ant-spin-container{min-height: 185px;margin: 0;overflow-x: hidden;}
</style>