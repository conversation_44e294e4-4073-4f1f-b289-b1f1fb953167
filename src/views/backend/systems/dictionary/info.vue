<template>
    <div class=''>
      <a-modal centered v-model:visible="info.isShow" :title="info.isAdd ? addText : editText" :maskClosable="false" style="min-width: 700px" @cancel="cancel" :getContainer="modalBindNode">
        <template #footer>
            <a-button @click="cancel">取消</a-button>
            <a-button type="primary" style="margin: 0 10px" @click="submit()" v-if="isShowBtn('sys:sysdictdata:save')">提交</a-button>
        </template>
        <a-form :model="noticeform" ref="noticeform" :rules="rules1">
          <a-form-item name="dictValue" label="数据项" :labelCol="{ span: 3, offset: 0 }" :wrapperCol="{ span: 19, offset: 0 }">
            <a-input v-model:value="noticeform.dictValue"></a-input >
          </a-form-item >
          <a-form-item name="dictLabel" label="数据描述" :labelCol="{ span: 3, offset: 0 }" :wrapperCol="{ span: 19, offset: 0 }">
            <a-input v-model:value="noticeform.dictLabel"></a-input>
          </a-form-item >
          <a-form-item name="dictType" label="字典类型" :labelCol="{ span: 3, offset: 0 }" :wrapperCol="{ span: 19, offset: 0 }">
            <a-select
                ref="select"
                v-model:value="noticeform.dictType"
                :options="dicttypelist"
                @focus="focus"
                @change="handleChange"
                disabled
            >
            </a-select>
          </a-form-item >
          <a-form-item name="remark" label="备注" :labelCol="{ span: 3, offset: 0 }" :wrapperCol="{ span: 19, offset: 0 }">
            <a-textarea v-model:value="noticeform.remark"></a-textarea>
          </a-form-item >
          <a-form-item name="status" label="状态" :labelCol="{ span: 3, offset: 0 }" :wrapperCol="{ span: 19, offset: 0 }">
              <a-select v-model:value="noticeform.status" :getPopupContainer="triggerNode => triggerNode.parentNode">
                <a-select-option :value="0">禁用</a-select-option >
                <a-select-option :value="1">正常</a-select-option >
            </a-select >
          </a-form-item >
        </a-form  >
      </a-modal >
    </div>
</template>
<script lang="ts">
import { message } from 'ant-design-vue';
import { isShowBtn } from "@/utils/tool";
import {saveDict,updateDict} from "@/api/backend/systems/dictionary";
const checkProducts = async (rule, value) => {

};
export default {
    name: '',
    props: {
      info:{
        type:Object,
        default(){
          return {
            isAdd:true,
            isShow:false
          }
        }
      },
      dicttypelist:[]
    },
    data() {
        return {
          modalBindNode:null,
          addText:'新增',
          editText:'修改',
          noticeform:{
            dictValue:'',
            dictLabel:'',
            dictType:''
          },
          value3:[],
          rules1:{
            dictValue: [{ required: true, message:'请填写数值' }],
            dictLabel: [{ required: true, message:'请填写数据释义' }],
          }
        };
    },
    computed: {},
    watch: {},
    methods: {
      isShowBtn,
      saveDict,
      updateDict,
      setinfo(data){
        this.noticeform = {...data};
      },
      cancel(){
        this.info.isShow = false;
        this.$refs.noticeform.resetFields()
        this.noticeform = {};
        this.value3=[]
      },
      submit(){
        this.$handleSave(this.$refs.noticeform, this.saveDict, this.updateDict, this, this.noticeform, ()=>{
          this.cancel()
          this.$emit('onSubmit3',this.noticeform.dictType)
        })
      }
    },
    components: {},
    filters: {},
    mounted(){
      this.modalBindNode = document.getElementsByClassName('uncloudRight')[0];
    }
};
</script>
<style lang='scss' scoped>
</style>