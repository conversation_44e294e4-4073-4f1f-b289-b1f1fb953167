<template>
    <div class='uncloudRight'>
        <a-page-header
          style="background-color:#fff"
          :title="dictType"
          @back="()=>{info.isDictionary = false}"
      />
      <div class="back-content">
        <a-row class="buttonGroup">
            <a-button type="primary" class="btnMargin" @click="toAdd" v-if="isShowBtn('sys:sysdictdata:save')">新增</a-button>
            <!-- <a-button type="primary" class="btnMargin" @click="()=>{info.isDictionary = false}" > 返回 </a-button> -->
        </a-row>
        <a-table :data-source="dictlist" row-key="id" :scroll="{ x: true }" :columns="columns" >
            <template #index={record,index}>
                {{index+1}}
            </template>
            <template #action="{ record }">
                <span>
                    <a-button @click="handleEdit(record)" class="button_E">修改</a-button  >
                    <a-button @click="$handleDel([record.id],deleteDictByid,getDictList)" class="button_D">删除</a-button  >
                </span>
            </template>
        </a-table >
      </div>
        
        <Info :info="info1" :dicttypelist="dicttypelist" @onSubmit3="getDictList" ref="dtRef" />
    </div>
</template>
<script lang="ts">
import { selectDictList,selectDicttypeList,deleteDictByid } from "@/api/backend/systems/dictionary";
import {isShowBtn} from "@/utils/tool";
import Info from "./info.vue";
import { message, Modal, notification } from 'ant-design-vue';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
export default {
    name: '',
    props: {
        info:{
        type:Object,
        default(){
          return {
            isAdd:true,
            isShow:false,
            isDictionary:true
          }
        }
        }
    },
    data() {
        return {
            info1:{isAdd:true,isShow:false},
            searchform:{},
            dictlist:[],
            dictType:'',
            dicttypelist:[],
            columns: [
                {
                    title: '序号',
                    slots: { customRender: 'index' },
                    key: 'index',
                },
                {
                    title: '数据项',
                    dataIndex: 'dictValue',
                    key: 'dictValue',
                },
                {
                    title: '数据描述',
                    dataIndex: 'dictLabel',
                    key: 'dictLabel',
                },
                
                {
                    title: '字段类型',
                    dataIndex: 'dictType',
                    key: 'dictType',
                },
                {
                    title: '操作',
                    key: 'action',
                    slots: { customRender: 'action' },
                   
                },
                
            ],
            loading:false,
        };
    },
    mounted(){
        console.log('data')
        // this.getDictList()
        this.SelectDicttypeList()
    },
    computed: {},
    watch: {},
    methods: {
        isShowBtn,deleteDictByid,
        getDictList(dictType){
            this.loading = true;
            if(dictType)
            this.searchform.dictType = dictType;
            else
            this.searchform.dictType = this.dictType;
            const searchObj = Object.assign({}, this.searchform);
            selectDictList(searchObj).then((res:any)=>{
                if(res.code == 0){
                    this.dictlist = res.data;
                    this.loading = false;
                }else{
                    this.loading = false;
                }
            }).catch((err) => {
                this.loading = false;
            });
        },
        SelectDicttypeList(){
            selectDicttypeList().then((res:any)=>{
                if(res.code == 0){
                    res.data.forEach(item => {
                        item.value = item.dictType;
                        item.label = item.dictType;
                    });
                    this.dicttypelist = res.data;
                }
            })
        },
        toAdd(){
            this.info1.isAdd = this.info1.isShow = true;
            this.$refs.dtRef.noticeform.dictType = this.dictType;
        },
        //修改
        handleEdit(data){
            this.info1.isAdd = false;
            this.info1.isShow = true;
            this.$nextTick(()=>{
                this.$refs.dtRef.setinfo(data);
            })
        },
    },
    components: {Info},
    filters: {}
};
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
.back-content{padding: 20px;overflow-y: initial;.buttonGroup{margin-bottom: 10px;}}
</style>