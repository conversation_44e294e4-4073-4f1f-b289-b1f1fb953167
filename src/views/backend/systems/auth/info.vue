<template>
    <div class=''>
        <a-modal 
        :title="info.isInfo ? '查看' : (info.isAdd ? '新增':'修改')"
        v-model:visible="info.isShow"
        :body-style="{height:'501px',overflowY:'auto'}"
        @cancel="cancel"
        :maskClosable="info.isInfo"
        centered
        :getContainer="modalBindNode"
        >
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="projectform" ref="projectForm" :rules="rules" :labelCol="{span:5}" >
                <!-- <a-form-item label="域名" name="domainId">
                    <a-select v-model:value="projectform.domainId" placeholder="请选择" :disabled="info.isInfo || !info.isAdd" allow-clear> 
                        <a-select-option v-for="(item,index) in options3" :key="index" :value="item.id+''" >{{item.domainName}}</a-select-option>
                    </a-select>
                </a-form-item> -->
              
                <a-form-item label="标识" name="code">
                    <a-input v-model:value.trim="projectform.code" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="名称" name="name">
                    <a-input v-model:value.trim="projectform.name" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                 <a-form-item label="类型" name="type">
                     <a-select v-model:value="projectform.type" :options="options1" placeholder="请选择"  :getPopupContainer="triggerNode => triggerNode.parentNode" :disabled="info.isInfo" allow-clear> 
                    </a-select>
                    <!-- <a-select v-model:value="projectform.type" placeholder="请选择"  :disabled="info.isInfo" allow-clear> 
                        <a-select-option v-for="(item,index) in authlist" :key="index" :value="item.createUserName"  >{{item.createUserName}}</a-select-option>
                    </a-select> -->
                </a-form-item>
                <a-form-item label="域名" name="domian" v-if="projectform.type=='AD'">
                     <a-input v-model:value.trim="projectform.domian"  placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <!-- <a-form-item label="驱动类" name="driverClass">
                     <a-input v-model:value.trim="projectform.driverClass" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item> -->
               <a-form-item label="服务器地址" name="serverAddress">
                     <a-input v-model:value.trim="projectform.serverAddress" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="端口" name="port">
                     <a-input-number v-model:value="projectform.port" :min="1"   :step="1" :precision="0" style="width:100%" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input-number>
                </a-form-item>
                <a-form-item label="Bind DN" name="bindDn">
                     <a-input v-model:value.trim="projectform.bindDn"  placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="密码" name="password">
                     <a-input-password v-model:value.trim="projectform.password"  placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input-password>
                </a-form-item>
                <a-form-item label="Base DN" name="baseDn">
                     <a-input v-model:value.trim="projectform.baseDn"  placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                   <a-form-item label="启用" name="state">
                    <a-switch v-model:checked="projectform.state" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                </a-form-item>
                 <a-form-item label="启用SSL" name="isSsl">
                    <a-switch v-model:checked="projectform.isSsl" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                </a-form-item>
               <a-space direction="vertical" align="end" class="connect">
                    <!-- <span style="margin-left:24px;margin-bottom:24px">本地链接连接</span> -->
                    <a-form-item label="账号字段" name="loginNameField" :label-col="{span:5}" :wrapper-col="{span:18}">
                        <a-input v-model:value="projectform.loginNameField" :disabled="info.isInfo" />
                    </a-form-item>
                    <a-form-item label="邮箱字段" name="emailField" :label-col="{span:5}" :wrapper-col="{span:18}">
                        <a-input v-model:value="projectform.emailField" :disabled="info.isInfo" />
                    </a-form-item>
                    <a-form-item label="电话字段" name="mobileField" :label-col="{span:5}" :wrapper-col="{span:18}">
                        <a-input v-model:value="projectform.mobileField" :disabled="info.isInfo" />
                    </a-form-item>
                    <a-form-item label="姓名字段" name="nameField" :label-col="{span:5}" :wrapper-col="{span:18}">
                        <a-input v-model:value="projectform.nameField" :disabled="info.isInfo" />
                    </a-form-item>
                </a-space>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {saveLdap,updateLdap ,getLdapList} from "@/api/backend/systems/auth"
import { selectDictList } from '@/api/backend/systems/dictionary';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})

const projectForm = ref();

 const options1 = ref([{
      value: 'AD',
      label: 'AD',
    }, {
      value: 'OpenLDAP',
      label: 'OpenLDAP',
    }
    // , {
    //   value: 'SunOne',
    //   label: 'SunOne',
     
    // }
    ]);
const defaultform = {
    // cloudId:localStorage.getItem('cloudId'),
    state:false,
    code:'',
    name:'',
    type:undefined,
    domian:'',
    // driverClass:'',
    isSsl:false,
    serverAddress:'',
    port:389,
    // userName:'',
    bindDn:'',
    password:'',
    baseDn:'',
    loginNameField:'uid',
    emailField:'mail',
    mobileField:'mobile',
    nameField:'cn',
}
const projectform = reactive({
    // cloudId:localStorage.getItem('cloudId'),
    state:false,
    code:'',
    name:'',
    type:undefined,
    domian:'',
    // driverClass:'',
    isSsl:false,
    serverAddress:'',
    port:389,
    // userName:'',
    bindDn:'',
    password:'',
    baseDn:'',
    loginNameField:'uid',
    emailField:'mail',
    mobileField:'mobile',
    nameField:'cn',
   
})
const rules = {
    code:[{required:true, message:'请输入',trigger:'change'}],
    name:[{required:true, message:'请输入',trigger:'change'}],
    type:[{required:true, message:'请选择',trigger:'change'}],
    domian:[{required:true, message:'请输入',trigger:'change'}],
    serverAddress:[{required:true, message:'请输入',trigger:'change'}],
    port:[{required:true,type:'number',message:'请输入',trigger:'change'}],
    bindDn:[{required:true, message:'请输入',trigger:'change'}],
    baseDn:[{required:true, message:'请输入',trigger:'change'}],
    password:[{required:true, message:'请输入',trigger:'change'}],
}

// const authlist = ref([]);
// const getServerList = async () => {
//     let res = await getLdapList()
//     if(res){
//         if(res.code == 0){
//             // Object.assign(serverlist,res.data);
//             authlist.value = res.data;
//         }
//     }
// }
const handleSave = () => {
    // cancel();
    let projectform1 = {...projectform}
    proxy.$handleSave(projectForm.value, saveLdap, updateLdap, props,projectform1, ()=>{cancel();emit('getlist');},null,()=>{
        if(projectform1.state == true){
          projectform1.state = 1;
        }else{
          projectform1.state = 0;
        }

        if(projectform1.isSsl == true){
          projectform1.isSsl = 1;
        }else{
          projectform1.isSsl = 0;
         }
      
    })
}
const cancel = () => {

   props.info.isShow = false
    props.info.isInfo = false
    // isInfo.value = false
    projectForm.value.resetFields()
    Object.assign(projectform,defaultform)
}
onMounted(() => {})
 defineExpose({projectform})
</script>
<style lang='scss' scoped>
.connect{background-color:#fafafa;border:1px solid #f0f2f5;width:100%;margin-bottom:24px;padding-top:10px;
:deep(.ant-space-item){width:100%}}
</style>