<template>
<div>
  <a-modal centered :title="info.isAdd ? '新增任务' : '修改任务'" v-model:visible="info.isShow" @cancel="cancel" :maskClosable="false" :getContainer="modalBindNode" style="min-width: 800px">
    <template #footer>
            <a-button @click="reset">重置</a-button>
        <a-button type="primary" @click="onSubmit" v-if="isShowBtn('sys:schedule:save')">提交</a-button>
        </template>
    <a-form :model="beanform" ref="beanformRef" :rules="rule4">
      <a-form-item label="bean名称" name="beanName" :label-col="{ span: 3, offset: 2 }" :wrapper-col="{ span: 14 }">
        <a-input v-model:value="beanform.beanName" placeholder="spring bean名称,如:testTask" allow-clear></a-input>
      </a-form-item>
      <a-form-item label="参数" name="params" :label-col="{ span: 3, offset: 2 }" :wrapper-col="{ span: 14 }">
        <a-input v-model:value="beanform.params" placeholder="参数" allow-clear></a-input>
      </a-form-item>
      <a-form-item label="cron表达式" name="cronExpression" :label-col="{ span: 3, offset: 2 }" :wrapper-col="{ span: 14 }">
        <a-input v-model:value="beanform.cronExpression" placeholder="如:0 0 0/12 * * ?" @click="openModal" allow-clear readonly></a-input>
      </a-form-item>
      <a-form-item label="备注" name="remark" :label-col="{ span: 3, offset: 2 }" :wrapper-col="{ span: 14 }">
        <a-input v-model:value="beanform.remark" placeholder="备注" allow-clear></a-input>
      </a-form-item>
    </a-form>
  </a-modal>
  <!-- <CronModal ref="cronDialog" :cronprops="cronProps" @setExpression="(e)=>{console.log('e',e);beanform.cronExpression = e}" /> -->
    <JCronModal ref="cronDialog" :data="beanform.cronExpression" @okk="handleOK"></JCronModal>
  </div>
</template>
<script setup lang="ts">
// import CronModal from "@/components/cron/cronmodal.vue";
import JCronModal from '@/components/cron/JCronModal.vue'
import { saveSchedule, updateSchedule } from "@/api/backend/systems/schedule";
import { message } from "ant-design-vue";
import { isShowBtn } from "@/utils/tool";
import { reactive, ref, getCurrentInstance, nextTick, onMounted } from "vue";
import { scheduleStore } from '@/store/schedule'
import { storeToRefs } from "pinia";
const schedule_store = scheduleStore()
const { proxy }: any = getCurrentInstance();

const props = defineProps({
  info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: false
      };
    }
  }
});

const beanformRef: any = ref(null);
const cronDialog = ref()

const beanform: any = reactive({
  beanName: "",
  cronExpression: "",
  jobId: 0,
  params: "",
  remark: "",
  status: 0
});

const defaultform: any = {
  beanName: "",
  cronExpression: "",
  jobId: 0,
  params: "",
  remark: "",
  status: 0
};
const resetform = { ...defaultform };
const cronProps = reactive({
  isShow:false
})
const openModal = () => {
    nextTick(()=>{
        cronDialog.value.show()
    })
}
const handleOK = (val) => {
  console.log("val",val,typeof val)
    if(typeof val == 'string'){
        beanform.cronExpression = val;
        // emit('change', val)
    }
}
const setValue = (val: any, formVal: any) => {
  formVal.beanName = val.beanName;
  formVal.cronExpression = val.cronExpression;
  formVal.jobId = val.jobId;
  formVal.params = val.params;
  formVal.remark = val.remark;
  formVal.status = val.status;
};
const checkBean = async (rule: any, value: any) => {
  if (value.length > 60) {
    return Promise.reject("bean名称长度能超过60个汉字或字母");
  } else if (value.trim().length == 0) {
    return Promise.reject("bean名称不能为空");
  } else {
    return Promise.resolve();
  }
};
const handleCron = () => {
  cronProps.isShow = true;
  proxy.$nextTick(()=>{
    console.log("scheduleform.corn",scheduleform.corn)
    cronDialog.value.setExpression(scheduleform.corn);
  })
}
const checkCron = async (rule: any, value: any) => {
  if (value.length > 60) {
    return Promise.reject("目录名称长度能超过60个汉字或字母");
  } else if (value.trim().length == 0) {
    return Promise.reject("cron表达式不能为空");
  } else {
    return Promise.resolve();
  }
};
const rule4 = reactive({
  beanName: [{ required: true, validator: checkBean, trigger: "change" }],
  cronExpression: [{ required: true, validator: checkCron, trigger: "change" }]
});

isShowBtn;
const QuerySchedulePage = schedule_store.QuerySchedulePage;
  
// 定义派发事件
const emit = defineEmits(['getlist'])

const cancel = () => {
  setValue(resetform, beanform);
};
const reset = () => {
  if (props.info.isAdd) {
    // 添加时候的重置！
    setValue(resetform, beanform);
  } else {
    // 修改时候的重置！
    setValue(defaultform, beanform);
  }
};
const setInfo = (val: any) => {
  console.log("我执行了", val);

  setValue(val, defaultform);
  setValue(JSON.parse(JSON.stringify(val)), beanform);
};
const onSubmit = () => {
  proxy.$handleSave(beanformRef.value, saveSchedule, updateSchedule, props, beanform, ()=>{
    emit('getlist');
    cancel()
  })
};
onMounted(()=>{})
defineExpose({
  setInfo
});
</script>
<style lang="scss" scoped></style>