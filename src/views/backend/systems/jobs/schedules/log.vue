<template>
<div>
  <a-modal centered v-model:visible="log.islog" @cancel="cancel" width="100% !important" wrap-class-name="full-modal" :footer="null">
    <a-pagination
      @show-size-change="setlimit"
      @change="set_logpage"
      show-size-changer
      show-quick-jumper
      v-model:current="logpage"
      v-model:page-size="loglimit"
      :page-size-options="['10', '30', '50']"
      :total="logtotal"
      :show-total="total => '共有' + logtotal + '条日志'"
      style="margin:20px;"
    >
    </a-pagination>
    <a-table :scroll="{ x: 1642,y:560 }" :data-source="loglist" row-key="logId" :pagination="false" :loading="loading">
      <a-table-column title="日志ID" data-index="logId" align="center" />
      <a-table-column title="bean名称" data-index="beanName" align="center" />
      <a-table-column title="参数" data-index="params" align="center" />
      <a-table-column title="状态" align="center">
        <template v-slot="scope">
          <span>
            <a-tag v-if="scope.text.status == 0" color="success">正常</a-tag>
            <a-tag v-if="scope.text.status == 1" color="error">停用</a-tag>
          </span>
        </template>
      </a-table-column>
      <a-table-column key="error" title="报错" align="center" :ellipsis="true">
        <template v-slot="scope">
          <span :title="scope.text.error">
            {{ scope.text.error ? scope.text.error : "null" }}
          </span>
        </template>
      </a-table-column>
      <a-table-column key="times" title="操作次数" data-index="times" align="center" />
      <a-table-column key="createTime" title="创建时间" data-index="createTime" align="center" />
      
    </a-table>
  </a-modal>
</div>
</template>
<script setup lang="ts">
import { getCurrentInstance, computed, onMounted, ref } from "vue";
import { scheduleStore } from '@/store/schedule'
import { storeToRefs } from "pinia";
const schedule_store = scheduleStore()
const { proxy }: any = getCurrentInstance();
const props = defineProps({
  log: {
    type: Object,
    default() {
      return {
        islog: true
      };
    }
  }
});

const {loglist,logpage,loglimit,logtotal,loading} = storeToRefs(schedule_store);
const QueryScheduleLog = schedule_store.QueryScheduleLog;
const set_logpage = schedule_store.set_logpage;
const set_loglimit = schedule_store.set_loglimit;
const cancel = () => {
  props.log.islog = false;
};
const setlimit = (data1: any, data2: any) => {
  set_loglimit({ data1, data2 });
};
onMounted(()=>{
  
})
// QueryScheduleLog();
defineExpose({QueryScheduleLog})
</script>
<style lang="scss" scoped>
.full-modal {
  .ant-modal-body {
    flex: 1;
  }
}
</style>
