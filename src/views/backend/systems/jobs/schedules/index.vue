<template>
  <div class="uncloudRight">
    <div class="buttonPadding">
      <a-form layout="inline" :model="searchform" class="searchform">
        <a-form-item label="bean">
          <a-input placeholder="请输入bean名称" @pressEnter="onSearch" v-model:value="searchform.beanName" allow-clear />
        </a-form-item>
      </a-form>
      <MoreSearch @search="onSearch" @reset="reset" />
    </div>
    <div class="innerPadding">
      <a-row class="buttonGroup">
        <a-button type="primary" class="btnMargin" @click="toAdd"> 新增 </a-button>
        <a-button class="btnMargin" v-if="isShowBtn('sys:schedule:delete')" @click="del(selectedRowKeys)" :disabled="selectedRowKeys.length <= 0">
          批量删除
        </a-button>
        <a-button class="btnMargin" v-if="isShowBtn('sys:schedule:pause')" @click="pause(selectedRowKeys)" :disabled="selectedRowKeys.length <= 0">
          批量暂停
        </a-button>
        <a-button class="btnMargin" v-if="isShowBtn('sys:schedule:resume')" @click="resume(selectedRowKeys)" :disabled="selectedRowKeys.length <= 0">
          批量恢复
        </a-button>
        <a-button class="btnMargin" v-if="isShowBtn('sys:schedule:run')" @click="run(selectedRowKeys)" :disabled="selectedRowKeys.length <= 0">
          批量立即执行
        </a-button>
        <a-button class="btnMargin" v-if="isShowBtn('sys:schedule:log')" @click="toLog">
          日志列表
        </a-button>
      </a-row>
      <a-table :row-selection="rowSelection" :data-source="schedulelist" row-key="jobId" :scroll="{ x: true }" :pagination="pagination" @change="changeTable">
        <a-table-column key="id" title="序号" align="center" :width="60">
          <template #default={record,index}>
              {{index+1+(pagination.pageSize * (pagination.current-1))}}
          </template>
        </a-table-column>
        <!-- <a-table-column key="jobId" title="ID" data-index="jobId" align="center" /> -->
        <a-table-column key="beanName" title="bean名称" data-index="beanName" />
        <a-table-column key="params" title="参数" data-index="params" :width="60" />
        <a-table-column key="cronExpression" title="cron表达式" data-index="cronExpression" align="center" />
        <a-table-column key="remark" title="备注" data-index="remark" />
        <a-table-column key="status" title="状态" :width="60" align="center">
          <template v-slot="scope">
            <span>
              <a-tag v-if="scope.text.status == 0" color="success">正常</a-tag>
              <a-tag v-if="scope.text.status == 1" color="error">停用</a-tag>
            </span>
          </template>
        </a-table-column>
        <a-table-column key="action" title="操作" :width="290" align="center">
          <template v-slot="scope">
            <span>
              <a-button @click="pause([scope.record.jobId])" v-if="isShowBtn('sys:schedule:pause')" class="button_E">
                暂停
              </a-button>
              <a-button @click="resume([scope.record.jobId])" v-if="isShowBtn('sys:schedule:resume')" class="button_E">
                恢复
              </a-button>
              <a-button @click="run([scope.text.jobId])" v-if="isShowBtn('sys:schedule:run')" class="button_E">
                立即执行
              </a-button>
              <a-button @click="edit(scope.record)" v-if="isShowBtn('sys:schedule:update')" class="button_E">
                修改
              </a-button>
              <a-button @click="$handleDel([scope.record.jobId],deleteSchedule,getList)" v-if="isShowBtn('sys:schedule:delete')" class="button_D">
                删除
              </a-button>
            </span>
          </template>
        </a-table-column>
      </a-table>
    </div>
    <Info :info="info" ref="schdialog" @getlist="getList" />
    <Log :log="log" ref="logDialog" />
  </div>
</template>
<script lang="ts" setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Log from "./log.vue";
import { isShowBtn } from "@/utils/tool";
import { deleteSchedule, runSchedule, pauseSchedule, resumeSchedule } from "@/api/backend/systems/schedule";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { createVNode, nextTick, reactive, ref, computed, getCurrentInstance, onMounted } from "vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { message, Modal, notification } from "ant-design-vue";
import { scheduleStore } from '@/store/schedule'
import { storeToRefs } from "pinia";
const schedule_store = scheduleStore()
const { proxy }: any = getCurrentInstance();
const logDialog = ref()
const info = reactive({
  isAdd: true,
  isShow: false
});
const log = reactive({
  islog: false
});
const defaultform = ref({
  page: 1,
  limit: 10,
  beanName:''
});
const searchform = ref({
  page: 1,
  limit: 10,
  beanName:''
});
const selectedRowKeys = ref([]);
// let {page, limit, beanName, schedulelist, total} = storeToRefs(schedule_store)
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectedRowKeys.value,
    onChange: (selectedRowKey: any, selectedRows: any) => {
     console.log("selectedRowKey",selectedRowKey,selectedRows)
      selectedRowKeys.value = [...selectedRowKey];
    },
    getCheckboxProps: (record: any) => ({
      props: {
        disabled: record.name === "Disabled User", // Column configuration not to be checked
        name: record.name
      }
    })
  };
});

// const QuerySchedulePage = schedule_store.QuerySchedulePage;
const SearchSchedule = schedule_store.SearchSchedule;
const set_page = schedule_store.set_page;
const set_limit = schedule_store.set_limit;
const schedulelist = ref([])
const loading = ref(false);
const pagination: any = ref({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["5", "10", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
const getList: any = async () => {
  schedulelist.value = await proxy.$getList(loading, schedule_store.QuerySchedulePage, searchform.value, pagination.value, getList)
};
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.value.page = pagination.current;
  searchform.value.limit = pagination.pageSize;
  getList();
};
const onSearch = () => {
  searchform.value.page = 1;
  searchform.value.limit = pagination.value.pageSize
  getList()
  // QuerySchedulePage(searchform.value);
};
const reset = () => {
  searchform.value = {...defaultform}
  // getList();
};
const toLog = () => {
  log.islog = true;
  logDialog.value.QueryScheduleLog()
};
const toAdd = () => {
  info.isAdd = true;
  info.isShow = true;
};
const schdialog: any = ref(null);
const edit = (val: any) => {
  info.isAdd = false;
  info.isShow = true;
  schdialog.value.setInfo(val);
};

const run = (val: any) => {
  runSchedule(val)
    .then((res: any) => {
      if (res.code == 0 && res.data !== false) {
        message.success("任务执行成功");
        getList();
      } else {
        message.error((!res.msg || res.msg == 'success') ? '任务执行失败' : res.msg);
      }
    })
};
const resume = (val: any) => {
  resumeSchedule(val)
    .then((res: any) => {
      if (res.code == 0 && res.data !== false) {
        message.success("任务已恢复");
        getList();
      } else {
        message.error((!res.msg || res.msg == 'success') ? '任务恢复失败' : res.msg);
      }
    })
};
const pause = (val: any) => {
  pauseSchedule(val)
    .then((res: any) => {
      if (res.code == 0 && res.data !== false) {
        message.success("任务已暂停");
        getList();
      } else {
        message.error((!res.msg || res.msg == 'success') ? '任务暂停失败' : res.msg);
      }
    })
};
onMounted(()=>{
  getList();
  nextTick(()=>{
      handleWidth()
  })
})
</script>
<style lang="scss" scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
</style>
