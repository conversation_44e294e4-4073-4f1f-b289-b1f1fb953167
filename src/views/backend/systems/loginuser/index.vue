<template>
  <div class="uncloudRight">
    <div class="buttonPadding">
      <a-form layout="inline" :model="listQuery" class="searchform">
        <a-form-item label="用户名">
          <a-input placeholder="请输入" @pressEnter="getList" v-model:value="listQuery.userName" allowClear />
        </a-form-item>
      </a-form>
      <MoreSearch @search="getList" @reset="clickrest" />
    </div>
    <div class="innerPadding">
      <a-table :data-source="data" :pagination="pagination" row-key="userId" :scroll="{ x: true }" @change="changeTable">
        <a-table-column key="id" title="序号" align="center" >
            <template #default={record,index}>
                {{index+1+(pagination.pageSize * (pagination.current-1))}}
            </template>
        </a-table-column>
        <a-table-column key="loginName" title="登录账号" data-index="loginName" align="center" />
        <a-table-column key="userName" title="用户名" data-index="userName" :ellipsis="true" align="center" />
        <a-table-column key="depName" title="部门" data-index="depName" :ellipsis="true" align="center" />
        <a-table-column key="loginTime" title="登录时间" data-index="loginTime" :ellipsis="true" align="center" />
        <!-- <a-table-column key="createTime" title="创建时间" data-index="createTime" :ellipsis="true" align="center" /> -->
        <a-table-column key="action" title="操作" align="center">
          <template v-slot="{record}">
            <span>
              <a-button class="button_E" v-if="$isShowBtn('sys:user:force')" @click="forceLogout(record.userId)">强制登出</a-button>
            </span>
          </template>
        </a-table-column>
      </a-table>
    </div>
  </div>
</template>
<script lang="ts" setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import { ExclamationCircleOutlined, PlusOutlined } from "@ant-design/icons-vue";
import { isShowBtn, listToTree } from "@/utils/tool";
import { getUserId } from "@/utils/auth";
import * as Sha256 from "js-sha256"; 
import difference from "lodash/difference";
import { ref, reactive, onMounted, getCurrentInstance, createVNode, h, nextTick } from "vue";
import { message, Modal, notification } from "ant-design-vue";
import { userStore } from "@/store/user"
import { roleStore } from "@/store/role"
import { resetPwd,update,getLoginList, forceLoginOut } from "@/api/backend/systems/user";
import { randomString } from "@/utils/random";
import { buttonExpand, handleWidth } from "@/utils/moreform";
const user_store = userStore()
const role_store = roleStore()
// 语法糖写法
//获取全局方法
const { proxy } = getCurrentInstance() as any;
const props = defineProps({
  treeData: {
    type: Array,
    default() {
      return [];
    }
  }
});
//调用子组件方法
const title: any = ref("");
const showModal: any = ref(false); //添加、修改modal的flag
const listQuery: any = reactive({
  pageSize: 10,
  pageIndex: 1,
  userName: ""
});
//管理员信息对象
const roleList: any = ref([]); //角色列表
const data: any = ref([]); //列表数据
const loading: any = ref(false);
const selectRowIds: any = ref(""); //被选中都行
const isShow: any = ref(false); //是否查看
const pagination: any = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["5", "10", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
//重置
const clickrest = () => {
  listQuery.pageSize = 10;
  listQuery.pageIndex = "1";
  listQuery.userName = "";
  // listQuery.parentId = 1;
  // getList();
};
// // 查询
const getList: any = async () => {
  data.value = await proxy.$getList(loading, getLoginList, listQuery, pagination, getList)
};
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  listQuery.pageIndex = pagination.current;
  listQuery.pageSize = pagination.pageSize;
  getList();
};
const forceLogout = async (userId) => {
    let res = await forceLoginOut({userId})
    if(res.code == 0 && res.data !== false){
        message.success('强制登出成功');
        getList();
    }else if(res.code == 0){
        message.error((!res.msg || res.msg == 'success') ? '登出失败' : res.msg);
    }
}
onMounted(() => {
  getList();
  nextTick(()=>{
    handleWidth()
  })
});
defineExpose({})
</script>
<style lang="scss" scoped>
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
</style>