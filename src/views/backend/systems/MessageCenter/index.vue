<template>
    <div class='uncloudRight'>
        <div class="buttonPadding">
            <a-form layout="inline" ref="" :model="searchform" class="searchform">
              <a-form-item label="类型"> 
                <a-select v-model:value="searchform.noticeType" placeholder="请选择" style="width:120px" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                  <a-select-option :value="1" label="警告">警告</a-select-option>
                  <a-select-option :value="2" label="通知">通知</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="状态">
                <a-select v-model:value="searchform.transactState" placeholder="请选择" style="width:120px" :getPopupContainer="triggerNode => triggerNode.parentNode" allowClear>
                  <a-select-option :value="0" label="未处理">未处理</a-select-option>
                  <a-select-option :value="1" label="已处理">已处理</a-select-option>
                </a-select>
              </a-form-item>
            </a-form>
            <MoreSearch @search="onSearch" @reset="reset" />
       </div>
      
         <div class='innerPadding'>
           <a-row class="buttonGroup">
      <a-button type="primary" class="btnMargin" @click="restlist" >标记处理</a-button>
      <a-button type="primary" style="margin-left:10px" @click="restall" >
       标记全部
      </a-button>
    </a-row>
        <a-table :columns="columns" row-key="id" :data-source="ticketlist" :pagination="pagination" @change="changeTable" 
         :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        >    
        <template #index={record,index}>
                {{index+1+(pagination.pageSize * (pagination.current-1))}}
            </template>
          <template #noticeTitle={record}>
                <a  @click="toTitle(record.id)">{{record.noticeTitle}}</a>
             </template>
             <template #noticeMessage="{record}">
              <div style="height:24px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis" v-html="record.noticeMessage"></div>
             </template>
             <template #transactState={record}>
                <span v-if="record.transactState == 0">未处理</span>
                <span v-else-if="record.transactState == 1">已处理</span>
             </template>
              <template #noticeType={record}>
                <span v-if="record.noticeType == 1">警告</span>
                <span v-else-if="record.noticeType == 2">通知</span>
             </template>
           
        </a-table>
        </div>
        <!-- <Info ref="ticketDialog" :info="info" @getlist="getList" /> -->
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
// import Info from "./info.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { getCurrentInstance, nextTick, onMounted, reactive, ref, watch } from 'vue';
import { getMessageList,transactlist,transactAll } from "@/api/backend";
import router from "@/router";
import { indexStore } from "@/store";
import { message } from "ant-design-vue";
import { useRoute } from 'vue-router';
import { menuStore } from "@/store/menu";
import { handleWidth } from "@/utils/moreform";
const route = useRoute();
const { proxy } = getCurrentInstance();
const ticketDialog = ref(null);
const loading = ref(false);
const ticketlist = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false
})

   const selectedRowKeys=ref([])
      // Check here to configure the default column

const defaultform = {
    pageIndex:1,
    pageSize:10,
    noticeType:undefined,
    transactState:undefined,
}
const searchform = reactive({
    pageIndex:1,
    pageSize:10,
    noticeType:undefined,
    transactState:undefined,
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
  {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '标题', dataIndex: 'noticeTitle',slots:{ customRender: 'noticeTitle'}, key: 'id',align:'left'},
    {title: '内容', dataIndex: 'noticeMessage',slots:{ customRender: 'noticeMessage'}, key: 'id',align:'left',with:200,ellipsis:true},
    {title: '公告状态', dataIndex: 'transactState',slots:{ customRender: 'transactState'},key: 'id',align:'center'},
    {title: '公告类型', dataIndex: 'noticeType',slots:{ customRender: 'noticeType'} ,key: 'id',align:'center'},
    {title: '创建时间', dataIndex: 'createTime', key: 'id',align:'center'},
    // {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },align:'center' }
];
const getList = async () => {
    ticketlist.value = await proxy.$getList(loading, getMessageList, searchform, pagination, getList )
}

const onSearch = () => {
  searchform.pageIndex = 1;
  searchform.pageSize = pagination.pageSize
  getList()
 
 
};
const reset = () => {
  Object.assign(searchform,defaultform)
  // searchform.noticeType=
//  searchform.transactState = ""
  // getList();
};
const sign=ref()
 const onSelectChange = selectedrowKeys => {
      console.log('selectedRowKeys changed: ', selectedrowKeys);
      selectedRowKeys.value = selectedrowKeys;
    };
const toTitle=(id)=>{
    //  alert(id)
      router.push({path:'/admin/systems/MessageDetails',query:{ticketId:id}})
      // index_store.addTag({menuId: '消息详情', name: '消息详情', url: 'systems/MessageDetails',query:{ticketId:id}})
}

const restlist=()=>{
  if(selectedRowKeys.value.length>0){
    transactlist(selectedRowKeys.value).then((res)=>{
    if(res.code==0 && res.data !== false){
      message.success("操作成功")
      getList()
      proxy.$mitt.emit('RefreshNotice')
    // location.reload()
    }else if(res.code==0){
      message.error((!res.msg || res.msg == 'success') ? '操作失败' : res.msg)
    }
    selectedRowKeys.value = [];
  })
  }else{
    message.error("请选择未读数据")
  }
  
 
}
const restall=()=>{
  transactAll().then((res)=>{
    if(res.code==0 && res.data !== false){
          message.success("操作成功")
           getList()
           proxy.$mitt.emit('RefreshNotice')
          // location.reload()
      }else{
           message.error((!res.msg || res.msg == 'success') ? '操作失败' : res.msg)
         
      }
       selectedRowKeys.value = [];
  })
}

onMounted(async() => {
  nextTick(()=>{
      handleWidth()
  })
  console.log('showLeftMenu',menuStore().showLeftMenu)
  if(route.query.noticeType==1){
     if(route.query.noticeType==1&&route.query.transactState==0){
          searchform.noticeType = route.query.noticeType
          searchform.transactState = route.query.transactState
          
          ticketlist.value = await proxy.$getList(loading, getMessageList, searchform, pagination, getList )
            // if(item.value === val){
            // 	this.params.odsTypeName = item.label
            // })
          // console.log("处理",searchform.transactState)
          searchform.transactState="未处理"
          searchform.noticeType="警告"

     }else if(route.query.noticeType==1&&route.query.transactState==1){
          searchform.noticeType = route.query.noticeType
          searchform.transactState = route.query.transactState
          ticketlist.value = await proxy.$getList(loading, getMessageList, searchform, pagination, getList )
          searchform.transactState="已处理"
          searchform.noticeType="警告"
     }else{
         searchform.noticeType = route.query.noticeType
         ticketlist.value = await proxy.$getList(loading, getMessageList, searchform, pagination, getList )
         searchform.noticeType="警告"
     }
     
  }else{
         getList()
  }
  
 })
</script>
<style lang='scss' scoped>
</style>