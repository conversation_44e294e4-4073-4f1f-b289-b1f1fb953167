<template>
  <div>
    <a-form layout="inline" :model="listQuery" style="margin-bottom:20px;">
      <a-form-item label="用户名">
        <a-input placeholder="请输入" @pressEnter="getList(listQuery.parentId)" v-model:value="listQuery.userName" allowClear />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="getList(listQuery.parentId)">
          {{ $t("m.search") }}
        </a-button>
      </a-form-item>

      <a-form-item>
        <a-button @click="clickrest">
          {{ $t("m.reset") }}
        </a-button>
      </a-form-item>
      
    </a-form>
    <a-row>
      <a-button type="primary" @click="handleAdd(false)"> 新增 </a-button>
    </a-row><br/>
    <a-table :data-source="data" :pagination="pagination" row-key="userId" @change="changeTableList">
      <!-- <a-table-column key="id" title="序号" align="center" >
          <template #default={record,index}>
              {{index+1+(pagination.pageSize * (pagination.current-1))}}
          </template>
      </a-table-column> -->
      <a-table-column key="loginName" title="登陆账号" data-index="loginName" align="center" />
      <a-table-column key="userName" title="用户名" data-index="userName" :ellipsis="true" align="center" />
      <a-table-column key="depName" title="所属部门" data-index="depName" :ellipsis="true" align="center" />
      <a-table-column key="status" title="状态" data-index="status" align="center" >
        <template v-slot="scope">
          {{ scope.record.status == 1 ? '正常' : '禁用'}}
        </template>
      </a-table-column>
      <!-- <a-table-column key="createTime" title="创建时间" data-index="createTime" :ellipsis="true" align="center" :width="120" /> -->
      <a-table-column key="action" title="操作" align="center" :width="243">
        <template v-slot="scope">
          <span>
            <a-button @click="handleShow(scope.record)" v-if="isShowBtn('sys:user:info')" class="button_V">
              {{ $t("m.show") }}
            </a-button>
            <a-button @click="handleEdit(scope.record)" v-if="isShowBtn('sys:user:update')" class="button_E">
              {{ $t("m.modify") }}
            </a-button>
            <a-button @click="handleResetPwd(scope.record)" v-if="isShowBtn('sys:user:update')" class="button_E">
              重置密码
            </a-button>
            <a-button @click="$handleDel(scope.record.userId,user_store.DeleteUserById,getList)" v-if="isShowBtn('sys:user:delete')" class="button_D">
              {{ $t("m.del") }}
            </a-button>
          </span>
        </template>
      </a-table-column>
    </a-table>
    <!-- 添加、修改modal -->
    <a-modal centered :title="title" v-model:visible="showModal" :maskClosable="isShow" :centered="true" width="608.8px" :getContainer="modalBindNode">
      <template #footer>
          <a-button style="margin-left: 10px;" @click="handleCancle" v-if="!isShow">{{ $t("m.cancel") }}</a-button>
        <a-button type="primary" @click="handleSave" v-if="!isShow">{{ $t("m.save") }}</a-button>
          <a-button type="primary" style="margin-left: 10px;" @click="handleCancle" v-if="isShow">{{ $t("m.close") }}</a-button>
        </template>
      <a-form :labelCol="{ span: 5}" ref="addForm" :model="userInfo" :rules="rules">
        <a-form-item label="登录名" name="loginName">
          <a-input v-model:value="userInfo.loginName" :disabled="isShow || userInfo.userId != ''" allow-clear />
        </a-form-item>
        <a-form-item label="用户名" name="userName">
          <a-input v-model:value="userInfo.userName" :disabled="isShow"  allow-clear />
        </a-form-item>
        <a-form-item label="所属部门" name="depId">
          <a-tree-select
            v-model:value="userInfo.depId"
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :tree-data="treeData"
            placeholder="请选择"
            :replace-fields="replaceFields"
            tree-default-expand-all
            :disabled="isShow"
            allowClear>
          </a-tree-select>
        </a-form-item>
        <a-form-item label="是否为部门领导" name="isLeader">
          <a-switch v-model:checked="userInfo.isLeader" :disabled="isShow" checked-children="是" un-checked-children="否"></a-switch>
        </a-form-item>
        <a-form-item label="密码" name="pass" v-if="!isShow && !userInfo.userId">
          <a-input v-model:value="userInfo.pass" allow-clear />
        </a-form-item>
        <!-- <a-form-item label="确认密码" name="ispass" v-if="!isShow">
          <a-input v-model:value="userInfo.ispass" type="password" />
        </a-form-item> -->
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="userInfo.email" :disabled="isShow" allow-clear />
        </a-form-item>
        <a-form-item label="手机号" name="mobile">
          <a-input v-model:value="userInfo.mobile" :disabled="isShow" allow-clear />
        </a-form-item>
        <a-form-item label="角色" name="roleIdList">
          <a-checkbox-group v-model:value="userInfo.roleIdList" :disabled="isShow">
            <a-checkbox v-for="item in roleList" :key="item.roleId" :value="item.roleId" name="type">
              {{ item.roleName }}
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="状态">
          <!-- <a-radio-group v-model:value="userInfo.status">
            <a-radio value="1">正常</a-radio>
            <a-radio value="7">禁用</a-radio>
          </a-radio-group> -->
          <a-switch checked-children="正常" un-checked-children="禁用" v-model:checked="userInfo.status" :disabled="isShow" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { ExclamationCircleOutlined, PlusOutlined } from "@ant-design/icons-vue";
import { isShowBtn, listToTree } from "@/utils/tool";
import { getUserId } from "@/utils/auth";
import * as Sha256 from "js-sha256"; 
import difference from "lodash/difference";
import { ref, reactive, onMounted, getCurrentInstance, createVNode, h } from "vue";
import { message, Modal, notification } from "ant-design-vue";
import { getDepartList } from "@/api/backend/systems/depart"
import {update} from "@/api/backend/systems/user"
import { userStore } from "@/store/user"
import { roleStore } from "@/store/role"
import { resetPwd } from "@/api/backend/systems/user";
import { randomString } from "@/utils/random";
const user_store = userStore()
const role_store = roleStore()
// 语法糖写法
//获取全局方法
const { proxy } = getCurrentInstance() as any;
const props = defineProps({
  treeData: {
    type: Array,
    default() {
      return [];
    }
  }
});
const replaceFields = {
  title: 'depName',
  key: 'id',
  value: 'id'
};

//调用子组件方法
const addForm: any = ref();
const userInfo: any = ref({
  userId: "",
  loginName: "",
  userName: "",
  depId:undefined,
  isLeader:false,
  pass: randomString(6),
  // ispass: "",
  email: "",
  mobile: "",
  roleIdList: [2],
  status: true,
  loginId: "",
  sex: ""
});
// const treeData: any = ref([]);
const validatepass: any = async (rule: any, value: any) => {
  if (value === undefined || value === '') {
    return Promise.reject("请输入密码");
  }
    const pwdRegex = new RegExp("(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{6,16}$");
    if (value === "") {
      return Promise.reject("请输入密码");
    } else if (value.length < 6 || value.length > 18 || !pwdRegex.test(value)) {
      return Promise.reject("密码复杂度太低（密码中必须包含字母、数字、特殊字符,长度为6-18位），请重新设置密码！");
    } else {
      return Promise.resolve();
    }
};
const validateIspass: any = async (rule: any, value: any, callback: any) => {
  if (value === "" || value === undefined) {
    return Promise.reject("请输入确认密码");
  } else if (value !== userInfo.value.pass) {
    return Promise.reject("确认密码与密码不一致");
  } else {
    return Promise.resolve();
  }
};
const validateEmail: any = async (rule: any, value: any) => {
  const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  if (value === "") {
    return Promise.reject("请输入邮箱");
  } else if (!reg.test(value)) {
    return Promise.reject("邮箱格式不正确");
  } else {
    return Promise.resolve();
  }
};
const validateMobile: any = async (rule: any, value: any) => {
  const reg = /^1[3|4|5|7|8|9][0-9]\d{8}$/;
  if (value === "") {
    return Promise.reject("请输入手机号");
  } else if (!reg.test(value)) {
    return Promise.reject("手机号格式不正确");
  } else {
    return Promise.resolve();
  }
};
const validateut: any = async (rule: any, value: any) => {
  const req = /^[A-Za-z0-9]+$/;
  if (value === "" || value == undefined) {
    return Promise.reject("请输入工号");
  } else if (!req.test(value)) {
    return Promise.reject("工号格式不正确");
  } else if (value.length > 60) {
    return Promise.reject("工号限制60个字符");
  } else {
    return Promise.resolve();
  }
};
const validateLoginName: any = async (rule: any, value: any) => {
  if (value === "" || value == undefined) {
    return Promise.reject("请输入登录名");
  } else if (value.length > 60) {
    return Promise.reject("登录名限制60个字符");
  } else if (value.trim().length == 0) {
    return Promise.reject("登录名不能为空");
  } else {
    return Promise.resolve();
  }
};
const validateUserName: any = async (rule: any, value: any) => {
  if (value === "" || value == undefined) {
    return Promise.reject("请输入用户名");
  } else if (value.length > 60) {
    return Promise.reject("用户名限制60个字符");
  } else if (value.trim().length == 0) {
    return Promise.reject("用户名不能为空");
  } else {
    return Promise.resolve();
  }
};
const validateRoleIdList: any = async (rule: any, value: any) => {
  if (userInfo.value.roleIdList.length == 0) {
    return Promise.reject("请选择角色后提交");
  } else {
    return Promise.resolve();
  }
};
const validateDepartId = async (rule: any, value: any) => {
  if (!value) {
    return Promise.reject("请输入所属部门");
  } else {
    return Promise.resolve();
  }
};

const title: any = ref("");
const rules: any = reactive({
  sex: [{ required: true, message: "请输入性别", trigger: "blur" }],
  loginId: [{ required: true, validator: validateut, trigger: "blur" }],
  loginName: [{ required: true, validator: validateLoginName, trigger: "blur" }],
  userName: [{ required: true, validator: validateUserName, trigger: "blur" }],
  // isLeader: [{ required: true, type:'boolean', message:'请选择', trigger: "blur" }],
  pass: [{ required: !userInfo.userId, validator: validatepass, trigger: "change" }],
  // ispass: [{ validator: validateIspass, trigger: "change" }],
  email: [{ required: true, validator: validateEmail, trigger: "change" }],
  mobile: [{ required: true, validator: validateMobile, trigger: "change" }],
  roleIdList: [{ required: true, validator: validateRoleIdList, trigger: "blur" }],
  depId: [{ required: true, validator: validateDepartId, trigger: "change" }]
});
const showModal: any = ref(false); //添加、修改modal的flag
const listQuery: any = reactive({
  pageSize: 10,
  pageIndex: 1,
  userName: "",
  parentId: 1
});
//管理员信息对象
const roleList: any = ref([]); //角色列表
const data: any = ref([]); //列表数据
const loading: any = ref(false);
const selectRowIds: any = ref(""); //被选中都行
const isShow: any = ref(false); //是否查看
const pagination: any = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["5", "10", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});

// 情况添加数据
const temp: any = (depId) => {
  userInfo.value = {
    userId: "",
    depId,
    loginName: "",
    userName: "",
    isLeader:false,
    pass: randomString(6),
    // ispass: "",
    email: "",
    mobile: "",
    roleIdList: [2],
    status: true,
    loginId: "",
    sex: ""
  };
};
// 获取所有角色列表
const getRoleList: any = () => {
  role_store.SelecRoletList({})
    .then((res: any) => {
      if (res.code === 0) {
        roleList.value = res.data;
      }
    })
};
// 新增
const handleAdd: any = (id) => {
  console.log('userInfo.depId',userInfo.depId,id)
  title.value = "新增用户信息";
  isShow.value = false;
  showModal.value = true;
  if (addForm.value) {
    addForm.value.resetFields();
  }
  if(id)
  temp(id);
  else
  temp(userInfo.depId)
  
  
};

// 取消
const handleCancle: any = () => {
  showModal.value = false;
  if (addForm.value) {
    addForm.value.resetFields();
  }
  showModal.value = false;
  temp();
};
// 提交用户信息
const handleSave: any = () => {
  const userObj: any = {...userInfo.value};
  if(userInfo.value.status == true){
    userObj.status = 1;
  }else{
    userObj.status = 0;
  }
  if(userInfo.value.isLeader == true){
    userObj.isLeader = 1;
  }else{
    userObj.isLeader = 0;
  }
  userObj.id = userInfo.value.userId;
  let sha256 = Sha256.sha256
    let pass = userObj.pass;
    // let ispass = userObj.ispass;
    if(userObj.pass){
      userObj.password = sha256(userObj.pass);
    }
    proxy.$handleSave(addForm.value, user_store.SaveUser, update, userObj.id, userObj, async ()=>{
      showModal.value = false;
      temp();
      getList();
      // if(userObj.id == user_store.userId){
      //   let res = await user_store.UserInfo();
      //   if(res.code == 0){
      //     user_store.set_userinfo(res.data)
      //   }
      //    console.log('re',res)
      // }
    },()=>{
      userObj.pass = pass;
      // userObj.ispass = ispass;
    },()=>{
      userObj.pass = '';
      // userObj.ispass = '';
    })
};
// 查看
const handleShow: any = (rowData: any) => {
  title.value = "管理员信息";
  isShow.value = true;
  showModal.value = true;
  if (addForm.value) {
    addForm.value.resetFields();
  }
  user_store.SelectUserById({ userId: rowData.userId })
    .then((res: any) => {
      if (res.code === 0) {
        let obj: any = {};
        obj = res.data;
        // Object.assign({}, res.data);
        // obj.status = res.data.status + "";
        obj.roleIdList = res.data.roleIdList;
        if(obj.status == 1){
          obj.status = true;
        }else{
          obj.status = false;
        }
        if(obj.isLeader == 1){
          obj.isLeader = true;
        }else{
          obj.isLeader = false;
        }
        userInfo.value = Object.assign(userInfo.value, obj);
      }
    })
};
// 修改
const handleEdit: any = (rowData: any) => {
  title.value = "修改管理员信息";
  isShow.value = false;
  showModal.value = true;
  if (addForm.value) {
    addForm.value.resetFields();
  }
  user_store.SelectUserById({ userId: rowData.userId })
    .then((res: any) => {
      if (res.code === 0) {
        let obj: any = {};
        obj = res.data;
        obj.roleIdList = res.data.roleIdList;
        if(obj.status == 1){
          obj.status = true;
        }else{
          obj.status = false;
        }
        if(obj.isLeader == 1){
          obj.isLeader = true;
        }else{
          obj.isLeader = false;
        }
        userInfo.value = Object.assign(userInfo.value, obj);
      }
    })
};

const handleResetPwd = (record) => {
  Modal.confirm({
    title: "确认重置密码",
    icon: createVNode(ExclamationCircleOutlined),
    content: "重置密码操作不可撤销，请谨慎操作。",
    okText: "提交",
    cancelText: "取消",
    maskClosable: true,
    async onOk() {
      let res = await resetPwd(record.userId)
      if(res.code == 0 && res.data !== false){
        Modal.info({
            title: () => '重置密码结果',
            content: () => h('div', {}, [
              h('p', '用户'+record.userName+'的新密码为：'),
              h('p', res.data),
            ]),
            maskClosable: true
          });
      }else if(res.code == 0){
        message.error((!res.msg || res.msg == 'success') ? '重置密码失败' : res.msg);
      }
    }
  })
  
}
// // 查询
const getList: any = async (id) => {
  
  if(id){
    console.log('id',userInfo.depId,listQuery.parentId)
    userInfo.depId = id;
    listQuery.parentId = id;
  }
  data.value = await proxy.$getList(loading, user_store.SelectUserList, listQuery, pagination, getList)
};
//重置
const clickrest = () => {
  listQuery.pageSize = 10;
  listQuery.pageIndex = "1";
  listQuery.userName = "";
  // listQuery.parentId = 1;
  getList();
};
// 分页、序号、筛选变化时触发
const changeTableList: any = (pagination: any, filters: any, sorter: any) => {
  listQuery.pageIndex = pagination.current;
  listQuery.pageSize = pagination.pageSize;
  getList();
};
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  listQuery.pageIndex = pagination.current;
  listQuery.pageSize = pagination.pageSize;
  getList();
};

// const getDepartlist = () => {
//   const res = {
//     code:0,
//     data:[
//       {id: 1, depName: "统信软件", depCode: "-1", depCodeName: null, depId: -1, depLevel: 1, orderNum: 1, children:[]},
//       {id: 2, depName: "测试部门1", depCode: "-1_1", depCodeName: null, depId: 1, depLevel: 2, orderNum: 2}
//     ]
//   }
//   // getDepartList().then((res)=>{
//     if (res.code === 0) {
//         // 给返回的树形菜单结构，添加一级菜单项
//         let newArr = listToTree(res.data,treeData.value)
//       }
//   // })
// }

onMounted(() => {
  
  getRoleList();
  getList();
  // getDepartlist()
});
defineExpose({handleAdd,getList})
</script>
<style lang="scss" scoped>
  :deep(.ant-table){min-width: 680px;}
</style>