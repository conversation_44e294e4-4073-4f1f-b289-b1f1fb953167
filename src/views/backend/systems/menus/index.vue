<template>
  <div class="contentPadding">
    <a-form layout="inline" :model="listQuery" style="margin-bottom: 20px">
      <a-form-item>
        <a-button type="primary" @click="handleAdd" v-if="isShowBtn('sys:menu:save')"> 新增 </a-button>
      </a-form-item>
    </a-form>
    <a-table :data-source="data" :pagination="pagination" row-key="menuId" :scroll="{ x: true }" @change="changeTable" :expandIcon="expandIcon">
      <!-- <a-table-column key="id" title="序号" align="center">
        <template #default={record,index}>
            {{index+1+(pagination.pageSize * (pagination.current-1))}}
        </template>
      </a-table-column> -->
      <a-table-column key="name" title="名称" data-index="name" :ellipsis="true" />
      <a-table-column key="parentName" title="上级" data-index="parentName" :ellipsis="true" align="center" />
      <a-table-column key="icon" title="图标" data-index="icon" :ellipsis="true" align="center">
        <template v-slot="scope">
          <component :is="$icons[scope.record.icon]" />
        </template>
      </a-table-column>
      <a-table-column key="type" title="类型" data-index="type" :ellipsis="true" align="center">
        <template v-slot="scope">
          <a-tag color="green" v-if="scope.record.type === 0"> 目录 </a-tag>
          <a-tag color="cyan" v-if="scope.record.type === 1"> 菜单 </a-tag>
          <a-tag color="default" v-if="scope.record.type === 2"> 按钮 </a-tag>
        </template>
      </a-table-column>
      <a-table-column key="orderNum" title="排序" data-index="orderNum" :ellipsis="true" align="center" />
      <a-table-column key="url" title="菜单URL" data-index="url" width="120px" :ellipsis="true" align="center" />
      <a-table-column key="perms" title="授权标识" data-index="perms" width="120px" :ellipsis="true" align="center" />
      <a-table-column key="action" title="操作" :ellipsis="true" :width="225" align="center">
        <template v-slot="scope">
          <span>
            <a-button @click="handleShow(scope.record)" v-if="isShowBtn('sys:menu:info')" class="button_V">
              查看
            </a-button>
            <a-button @click="handleEdit(scope.record)" v-if="isShowBtn('sys:menu:update')" class="button_E">
              {{ $t("m.modify") }}
            </a-button>
            <a-button  @click="$handleDel(scope.record.menuId,menu_store.DeleteMenuById,()=>{getList();getMenuList();})" v-if="isShowBtn('sys:menu:delete')" class="button_D">
              {{ $t("m.del") }}
            </a-button>
          </span>
        </template>
      </a-table-column>
    </a-table>
    <!-- 添加、修改modal -->
    <a-modal centered :title="title" v-model:visible="showModal" width="608.8px" :maskClosable="isShow" :getContainer="modalBindNode">
      <template #footer>
          <a-button style="margin-left: 10px" @click="handleCancle" v-if="!isShow" >{{ $t("m.cancel") }}</a-button>
          <a-button type="primary" @click="handleSave" v-if="!isShow">{{ $t("m.save") }}</a-button>
          <a-button type="primary" style="margin-left: 10px" @click="handleCancle" v-if="isShow" >{{ $t("m.close") }}</a-button>
        </template>
      <a-form :labelCol="{ span: 5 }" ref="addForm" :model="menuInfo" :rules="rules">
        <a-form-item label="类型">
          <a-radio-group v-model:value="radioValue" :options="options" @change="onChangeRadio" :disabled="isShow" />
        </a-form-item>
        <a-form-item :label="menuTypeTitle" name="name">
          <a-input v-model:value="menuInfo.name" :disabled="isShow" placeholder="请输入名称" allow-clear />
        </a-form-item>
        <a-form-item label="上级名称" name="parentId">
          <a-tree-select
            v-model:value="menuInfo.parentId"
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :tree-data="treeData"
            placeholder="请选择"
            tree-default-expand-all
            :disabled="isShow"
            allowClear
          >
          </a-tree-select>
        </a-form-item>
        <a-form-item :label="radioValue === 1 ? '菜单路由' : '目录路由'" name="url" v-if="radioValue === 1 || (radioValue === 0 && menuInfo.parentId === 0)">
          <a-input v-model:value="menuInfo.url" :disabled="isShow" :placeholder="radioValue === 1 ? '请输入菜单路由' : '请输入目录路由'" allow-clear />
        </a-form-item>
        <a-form-item label="授权标识" name="perms" v-if="radioValue === 2 || radioValue === 1">
          <a-input v-model:value="menuInfo.perms" :disabled="isShow" placeholder="多个用逗号分隔，如：user:list,user:create" allow-clear />
        </a-form-item>
        <a-form-item label="序号号" name="orderNum" v-if="radioValue === 0 || radioValue === 1">
          <a-input-number :disabled="isShow" id="inputNumber" v-model:value="menuInfo.orderNum" :min="1" :max="100000" />
        </a-form-item>
        <a-form-item label="菜单图标" name="icon" v-if="radioValue === 0 || radioValue === 1">
          <a-popover
            v-model:visible="iconVisible"
            trigger="click"
            :overlayStyle="{
              width: '50%', // 估算好的大小
              height: '30vh', // 高度也应该是132px，另外2px是底部小三角箭头的高度
              overflow: 'auto',
              paddingBottom: '0px'
            }"
          >
            <template #content>
              <span
                v-for="(item, index) in iconsArr"
                :key="index"
                @click="checkIcon(item)"
                style="width: 40px;height: 40px;display: inline-block;text-align: center;font-size: 26px;border-radius: 5px;border: 1px solid #dcdfe6;margin: 4px;"
              >
                <component :is="$icons[item]" />
              </span>
            </template>
            <div @click="shwoIconVisible">
              <a-input v-model:value="menuInfo.icon" :disabled="isShow" class="readOnly">
                <template #suffix>
                  <info-circle-outlined style="color: rgba(0,0,0,.45)" />
                </template>
              </a-input>
            </div>
          </a-popover>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { ExclamationCircleOutlined, DownOutlined, RightOutlined } from "@ant-design/icons-vue";
import { createVNode, nextTick } from "vue";
import { Modal, notification, message } from "ant-design-vue";
import { isShowBtn } from "@/utils/tool";
import { ref, onMounted, reactive, getCurrentInstance, h, computed } from "vue";
import { userStore } from '@/store/user';
import { menuStore } from '@/store/menu';
const user_store = userStore();
const menu_store = menuStore()
const { proxy }: any = getCurrentInstance();
const addForm: any = ref(null);
const validateParentId = async (rule: any, value: any) => {
  if (value==='') {
    return Promise.reject("请输入上级");
  } else {
    return Promise.resolve();
  }
};
const checkMenuName = async (rule: any, value: any) => {
  if (value.length > 60) {
    return Promise.reject("菜单名称长度能超过60个汉字或字母");
  } else if (value.trim().length == 0) {
    return Promise.reject("菜单名称不能为空");
  } else {
    return Promise.resolve();
  }
};
const checkMenuUrl = async (rule: any, value: any) => {
  let label = radioValue === 1 ? '菜单' : '目录';
  if (value.length > 60) {
    return Promise.reject(label+"路由不能超过60个汉字或字母");
  } else if (value.trim().length == 0) {
    return Promise.reject(label+"路由不能为空");
  } else {
    return Promise.resolve();
  }
};
const title: any = ref("");
const rules: any = reactive({
  name: [{ required: true, validator: checkMenuName }],
  parentId: [{ required: true, validator: validateParentId, trigger: "change" }],
  url: [{ required: true, validator: checkMenuUrl }]
});
const showModal = ref(false); //添加、修改modal的flag
const listQuery = reactive({
  pageSize: 10,
  pageIndex: 1,
  name: ""
});
const options = reactive([
  { label: "目录", value: 0 },
  { label: "菜单", value: 1 },
  { label: "按钮", value: 2 }
]); //菜单类型
const menuInfo = ref({
  name: "",
  parentName: "",
  parentId: 0,
  menuId: "",
  icon: "",
  orderNum: 1,
  perms: "",
  url: "",
  list: [],
  type: 0
}); //角色信息对象

const radioValue = ref(0); //默认菜单类型
const menuTypeTitle = ref("目录名称"); //添加菜单类型名称
const data = ref([]); //列表数据
const loading = ref(false);
const selectRowIds = ref(""); //被选中都行
const isShow = ref(false); //是否查看
const iconVisible = ref(false); //是否展示icons
const iconsArr: any = ref([]); //存储icons的名称数组
const pagination = ref({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["5", "10", "30"] // 指定每页可以显示多少条
});
// /////////////////角色变量//////////////////////////
const treeData: any = ref([]);
const selectedKeys = ref([]); //选择的树节点
const iconsstr = ref("");

const excludes = ['createFromIconfontCN', 'default', 'setTwoToneColor', 'getTwoToneColor']
// 获取所有icons的名称
for (const item in proxy.$icons) {
  if(!excludes.includes(item))
    iconsArr.value.push(item);
}
// 清空菜单对象
const temp = () => {
  menuInfo.value = {
    name: "",
    parentName: "",
    parentId: 0,
    menuId: "",
    icon: "",
    orderNum: 1,
    perms: "",
    url: "",
    list: [],
    type: 0
  }; //角色信息对象
};
// 设置嵌套表格嵌套行的展开图标
const expandIcon = (props: any) => {
  if (props.record.children) {
    if (props.record.children.length > 0) {
      if (props.expanded) {
        // 有子菜单 展开
        return h("a",{style: "color:black;margin:0 5px;",onClick: (e: any) => {props.onExpand(props.record, e);}},h(DownOutlined));
      } else {
        // 有子菜单 未展开
        return h("a",{style: "color:black;margin:0 5px;",onClick: (e: any) => {props.onExpand(props.record, e);}},h(RightOutlined));
      }
    }
  } else {
    // 没有子菜单
    return h("a", {
      style: "color:black;margin:0 4px;",
      onClick: (e: any) => {
        props.onExpand(props.record, e);
      }
    });
  }
};
// 选择菜单类型
const onChangeRadio = (value: any) => {
  // this.radioValue = value
  if (radioValue.value === 0) {
    menuTypeTitle.value = "目录名称";
  } else if (radioValue.value === 1) {
    menuTypeTitle.value = "菜单名称";
  } else {
    menuTypeTitle.value = "按钮名称";
  }
};
// 选择图标
const checkIcon = (iconStr: any) => {
  iconVisible.value = false;
  menuInfo.value.icon = iconStr;
};
// 展示icons面板
const shwoIconVisible = () => {
  iconVisible.value = true;
};
// 获取菜单权限树
const getMenuList = () => {
  menu_store.SelectAllMenuList()
    .then((res: any) => {
      if (res.code === 0) {
        // 给返回的树形菜单结构，添加一级目录项
        const obj: any = {
          title: "一级目录",
          key: 0,
          value: 0,
          icon: null,
          list: null,
          menuId: 0,
          name: "一级目录",
          open: true,
          orderNum: null,
          parentId: -1,
          parentName: null,
          perms: null,
          type: null,
          url: null
        };
        obj.children = toTree(res.data, true);
        treeData.value = [obj];
      }
    })
};

// 新增
const handleAdd = () => {
  title.value = "新增菜单信息";
  isShow.value = false;
  showModal.value = true;
  temp();
};
// 取消
const handleCancle = () => {
  showModal.value = false;
  addForm.value.resetFields();
  temp();
};
// 提交信息
const handleSave = () => {
  const obj = Object.assign(menuInfo.value);
  obj.type = radioValue.value;
  if (radioValue.value === 2 && obj.parentId === 0) {
    message.error("按钮的上级不能为一级目录");
    return;
  }
  if(radioValue.value === 0){
    if(obj.parentId !== 0)
      menuInfo.url = null;
    menuInfo.perms = null;
    menuInfo.icon = null;
  }else if(radioValue.value = 2){
    menuInfo.url = null;
    menuInfo.orderNum =null;
    menuInfo.icon =null;
  }
  proxy.$handleSave(addForm.value, menu_store.SaveMenu, menu_store.UpdateMenu, obj.menuId, menuInfo.value, ()=>{
    showModal.value = false;
    nextTick(()=>{
      temp();
    getList();
    getMenuList();
    })
    
    // 更新左侧菜单的持久化数据，在localStorage
    menu_store.SelectNav().then((res)=>{
      if (res.code === 0) {
            const menuList = res.data;
            const permissions = res.permissions;
            user_store.set_menulist(menuList);
            user_store.set_perm(permissions);
          }
    })
    
  })
};
// 查看
const handleShow = (rowData: any) => {
  title.value = "查看菜单信息";
  isShow.value = true;
  showModal.value = true;
    menu_store.SelectMenuById({ menuId: rowData.menuId })
    .then((res: any) => {
      if (res.code === 0) {
        menuInfo.value = res.data;
        radioValue.value = menuInfo.value.type;
      }
    })
};
// 修改
const handleEdit = (rowData: any) => {
  title.value = "修改菜单信息";
  isShow.value = false;
  showModal.value = true;
    menu_store.SelectMenuById({ menuId: rowData.menuId })
    .then((res: any) => {
      if (res.code === 0) {
        menuInfo.value = res.data;
        radioValue.value = menuInfo.value.type;
      }
    })
};
// 查询
const handleSearch = () => {
  listQuery.pageIndex = 1;
  listQuery.pageSize = pagination.value.pageSize;
  getList();
};
// 列表
const getList: any = async () => {
  let data1 = await proxy.$getList(loading, menu_store.SelectAllMenuList, listQuery, pagination.value, getList,true)
  data.value = toTree(data1)
};

// 分页、序号、筛选变化时触发
const changeTable = (pagination: any, filters: any, sorter: any) => {
  listQuery.pageIndex = pagination.current;
  listQuery.pageSize = pagination.pageSize;
  getList();
};
/**
 * 对返回的树数据进行整理
 * filterBtnFlg   false:不过滤按钮，true:过滤按钮
 */
const toTree = (data: any, filterBtnFlg: any = false) => {
  //没有父节点的数据
  const parents = data.filter((value: any) => value.parentId === 0);

  //有父节点的数据
  const children = data.filter((value: any) => value.parentId !== 0);

  //定义转换方法的具体实现
  const translator = (parents: any, children: any) => {
    //遍历父节点数据
    parents.forEach((parent: any) => {
      parent.key = parent.menuId;
      parent.value = parent.menuId;
      parent.title = parent.name;
      //遍历子节点数据
      children.forEach((current: any, index: any) => {
        current.key = current.menuId;
        current.value = current.menuId;
        current.title = current.name;
        // 过滤掉按钮
        if (filterBtnFlg) {
          if (current.type !== 2) {
            //此时找到父节点对应的一个子节点
            if (current.parentId === parent.menuId) {
              //对子节点数据进行深复制，这里只支持部分类型的数据深复制，对深复制不了解的童靴可以先去了解下深复制
              const temp = JSON.parse(JSON.stringify(children));
              //让当前子节点从temp中移除，temp作为新的子节点数据，这里是为了让递归时，子节点的遍历次数更少，如果父子关系的层级越多，越有利
              temp.splice(index, 1);
              //让当前子节点作为唯一的父节点，去递归查找其对应的子节点
              translator([current], temp);
              //把找到子节点放入父节点的children属性中
              typeof parent.children !== "undefined" ? parent.children.push(current) : (parent.children = [current]);
            }
          }
        } else {
          //此时找到父节点对应的一个子节点
          if (current.parentId === parent.menuId) {
            //对子节点数据进行深复制，这里只支持部分类型的数据深复制，对深复制不了解的童靴可以先去了解下深复制
            const temp = JSON.parse(JSON.stringify(children));
            //让当前子节点从temp中移除，temp作为新的子节点数据，这里是为了让递归时，子节点的遍历次数更少，如果父子关系的层级越多，越有利
            temp.splice(index, 1);
            //让当前子节点作为唯一的父节点，去递归查找其对应的子节点
            translator([current], temp);
            //把找到子节点放入父节点的children属性中
            typeof parent.children !== "undefined" ? parent.children.push(current) : (parent.children = [current]);
          }
        }
      });
    });
  };

  //调用转换方法
  translator(parents, children);

  //返回最终的结果
  return parents;
};
onMounted(() => {
  
  getList();
  getMenuList();
});
</script>
<style scoped lang="scss">
.iconClass {
  color: black;
}
.Button_l{
background: #ffb800;
color: white;
margin: 0 10px 0 10px;
width: 44px;
height: 25px;
line-height: 20px;
font-size:12px;
border: 0;
padding: 0 5px;

}
.Button_red{
  width: 44px;
height: 25px;
line-height: 20px;
font-size:12px;
border: 0;
padding: 0 5px;
}
:deep(.ant-table th) { white-space: nowrap; }
:deep(.ant-table td) { white-space: nowrap; }
</style>
