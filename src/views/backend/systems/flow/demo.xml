<bpmn:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1p0rmoe" targetNamespace="http://bpmn.io/schema/bpmn" exporter="bpmn-js (https://demo.bpmn.io)" exporterVersion="7.3.0">	
  <bpmn:process id="Process_106ceaq" isExecutable="true">	
    <bpmn:startEvent id="Event_3sb0mnr" name="输入文本">	
      <bpmn:outgoing>Flow_3mgl6rb</bpmn:outgoing>	
    </bpmn:startEvent>	
    <bpmn:userTask id="Activity_1t3qqk0" name="输入文本">	
      <bpmn:incoming>Flow_3mgl6rb</bpmn:incoming>	
      <bpmn:outgoing>Flow_3nbmlmh</bpmn:outgoing>	
    </bpmn:userTask>	
    <bpmn:endEvent id="Event_3hpunda" name="输入文本">	
      <bpmn:incoming>Flow_3nbmlmh</bpmn:incoming>	
    </bpmn:endEvent>	
    	
      <bpmn:sequenceFlow id="Flow_3mgl6rb" sourceRef="Event_3sb0mnr" targetRef="Activity_1t3qqk0"/>	
    	
      <bpmn:sequenceFlow id="Flow_3nbmlmh" sourceRef="Activity_1t3qqk0" targetRef="Event_3hpunda"/>	
  </bpmn:process>	
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">	
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_106ceaq">	
      	
        <bpmndi:BPMNEdge id="Flow_3mgl6rb_di" bpmnElement="Flow_3mgl6rb">	
          	
            <di:waypoint x="358" y="240"/>	
          	
            <di:waypoint x="450" y="240"/>	
        </bpmndi:BPMNEdge>	
      	
        <bpmndi:BPMNEdge id="Flow_3nbmlmh_di" bpmnElement="Flow_3nbmlmh">	
          	
            <di:waypoint x="550" y="240"/>	
          	
            <di:waypoint x="682" y="240"/>	
        </bpmndi:BPMNEdge>	
      	
        <bpmndi:BPMNShape id="Event_3sb0mnr_di" bpmnElement="Event_3sb0mnr">	
          <dc:Bounds x="320" y="220" width="40" height="40"/>	
          <bpmndi:BPMNLabel>	
            <dc:Bounds x="320" y="273" width="40" height="14"/>	
          </bpmndi:BPMNLabel>	
        </bpmndi:BPMNShape>	
      	
        <bpmndi:BPMNShape id="Activity_1t3qqk0_di" bpmnElement="Activity_1t3qqk0">	
          <dc:Bounds x="450" y="200" width="100" height="80"/>	
          <bpmndi:BPMNLabel>	
            <dc:Bounds x="480" y="233" width="40" height="14"/>	
          </bpmndi:BPMNLabel>	
        </bpmndi:BPMNShape>	
      	
        <bpmndi:BPMNShape id="Event_3hpunda_di" bpmnElement="Event_3hpunda">	
          <dc:Bounds x="680" y="220" width="40" height="40"/>	
          <bpmndi:BPMNLabel>	
            <dc:Bounds x="680" y="273" width="40" height="14"/>	
          </bpmndi:BPMNLabel>	
        </bpmndi:BPMNShape>	
    </bpmndi:BPMNPlane>	
  </bpmndi:BPMNDiagram>	
</bpmn:definitions>