<template>
    <div class=''>
        <a-drawer
            title="修改节点信息"
            :width="400"
            :visible="info.dialogVisible"
            :body-style="{ paddingBottom: '80px' }"
            @close="onClose"
        >
            <a-form>
                <a-form-item label="指定方式">
                    <a-radio-group v-model:value="flowform.appointMethod" @change="changeAppoint">
                        <a-radio :value="1">指定成员</a-radio>
                        <a-radio :value="2">指定角色</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="指定人员" v-if="flowform.appointMethod == 1">
                    <a-select v-model:value="flowform.ownerId" mode="multiple" @change="onSelectChange" placeholder="请选择" 
                    :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}" 
                     :getPopupContainer="triggerNode => triggerNode.parentNode"
                    show-search>
                        <a-select-option v-for="(item,index) in userlist" :key="index" :value="item.userId">{{item.userName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="指定角色" v-if="flowform.appointMethod == 2">
                    <a-select v-model:value="flowform.roleId" mode="multiple" @change="onSelectChange" placeholder="请选择" 
                    :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}" 
                     :getPopupContainer="triggerNode => triggerNode.parentNode"
                    show-search>
                        <a-select-option v-for="(item,index) in rolelist" :key="index" :value="item.roleId">{{item.roleName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <!-- <a-form-item>
                    <a-button type="primary" @click="saveProps">保存</a-button>
                </a-form-item> -->
            </a-form>
            <div
            :style="{
                position: 'absolute',
                right: 0,
                bottom: 0,
                width: '100%',
                borderTop: '1px solid #e9e9e9',
                padding: '10px 16px',
                background: '#fff',
                textAlign: 'right',
                zIndex: 1,
            }"
            >
                <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
                <a-button type="primary" @click="saveProps">保存</a-button>
            </div>
        </a-drawer>
    </div>
</template>
<script lang='ts' setup>
import { selectList } from '@/api/backend/systems/role';
import { queryWorker } from '@/api/backend/systems/user';
import { onMounted, reactive, ref } from 'vue';
const emit = defineEmits(['getNewNodeText'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                dialogVisible:false
            }
        }
    }
})
const defaultform = {
    appointMethod:1,
    nodeId:'',
    ownerId:[],
    roleId:[]
}
const flowform = reactive({
    appointMethod:1,
    nodeId:'',
    ownerId:[],
    roleId:[]
})
const userlist = ref([])
const rolelist = ref([])
const roleIdList = ref()
const userName = ref()
const onClose = () => {
    props.info.dialogVisible = false;
    Object.assign(flowform,defaultform)
}
const changeAppoint = (e) => {
    console.log("e",e)
    if(e.target.value == 1){
        queryworker()
    }else if(e.target.value == 2){
        queryRole()
    }
}
const onSelectChange = (e,b) => {
    console.log(e,b)
    
    // roleIdList.value = b.roleIdList;
    // flowform.ownerId = e;
    // userName.value = b.userName;
}
const saveProps = () => {
    let flowform1 = {...flowform}
    if(flowform1.appointMethod == 1){
        flowform1.roleId = undefined;
        emit('getNewNodeText',flowform1)
    }else if(flowform1.appointMethod == 2){
        flowform1.ownerId = undefined;
        emit('getNewNodeText',flowform1)
    }
    onClose()
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
}
const queryRole = async () => {
    let res = await selectList()
    if(res.code == 0){
        rolelist.value = res.data;
    }
}
const setInfo = (data) => {
    console.log('data',data)
    flowform.nodeId = data.id;
    Object.assign(flowform,data.properties)
    queryworker()
    // flowform.ownerId = data.properties?.userId;
    // userName.value = data.properties?.userName;
    // roleIdList.value = data.properties?.roleIdList;
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>