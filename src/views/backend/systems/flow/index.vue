<template>
<div class="cloudContent">
  <div class="cloudRight">
    <div class="buttonPadding">
            <a-form layout="inline" :model="searchform" class="searchform">
            <a-form-item label="流程名称">
                <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.flowName" allowClear />
            </a-form-item>
            <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </a-form>
        </div>
    <div class='innerPadding'>
      <a-row>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </a-row><br>
        <a-table :columns="columns" row-key="id" :data-source="flowlist" :pagination="pagination" @change="changeTable">
            <template #index="{record,index}">
                {{index+1+(pagination.pageSize * (pagination.current-1))}}
            </template>
            <template #suspensionState="{record}">
              {{record.suspensionState ? '激活':'挂起'}}
            </template>
            <template #action="{record}">
               <a-button class="button_V" @click="handleView(record)">查看</a-button>
               <a-button class="button_E" @click="handleEdit(record)">修改</a-button>
               <a-button class="button_D" @click="$handleDel([record.deploymentId],deleteFlow,getProcessList)">删除</a-button>
            </template>
        </a-table>
        <a-modal :title="onestep.isAdd ? '新增流程':'修改流程'" v-model:visible="onestep.isShow" ok-text="下一步" @ok="handleNext" 
        @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
          <a-form :model="onestep" ref="onestepForm">
            <a-form-item label="流程名称" name="name" :rules="[{ required:true,message:'请输入流程名称',trigger:'change'}]">
              <a-input v-model:value="onestep.name" placeholder="请输入流程名称"></a-input>
            </a-form-item>
          </a-form>
        </a-modal>
        <Process ref="processModal" :info="processinfo" @getlist="getProcessList" />
    </div>
  </div>
</div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Process from "./process.vue";
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import {getFlowList,deleteFlow} from "@/api/backend/systems/flow"
import { handleWidth } from "@/utils/moreform";
const {proxy} = getCurrentInstance();

const searchform = reactive({flowName:'',pageIndex:1,pageSize:10})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、排序、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getProcessList();
};
const flowlist = ref([])
const loading = ref(false)
const onestepForm = ref()
const defaultstep = {isShow:false,isAdd:true,name:'',xml:''}
const onestep = reactive({isShow:false,isAdd:true,name:'',xml:''})
const processinfo = reactive({isShow:false,isAdd:true,isInfo:false})
const processModal = ref()
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'Flow Key',
    dataIndex: 'flowKey',
    key: 'flowKey',
  },
  {
    title: '类型',
    dataIndex: 'category',
    key: 'category',
    align:'center'
  },
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
    align:'center'
  },
  {
    title: '状态',
    dataIndex: 'suspensionState',
    key: 'suspensionState',
    align:'center',
    slots:{customRender:'suspensionState'},
  },
  // {
  //   title: '创建人',
  //   dataIndex: 'createUserName',
  //   key: 'createUserName',
  //   align:'center'
  // },
  {
    title: '发布时间',
    dataIndex: 'deploymentTime',
    key: 'deploymentTime',
    align:'center'
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    slots:{customRender:'action'},
    align:'center'
  },
];
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getProcessList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    flowName: ""
  })
}
const cancel=()=>{
  onestepForm.value.resetFields();
  Object.assign(onestep,defaultstep);
}
const handleNext = () => {
  onestepForm.value.validate().then(()=>{
    onestep.isShow = false;
    processinfo.isShow = true;
    processinfo.isInfo = false;
    processinfo.isAdd = onestep.isAdd;
    proxy.$nextTick(()=>{
      if(onestep.isAdd){
        processModal.value.setName(onestep.name)
        processModal.value.initProcess()
      }else{
        processModal.value.setProcess(onestep)
        processModal.value.setName(onestep.name)
      }
      cancel();
    })
  })
}
const handleAdd = () => {
  // processinfo.isShow = true;
  // processinfo.isAdd = true;
  // proxy.$nextTick(()=>{
  //     processModal.value.initProcess()
  // })
  onestep.isShow = true;
  onestep.isAdd = true;
}
const handleView = (record) => {
  processinfo.isShow = true;
  processinfo.isInfo = true;
  // onestep.isAdd = false;
  // if(onestep.isAdd){
      // processModal.value.setName(onestep.name)
      // processModal.value.initProcess()
    // }else{
  proxy.$nextTick(()=>{
    processModal.value.setProcess(record)
    processModal.value.setName(record.name)
  })
      
    // }
  // Object.assign(onestep,record)
}
const handleEdit = (record) => {
    // onestep.isShow = true;
    // onestep.isAdd = false;
    processinfo.isShow = true;
    processinfo.isAdd = false;
    processinfo.isInfo = false;
    proxy.$nextTick(()=>{
      processModal.value.setProcess(record)
      processModal.value.setName(record.name)
    })
    // Object.assign(onestep,record)
    // onestep.name = record.name;
    // onestep.xml = record.xml;
}
// const getProcessList = async () => {
//   let res = await getFlowList()
// }
const getProcessList = async () => {
  onestep.name = '';
  // loading.value = true;
    flowlist.value = await proxy.$getList(loading, getFlowList, searchform, pagination, getProcessList )
}
onMounted(() => {
  
  getProcessList();
  nextTick(()=>{
      handleWidth()
  })
})
</script>
<style lang='scss' scoped>
.buttonPadding,.innerPadding{min-width: 822px;}
</style>