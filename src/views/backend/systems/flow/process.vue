<template>
    <div class=''>
        <a-modal :title="info.isInfo ? flowform.flowName : (info.isAdd ? '新增流程' : '修改流程')" 
        v-model:visible="info.isShow" width="100% !important" 
        wrap-class-name="full-modal" @cancel="cancel" :maskClosable="false" :closable="!loading" centered :getContainer="modalBindNode">
          <template #footer>
            <a-button @click="cancel" v-if="!info.isInfo" :disabled="loading">取消</a-button>
            <a-button type="primary" @click="saveXML" v-if="!info.isInfo" :disabled="loading">提交</a-button>
            <span></span>
            <!-- <a-button>关闭</a-button> -->
          </template>
          <div class="ant-tooltip ant-tooltip-placement-top" style="left: 0px;top: 36px;display: none;" v-if="!info.isInfo">
            <div class="ant-tooltip-content">
              <div class="ant-tooltip-arrow">
                <span class="ant-tooltip-arrow-content"></span>
              </div>
              <div class="ant-tooltip-inner" role="tooltip">单击编辑节点其他信息</div>
            </div>
          </div>
          <a-spin :spinning="loading">
            <a-form :model="flowform" ref="flowForm">
              <!-- <a-form-item label="流程名称" :rules="[{ required:true,message:'请输入流程名称',trigger:'change'}]">
                  <a-input v-model:value="flowform.flowName" placeholder="请输入流程名称"></a-input>
              </a-form-item> -->
              <a-form-item>
                <div ref="container" class="container"></div>
              </a-form-item>
            </a-form>
          </a-spin>
        </a-modal>
        <Drawer ref="drawerDialog" :info="drawerinfo" @getNewNodeText="setNewNodeText" />
    </div>
</template>
<script lang='ts' setup>
import Drawer from "./drawer.vue";
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import LogicFlow from "@logicflow/core";
import { DndPanel, SelectionSelect, Group, Menu, Control, BpmnXmlAdapter, BpmnElement} from "@logicflow/extension";
import registerPolyline from '@/utils/flow/registerPolyline'
import registerStart from '@/utils/flow/registerStart'
import registerJudge from '@/utils/flow/registerJudge'
import registerEnd from '@/utils/flow/registerEnd'
import "@logicflow/core/dist/style/index.css";
import "@logicflow/extension/lib/style/index.css";
// 导入模块
import { saveFlow, updateFlow } from '@/api/backend/systems/flow';
import $ from "jquery"
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true,
                isInfo:false
            }
        }
    }
})
const plugins = [Menu, DndPanel, SelectionSelect, Group, Control, BpmnElement, BpmnXmlAdapter];
const drawerinfo = reactive({dialogVisible:false})
const loading = ref(false);
const flowForm = ref()
const drawerDialog = ref()
const clickNode = ref()
const flowform = reactive({
  "flowName": "",
  "xml": "",
})
const patternItems = [
    {
      type: 'bpmn:startEvent',
        // type: 'start',
        text: '双击输入文本',
        title: '双击输入文本',

        label: '开始',
        // callback: () => {
        //     lf.value.emit('bpmn:startEvent')
        // },
        // properties:{
        //     width:30,
        //     height:30,
        //     border: '1px solid #000',
        //     borderRadius: 50
        // },
        icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAAH6ji2bAAAABGdBTUEAALGPC/xhBQAAAnBJREFUOBGdVL1rU1EcPfdGBddmaZLiEhdx1MHZQXApraCzQ7GKLgoRBxMfcRELuihWKcXFRcEWF8HBf0DdDCKYRZpnl7p0svLe9Zzbd29eQhTbC8nv+9zf130AT63jvooOGS8Vf9Nt5zxba7sXQwODfkWpkbjTQfCGUd9gIp3uuPP8bZ946g56dYQvnBg+b1HB8VIQmMFrazKcKSvFW2dQTxJnJdQ77urmXWOMBCmXM2Rke4S7UAW+/8ywwFoewmBps2tu7mbTdp8VMOkIRAkKfrVawalJTtIliclFbaOBqa0M2xImHeVIfd/nKAfVq/LGnPss5Kh00VEdSzfwnBXPUpmykNss4lUI9C1ga+8PNrBD5YeqRY2Zz8PhjooIbfJXjowvQJBqkmEkVnktWhwu2SM7SMx7Cj0N9IC0oQXRo8xwAGzQms+xrB/nNSUWVveI48ayrFGyC2+E2C+aWrZHXvOuz+CiV6iycWe1Rd1Q6+QUG07nb5SbPrL4426d+9E1axKjY3AoRrlEeSQo2Eu0T6BWAAr6COhTcWjRaYfKG5csnvytvUr/WY4rrPMB53Uo7jZRjXaG6/CFfNMaXEu75nG47X+oepU7PKJvvzGDY1YLSKHJrK7vFUwXKkaxwhCW3u+sDFMVrIju54RYYbFKpALZAo7sB6wcKyyrd+aBMryMT2gPyD6GsQoRFkGHr14TthZni9ck0z+Pnmee460mHXbRAypKNy3nuMdrWgVKj8YVV8E7PSzp1BZ9SJnJAsXdryw/h5ctboUVi4AFiCd+lQaYMw5z3LGTBKjLQOeUF35k89f58Vv/tGh+l+PE/wG0rgfIUbZK5AAAAABJRU5ErkJggg==',
    },
    {
      type: 'bpmn:userTask',
    //   type: 'polygon',
      text: '双击输入文本',
      label: '业务',
    //   callback: () => {
    //         lf.value.emit('bpmn:exclusiveGateway')
    //     },
    // properties:{
    //         userId:'',
    //         roleIdList:''
    //     },
      icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAEFVwZaAAAABGdBTUEAALGPC/xhBQAAAqlJREFUOBF9VM9rE0EUfrMJNUKLihGbpLGtaCOIR8VjQMGDePCgCCIiCNqzCAp2MyYUCXhUtF5E0D+g1t48qAd7CCLqQUQKEWkStcEfVGlLdp/fm3aW2QQdyLzf33zz5m2IsAZ9XhDpyaaIZkTS4ASzK41TFao88GuJ3hsr2pAbipHxuSYyKRugagICGANkfFnNh3HeE2N0b3nN2cgnpcictw5veJIzxmDamSlxxQZicq/mflxhbaH8BLRbuRwNtZp0JAhoplVRUdzmCe/vO27wFuuA3S5qXruGdboy5/PRGFsbFGKo/haRtQHIrM83bVeTrOgNhZReWaYGnE4aUQgTJNvijJFF4jQ8BxJE5xfKatZWmZcTQ+BVgh7s8SgPlCkcec4mGTmieTP4xd7PcpIEg1TX6gdeLW8rTVMVLVvb7ctXoH0Cydl2QOPJBG21STE5OsnbweVYzAnD3A7PVILuY0yiiyDwSm2g441r6rMSgp6iK42yqroI2QoXeJVeA+YeZSa47gZdXaZWQKTrG93rukk/l2Al6Kzh5AZEl7dDQy+JjgFahQjRopSxPbrbvK7GRe9ePWBo1wcU7sYrFZtavXALwGw/7Dnc50urrHJuTPSoO2IMV3gUQGNg87IbSOIY9BpiT9HV7FCZ94nPXb3MSnwHn/FFFE1vG6DTby+r31KAkUktB3Qf6ikUPWxW1BkXSPQeMHHiW0+HAd2GelJsZz1OJegCxqzl+CLVHa/IibuHeJ1HAKzhuDR+ymNaRFM+4jU6UWKXorRmbyqkq/D76FffevwdCp+jN3UAN/C9JRVTDuOxC/oh+EdMnqIOrlYteKSfadVRGLJFJPSB/ti/6K8f0CNymg/iH2gO/f0DwE0yjAFO6l8JaR5j0VPwPwfaYHqOqrCI319WzwhwzNW/aQAAAABJRU5ErkJggg==',
    },
    {
      type: 'bpmn:exclusiveGateway',
    //   type: 'polygon',
      text: {editable:false},
      label: '条件',
    //   callback: () => {
    //         lf.value.emit('bpmn:exclusiveGateway')
    //     },
      icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVCAYAAAHeEJUAAAAABGdBTUEAALGPC/xhBQAAAvVJREFUOBGNVEFrE0EU/mY3bQoiFlOkaUJrQUQoWMGePLX24EH0IIoHKQiCV0G8iE1covgLiqA/QTzVm1JPogc9tIJYFaQtlhQxqYjSpunu+L7JvmUTU3AgmTfvffPNN++9WSA1DO182f6xwILzD5btfAoQmwL5KJEwiQyVbSVZ0IgRyV6PTpIJ81E5ZvqfHQR0HUOBHW4L5Et2kQ6Zf7iAOhTFAA8s0pEP7AXO1uAA52SbqGk6h/6J45LaLhO64ByfcUzM39V7ZiAdS2yCePPEIQYvTUHqM/n7dgQNfBKWPjpF4ISk8q3J4nB11qw6X8l+FsF3EhlkEMfrjIer3wJTLwS2aCNcj4DbGxXTw00JmAuO+Ni6bBxVUCvS5d9aa04+so4pHW5jLTywuXAL7jJ+D06sl82Sgl2JuVBQn498zkc2bGKxULHjCnSMadBKYDYYHAtsby1EQ5lNGrQd4Y3v4Zo0XdGEmDno46yCM9Tk+RiJmUYHS/aXHPNTcjxcbTFna000PFJHIVZ5lFRqRpJWk9/+QtlOUYJj9HG5pVFEU7zqIYDVsw2s+AJaD8wTd2umgSCCyUxgGsS1Y6TBwXQQTFuZaHcd8gAGioE90hlsY+wMcs30RduYtxanjMGal8H5dMW67dmT1JFtYUEe8LiQLRsPZ6IIc7A4J5tqco3T0pnv/4u0kyzrYUq7gASuEyI8VXKvB9Odytv6jS/PNaZBln0nioJG/AVQRZvApOdhjj3Jt8QC8Im09SafwdBdvIpztpxWxpeKCC+EsFdS8DCyuCn2munFpL7ctHKp+Xc5cMybeIyMAN33SPL3ZR9QV1XVwLyzHm6Iv0/yeUuUb7PPlZC4D4HZkeu6dpF4v9j9MreGtMbxMMRLIcjJic9yHi7WQ3yVKzZVWUr5UrViJvn1FfUlwe/KYVfYyWRLSGNu16hR01U9IacajXPei0wx/5BqgInvJN+MMNtNme7ReU9SBbgntovn0kKHpFg7UogZvaZiOue/q1SBo9ktHzQAAAAASUVORK5CYII=',
    },
    {
      type: 'bpmn:endEvent',
    //   type: 'end',
      text: '双击输入文本',
      label: '结束',
    //   callback: () => {
    //         lf.value.emit('bpmn:endEvent')
    //     },
      icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAAH6ji2bAAAABGdBTUEAALGPC/xhBQAAA1BJREFUOBFtVE1IVUEYPXOf+tq40Y3vPcmFIdSjIorWoRG0ERWUgnb5FwVhYQSl72oUoZAboxKNFtWiwKRN0M+jpfSzqJAQclHo001tKkjl3emc8V69igP3znzfnO/M9zcDcKT67azmjYWTwl9Vn7Vumeqzj1DVb6cleQY4oAVnIOPb+mKAGxQmKI5CWNJ2aLPatxWa3aB9K7/fB+/Z0jUF6TmMlFLQqrkECWQzOZxYGjTlOl8eeKaIY5yHnFn486xBustDjWT6dG7pmjHOJd+33t0iitTPkK6tEvjxq4h2MozQ6WFSX/LkDUGfFwfhEZj1Auz/U4pyAi5Sznd7uKzznXeVHlI/Aywmk6j7fsUsEuCGADrWARXXwjxWQsUbIupDHJI7kF5dRktg0eN81IbiZXiTESic50iwS+t1oJgL83jAiBupLDCQqwziaWSoAFSeIR3P5Xv5az00wyIn35QRYTwdSYbz8pH8fxUUAtxnFvYmEmgI0wYXUXcCCSpeEVpXlsRhBnCEATxWylL9+EKCAYhe1NGstUa6356kS9NVvt3DU2fd+Wtbm/+lSbylJqsqkSm9CRhvoJVlvKPvF1RKY/FcPn5j4UfIMLn8D4UYb54BNsilTDXKnF4CfTobA0FpoW/LSp306wkXM+XaOJhZaFkcNM82ASNAWMrhrUbRfmyeI1FvRBTpN06WKxa9BK0o2E4Pd3zfBBEwPsv9sQBnmLVbLEIZ/Xe9LYwJu/Er17W6HYVBc7vmuk0xUQ+pqxdom5Fnp55SiytXLPYoMXNM4u4SNSCFWnrVIzKG3EGyMXo6n/BQOe+bX3FClY4PwydVhthOZ9NnS+ntiLh0fxtlUJHAuGaFoVmttpVMeum0p3WEXbcll94l1wM/gZ0Ccczop77VvN2I7TlsZCsuXf1WHvWEhjO8DPtyOVg2/mvK9QqboEth+7pD6NUQC1HN/TwvydGBARi9MZSzLE4b8Ru3XhX2PBxf8E1er2A6516o0w4sIA+lwURhAON82Kwe2iDAC1Watq4XHaGQ7skLcFOtI5lDxuM2gZe6WFIotPAhbaeYlU4to5cuarF1QrcZ/lwrLaCJl66JBocYZnrNlvm2+MBCTmUymPrYZVbjdlr/BxlMjmNmNI3SAAAAAElFTkSuQmCC',
    }
  ]
// 声明容器的对应ref对象和LF对象
const container = ref();
const lf = ref<LogicFlow>();
const graphData = {
    nodes: [
        // {
        //     id: "1",
        //     type: "rect",
        //     x: 100,
        //     y: 100,
        //     text: "节点1",
        // },
        // {
        //     id: "2",
        //     type: "circle",
        //     x: 300,
        //     y: 200,
        //     text: "节点2",
        // },
    ],
    edges: [
        // {
        //     sourceNodeId: "1",
        //     targetNodeId: "2",
        //     type: "polyline",
        //     text: "连线",
        // },
    ]
}
const menuConfig = {
    
  nodeMenu: [
    // {
    //     text: '删除',
    //     callback(node) {
    //         // node为该节点数据
    //         lf.value.deleteNode(node.id);
    //     },
    // },
    {
      text: "修改节点信息",
      callback(node) {
        lf.value.emit('node:click',{data:node})
        // lf.deleteNode(node.id);
        console.log('ndode',node)
      },
    },
//     {
//       text: '分享',
//       callback() {
//         alert('分享成功！');
//       }
//     },
//     {
//       text: '属性',
//       callback(node: any) {
//         alert(`
//           节点id：${node.id}
//           节点类型：${node.type}
//           节点坐标：(x: ${node.x}, y: ${node.y})`
//         );
//       },
//     },
  ],
  edgeMenu: false, // 删除默认的边右键菜单
  graphMenu: [], // 覆盖默认的边右键菜单，与false表现一样
};
const setNewNodeText = (flowform) => {
    console.log('id, text',flowform)
    // lf.value.updateText(id, userName)
    // lf.value.setProperties(id, {userId,userName,roleIdList:(roleIdList ? roleIdList : '')})
    lf.value.setProperties(flowform.nodeId,flowform)
}
// 注册鼠标点击事件
const clickEvent = () => {
    let timer5 = null;
    lf.value.on("node:dbclick", ({ data }) => {
        clearTimeout(timer5); //清除未执行的定时器
            if(data.type != "bpmn:exclusiveGateway"){
                lf.value.graphModel.editText(data.id);
                // lf.value.once("graph:transform,node:click,blank:click", () => {
                //     lf.graphModel.textEditElement.setElementState(1);
                // });
            }
    });
    lf.value.on('node:click', ({data}) => {
        clearTimeout(timer5);
        console.log('node:click', data)
        timer5 = setTimeout(function () {
          if(data.type == "bpmn:userTask"){
            drawerinfo.dialogVisible = true;
            proxy.$nextTick(()=>{
                drawerDialog.value.setInfo(data);
            })
            // clickNode.value = data
        }
        },400)
        
      })
      lf.value.on("node:mouseenter",({data,e})=>{
        console.log("enter")
        if(data.x > 100 && data.type == "bpmn:userTask")
        $(".ant-tooltip").css({
          display: "block",
          left: data.x - 50,
          top: data.y,
        });
      })
      lf.value.on("node:mouseleave",({data})=>{
        if(data.x > 100 && data.type == "bpmn:userTask"){
          lf.value.on("node:mouseenter",({data,e})=>{
          console.log("enter")
          
          $(".ant-tooltip").css({
            display: "block",
            left: data.x - 54,
            top: data.y - 5,
          });
        })
          console.log("leave")
          $(".ant-tooltip").css({
            display: "none",
          });
        }
        
      })
      lf.value.on("node:mousedown",({data})=>{
        if(data.x > 100 && data.type == "bpmn:userTask")
        $(".ant-tooltip").css({
          display: "none",
        });
      })
      lf.value.on("node:mousemove",({data})=>{
        if(data.x > 100 && data.type == "bpmn:userTask"){
          lf.value.off("node:mouseenter")
          console.log("move")
          $(".ant-tooltip").css({
            display: "none",
          });
        }
        
      })
      lf.value.on('edge:click', ({data}) => {
        console.log('edge:click', data)
        //  clickNode.value = data
        //   drawerinfo.dialogVisible = true
      })
}
const initProcess = (data) => {
    lf.value = new LogicFlow({
        isSilentMode: props.info.isInfo,
        // disabledPlugins: [props.info.isInfo?undefined:Menu],
        // 通过选项指定了渲染的容器和需要显示网格
        container: container.value,
        grid: true,
        hoverOutline: false,
        nodeTextEdit:true,
        edgeTextEdit:false,
        textEdit:true,
        isExecutable:true,
        plugins: (props.info.isInfo ? plugins.slice(1) : plugins)
    })
    if(!props.info.isInfo)
    lf.value.extension.menu.addMenuConfig(menuConfig);
    if(!props.info.isInfo){
      lf.value.extension.dndPanel.setPatternItems(patternItems);
    }
    else{
      lf.value.extension.dndPanel.setPatternItems([]);
    }
    lf.value.extension.selectionSelect.openSelectionSelect();
    // registerPolyline(lf.value);
    // registerStart(lf.value);
    // registerJudge(lf.value);
    // registerEnd(lf.value);
    proxy.$nextTick(()=>{
        if(data){
            // console.log('data',data)
            lf.value.render(data);
        }
        else
        lf.value.render(graphData);
        clickEvent()
    })
    
    
}



var loadXML = function(xmlString)
{
    // console.log('xmlString',window.DOMParser, window.ActiveXObject)
var xmlDoc=null;

if(!window.DOMParser && window.ActiveXObject)
{
    console.log('xmlDoc1')
var xmlDomVersions = ['MSXML.2.DOMDocument.6.0','MSXML.2.DOMDocument.3.0','Microsoft.XMLDOM'];
for(var i=0;i<xmlDomVersions.length;i++)
{
xmlDoc = new ActiveXObject(xmlDomVersions[i]);
xmlDoc.async = false;
xmlDoc.loadXML(xmlString); //loadXML方法载入xml字符串
}
}
else if(window.DOMParser && document.implementation && document.implementation.createDocument)
{
    console.log('xmlDoc2')
var domParser = new DOMParser();
xmlDoc = domParser.parseFromString(xmlString, 'text/xml');
}
else
{
    console.log('xmlDoc3')
return null;
}
return xmlDoc;
}
const XML2String = (xmlObject) => {
    // for IE
    if (window.ActiveXObject) {
　　　　return xmlObject.xml;
    }
    // for other browsers
    else {       
      return (new XMLSerializer()).serializeToString(xmlObject);
    }
}
const setProcess = (record) => {
    flowform.id = record.id;
    // record.xml = ``;
// //js解析xml
// $(function(){
// //加载xmlDoc
// var xmlDoc=loadXML(record.xml);
// //获取xml指定标签
// var tag=xmlDoc.getElementsByTagName("bpmn:process")[0];
// //获取标签指定属性值
// var attr=tag.setAttribute("isExecutable",true);
// record.xml = tag;
// console.log('record.xml',tag)
    if(record.xml)
    initProcess(record.xml)
    else
    initProcess()
// });
// //jquery解析xml
// $(function(){
// var xmlStr =loadXML(record.flowJSON);
// // console.log('xmlStr4',xmlStr)
// var attr=$(xmlStr).find('bpmn:process').children();
// console.log('attr',attr)
// // alert(attr);
// // record.flowJSON = attr;
// });
    // Object.assign(flowform,record)
    
}
const setName = (flowName) => {
    flowform.flowName = flowName;
}
const cancel = () => {
    flowform.flowName = '';
    flowform.id = undefined;
    flowform.xml = '';
    lf.value.extension.dndPanel.setPatternItems([]);
    lf.value.clearData();
    props.info.isShow = false;
    loading.value = false;
    // lf.value.graphModel.resize();
}
// function download(filename: string, text: string) {
//   var element = document.createElement('a');
//   element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text));
//   element.setAttribute('download', filename);

//   element.style.display = 'none';
//   document.body.appendChild(element);

//   element.click();

//   document.body.removeChild(element);
// }
// function downloadXml() {
//     const data = lf.value.getGraphData() as string;
//     download('logic-flow.xml', data);
// }
const saveXML = () => {
    //js解析xml
    // $.(function(){
        var data = lf.value.getGraphData();
    //加载xmlDoc
    var xmlDoc=loadXML(data);
    //获取xml指定标签
    var tag=xmlDoc.getElementsByTagName("bpmn:process")[0];
    //获取标签指定属性值
    var attr=tag.setAttribute("isExecutable",true);
    // data = tag;
    
    proxy.$handleSave(flowForm.value, saveFlow, updateFlow, props, flowform, ()=>{
        emit('getlist')
        cancel();
    },()=>{
        loading.value = false;
    },()=>{
        loading.value = true;
        // flowform.flowJSON = JSON.stringify(rawdata);
        flowform.xml = XML2String(xmlDoc);
        console.log('data',flowform.xml);
    })
    // })
    // const rawdata = lf.value.getGraphRawData() as string;
    
}
onMounted(() => {
  
    // initProcess()
})
defineExpose({initProcess,setProcess,setName})
</script>
<style lang='scss' scoped>
// @import '@/utils/flow/index.scss';
.container {
  width: 100%;
  height: 744px;
}
:deep(.ant-form-item){
    margin-bottom: 0;
}
.ant-tooltip{display: block;}
.full-modal {
  .ant-modal-body {
    flex: 1;
  }
}
</style>