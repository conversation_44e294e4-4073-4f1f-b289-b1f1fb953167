<template>
    <div class='contentPadding'>
        <div style="width:1000px">
            <a-form :model="sysform" :labelCol="{span:4}">
            <a-form-item label="是否开启白名单" name="openWhiteList">
                <a-radio-group v-model:value="sysform.openWhiteList" button-style="solid">
                    <a-radio-button :value="1">是</a-radio-button>
                    <a-radio-button :value="0">否</a-radio-button>
                </a-radio-group>
            </a-form-item>
            <template v-if="sysform.openWhiteList">
                <a-form-item label="是否限制访问时间" name="allowAllTime">
                    <a-radio-group v-model:value="sysform.allowAllTime" button-style="solid">
                        <a-radio-button :value="1">是</a-radio-button>
                        <a-radio-button :value="0">否</a-radio-button>
                    </a-radio-group>
                </a-form-item>
                <template v-if="sysform.allowAllTime">
                    <!-- <template> -->
                        <a-row v-for="(item,index) in sysform.timeEntityList" :key="item.key" align="middle">
                            <a-col :span="7.1">
                                <a-form-item :labelCol="{style:{width:'166.66px'}}" :wrapperCol="{style:{marginLeft:index == 0 ? 0 : '166.66px'}}" :label="index == 0 ? '允许访问时间' : ''" class="ipclass" :name="['timeEntityList',index,'startTime']" :rules="[{ required: true, message:'请选择' }]">
                                    <a-time-picker
                                     v-model:value="item.startTime" 
                                     :disabledHours="()=>disabledHours(item.endTime,'startTime')"  
                                     :disabledMinutes="(selectedHour)=>disabledMinutes(selectedHour,item.endTime,'startTime')"
                                     :disabledSeconds="(selectedHour, selectedMinute)=>disabledSeconds(selectedHour, selectedMinute, item.endTime, 'startTime')"
                                     valueFormat="HH:mm:ss" />
                            <!-- <a-date-picker
                            v-model:value="item.startTime"
                            :disabled-date="(startValue)=>disabledStartDate(startValue,item)"
                            show-time
                            format="YYYY-MM-DD HH:mm:ss"
                            placeholder="Start"
                            @openChange="handleStartOpenChange"
                            /> -->
                            

                            <!-- <a-range-picker
                            v-model:value="item.time"
                            :show-time="{ format: 'HH:mm' }"
                            format="YYYY-MM-DD HH:mm"
                            :placeholder="['开始日期', '结束日期']"
                            @change="onChange"
                            @ok="onOk"
                            class="time-picker-ui"
                            :locale="locale"
                            /> -->
                            <!-- <a-date-picker
                            v-model:value="startValue"
                            :disabled-date="disabledStartDate"
                            show-time
                            format="YYYY-MM-DD HH:mm:ss"
                            placeholder="Start"
                            @openChange="handleStartOpenChange"
                            />
                            <a-date-picker
                            v-model:value="endValue"
                            :disabled-date="disabledEndDate"
                            show-time
                            format="YYYY-MM-DD HH:mm:ss"
                            placeholder="End"
                            :open="endOpen"
                            @openChange="handleEndOpenChange"
                            /> -->
                        </a-form-item>
                            </a-col>
                            <a-col :span="1"><a-form-item style="text-align:center"><span>~</span></a-form-item></a-col>
                            <a-col :span="3.1">
                                <a-form-item class="ipclass" :name="['timeEntityList',index,'endTime']" :rules="[{ required: true, message:'请选择' }]">
                            <a-time-picker
                             v-model:value="item.endTime" 
                             :disabledHours="()=>disabledHours(item.startTime,'endTime')" 
                             :disabledMinutes="(selectedHour)=>disabledMinutes(selectedHour,item.startTime,'endTime')"
                             :disabledSeconds="(selectedHour, selectedMinute)=>disabledSeconds(selectedHour, selectedMinute, item.startTime, 'endTime')"
                             valueFormat="HH:mm:ss" />
                            <!-- <a-date-picker
                            v-model:value="item.endTime"
                            :disabled-date="(endValue)=>disabledEndDate(endValue,item)"
                            show-time
                            format="YYYY-MM-DD HH:mm:ss"
                            placeholder="End"
                            :open="endOpen"
                            @openChange="handleEndOpenChange"
                            /> -->
                            
                        </a-form-item>
                            </a-col>
                            <a-col :span="1">
                                <a-form-item style="margin-left:5px">
                                    <MinusCircleOutlined @click="removeTimeItem(item)" />
                                </a-form-item>
                            </a-col>
                        
                        </a-row>
                        
                    <!-- </template> -->
                    <a-form-item label=" " :colon="false">
                        <a-button type="dashed" style="width:300px" @click="addTimeItem">
                            <PlusOutlined />
                            添加
                        </a-button>
                    </a-form-item>
                </template>
                <a-form-item label="是否限制访问网络" name="allowAllNetwork">
                    <a-radio-group v-model:value="sysform.allowAllNetwork" button-style="solid">
                        <a-radio-button :value="1">是</a-radio-button>
                        <a-radio-button :value="0">否</a-radio-button>
                    </a-radio-group>
                </a-form-item>
                <template v-if="sysform.allowAllNetwork">
                    <template v-for="(item,index) in sysform.networkEntityList" :key="item.key">
                        <a-form-item :label="index == 0 ? '允许访问网络' : ''" :wrapperCol="{offset:index == 0 ? 0 : 4}" class="ipclass" :name="['networkEntityList',index,'networkinfo']" :rules="[{ required: true, pattern:regexps.cidr, message:'ip格式错误' }]">
                            <a-input v-model:value="item.networkinfo" class="time-picker-ui" placeholder="请输入CIDR 例如***********/24"></a-input>
                            <MinusCircleOutlined @click="removeIpItem(item)" />
                        </a-form-item>
                    </template>
                    <a-form-item :wrapperCol="{offset:4}">
                        <a-button type="dashed" style="width:300px" @click="addIpItem">
                            <PlusOutlined />
                            添加
                        </a-button>
                    </a-form-item>
                </template>
            </template>
            
            <a-form-item :wrapperCol="{offset:4}">
                <a-button type="primary" @click="handleSave">保存</a-button>
            </a-form-item>
        </a-form>
        </div>
        
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive } from 'vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import {infoWhitelist, saveWhitelist} from "@/api/backend/systems/whitelist";
import {regexps} from "@/common/regexp/index";
import emiter from '@/utils/Bus';
import moment from 'moment';
import { message } from 'ant-design-vue';
const {proxy} = getCurrentInstance();
const defaultform = {
    openWhiteList:0,
    allowAllTime:0,
    allowAllNetwork:0,
    timeEntityList:[],
    networkEntityList:[]
}
const sysform = reactive({
    openWhiteList:0,
    allowAllTime:0,
    allowAllNetwork:0,
    timeEntityList:[],
    networkEntityList:[]
})
const disabledHours = (time,timetype) => {
    // const time = new Date()
    if(!time)
        return [];
    const hour = Number(time.substr(0,2))
    const hours = []
    for (let i = 0; i < 24; i++) {
        if(timetype == 'startTime'){
            if (i > hour) {
                hours.push(i)
            }
        }else{
            if (i < hour) {
                hours.push(i)
            }
        }
    }
    return hours;
}

const disabledMinutes = (selectedHour,time,timetype) => {
    // const time = new Date()
    if(!time)
        return [];
    const minutes = Number(time.substr(3,2));
    const minutesList = []
    if(selectedHour == Number(time.substr(0,2))){
        for (let i = 0; i < 60; i++) {
            if(timetype == 'startTime'){
                if (i > minutes) {
                    minutesList.push(i)
                }
            }else{
                if (i < minutes) {
                    minutesList.push(i)
                }
            }
            
        }
    }
    return minutesList;
}
const disabledSeconds = (selectedHour, selectedMinute, time, timetype) => {
    // const time = new Date()
    if(!time)
        return [];
    const seconds = Number(time.substr(6,2))
    const secondsList = []
    if(selectedHour == Number(time.substr(0,2))){
        for (let i = 0; i < 60; i++) {
            if(timetype == 'startTime'){
                if(selectedMinute > Number(time.substr(3,2))){
                    secondsList.push(i)
                }else{
                    if (i > seconds) {
                        secondsList.push(i)
                    }
                }
            }else{
                if(selectedMinute < Number(time.substr(3,2))){
                    secondsList.push(i)
                }else{
                    if (i < seconds) {
                        secondsList.push(i)
                    }
                }
            }
        }
    }
    return secondsList;
}
const disabledStartDate = (startValue, item) => {
    if (!startValue || !item.endTime) {
    return false;
    }
    return startValue.valueOf() > item.endTime.valueOf();
};
const disabledEndDate = (endValue,item) => {
    if (!endValue || !item.startTime) {
    return false;
    }
    return item.startTime.valueOf() >= endValue.valueOf();
};
const addTimeItem = () => {
    sysform.timeEntityList.push({"key": Date.now(),"endTime": "","startTime": ""})
}
const removeTimeItem = (item) => {
    let index = sysform.timeEntityList.indexOf(item);
    if (index !== -1) {
        sysform.timeEntityList.splice(index, 1);
    }
}
const addIpItem = () => {
    sysform.networkEntityList.push({key: Date.now(),networkinfo:''})
}
const removeIpItem = (item) => {
    let index = sysform.networkEntityList.indexOf(item);
    if (index !== -1) {
        sysform.networkEntityList.splice(index, 1);
    }
}
const handleSave = () => {
    let sysform1 = {};
    if(!sysform.openWhiteList){
        sysform1.id = sysform.id;
        sysform1.openWhiteList = sysform.openWhiteList;
    }else{
        // if(!sysform.openWhiteList){
        //     sysform1.openWhiteList = sysform.openWhiteList;
        // }
        sysform1 = JSON.parse(JSON.stringify(sysform));
        // sysform1.id = null;
        if(!sysform.allowAllTime){
            sysform1.timeEntityList = [];
        }else if(sysform.timeEntityList.length > 0){
            sysform1.timeEntityList.map((item,index)=>{
                item.startTime = moment().format('YYYY-MM-DD ')+item.startTime;
                item.endTime = moment().format('YYYY-MM-DD ')+item.endTime;
            })
        }
        if(!sysform.allowAllNetwork){
            sysform1.networkEntityList = [];
        }
    }
    saveWhitelist(sysform1).then((res)=>{
        if(res.code == 0 && res.data !== false){
            message.success("保存成功");
        }else{
            if(res.code == 0)
                message.error((res.msg == 'success' || !res.msg) ? '保存失败' : res.msg)
            Object.assign(sysform,{...defaultform})
        }
    }).catch((error)=>{
        message.error(error.message ? error.message : '保存失败')
        Object.assign(sysform,{...defaultform})
    })
}
const setInfo = () => {
    infoWhitelist().then((res)=>{
        if(res.code == 0 && res.data !== false){
            let temp = {...res.data};
            if(temp.timeEntityList && temp.timeEntityList.length > 0){
                temp.timeEntityList.map((item,index)=>{
                    item.startTime = item.startTime.substr(11,8);
                    item.endTime = item.endTime.substr(11,8);
                })
            }
            Object.assign(sysform,{...temp})
            Object.assign(defaultform,{...temp})
        }else if(res.code == 0){
            message.error((res.msg == 'success' || !res.msg) ? '白名单获取失败' : res.msg)
        }
    }).catch((error)=>{
        message.error(error.message ? error.message : '白名单获取失败')
    })
}
onMounted(() => {
    // emiter.emit('allLoading',false)
    setInfo()
})
</script>
<style lang='scss' scoped>
.time-picker-ui{margin-right: 5px;width: 300px;}
// .ipclass.ant-form-item{margin-bottom: 5px;}
</style>