<template>
    <div v-if="$isShowBtn('sys:sysconfig:info')" class='contentPadding'>
        <a-form :model="sysform" :label-col="{style:'width:200px'}" :wrapper-col="{style:'max-width:500px;min-width:372px'}" style="min-width: 572px">
            <a-form-item label="系统名称" name="systemTitle">
                <a-input v-model:value="sysform.systemTitle" placeholder="不填为默认，UStack" allow-clear></a-input>
            </a-form-item>
            <a-form-item label="系统简称" name="shortTitle">
                <a-input v-model:value="sysform.shortTitle" placeholder="不填为默认，UStack" allow-clear></a-input>
            </a-form-item>
            <a-form-item label="登录页面标题" name="loginTitle">
                <a-input v-model:value="sysform.loginTitle" placeholder="不填为默认，统信云基础设施管理平台" allow-clear></a-input>
            </a-form-item>
            <a-form-item label="登录验证码校验" name="openCaptcha">
                <a-switch v-model:checked="sysform.openCaptcha" checked-children="开" un-checked-children="关" />
            </a-form-item>
            <a-form-item label="登录失败次数校验" name="openLoginFailCheck">
                <a-switch v-model:checked="sysform.openLoginFailCheck" checked-children="开" un-checked-children="关" />
            </a-form-item>
            <a-form-item label="登录失败次数" name="failNumber" v-if="sysform.openLoginFailCheck">
                <a-input-number v-model:value="sysform.failNumber" :min="1" :max="5" :step="1" :precision="0" />
            </a-form-item>
            <a-form-item label="登录页面logo" name="loginLogoAttachmentId">
                <a-upload
                      name="file"
                      list-type="picture-card"
                      class="avatar-uploader"
                      v-model:file-list="fileList1"
                      :beforeUpload="beforeUpload1"
                      :custom-request="customRequest1"
                      :remove="removeAttachment1"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      @preview="(file)=>handlePreview(file,1)"
                      
                  >
                  <!-- :customRequest="uploadImage" -->
                  <div class="avatar" v-if="fileList1.length < 1" title="点击上传登录logo">
                      <!-- <img v-if="imageUrl1!=''" :src="imageUrl1" alt=""/>
                      <img v-else :src="tempImg1" alt=""> -->
                       <plus-outlined />
                  </div>
                 </a-upload>
                 <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel(1)">
                    <img alt="example" style="width: 100%" :src="previewImage" />
                </a-modal>
            </a-form-item>
            <a-form-item label="favicon图标" name="faviconAttachmentId">
                <a-upload
                      name="file"
                      list-type="picture-card"
                      class="avatar-uploader"
                      v-model:file-list="fileList2"
                      :beforeUpload="beforeUpload2"
                      :custom-request="customRequest2"
                      :remove="removeAttachment2"
                      accept="image/jpeg,image/jpg,image/png,image/webp,image/x-icon"
                      @preview="(file)=>handlePreview(file,2)"
                  >
                  <!-- :customRequest="uploadImage" -->
                  <div class="avatar" v-if="fileList2.length < 1" title="点击上传窗口图标">
                      <!-- <img v-if="imageUrl2!=''" :src="imageUrl2"  alt=""/>
                      <img v-else :src="tempImg2" alt=""> -->
                      <plus-outlined />
                  </div>
                 </a-upload>
                 <a-modal :visible="previewVisible2" :footer="null" @cancel="handleCancel(2)">
                    <img alt="example" style="width: 100%" :src="previewImage2" />
                </a-modal>
            </a-form-item>
            <a-form-item :wrapper-col="{style:'margin-left:200px'}">
                <a-button type="primary" @click="handleSave" v-if="$isShowBtn('sys:sysconfig:save')">保存</a-button>
            </a-form-item>
        </a-form>
    </div>
</template>
<script lang='ts' setup>
import { onMounted, reactive, ref } from 'vue';
// import tempImg2 from "../../../../../public/favicon.ico";
import {getSysconfigInfo, saveSysconfig} from "@/api/backend/systems/sysconfig";
import { deleteAttach, getcode, upload } from '@/api/backend/systems/user';
import { message } from 'ant-design-vue';
import { getToken } from '@/utils/auth';
import emiter from '@/utils/Bus';
const defaultform = {
    systemTitle:'UStack',
    shortTitle:'UStack',
    loginTitle:'UStack有栈后台管理系统',
    openCaptcha:false,
    loginLogoAttachmentId:'',
    faviconAttachmentId:'',
    openLoginFailCheck:false,
    failNumber:3
}
const sysform = reactive({
    systemTitle:'UStack',
    shortTitle:'UStack',
    loginTitle:'UStack有栈后台管理系统',
    openCaptcha:false,
    loginLogoAttachmentId:'',
    faviconAttachmentId:'',
    openLoginFailCheck:false,
    failNumber:3
})
const loginLogoAttachment = ref('');
const faviconAttachment = ref('');
const imageUrl1 = ref('');
const imageUrl2 = ref('');
const fileList1 = ref([]);
const fileList2 = ref([]);
const previewVisible = ref<boolean>(false);
const previewImage = ref<string | undefined>('');
const previewVisible2 = ref<boolean>(false);
const previewImage2 = ref<string | undefined>('');
function getBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}
const handleCancel = (num) => {
    if(num == 1)
        previewVisible.value = false;
    else
        previewVisible2.value = false;
};
const handlePreview = async (file,num) => {
    if (!file.url && !file.preview) {
        file.preview = (await getBase64(file.originFileObj)) as string;
    }
    if(num == 1){
        previewImage.value = file.url || file.preview;
        previewVisible.value = true;
    }else{
        previewImage2.value = file.url || file.preview;
        previewVisible2.value = true;
    }
    
};
const beforeUpload1=(file)=> {
       const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png' || file.type === 'image/webp';
         if (!isJpgOrPng) {
            message.error('只能上传jpg/jpeg/png/格式的图片!')
            return false
         }
         return true
    }
    const beforeUpload2=(file)=> {
       const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png' || file.type === 'image/webp' || file.type === 'image/x-icon';
       const isLt2M = file.size / 1024 / 1024 < 2;
         if (!isJpgOrPng) {
            message.error('只能上传jpg/jpeg/png/格式的图片!')
            return false
         }else if(!isLt2M){
             message.error('图片不得大于2MB!')
            return false
         }
         return true
    }
const customRequest1 = (data) => {
    const formData = new FormData();
    formData.append("file", data.file);
    formData.append("readType", 'localhost');
    formData.append("attachmentCode", loginLogoAttachment.value);
    upload(formData)
        .then(res => {
        if (res.code == 0) {
            message.success('上传成功');
            
            sysform.loginLogoAttachmentId = res.data.id;
            getLogoImg(res.data.id,fileList1)
        } else {
            message.success('上传失败');
        }
        })
};
const customRequest2 = (data) => {
    const formData = new FormData();
    formData.append("file", data.file);
    formData.append("readType", 'localhost');
    formData.append("attachmentCode", faviconAttachment.value);
    upload(formData)
        .then(res => {
        if (res.code == 0) {
            message.success('上传成功');
            sysform.faviconAttachmentId = res.data.id;
            getLogoImg(res.data.id,fileList2)
        } else {
            message.success('上传失败');
        }
        })
};
const removeAttachment1 = async () => {
    let res = await deleteAttach(sysform.loginLogoAttachmentId)
    if(res.code == 0){
        message.success('删除成功')
        fileList1.value = [];
        sysform.loginLogoAttachmentId = null;
    }else{
        message.error('删除失败')
    }
}
const removeAttachment2 = async () => {
    let res = await deleteAttach(sysform.faviconAttachmentId)
    if(res.code == 0){
        message.success('删除成功')
        fileList2.value = [];
        sysform.faviconAttachmentId = null;
    }
}
const getLogoImg = (id,value) => {
    // if(id){
        const token = getToken();
        if(id){
        let res1 = import.meta.env.VITE_BASE_API+'/sys/attachment/download?id='+id+'&token='+token;
            value.value = [{url:res1}];
        }
    //   }
}
const getloginLogoAttachment = async () => {
    let res = await getcode();
    if(res.code){
        loginLogoAttachment.value = res.code;
    }
}
const getfaviconAttachment = async () => {
    let res = await getcode();
    if(res.code){
        faviconAttachment.value = res.code;
    }
}
const handleSave = async () => {
    console.log('sysform',sysform)
    let sysform1 = {...sysform}
    sysform1.openCaptcha = Number(sysform1.openCaptcha);
    if(!sysform1.openLoginFailCheck){
        sysform1.failNumber = null;
    }
    sysform1.openLoginFailCheck = Number(sysform1.openLoginFailCheck);
    let res = await saveSysconfig(sysform1)
    if(res.code == 0 && res.data !== false){
        message.success('保存成功')
    }else{
        message.error((!res.msg || res.msg == 'success') ? '保存失败' : res.msg)
        Object.assign(sysform,defaultform)
    }
}
const getInfo = async () => {
    let res = await getSysconfigInfo();
    if(res.code == 0){
        console.log('re',res)
        Object.assign(sysform,res.data)
        Object.assign(defaultform,res.data)
        sysform.openCaptcha = Boolean(sysform.openCaptcha);
        sysform.openLoginFailCheck = Boolean(sysform.openLoginFailCheck);
        if(res.data && res.data.loginLogoAttachmentId)
        getLogoImg(res.data.loginLogoAttachmentId,fileList1)
        if(res.data && res.data.faviconAttachmentId)
        getLogoImg(res.data.faviconAttachmentId,fileList2)
        // if(!res.data || (res.data && !res.data.loginLogoAttachmentId)){
            
            getloginLogoAttachment()
        // }
        // if(!res.data || (res.data && !res.data.faviconAttachmentId)){
            getfaviconAttachment()
        // }
    }
}
onMounted(() => {getInfo()})
</script>
<style lang='scss' scoped>
.contentPadding{padding: 40px;overflow-x: auto;}
// .avatar img{
//     // width: 102px;
//     height: 102px;
//  }
:deep(.ant-upload-list-picture-card-container){width: auto;height: auto;}
:deep(.ant-upload-list-picture-card .ant-upload-list-item){width: auto;}
</style>