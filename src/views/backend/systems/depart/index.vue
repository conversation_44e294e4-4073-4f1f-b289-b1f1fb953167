<template>
    <div class='contentPadding'>
        <a-transfer :showSelectAll="false" :showHeader="false">
            <template #children="{ direction, selectedKeys, onItemSelect }" class="left">
                <a-table v-if="direction === 'left'" :columns="columns" row-key="id" :data-source="treeData" :pagination="false" :showHeader="false" :expandedRowKeys="expandedRowKeys" @expand="expand"
                :customRow="rowClick"
                size="small"
                class="left-table"
                :row-class-name="activeRowClass"
                >
                    <template #action={record}>
                        <a-button type="link" title="新增部门" @click.stop="addDepart(record)">
                            <template #icon><plus-outlined /></template>
                        </a-button>
                        <a-button type="link" title="新增人员" @click.stop="userRef.handleAdd(record.id)">
                            <template #icon><user-add-outlined /></template>
                        </a-button>
                        <a-button type="link" title="修改" @click.stop="editDepart(record)">
                            <template #icon><form-outlined /></template>
                        </a-button>
                        <a-button type="link" title="删除" @click.stop="$handleDel([record.id], deleteDepart, getList)">
                            <template #icon><delete-outlined /></template>
                        </a-button>
                    </template>
                </a-table>
                <a-modal centered
                v-model:visible="info.isShow"
                :title="info.isAdd ? '新增部门' : '修改部门'"
                ok-text="提交"
                cancel-text="取消"
                @ok="save"
                @cancel="cancel"
                :maskClosable="false"
                :getContainer="modalBindNode"
                >
                    <a-form :model="departform" ref="readyformRef">
                        <a-form-item label="部门名称" name="depName">
                        <a-input v-model:value="departform.depName" />
                        </a-form-item>
                        <a-form-item label="所属部门" name="parentId">
                        <a-tree-select
                            v-model:value="departform.parentId"
                            style="width: 100%"
                            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                            :tree-data="treeData"
                            placeholder="请选择"
                            :replace-fields="replaceFields"
                            tree-default-expand-all
                            allowClear>
                        </a-tree-select>
                        </a-form-item>
                    </a-form>
                </a-modal>
                <UserTable ref="userRef" v-if="direction === 'right'" :treeData="treeData"  />
            </template>
        </a-transfer>
    </div>
</template>
<script lang='ts' setup>
import UserTable from "@/views/backend/systems/users/index.vue"
import { getCurrentInstance, onMounted, reactive, ref } from "vue";
import { listToTree } from "@/utils/tool";
import { getDepartList,saveDepart, updateDepart, deleteDepart } from "@/api/backend/systems/depart"
import {PlusOutlined,FormOutlined,DeleteOutlined,UserAddOutlined} from '@ant-design/icons-vue';
import { actionServer } from "@/api/backend/devops/server";
const { proxy }: any = getCurrentInstance();
const treeData = ref([]);
const expandedRowKeys = ref([]);
const readyformRef:any = ref(null)
const userRef:any = ref(null)
const departform = reactive({depName:'',parentId:1})
const info:any = reactive({isAdd:true,isShow:false})
const replaceFields = {
    title: 'depName',
    key: 'id',
    value: 'id'
};

const columns = [
  {dataIndex: 'depName', key: 'id', width:250,ellipsis:true },
  {dataIndex: 'action', key: 'id', slots: { customRender: 'action' }, width:160, className:'action-cell' },
];

const rules = reactive({
  depName: [{ pattern:/^([\u0021-\u007E]|[\u4E00-\u9FBF])*$/, message:"请输入合法字符（字母、数字和!"+'"'+"#$%&'()*+,-/:;<=>?@[\]^_`{|}~）", trigger: "change" }],
});

const activeRow = ref({});
const activeRowClass = (record) => {
    // console.log('re',record)
    return record.id === activeRow.value ? 'activeRowLight' : '';
}
const rowClick = (record) => {
    return {
        onClick: (e) => {
            console.log('e',e,record)
            userRef.value.getList(record.id);
            activeRow.value = record.id;
        }
    }
}
// 手动控制展开收起，添加部门时展开需要
const expand = (expanded, record) => {
    if(expanded)
    expandedRowKeys.value = Array.from(new Set([...expandedRowKeys.value,record.id]))
    else
    expandedRowKeys.value.splice(expandedRowKeys.value.indexOf(record.id), 1)
}

const getList = async () => {
    let res = await getDepartList();
    treeData.value=[]
    if(res){
        if(res.code == 0){
            listToTree(res.data,treeData.value)
        }
    }
}
const addDepart = (record:any) => {
    info.isAdd = true;
    info.isShow = true;
    departform.parentId = record.id;
}
const editDepart = (record:any) => {
    info.isAdd = false;
    info.isShow = true;
    Object.assign(departform,record)
}
const save = () => {
    proxy.$handleSave(readyformRef.value,saveDepart,updateDepart,{info},departform,()=>{getList();cancel()})
}
const cancel = () => {
    Object.assign(departform,{depName:'',parentId:1})
}
onMounted(() => {
    
    getList()
})
</script>
<style lang='scss' scoped>
:deep(.ant-transfer-list){
    padding: 0;
}
:deep(.ant-transfer-operation){
    // visibility: hidden;
    display: none;
}
:deep(.ant-transfer){
    height: 790px;
}
:deep(.ant-transfer-list-body){
    overflow-x: hidden;
    overflow-y: auto;
}
:deep(.ant-transfer-list-header){
    display: none;
}
.left-table{
    :deep(.ant-table-tbody > tr > td){
        border: none;
    }
}
:deep(.action-cell > button){
    visibility: hidden;
}
:deep(.ant-table-row:hover){
    cursor: pointer;
}
:deep(.ant-table-row:hover > .action-cell > button){
    visibility: visible;
}
.ant-table-wrapper:first-of-type{
    width: 410px;
}
// :deep(.ant-transfer){border-right: 1px solid #d9d9d9;}
:deep(.ant-transfer-list){border-radius: 0;}
:deep(.ant-transfer-customize-list .ant-table-wrapper .ant-table-small > .ant-table-content .ant-table-row:last-child td){
    border: none;
}
:deep(.activeRowLight){background-color: #e6f7ff;}
.contentPadding{min-width: auto;overflow-x: initial;}
</style>