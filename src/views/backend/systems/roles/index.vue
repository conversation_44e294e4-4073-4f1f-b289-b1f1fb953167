<template>
  <div class="uncloudRight">
    <div class="buttonPadding">
      <a-form layout="inline" :model="listQuery" class="searchform">
        <a-form-item label="角色名称">
          <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="listQuery.roleName" allowClear />
        </a-form-item>
      </a-form>
      <MoreSearch @search="handleSearch" @reset="handleAllReset" />
    </div>
    <div class="innerPadding">
      <a-row class="buttonGroup">
        <a-button type="primary" @click="handleAdd" v-if="isShowBtn('sys:role:save')"> 新增 </a-button>
      </a-row>
      <a-table :data-source="data" :pagination="pagination" row-key="roleId" @change="changeTable" :scroll="{x:true}">
        <a-table-column key="id" title="序号" align="center">
          <template #default={record,index}>
              {{index+1+(pagination.pageSize * (pagination.current-1))}}
          </template>
        </a-table-column>
        <a-table-column key="roleName" title="角色名称" data-index="roleName" align="center" />
        <a-table-column key="remark" title="备注" data-index="remark" align="center" />
        <a-table-column key="createTime" title="创建时间" data-index="createTime" align="center" />
        <a-table-column key="action" title="操作" :width="225" align="center">
          <template v-slot="scope">
            <span>
              <a-button type="primary" @click="handleShow(scope.record)" v-if="isShowBtn('sys:role:info')" class="button_V">
                {{ $t("m.show") }}
              </a-button>
              <a-button @click="handleEdit(scope.record)" v-if="isShowBtn('sys:role:update')" class="button_E">
                {{ $t("m.modify") }}
              </a-button>
              <a-button @click="$handleDel([scope.record.roleId],role_store.DeleteRole,getList)" v-if="isShowBtn('sys:role:delete')&&(scope.record.roleId!=1 && scope.record.roleId!=2)" class="button_D">
                {{ $t("m.del") }}
              </a-button>
            </span>
          </template>
        </a-table-column>
      </a-table>
      <!-- 添加、修改modal -->
      <a-modal centered :title="title" v-model:visible="showModal" :maskClosable="isShow" :centered="true" width="608.8px" :getContainer="modalBindNode">
        <template #footer>
            <a-button style="margin-left: 10px" @click="handleCancle" v-if="!isShow">{{ $t("m.cancel") }}</a-button>
        <a-button type="primary" @click="handleSave" v-if="!isShow">{{ $t("m.save") }}</a-button>
            <a-button type="primary" style="margin-left: 10px" @click="handleCancle" v-if="isShow">{{ $t("m.close") }}</a-button>
        </template>
        <a-form :labelCol="{ span: 4}" ref="addForm" :model="roleInfo" :rules="rules">
          <a-form-item label="角色名称" name="roleName">
            <a-input v-model:value="roleInfo.roleName" :disabled="isShow" allow-clear />
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-input v-model:value="roleInfo.remark" :disabled="isShow" allow-clear />
          </a-form-item>
          <a-form-item label="授权" name="pass">
            <a-tree
              v-model:checkedKeys="checkedKeys"
              checkable
              :disabled="isShow"
              :checkStrictly="checkStrictly"
              :selected-keys="selectedKeys"
              :tree-data="treeData"
              :defaultExpandAll="true"
              style="height:40vh;overflow:auto;"
              @check="onCheck"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>
<script lang="ts" setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { createVNode, nextTick } from "vue";
import { Modal, notification } from "ant-design-vue";
import { isShowBtn } from "@/utils/tool";
import { ref, reactive, onMounted, getCurrentInstance, watch } from "vue";
import { selectInfo } from "@/api/backend/systems/role";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { roleStore } from '@/store/role'
import { menuStore } from '@/store/menu'
import { userStore } from "@/store/user";
const user_store = userStore()
const role_store = roleStore()
const menu_store = menuStore()
const { proxy } = getCurrentInstance() as any;
//--------------------------------------------语法糖--------------------------------------------

const addForm: any = ref();
const title: any = ref("");
const validateRoleName: any = async (rule: any, value: any) => {
  if (value === "" || value == undefined) {
    return Promise.reject("请输入角色名称");
  } else if (value.length > 60) {
    return Promise.reject("用户名限制60个字符");
  } else if (value.trim().length == 0) {
    return Promise.reject("角色名称不能为空");
  } else {
    return Promise.resolve();
  }
};
const rules: any = ref({
  roleName: [{ required: true, validator: validateRoleName, trigger: "blur" }]
});
const showModal: any = ref(false); //添加、修改modal的flag
const listQuery: any = ref({
  pageSize: 10,
  pageIndex: 1,
  roleName: ""
});
const roleInfo: any = ref({
  roleId: "",
  remark: "",
  roleName: "",
  menuIdList: ""
}); //角色信息对象
const data: any = ref([]); //列表数据
const loading: any = ref(false);
const selectRowIds: any = ref(""); //被选中都行
const isShow: any = ref(false); //是否查看
const roleList: any = ref([]);
const pagination: any = ref({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["5", "10", "30"] // 指定每页可以显示多少条
});
// /////////////////角色变量//////////////////////////
const treeData: any = ref([]);
const selectedKeys: any = ref([]); //选择的树节点
const checkedKeys: any = ref([]); //双向绑定的树节点选择项
const apiSelectKey: any = ref([]); //向后台传送的选中节点，主要是为了，选择子节点也要将跟节点一起传给后台

// 列表;
const getList: any = async () => {
  data.value = await proxy.$getList(loading, role_store.SelectRolePage, listQuery.value, pagination.value, getList)
};

// 对返回的树数据进行整理;
const toTree: any = (data: any) => {
  //没有父节点的数据
  const parents: any = data.filter((value: any) => value.parentId === 0);
  //有父节点的数据
  const children: any = data.filter((value: any) => value.parentId !== 0);

  //定义转换方法的具体实现
  const translator = (parents: any, children: any) => {
    //遍历父节点数据
    parents.forEach((parent: any) => {
      parent.key = parent.menuId;
      parent.title = parent.name;
      //遍历子节点数据
      children.forEach((current: any, index: number) => {
        current.key = current.menuId;
        current.title = current.name;
        //此时找到父节点对应的一个子节点
        if (current.parentId === parent.menuId) {
          //对子节点数据进行深复制，这里只支持部分类型的数据深复制，对深复制不了解的童靴可以先去了解下深复制
          const temp: any = JSON.parse(JSON.stringify(children));
          //让当前子节点从temp中移除，temp作为新的子节点数据，这里是为了让递归时，子节点的遍历次数更少，如果父子关系的层级越多，越有利
          temp.splice(index, 1);
          //让当前子节点作为唯一的父节点，去递归查找其对应的子节点
          translator([current], temp);
          //把找到子节点放入父节点的children属性中
          typeof parent.children !== "undefined" ? parent.children.push(current) : (parent.children = [current]);
        }
      });
    });
  };

  //调用转换方法
  translator(parents, children);

  //返回最终的结果
  return parents;
};
const getMenuList: any = () => {
  menu_store.SelectAllMenuList()
    .then((res: any) => {
      if (res.code === 0) {
        treeData.value = toTree(res.data);
      }
    })
};
// 选择复选框的动作  点击复选框
const onCheck = (selectedKeys: any, node: any) => {
  checkedKeys.value = selectedKeys;

  // 有被选中的菜单项
  if (node.halfCheckedKeys.length > 0) {
    apiSelectKey.value = [...selectedKeys, ...node.halfCheckedKeys];
  } else {
    apiSelectKey.value = [...selectedKeys];
  }
};
// 情况添加数据
const temp: any = () => {
  roleInfo.value = {
    roleId: "",
    roleName: "",
    remark: "",
    menuIdList: ""
  };
};
// 获取所有角色列表
const getRoleList: any = () => {
  role_store.SelecRoletList()
    .then((res: any) => {
      if (res.code === 0) {
        roleList.value = res.data;
      }
    })
};
// 新增
const handleAdd: any = () => {
  title.value = "新增用户信息";
  checkedKeys.value = [];
  isShow.value = false;
  showModal.value = true;
  temp();
};
// 取消
const handleCancle: any = () => {
  showModal.value = false;
  addForm.value.resetFields();
  temp();
};
// 提交用户信息

const handleSave = async () => {
  // eslint-disable-next-line @typescript-eslint/no-this-alias
  const self: any = this;
  const obj: any = Object.assign(roleInfo.value);
  if (apiSelectKey.value.length === 0) {
    const objRes: any = await selectInfo({ id: obj.roleId });
    console.log("我执行了", objRes);
    obj.menuIdList = objRes.data.menuIdList;
  } else {
    obj.menuIdList = apiSelectKey.value;
  }
  proxy.$handleSave(addForm.value, role_store.SaveRole, role_store.UpdateRole, obj.roleId, roleInfo.value, ()=>{
    showModal.value = false;
    temp();
    getList();
  })
};
// 查看
const handleShow = (rowData: any) => {
  title.value = "角色信息";
  isShow.value = true;
  showModal.value = true;
  role_store.SelectInfo({ id: rowData.roleId })
    .then((res: any) => {
      if (res.code === 0) {
        checkedKeys.value = res.data.menuIdList;
        roleInfo.value = res.data;
      }
    })
};
const checkStrictly: any = ref(false);
// 修改
const handleEdit: any = (rowData: any) => {
  title.value = "修改角色信息";
  isShow.value = false;
  showModal.value = true;
  role_store.SelectInfo({ id: rowData.roleId })
    .then((res: any) => {
      if (res.code === 0) {
        checkedKeys.value = res.data.menuIdList;
        roleInfo.value = res.data;
      }
    })
};
// 查询
const handleSearch: any = () => {
  listQuery.value.pageIndex = "1";
  listQuery.value.pageSize = pagination.value.pageSize;
  getList();
};
const handleAllReset: any = () => {
  listQuery.value = {
    pageSize: 10,
    pageIndex: 1,
    roleName: ""
  };
  // getList();
};

// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  listQuery.value.pageIndex = pagination.current;
  listQuery.value.pageSize = pagination.pageSize;
  getList();
};
onMounted(() => {
  
  getRoleList();
  getList();
  getMenuList();
  nextTick(()=>{
    handleWidth()
  })
});
</script>
<style scoped>
  </style>
