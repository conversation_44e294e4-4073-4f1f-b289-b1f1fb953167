<template>
    <div class="contentPadding">
            <a-space>
                <a-button @click="()=>handleTrack()">开启追踪</a-button>
                <a-button @click="handleStopTrack">停止追踪</a-button>
                <a-button @click="handleDownload">下载日志</a-button>
                <a-select placeholder="日志类型" style="width:92px" v-model:value="searchform.type" :options="typeOptions" @select="()=>handleTrack(true)"></a-select>
                <a-select placeholder="追踪长度" style="width:88px" v-model:value="searchform.lines" :options="lengthOptions" @select="handleSearch"></a-select>
                <a-button @click="handleClear">清除日志</a-button>
            </a-space>
            <a-typography-paragraph style="height: calc(100% - 46px);">
                <pre style="height:100%"><p id="logtext" v-html="logText"></p></pre>
            </a-typography-paragraph>
    </div>
</template>
<script lang='ts' setup>
import { computed, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import { downloadText, termfind } from '@/utils/tool';
import $ from "jquery"
import { message } from 'ant-design-vue';
import { getToken } from '@/utils/auth';
const lengthOptions = ref([{value:'追踪长度',disabled:true},{value:50},{value:100},{value:500},{value:1000},{value:5000},{value:10000}])
const typeOptions = ref([{value:'日志类型',disabled:true},{value:'console'},{value:'debug'},{value:'error'},{value:'info'},{value:'warn'}])
const searchform = reactive({lines:100,type:'console'})
const ws = ref()
const term = ref()
const attachAddon = ref()
const fitAddon = ref()
const url = computed(()=>`ws://${location.host}/api/sys/logs/websocket/${searchform.type}/${searchform.lines}?token=${getToken()}`)
// const url = computed(()=>`ws://***********:8886/api/sys/logs/websocket/${searchform.type}/${searchform.lines}?token=${getToken()}`)
const logRawText = ref();
const logText = ref();
let timer = null;
const handleDownload = () => {
    downloadText('系统日志.html',logText.value);
}
const handleSearch = () => {
    if(searchform.lines){
        let index = termfind(logRawText.value,'<br/>',searchform.lines-1);
        if(index != -1)
            logText.value = logRawText.value.slice(index+5,logRawText.value.length)
        else{
            logText.value = logRawText.value;
        }
        setTimeout(()=>{
            $(`pre`).scrollTop($('#logtext').height())
        })
    }else{
        handleReset()
    }
}
const handleReset = () => {
    searchform.lines = undefined
    logText.value = logRawText.value;
    nextTick(()=>{
        $(`pre`).scrollTop($('#logtext').height())
    })
}
const startTrack = () => {
    console.log("readyState",ws.value.readyState)
    if (ws.value && ws.value.readyState == 3) {  // 网络断开
        initSocket(true)
    }
    if(!ws.value){
        initSocket(true)
    }
}
const handleTrack = (isChangeType) => {
    if(isChangeType){
        handleStopTrack();
        initSocket(true)
    }else{
        startTrack();
        timer = setInterval(() => {
            startTrack()
        }, 60000);
    }
    
}
const handleStopTrack = () => {
    if(ws.value)
        ws.value.close();
    if(timer){
        console.log("timer",timer)
        clearInterval(timer)
    }
}
const onSocketOpen = () => {
    ws.value.onopen = () => {
    console.log("连接成功");
    }
}
const onSocketMessage = (isStart) => {
    ws.value.onmessage = function (evt) { 
        var received_msg;
        if(evt.data){
            received_msg = evt.data;
            logRawText.value = logText.value + (logText.value.length > 0 ? '<br/>' : '') + evt.data;
            handleSearch()
        }
    };
}
const onSocketError = () => {
    ws.value.onerror = () => {
        console.log("连接失败")
    }
}
const onSocketClose = () => {
    ws.value.onclose = () => {
        console.log('close socket')
    }
}
const initSocket = (isStart) => {
    logText.value = '';
    ws.value = new WebSocket(url.value)
    onSocketOpen()
    onSocketMessage(isStart)
    onSocketError()
    onSocketClose()
}
const handleClear = () => {
    logText.value = '';
}
onMounted(() => {initSocket()})
onUnmounted(()=>{
    handleStopTrack();
})
</script>
<style lang='scss' scoped>
.contentPadding{height: calc(100vh - 96px);}
div.ant-typography{margin-bottom: 0;}
.ant-typography pre{margin-bottom: 0;}
</style>