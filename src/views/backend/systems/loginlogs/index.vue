<template>
  <div class="uncloudRight">
    <div class="buttonPadding">
      <a-form layout="inline" :model="listQuery" class="searchform">
        <a-form-item label="用户账号">
          <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="listQuery.userName" allowClear />
        </a-form-item>
      </a-form>
      <MoreSearch @search="handleSearch" @reset="resaSearch" />
    </div>
    <div class="innerPadding">
      <a-row class="buttonGroup">
        <a-button @click="handleDownload" v-if="$isShowBtn('sys:log:export')">导出日志</a-button>
      </a-row>
      <a-table :data-source="data" :pagination="pagination" row-key="id" :scroll="{ x: true }" @change="changeTable">
        <a-table-column key="id" title="序号" align="center">
          <template #default={record,index}>
              {{index+1+(pagination.pageSize * (pagination.current-1))}}
          </template>
        </a-table-column>
        <a-table-column key="username" title="用户账号" data-index="username" :ellipsis="true" align="center" />
        <a-table-column key="operation" title="用户操作" data-index="operation" :ellipsis="true" align="center" />
        <a-table-column key="method" title="请求方法" data-index="method" :ellipsis="true" align="center" />
        <!-- <a-table-column key="params" title="请求参数" data-index="params" :ellipsis="true" align="center" /> -->
        <a-table-column key="time" title="执行时长(毫秒)" data-index="time" align="center" />
        <a-table-column key="ip" title="IP地址" data-index="ip" align="center" />
        <a-table-column key="os" title="操作系统" data-index="os" align="center" />
        <a-table-column key="browser" title="浏览器" data-index="browser" align="center" />
        <a-table-column key="createDate" title="操作时间" data-index="createDate" align="center" />
      </a-table>
    </div>
  </div>
</template>
<script setup lang="ts">
import MoreSearch from "@/components/moresearch/moresearch.vue";
import { onMounted, reactive, ref, getCurrentInstance, nextTick } from "vue";
import { logStore } from "@/store/log"
import { getToken } from "@/utils/auth";
import { buttonExpand, handleWidth } from "@/utils/moreform";
const log_store = logStore()
const { proxy }: any = getCurrentInstance();
const listQuery: any = reactive({
  pageSize: 10,
  pageIndex: 1,
  userName: "",
  type:'login'
});
const loading = ref(false);
const data = ref([]); //列表数据
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["5", "10", "30"] // 指定每页可以显示多少条
});

// 列表
const getList: any = async () => {
  data.value = await proxy.$getList(loading, log_store.SelectLogList, listQuery, pagination, getList)
};
//重置查询
const resaSearch: any = () => {
  listQuery.userName = "";
  listQuery.pageIndex = "1";
  // getList();
};
// 查询
const handleSearch = () => {
  listQuery.pageIndex = "1";
  listQuery.pageSize = pagination.pageSize;
  getList();
};
const handleDownload = async () => {
  location.href=import.meta.env.VITE_BASE_API+"/sys/log/export?userName="+listQuery.userName+'&type='+listQuery.type+'&token='+getToken();
}
// 分页、序号、筛选变化时触发
const changeTable = (pagination: any, filters: any, sorter: any) => {
  listQuery.pageIndex = pagination.current;
  listQuery.pageSize = pagination.pageSize;
  getList();
};

onMounted(() => {
  getList();
  nextTick(()=>{
    handleWidth()
  })
});
</script>
<style scoped>
/* :deep(.ant-table th) { white-space: nowrap; }
:deep(.ant-table td) { white-space: nowrap; } */
</style>
