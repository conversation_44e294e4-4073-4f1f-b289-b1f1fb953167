<template>
    <div class=''>
      <a-modal centered v-model:visible="info.isShow" :title="info.isAdd ? addText : editText" @cancel="cancel" :maskClosable="false" style="width: 608.8px" :getContainer="modalBindNode">
        <template #footer>
            <a-button @click="cancel">取消</a-button  >
            <a-button type="primary" style="margin: 0 10px" @click="submit" v-if="isShowBtn('sys:sysdicttype:save')">提交</a-button >
        </template>
        <a-form :model="noticeform" ref="noticeform" :rules="rules1" :labelCol="{ span: 6}">
          <a-form-item name="dictType" label="字典类型">
            <a-input v-model:value="noticeform.dictType" :disabled="!info.isAdd"></a-input>
          </a-form-item >
          <a-form-item name="dictName" label="字典类型描述">
            <a-input v-model:value="noticeform.dictName"></a-input>
          </a-form-item >
          <a-form-item name="remark" label="备注">
            <a-textarea v-model:value="noticeform.remark"></a-textarea>
          </a-form-item >
          <a-form-item name="status" label="状态">
              <a-select v-model:value="noticeform.status" :getPopupContainer="triggerNode => triggerNode.parentNode">
                <a-select-option :value="0">禁用</a-select-option >
                <a-select-option :value="1">正常</a-select-option >
            </a-select >
          </a-form-item >
        </a-form  >
      </a-modal >
    </div>
</template>
<script lang="ts">
import { message } from 'ant-design-vue';
import { isShowBtn } from "@/utils/tool";
import {saveDicttype,updateDicttype} from "@/api/backend/systems/dictionary";
const checkProducts = async (rule, value) => {

};
export default {
    name: '',
    props: {
      info:{
        type:Object,
        default(){
          return {
            isAdd:true,
            isShow:false
          }
        }
      }
    },
    data() {
        return {
          modalBindNode:null,
          addText:'新增',
          editText:'修改',
          noticeform:{
            dictName:'',
            dictType:''
          },
          value3:[],
          rules1:{
            dictType: [{ required: true, message:'请填写数值', trigger: "blur"}],
            dictName: [{ required: true, message:'请填写数据释义', trigger: "blur" }],
          }
        };
    },
    computed: {},
    watch: {},
    methods: {
      isShowBtn,
      saveDicttype,
      updateDicttype,
      setinfo(data){
        this.noticeform = {...data};
      },
      cancel(){
        this.info.isShow = false;
        this.$refs.noticeform.resetFields()
        this.noticeform = {};
        this.value3=[]
      },
      submit(){
        this.$handleSave(this.$refs.noticeform, this.saveDicttype, this.updateDicttype, this, this.noticeform, ()=>{
          this.cancel()
          this.$emit('onSubmit2')
        })
      }
    },
    components: {},
    filters: {},
    mounted(){
      this.modalBindNode = document.getElementsByClassName('contentPadding')[0];
    }
};
</script>
<style lang='scss' scoped>
</style>