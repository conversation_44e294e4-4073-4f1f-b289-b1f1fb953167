<template>
    <div class='contentPadding' v-if="!info.isDictionary">
        <div style="min-width: 530px;">
            <a-row class="buttonGroup">
                <a-button type="primary" class="btnMargin" @click="toAdd" v-if="isShowBtn('sys:sysdictdata:save')"> 新增 </a-button>
            </a-row>
            <a-table :data-source="dictlist" row-key="id" :columns="columns" :pagination="pagination" @change="changeTable" >
                <template #index={record,index}>
                    {{index+1+(pagination.pageSize * (pagination.current-1))}}
                </template>
                <template #action="{ record }">
                    <span>
                        <a-button @click="handleType(record)" class="button_V" >字典数据</a-button>
                        <a-button @click="handleEdit(record)" class="button_E">修改</a-button>
                        <a-button @click="$handleDel([record.id],deleteDicttypeByid,getDicttypeList)" class="button_D">删除</a-button>
                    </span>
                </template>
            </a-table >
        </div>
        <Info :info="info" @onSubmit2="getDicttypeList" ref="dttpRef" />
        
    </div>
    <Dictionary :info="info" ref="dictionaryRef" v-else />
</template>
<script lang="ts">
import { deleteDicttypeByid, searchDicttypeList } from "@/api/backend/systems/dictionary";
import {isShowBtn} from "@/utils/tool";
import Info from "./info.vue";
import Dictionary from "../dictionary/index.vue";
import { message, Modal, notification } from 'ant-design-vue';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import router from '@/router';
import { info } from '@/api/backend/systems/user';
export default {
    name: '',
    props: {},
    data() {
        return {
            info:{isAdd:true,isShow:false,dictType:{},isDictionary:false},
            searchform:{pageIndex:1,pageSize:10},
            dictlist:[],
            columns: [
                {
                    title: '序号',
                    slots: { customRender: 'index' },
                    key: 'index',
                },
                {
                    title: '字典类型',
                    dataIndex: 'dictType',
                    key: 'id',
                },
                {
                    title: '字典类型描述',
                    dataIndex: 'dictName',
                    key: 'id',
                },
                {
                    title: '操作',
                    key: 'id',
                    slots: { customRender: 'action' },
                   
                    align: 'center'
                },
                
            ],
            pagination: {
                total: 0,
                current: 1,
                pageSize: 10, //每页中显示10条数据
                showSizeChanger: true,
                showQuickJumper: true,
                pageSizeOptions: ["5", "10", "30", "50"], //每页中显示的数据
                showTotal: (total) => `共有 ${total} 条数据` //分页中显示总的数据
            },
            loading:{value:false},
        };
    },
    mounted(){
        this.getDicttypeList()
    },
    computed: {},
    watch: {},
    methods: {
        isShowBtn,deleteDicttypeByid,
        async getDicttypeList(){
            this.dictlist = await this.$getList(this.loading, searchDicttypeList, this.searchform, this.pagination, this.getDicttypeList)
        },
        changeTable(pagination, filters, sorter){
            this.searchform.pageIndex = pagination.current;
            this.searchform.pageSize = pagination.pageSize;
            this.getDicttypeList();
        },
        toAdd(){
            this.info.isAdd = this.info.isShow = true;
        },
        //修改
        handleEdit(data){
            this.info.isAdd = false;
            this.info.isShow = true;
            this.$refs.dttpRef.setinfo(data);
        },
        handleType(rowData){
            // router.push({path:'/admin/systems/dictionary',query:{dictType:rowData.dictType}})
            // this.info.dictType = rowData;
            this.info.isDictionary = true;
            const _this = this;
            this.$nextTick(()=>{

                _this.$refs.dictionaryRef.getDictList(rowData.dictType)
                _this.$refs.dictionaryRef.dictType = rowData.dictType
            })
        }
    },
    components: {Info,Dictionary},
    filters: {}
};
</script>
<style lang='scss' scoped>
.contentPadding{overflow-x: auto;}
// :deep(.ant-spin-container){overflow-x: auto;}
.Buttonl{
width: 30px;
height: 22px;
line-height: 20px;
font-size:12px;
border:0;
margin: 0;
background: #1890FF;
color: #fff;
padding: 5px 5px;

}

.Buttonclass{
background: #ffb800;
color: white;
margin: 0 10px 0 10px;
width: 44px;
height: 22px;
line-height: 20px;
font-size:12px;
border: 0;
color: #fff;
padding: 5px 10px;

}
.Button_red{
  width: 44px;
height: 22px;
line-height: 20px;
font-size:12px;
border: 0;
padding: 0 5px;
background: #FF7875;
color: #fff;
padding: 5px 10px;
}
:deep(.ant-table th) { white-space: nowrap; }
:deep(.ant-table td) { white-space: nowrap; }
</style>