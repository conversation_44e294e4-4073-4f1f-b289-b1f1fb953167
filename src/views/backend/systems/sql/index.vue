<template>
    <div class=''>
        <form :action="url" id="ifForm" method="post" target="iframe"></form>
        <iframe name="iframe" frameborder="0"></iframe>
    </div>
</template>
<script lang='ts' setup>
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';
const url = useRoute().query.url
onMounted(() => {document.getElementById("ifForm").submit()})
</script>
<style lang='scss' scoped>
</style>