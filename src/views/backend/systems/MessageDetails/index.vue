<template>
    <div class="contentPadding">
        <div class="head">
            <h2>{{messageEntityList.noticeTitle}} </h2>
            <span style="color:#aaa">{{messageEntityList.createTime}}</span>
        </div>
        <div style="margin-top:20px;width:1000px;margin:0 auto">
            <a-divider></a-divider>
            <div style="font-size:14px;margin-top:20px;" v-html="messageEntityList.noticeMessage">
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import router from '@/router';
import { useRoute } from 'vue-router';
import { onMounted, reactive, ref, watch } from 'vue';
import { getMessageInfo } from "@/api/backend";
const route = useRoute();

const messageEntityList = ref([])
const getInfo = async () => {
    let res = await getMessageInfo({id:route.query.ticketId})
    if(res.code == 0){
        messageEntityList.value = res.data
    }
}
watch(()=>router.currentRoute.value,(to,from)=>{
    if(router.currentRoute.value.query.ticketId)
  getInfo()
},{immediate:true,deep:true})
onMounted(() => {})
</script>

<style lang="scss" scoped>
  .ready-part{
    padding:20px 150px;background-color:#fff;
    height: 100%;
}
:deep(.ant-descriptions-item-label){
    text-align: right;
}
:deep(.ant-descriptions-title){
    &::before{
        content: '';
        display: inline-block;
        width: 4px;
        height: 17px;
        margin-right: 6px;
        vertical-align: text-bottom;
        background-color: #1890ff;
    } 
} 
.head{
    text-align: center;
} 
</style>