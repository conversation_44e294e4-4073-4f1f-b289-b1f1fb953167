<template>
<div class="uncloudRight">
  <div class="buttonPadding">
    <a-form layout="inline" :model="listQuery" class="searchform">
      <a-form-item label="参数名称">
        <a-input placeholder="请输入参数名称" @pressEnter="getList()" v-model:value="listQuery.paramKey" allow-clear />
      </a-form-item>
    </a-form>
    <MoreSearch @search="getList" @reset="updateRest" />
  </div>
  <div class="innerPadding">
    <a-row class="buttonGroup">
      <a-button type="primary" class="btnMargin" @click="toAdd" v-if="isShowBtn('sys:config:save')">新增 </a-button>
      <a-button class="btnMargin" v-if="isShowBtn('sys:config:delete')" @click="$handleDel(selectRowIds,config_store.DeleteBatchConfig,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
        批量删除
      </a-button>
    </a-row>
    <a-table :row-selection="rowSelection" :data-source="articlelist" rowKey="id" :scroll="{ x: true }" :pagination="pagination" @change="changeList">
      <!-- <a-table :row-selection="{ onChange: rowSelection }" :data-source="articlelist" row-key="id" :pagination="pagination" @change="getList"> -->
      <a-table-column key="id" title="序号" align="center">
        <template #default={record,index}>
            {{index+1+(pagination.pageSize * (pagination.current-1))}}
        </template>
      </a-table-column>
      <a-table-column key="paramKey" title="参数名" data-index="paramKey" align="center" />
      <a-table-column key="paramValue" title="参数值" data-index="paramValue" align="center" :ellipsis="true" />
      <a-table-column key="remark" title="备注" data-index="remark" align="center" />
      <a-table-column key="action" title="操作" align="center" :width="225">
        <template v-slot="scope">
          <span>
            <a-button type="primary" @click="handleEdit(scope.record)" v-if="isShowBtn('sys:config:update')" class="button_E">
              {{ $t("m.edit") }}
            </a-button>
            <a-button type="primary" danger @click="$handleDel([scope.record.id],config_store.DeleteBatchConfig,getList)" v-if="isShowBtn('sys:config:delete')" class="button_D">
              {{ $t("m.del") }}
            </a-button>
          </span>
        </template>
      </a-table-column>
    </a-table>
    <!-- 添加、修改modal -->
    <a-modal 
    centered
     :title="title" 
     v-model:visible="showModal" 
     :maskClosable="false" 
     width="608.8px"
     ok-text="提交"
     cancel-text="取消"
     @ok="handleSave"
     @cancel="handleCancle"
     :getContainer="modalBindNode"
     >
      <a-form :labelCol="{ span: 3, offset: 0 }" :wrapperCol="{ span: 20, offset: 0 }" ref="addForm" :model="rowData" :rules="rules">
        <a-form-item label="参数名" name="paramKey">
          <a-input maxlength="60" v-model:value="rowData.paramKey" />
        </a-form-item>
        <a-form-item label="参数值" name="paramValue">
          <!-- <a-input v-model:value="rowData.paramValue" /> -->
          <a-textarea
            v-model:value="rowData.paramValue"
            :autosize="{ minRows: 2, maxRows: 6 }"
            :allowClear="true"
            :showCount="true"
            :maxlength="500"
          />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="rowData.remark" :autosize="{ minRows: 2, maxRows: 6 }" :allowClear="true" :showCount="true" :maxlength="500" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</div>
  
</template>
<script setup lang="ts">
import MoreSearch from "@/components/moresearch/moresearch.vue";
import { PlusOutlined, ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { createVNode, ref, reactive, computed, getCurrentInstance, onMounted, nextTick } from "vue";
import { Modal, notification, message } from "ant-design-vue";
import { isShowBtn } from "@/utils/tool";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { configStore } from '@/store/config'
const config_store = configStore()
const { proxy }: any = getCurrentInstance();
const validateParentId = async (rule: any, value: any) => {
  if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(value)) {
    return Promise.reject("只能以字母开头，并且只允许输入字母与数字");
  } else {
    return Promise.resolve();
  }
};
const checkTitle = async (rule: any, value: any) => {
  if (value.trim().length == 0) {
    return Promise.reject("参数值不能为空");
  } else {
    return Promise.resolve();
  }
};
const rules = reactive({
  paramKey: [{ required: true, validator: validateParentId, trigger: "blur" }],
  paramValue: [{ required: true, validator: checkTitle, trigger: "blur" }]
});

const loading = ref(false);
const showModal = ref(false);
const articlelist: any = ref([]);
let selectRowIds: string[] = ref([]);
let selectRowIdsBak: string[] = ref([]);
const addForm: any = ref(null);
const title = ref("");
let rowData = reactive({
  id: "",
  paramKey: "",
  paramValue: "",
  remark: ""
});
const listQuery: any = reactive({
  paramKey: "",
  pageSize: 10,
  pageIndex: 1
});
const pagination: any = reactive({
  total: 0,
  current: 1,
  pageSize: 10, //每页中显示10条数据
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ["5", "10", "30"], //每页中显示的数据
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
/**
 * pagination、filters、sorter、currentDataSource为  ant-design中分页所需的参数
 * btnType：不同按钮   0：查询、分页   1：重置
 */
const getList: any = async () => {
  articlelist.value = await proxy.$getList(loading, config_store.QueryPageConfig, listQuery, pagination, getList)
};
//重置
const updateRest: any = () => {
  listQuery.pageSize = 10;
  listQuery.paramKey = "";
  listQuery.pageIndex = "1";
  // getList();
};

// 分页、序号、筛选变化时触发
const changeList: any = (pagination: any, filters: any, sorter: any) => {
  listQuery.pageIndex = pagination.current;
  listQuery.pageSize = pagination.pageSize;
  getList();
};
const toAdd = () => {
  showModal.value = true;
  title.value = "添加信息";
  temp();
};
const temp = () => {
  rowData.id = "";
  rowData.paramKey = "";
  rowData.paramValue = "";
  rowData.remark = "";
};

const handleSave = () => {
  proxy.$handleSave(addForm.value, config_store.SaveConfig, config_store.UpdateConfig, rowData.id, rowData, ()=>{
    showModal.value = false;
    temp();
    getList();
  })
};
const handleEdit = (data: any) => {
  showModal.value = true;
  title.value = "修改信息";
    config_store.SelectInfoConfig({id:data.id})
    .then((res: any) => {
      if (res.code === 0) {
        rowData = Object.assign(rowData, res.data);
      }
    })
};

const handleCancle = () => {
  showModal.value = false;
  temp();
};

onMounted(() => {
  
  getList();
  nextTick(()=>{
      handleWidth()
  })
});
</script>
<style lang="scss" scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
</style>