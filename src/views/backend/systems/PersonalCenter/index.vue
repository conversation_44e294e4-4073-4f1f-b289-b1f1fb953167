<template>
  <div>
    <div class="User">
       <a-card size="small" title="个人中心" style="width: 30%;min-width:278px" class="card">
            <div class="Font_img">
                  <a-upload
                      name="file"
                      list-type="picture-card"
                      class="avatar-uploader"
                      :showUploadList="false"
                      :beforeUpload="beforeUpload"
                      :custom-request="function(){}"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      @change="handleUploadChange"
                  >
                  <!-- :customRequest="uploadImage" -->
                  <div class="avatar" title="点击上传头像">
                      <img v-if="imageUrl!=''" :src="imageUrl"  alt=""/>
                      <img v-else :src="tempImg" alt="">
                  </div>
                 </a-upload>
                 <!-- <a-button type="link" @click="updataImg" style=" text-align: center;">修改头像</a-button> -->
            </div>

       <!-- <a-upload
          name="file"
          list-type="picture-card"
          class="avatar-uploader"
          :showUploadList="false"
          :customRequest="uploadImage"
          :beforeUpload="beforeUpload"
          accept="image/jpeg,image/jpg,image/png"
        >
          <div class="avatar">
             <img v-if="imageUrl!='' " :src="imageUrl"  alt=""/>
             <img v-else src="@/assets/touxiang.jpg " alt="">
            </div>
        </a-upload> -->

       
   
                <p class="Fontcolor">用户名 : <span>{{Fromuser.userName}}</span> </p>
                <p class="Fontcolor">账号 : <span>{{Fromuser.loginName}}</span> </p>
                <p class="Fontcolor">手机号码 : <span>{{Fromuser.mobile}}</span></p>
                <p class="Fontcolor">邮箱地址 : <span>{{Fromuser.email}}</span></p>
              
                <p class="Fontcolor">创建时间 : <span>{{Fromuser.createTime}}</span></p>
      </a-card>


       <a-card size="small" title="基本资料" style="width:69%;min-width:445px" class="card">
        <a-tabs v-model:activeKey="activeKey">

            <a-tab-pane key="1" tab="基本资料"> <BasicDate @topersonal='topersonal'/></a-tab-pane>
            <a-tab-pane key="2" tab="修改密码" force-render><Passwords /></a-tab-pane>
                
        </a-tabs>
      </a-card>
      <cropper-modal @ok="handleOK" ref="cropperModal" />
 

 
    </div>
  </div>
    
</template>
<script  lang="ts" setup>
import CropperModal from "@/components/cropper/cropper.vue";
import { getCurrentInstance,defineComponent, onMounted, ref ,reactive,inject} from 'vue';
import BasicDate from './BasicDate.vue'
import Passwords from './Password.vue'
 import {getcode,upload,updateAvatar,download} from '@/api/backend/systems/user'
 import { message } from 'ant-design-vue';
 import { getToken } from "@/utils/auth";
 import {dataURLtoFile,dataURLtoBlob} from "@/utils/tool";
 import { indexStore } from "@/store/index"
 import tempImg from "@/assets/touxiang.jpg";
import { userStore } from '@/store/user';
import emiter from "@/utils/Bus";
 const index_store = indexStore()
 const user_store = userStore()
//  console.log("img",index_store.img)
 const { proxy } = getCurrentInstance();
const  activeKey=ref('1');
const imageUrl=ref(user_store.img)
const imageId=ref("")
const Fromuser = reactive({
                userName: '',
                mobile: '',
                email:'',
                createTime:''
             });
 const   dataimg=ref("") 
const cropperModal = ref()
const getUserList: any = () => {
  user_store.UserInfo().then((res: any) => {
    emiter.emit('allLoading',false)
      Object.assign(Fromuser, res.data)
      if(res.data.attachmentId){
        const token = getToken();
        let res1 = import.meta.env.VITE_BASE_API+'/sys/attachment/download?id='+res.data.attachmentId+'&token='+token;
        if(res1)
        imageUrl.value = res1
        else
        imageUrl.value = tempImg;
      }else
      imageUrl.value = tempImg;
    })
    .catch((err: any) => {});
};

  




const topersonal=(val)=>{
    
   Object.assign(Fromuser, val)
}

const handleUploadChange = (info) => {
  // 这个回调函数的参数是个File对象,所以要用FileReader将对象转换成 data-uri (base64) 对象,才能给vue-cropper
      var reader = new FileReader()
      reader.readAsDataURL(info.file.originFileObj)
      // 当文件处理完成的回调函数
      reader.onload = function (e) {
        // e.target.result 该属性表示目标对象的DataURL
        // 然后调用cropperModal的edit方法使得对话框可见
        let data 
        if (typeof e.target.result === 'object') {
          const blob = new Blob([e.target.result])
          data = window.URL.createObjectURL(blob)
        } else {
          data = e.target.result
        }
        cropperModal.value.edit(data)
      }
     // 记录原始的文件名字,用于DataURL(base64)转成file对象,不转就可以不用
      // this.tmpImgName = info.file.name
     // 文件处理 file=>base64 
     console.log('info.file',info.file)
      
}

const beforeUpload=(file)=> {
     
    // return  new Promise((resolve,reject)=>{
       const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png' || file.type === 'image/webp';
       const isLt2M = file.size / 1024 / 1024 < 2;
         if (!isJpgOrPng) {
            message.error('只能上传jpg/jpeg/png/格式的头像!')
            // return reject(false)
            return false
         }else if(!isLt2M){
             message.error('图片不得大于2MB!')
            //  reject(false)
            return false
         }
         return true
          //  return resolve(true)
        // return reject(false)
      // })
      // if (!isJpgOrPng) {
      //   message.error('只能上传jpg/png格式的头像!')
      //   console.log('只能上传jpg/png格式的头像!')
      // }
      // const isLt2M = file.size / 1024 / 1024 < 2
      // if (!isLt2M) {
      //   message.error('图片不得大于2MB!')
      //     console.log('图片不得大于2MB!')
      // }
      // return isJpgOrPng && isLt2M

      // console.log('修改的状态',file)
    }

const handleOK = (data) => {
  uploadImage(dataURLtoFile(data))
}

const  uploadImage= async (file)=> {
  console.log("上传的值",file)


  let coderes = await getcode()
  if(coderes.code){
    const formData = new FormData();
    formData.append("file",file);
    formData.append("attachmentCode",coderes.code);
    formData.append("readType", "localhost");
    let upres = await upload(formData)
    if(upres.code == 0){
      updataImg(upres.data.id)
      
      // imageId.value=res.data.id
    }
  }
    }


const updataImg= async (attachmentId)=>{
  let res = await updateAvatar({attachmentId})
  if(res.code == 0){
    const token = getToken();
    imageUrl.value = import.meta.env.VITE_BASE_API+'/sys/attachment/download?id='+attachmentId+'&token='+token;
    proxy.$mitt.emit('setAvatar',imageUrl.value)
    user_store.set_img(imageUrl.value)
  }
  // .then((res)=>{
  //   //  index_store.img=imageUrl.value;
  //   //  location.reload()
  //   //    imageUrl.value=index_store.img
  //   // console.log("上传的值", index_store.img)
  
     
  // })
}
        
onMounted(()=>{getUserList()})

         
       

  
   
</script>


<style lang='scss' scoped>
.User{
    display: flex;
    justify-content: space-between;
    margin: 0 16px;
    overflow-x:auto
}

.Font_img {
  margin: 5.5px 0;
  display: flex;
  flex-direction:column;
  justify-content: center;
}
.Font_img span{
   display: flex;
    justify-content: center;
}
.Font_img button{
  margin-bottom: 30px;
}
.Font_img img{
    width: 90px;
    height: 90px;
    border-radius:50% ;
}
.Fontcolor{
    display: flex;
    justify-content: space-between;
    font-weight:500 ;
    color: #717578;
}
.a-upload{
  width: 90px;
    height: 90px;
    border-radius:50% ;
}
 .avatar img{
    width: 90px;
    height: 90px;
    border-radius:50% ;
 }   
 :deep(.ant-card-small > .ant-card-body){
  padding: 12px 24px;
 }
</style>