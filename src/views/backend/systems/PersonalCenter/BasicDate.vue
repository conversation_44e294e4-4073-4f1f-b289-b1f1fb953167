<template>
    <div><br>
   <a-form
    ref="formRef"
    :model="formState"
    :rules="rules"  
    :label-col="{ width:'100px' }"
    :wrapper-col="{ span: 19,offset:0 }"
    size="large"
  >
           

            <a-form-item  label="手机号码"  class="Formitem" name="mobile">
               <a-input v-model:value="formState.mobile"/>
            </a-form-item>
            <a-form-item  label="邮箱地址" class="Formitem" name="email">
                <a-input v-model:value="formState.email"/>
            </a-form-item>
            <a-form-item :wrapper-col="{ offset:2 }">
                <a-button type="primary" @click="onSubmit()">提交</a-button>
            </a-form-item>
    </a-form>
    </div>
</template>
<script  lang="ts" setup>

import { defineComponent, onMounted, ref ,reactive,toRaw, getCurrentInstance,provide} from 'vue';

 import {updateUser,saveUser} from '@/api/backend/systems/user'
 const { proxy } = getCurrentInstance();
 import useUseraStore from '@/store/PersonalCenter'
import router from '@/router';
import { userStore } from '@/store/user';
 const emit = defineEmits(['topersonal'])
const formRef = ref();
const user_store = userStore();
const formState = reactive({
                userName: '',
                mobile: '',
                email:'',
    })
 const validateEmail: any = async (rule: any, value: any) => {
  const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  if (value === "") {
    return Promise.reject("请输入邮箱");
  } else if (!reg.test(value)) {
    return Promise.reject("邮箱格式不正确");
  } else {
    return Promise.resolve();
  }
};
const validateMobile: any = async (rule: any, value: any) => {
  const reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/;
  if (value === "") {
    return Promise.reject("请输入手机号");
  } else if (!reg.test(value)) {
    return Promise.reject("手机号格式不正确");
  } else {
    return Promise.resolve();
  }
};          
const rules = {
           
            email: [{ required: true, validator: validateEmail, trigger: "change" }],
            mobile: [{ required: true, validator: validateMobile, trigger: "change" }],
      }
const getUserList: any = () => {
           
       user_store.UserInfo().then((res: any) => {
               Object.assign(formState, res.data)
       })
       .catch((err: any) => {});
};

    const onSubmit = () => {
          formState.id = formState.userId;
          proxy.$handleSave(formRef.value, saveUser, updateUser, formState.id, formState, ()=>{
                emit('topersonal',formState)
            })
    
    };


     onMounted(()=>{getUserList()})


  
   
</script>


<style lang='scss' scoped>
.Formitem{
   padding-left:30px ;
}
</style>