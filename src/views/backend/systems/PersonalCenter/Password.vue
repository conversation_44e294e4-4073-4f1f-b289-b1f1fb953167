<template>
    <div><br>
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ width:'100px' }"
    :wrapper-col="{ span: 19,offset:0 }"   
    >
            <a-form-item  label="原始密码" class="Formitem" name="password">
                <a-input v-model:value="formState.password" type="password" class="inputmitem"/>
            </a-form-item>

            <a-form-item  label="新 密 码 "  class="Formitem" name="newPassword">
               <a-input v-model:value="formState.newPassword" type="password"  class="itemright"/>
            </a-form-item>
              <a-form-item  label="确认密码"  class="Formitem" name="isnewPassword" >
               <a-input v-model:value="formState.isnewPassword" type="password" class="inputmitem"/>
            </a-form-item>
           
            <a-form-item :wrapper-col="{ offset:2 }">
                <a-button type="primary" @click="onSubmit()">提交</a-button>
            </a-form-item>
    </a-form>
    </div>
</template>

<script  lang="ts" setup>

import { defineComponent, onMounted, ref ,reactive,toRaw, getCurrentInstance,provide} from 'vue';
import * as Sha256 from "js-sha256"; 
 import {password} from '@/api/backend/systems/user'
import { userStore } from '@/store/user';
 const { proxy } = getCurrentInstance();



        
      const formRef = ref();
        const user_store = userStore();
         const formState = ref({
                password: '',
                newPassword: '',
                isnewPassword:''
             })
             const userState = ref({
                password: '',
                newPassword: '',
               
             })
             
             
const validatepass: any = async (rule: any, value: any) => {
  if (value === undefined || value === '') {
    return Promise.reject("请输入密码");
  }
    const pwdRegex = new RegExp("(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{6,16}$");
    if (value === "") {
      return Promise.reject("请输入密码");
    } else if (value.length < 6 || value.length > 18 || !pwdRegex.test(value)) {
      return Promise.reject("密码复杂度太低（密码中必须包含字母、数字、特殊字符,长度为6-18位），请重新设置密码！");
    } else {
      if (formState.value.isnewPassword !== '') {
          formRef.value.validateFields('isnewPassword');
        }
      return Promise.resolve();
    }
};
const validateIspass: any = async (rule: any, value: any, callback: any) => {
  if (value === "" || value === undefined) {
    return Promise.reject("请输入确认密码");
  } else if (value !== formState.value.newPassword) {
    return Promise.reject("确认密码与密码不一致");
  } else {
      return Promise.resolve();
  }
};
const rules = {
        password: [
                {
                required: true,
                message: '请输入密码',
                trigger: 'blur',
                }
            ], 
        newPassword:[{ required: true, validator: validatepass, trigger: "change" }],
        isnewPassword: [{required: true, validator: validateIspass, trigger: "change" }],
    
}

 const onSubmit = () => {
       
          let sha256 = Sha256.sha256
          userState.value.password=sha256(formState.value.password)
          userState.value.newPassword=sha256(formState.value.newPassword)

         proxy.$handleSave(formRef.value, {}, password, true, userState.value)
};


</script>


<style lang='scss' scoped>
.Formitem{
   padding-left:30px ;
}
.itemright{
   margin-left:5px ;
   width: 98.5%;
}
.inputmitem{
   width: 99%;
}
</style>