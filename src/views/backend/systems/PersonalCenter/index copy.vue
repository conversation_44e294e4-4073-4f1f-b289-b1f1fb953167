<template>
    <div class="User">
       <a-card size="small" title="个人中心" style="width: 30%" class="card">
            <div class="Font_img">
                  <a-upload
                      name="file"
                      list-type="picture-card"
                      class="avatar-uploader"
                      :showUploadList="false"
                      :customRequest="uploadImage"
                      :beforeUpload="beforeUpload"
                      accept="image/jpeg,image/jpg,image/png"
                  >
                  <div class="avatar" title="点击上传头像">
                      <img v-if="imageUrl!=''" :src="imageUrl"  alt=""/>
                      <img v-else :src="tempImg" alt="">
                  </div>
                 </a-upload>
                 <!-- <a-button type="link" @click="updataImg" style=" text-align: center;">修改头像</a-button> -->
            </div>

       <!-- <a-upload
          name="file"
          list-type="picture-card"
          class="avatar-uploader"
          :showUploadList="false"
          :customRequest="uploadImage"
          :beforeUpload="beforeUpload"
          accept="image/jpeg,image/jpg,image/png"
        >
          <div class="avatar">
             <img v-if="imageUrl!='' " :src="imageUrl"  alt=""/>
             <img v-else src="@/assets/touxiang.jpg " alt="">
            </div>
        </a-upload> -->

       
   
                <p class="Fontcolor">用户名 : <span>{{Fromuser.userName}}</span> </p>
                <p class="Fontcolor">账号 : <span>{{Fromuser.loginName}}</span> </p>
                <p class="Fontcolor">手机号码 : <span>{{Fromuser.mobile}}</span></p>
                <p class="Fontcolor">邮箱地址 : <span>{{Fromuser.email}}</span></p>
              
                <p class="Fontcolor">创建时间 : <span>{{Fromuser.createTime}}</span></p>
      </a-card>


       <a-card size="small" title="基本资料" style="width:69%" class="card">
        <a-tabs v-model:activeKey="activeKey">

            <a-tab-pane key="1" tab="基本资料"> <BasicDate @topersonal='topersonal'/></a-tab-pane>
            <a-tab-pane key="2" tab="修改密码" force-render><Passwords /></a-tab-pane>
                
        </a-tabs>
      </a-card>
      <!-- <cropper-modal @ok="handleOK" ref="cropperModal" /> -->
 

 
    </div>
</template>
<script  lang="ts" setup>
import CropperModal from "@/components/cropper/cropper.vue";
import { getCurrentInstance,defineComponent, onMounted, ref ,reactive,inject} from 'vue';
import BasicDate from './BasicDate.vue'
import Passwords from './Password.vue'
 import {getcode,upload,updateAvatar,download} from '@/api/backend/systems/user'
 import { message } from 'ant-design-vue';
 import { getToken } from "@/utils/auth";
 import { indexStore } from "@/store/index"
 import tempImg from "@/assets/touxiang.jpg";
import { userStore } from '@/store/user';
 const index_store = indexStore()
 const user_store = userStore()
//  console.log("img",index_store.img)
 const { proxy } = getCurrentInstance();
const  activeKey=ref('1');
const imageUrl=ref(user_store.img)
const imageId=ref("")
const Fromuser = reactive({
                userName: '',
                mobile: '',
                email:'',
                createTime:''
             });
 const   dataimg=ref("") 

const getUserList: any = () => {
  user_store.UserInfo().then((res: any) => {
      Object.assign(Fromuser, res.data)
      if(res.data.attachmentId){
        const token = getToken();
        let res1 = import.meta.env.VITE_BASE_API+'/sys/attachment/download?id='+res.data.attachmentId+'&token='+token;
        if(res1)
        imageUrl.value = res1
        else
        imageUrl.value = tempImg;
      }else
      imageUrl.value = tempImg;
    })
    .catch((err: any) => {});
};

  




const topersonal=(val)=>{
    
   Object.assign(Fromuser, val)
}

const beforeUpload=(file)=> {
     
    return  new Promise((resolve,reject)=>{
       const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png'
       const isLt2M = file.size / 1024 / 1024 < 2
         if (!isJpgOrPng) {
            message.error('只能上传jpg/png格式的头像!')
            return reject(false)
         }else if(!isLt2M){
             message.error('图片不得大于2MB!')
             reject(false)
         }
           return resolve(true)
        
      })
      // if (!isJpgOrPng) {
      //   message.error('只能上传jpg/png格式的头像!')
      //   console.log('只能上传jpg/png格式的头像!')
      // }
      // const isLt2M = file.size / 1024 / 1024 < 2
      // if (!isLt2M) {
      //   message.error('图片不得大于2MB!')
      //     console.log('图片不得大于2MB!')
      // }
      // return isJpgOrPng && isLt2M

      // console.log('修改的状态',file)
    }



const  uploadImage= async (file)=> {
  console.log("上传的值",file.file)


  let coderes = await getcode()
  if(coderes.code){
    const formData = new FormData();
    formData.append("file",file.file);
    formData.append("attachmentCode",coderes.code);
    formData.append("readType", "localhost");
    let upres = await upload(formData)
    if(upres.code == 0){
      updataImg(upres.data.id)
      
      // imageId.value=res.data.id
    }
  }
    }


const updataImg= async (attachmentId)=>{
  let res = await updateAvatar({attachmentId})
  if(res.code == 0){
    const token = getToken();
    imageUrl.value = import.meta.env.VITE_BASE_API+'/sys/attachment/download?id='+attachmentId+'&token='+token;
    proxy.$mitt.emit('setAvatar',imageUrl.value)
    user_store.set_img(imageUrl.value)
  }
  // .then((res)=>{
  //   //  index_store.img=imageUrl.value;
  //   //  location.reload()
  //   //    imageUrl.value=index_store.img
  //   // console.log("上传的值", index_store.img)
  
     
  // })
}
        
onMounted(()=>{getUserList()})

         
       

  
   
</script>


<style lang='scss' scoped>
.User{
    display: flex;
    justify-content: space-between;
    margin: 0 16px;
}

.Font_img {
  margin: 5.5px 0;
  display: flex;
  flex-direction:column;
  justify-content: center;
}
.Font_img span{
   display: flex;
    justify-content: center;
}
.Font_img button{
  margin-bottom: 30px;
}
.Font_img img{
    width: 90px;
    height: 90px;
    border-radius:50% ;
}
.Fontcolor{
    display: flex;
    justify-content: space-between;
    font-weight:500 ;
    color: #717578;
}
.a-upload{
  width: 90px;
    height: 90px;
    border-radius:50% ;
}
 .avatar img{
    width: 90px;
    height: 90px;
    object-fit: cover;
    border-radius:50% ;
 }   
 :deep(.ant-card-small > .ant-card-body){
  padding: 12px 24px;
 }
</style>