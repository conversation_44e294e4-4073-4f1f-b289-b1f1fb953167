<template>
    <!-- <div class=''> -->
        <!-- cancel-text=" " ok-text="关闭" -->
        <a-modal title="OSDs管理" v-model:visible="info.isOSD" @cancel="cancel" width="100% !important" wrap-class-name="full-modal" :footer="null" centered :getContainer="modalBindNodeTable">
            <!-- <a-row>
                <a-button type="primary" @click="addOSD"><PlusOutlined />新增</a-button>
            </a-row><br> -->
            <a-table :columns="columns" :data-source="osdlist" row-key="id" :loading="loading" @change="changeTable">
              <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                <div style="padding: 8px">
                  <a-input
                    ref="searchInput"
                    :placeholder="`搜索 ${column.title}`"
                    :value="selectedKeys[0]"
                    style="width: 188px; margin-bottom: 8px; display: block"
                    @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                    @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
                  />
                  <a-button
                    type="primary"
                    size="small"
                    style="width: 90px; margin-right: 8px"
                    @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
                  >
                    <template #icon><SearchOutlined /></template>
                    搜索
                  </a-button>
                  <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                    重置
                  </a-button>
                </div>
              </template>
              <template #filterIcon="filtered">
                <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
              </template>
              <template #customRender="{ record, column }">
                <span v-if="searchText && searchedColumn === column.dataIndex">
                  <template
                    v-for="(fragment, i) in record.host.name
                      .toString()
                      .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
                  >
                    <mark
                      v-if="fragment.toLowerCase() === searchText.toLowerCase()"
                      class="highlight"
                      :key="i"
                    >
                      {{ fragment }}
                    </mark>
                    <template v-else>{{ fragment }}</template>
                  </template>
                </span>
                <template v-else>
                  {{ record.host.name }}
                </template>
              </template>
              <template #state="{record}">
                <template v-for="(item,index) in record" :key="index">
                  <a-tag :color="states.colors[states.keys.indexOf(index)]" v-if="states.keys.includes(index) && item > 0">{{index}}</a-tag>
                </template>
              </template>
              <template #tree="{record}">
                <a-tag>{{record.tree.device_class}}</a-tag>
              </template>
              <template #numpg="{record}">
                {{record.stats.numpg}}
              </template>
              <template #stat_bytes="{record}">
                {{$ByteFormat(record.stats.stat_bytes,'i','str')}}
              </template>
              <template #usage="{record}">
                <a-progress :percent="Number((record.stats.stat_bytes_used*100/record.stats.stat_bytes).toFixed())" />
                <!-- <a-tag>{{(record.stats.stat_bytes_used / record.stats.stat_bytes).toFixed(2)}}%</a-tag> -->
              </template>
              <template #readBytes>
                <div class="rd_chart_osd"></div>
              </template>
              <template #writeBytes>
                <div class="wr_chart_osd"></div>
              </template>
              <template #readOps="{record}">
                {{record.stats.op_r.toFixed(1)}} /s
              </template>
              <template #writeOps="{record}">
                {{record.stats.op_w.toFixed(1)}} /s
              </template>
              <!-- <template #action="{record}">
                <a-button class="button_E" @click="editOSD(record)">修改</a-button>
                <a-button class="button_D" @click="delOSD(record.svc_id)">删除</a-button>
              </template> -->
            </a-table>
            <a-modal title="修改OSD" v-model:visible="osdeditinfo.isEdit" @ok="editSubmit" @cancel="cancelEdit" :maskClosable="false" centered :getContainer="modalBindNodeEdit">
              <a-form :model="editform" ref="editForm">
                <a-form-item label="Device class" name="device_class" :rules="{required: true,message: '请输入'}">
                  <a-input v-model:value="editform.device_class"></a-input>
                </a-form-item>
              </a-form>
            </a-modal>
        </a-modal>
    <!-- </div> -->
    <Info ref="osdInfoDialog" :info="osdeditinfo" @getlist="setInfo" />
</template>
<script lang='ts' setup>
import axios from "axios";
import { delOSDAPI, editOSDAPI } from '@/api/backend/ceph/ceph_origin';
import { getOSDList } from '@/api/backend/ceph/ceph';
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import Info from "./info.vue";
import { message } from "ant-design-vue";
import * as echarts from "echarts"
import router from "@/router";
const {proxy} = getCurrentInstance()
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isOSD:false
            }
        }
    }
})
const modalBindNodeTable = ref();
const modalBindNodeEdit = ref();
const searchInput = ref()
const searchText = ref()
const searchedColumn = ref()
const osdeditinfo = reactive({isShow:false,isEdit:false}) 
const osdInfoDialog = ref()
const editForm = ref()
const states = {keys:['in','up','down','out'],colors:['#87d068','#87d068','#f50','#f50']};
const loading = ref(false)
const osdlist = ref([])
const columns = [
  {title:'ID',dataIndex:'id'},
  {title:'主机',dataIndex:'host',key:'host',slots: {
        filterDropdown: 'filterDropdown',
        filterIcon: 'filterIcon',
        customRender: 'customRender',
      },
      onFilter: (value, record) =>
        record.host.name.toString().toLowerCase().includes(value.toLowerCase()),
      onFilterDropdownVisibleChange: visible => {
        if (visible) {
          setTimeout(() => {
            searchInput.value.focus();
          }, 100);
        }
      },
    },
  {title:'状态',dataIndex:'state',slots:{customRender:'state'}},
  {title:'设备类别',dataIndex:'tree',slots:{customRender:'tree'}},
  {title:'PGs',dataIndex:'stats',slots:{customRender:'numpg'}},
  {title:'大小',dataIndex:'stats',slots:{customRender:'stat_bytes'}},
  {title:'使用率',dataIndex:'stats',slots:{customRender:'usage'}},
  {title:'读取字节',dataIndex:'readBytes',slots:{customRender:'readBytes'}},
  {title:'写入字节',dataIndex:'writeBytes',slots:{customRender:'writeBytes'}},
  {title:'读取操作',dataIndex:'stats',slots:{customRender:'readOps'}},
  {title:'写入操作',dataIndex:'stats',slots:{customRender:'writeOps'}},
  // {title:'操作',dataIndex:'action',slots:{customRender:'action'}},
];
const editform = reactive({device_class:'',svc_id:''})
const handleSearch = (selectedKeys, confirm, dataIndex) => {
  confirm();
  searchText.value = selectedKeys[0];
  searchedColumn.value = dataIndex;
};
const handleReset = clearFilters => {
  clearFilters();
  searchText.value = '';
};
const addOSD = () => {
  osdeditinfo.isShow = true
}
const delOSD = (svc_id) => {
  proxy.$handleDel(svc_id,delOSDAPI,setInfo)
}
const editOSD = (record) => {
  osdeditinfo.isEdit = true;
  editform.svc_id = record.osd;
  editform.device_class = record.tree.device_class;
  // osdeditinfo.isAdd = false;
  // osdeditinfo.isShow = true;
  // proxy.$nextTick(()=>{
  //   osdInfoDialog.value.setInfo(record)
  // })
}
const cancelEdit = () => {
  osdeditinfo.isEdit = false;
  editform.svc_id = '';
  editform.device_class = '';
  editForm.value.resetFields()
}
const editSubmit = () => {
  // editform.svc_id = 
  editForm.value.validate().then(async ()=>{
    let res = await editOSDAPI(editform.svc_id,{device_class:editform.device_class})
    if(!res){
      message.success('操作成功')
      cancelEdit()
    }else{
      message.error('修改失败')
    }
  })
  
}
const changeTable = (pagination, filters, sorter, { currentDataSource }) => {
  nextTick(()=>{
    beginInterval(pagination)
    init(osdlist.value,pagination)
  })
}
const setInfo = async (isLoop,pagination) => {
  loading.value = !isLoop;
  let res = await getOSDList({cloudId:router.currentRoute.value.query.cloudId});
  loading.value = false;
  if(res.data){
    osdlist.value = JSON.parse(res.data);
    setTimeout(()=>{
      if(!isLoop){
        beginInterval({current:1,pageSize:10})
        init(osdlist.value,pagination)
      }else{
        loop(osdlist.value,pagination)
      }
    })
  }
  // await axios({
  //   url:'https://10.10.15.34:8443/api/osd',
  //   method: "GET",
  //   params:{},
  //   headers:{},
  //   timeout:{},
  // })
}
var myChart1;
var myChart2;
const option1 = [];
const option2 = [];
var timerOSD = null;
const beginInterval = (pagination) => {
  clearInterval(timerOSD)
  timerOSD = setInterval(()=>{
    setInfo(true,pagination);
  },60000)
}
const loop = (osds,pagination) => {
  osds.forEach((item,index)=>{
    if(index >= (pagination.current - 1) * pagination.pageSize && index < pagination.current * pagination.pageSize){
      let formatIndex = index - (pagination.current - 1) * pagination.pageSize;
      let sdata = [];
      let xdata = [];
      item.stats_history.op_out_bytes.forEach((t,i)=>{
        sdata.push(t[1]);
        xdata.push(t[0]);
      })
      option1[index].xAxis.data = xdata;
      option1[index].series[0].data = sdata;
      myChart1[formatIndex].setOption(option1[index]);
      let sdata1 = [];
      let xdata1 = [];
      item.stats_history.op_in_bytes.forEach((t,i)=>{
        sdata1.push(t[1]);
        xdata1.push(t[0]);
      })
      option2[index].xAxis.data = xdata1;
      option2[index].series[0].data = sdata1;
      myChart2[formatIndex].setOption(option2[index]);
    }
  })
}
const init = (osds,pagination) => {
  myChart1 = [];
  myChart2 = [];
  osds.forEach((item,index)=>{
    if(index >= (pagination.current - 1) * pagination.pageSize && index < pagination.current * pagination.pageSize){
      let formatIndex = index - (pagination.current - 1) * pagination.pageSize;
      myChart1.push(undefined)
      let sdata = [];
      let xdata = [];
      item.stats_history.op_out_bytes.forEach((t,i)=>{
        sdata.push(t[1]);
        xdata.push(t[0]);
      })
      option1.push({
        xAxis: {
          type: 'category',
          data: [],
          show:false
        },
        yAxis: {
          type: 'value',
          show:false,
          // splitNumber:10
        },
        grid: {
          top:0,
          // left: '40%',
          bottom:0,
          // boundaryGap: true
          // containLabel: true
        },
        tooltip:{
          show:true,
          formatter: '{c} B'
        },
        series: [
          {
            data: [],
            type: 'line',
            symbolSize: 2,
            areaStyle:'#e6f7ff',
            lineStyle:{width:1}
          }
        ]
      })
      if (document.getElementsByClassName('rd_chart_osd')[formatIndex] == null) {
        return
      }
      echarts.dispose(document.getElementsByClassName('rd_chart_osd')[formatIndex])
      myChart1[formatIndex]=echarts.init(document.getElementsByClassName('rd_chart_osd')[formatIndex]);
      option1[index].xAxis.data = xdata;
      option1[index].series[0].data = sdata;
      myChart1[formatIndex].setOption(option1[index]);
      myChart2.push(undefined)
      let sdata1 = [];
      let xdata1 = [];
      item.stats_history.op_in_bytes.forEach((t,i)=>{
        sdata1.push(t[1]);
        xdata1.push(t[0]);
      })
      option2.push({
        xAxis: {
          type: 'category',
          data: [],
          show:false
        },
        yAxis: {
          type: 'value',
          show:false,
          
        },
        grid: {
          top:0,
          // left: '-3%',
          bottom:0,
          // containLabel: true
        },
        tooltip:{
          show:true,
          formatter: '{c} B'
        },
        series: [
          {
            data: [],
            type: 'line',
            symbolSize: 2,
            areaStyle:'#e6f7ff',
            lineStyle:{width:1}
          }
        ]
      })
      if (document.getElementsByClassName('wr_chart_osd')[formatIndex] == null) {
        return
      }
      echarts.dispose(document.getElementsByClassName('wr_chart_osd')[formatIndex])
      myChart2[formatIndex]=echarts.init(document.getElementsByClassName('wr_chart_osd')[formatIndex]);
      option2[index].xAxis.data = xdata1;
      option2[index].series[0].data = sdata1;
      myChart2[formatIndex].setOption(option2[index]);
      window.addEventListener('resize',function(){
        myChart2[formatIndex].resize()
        myChart1[formatIndex].resize()
      })
    }
    
  })
}
const cancel = () => {
  clearInterval(timerOSD)
}
onMounted(() => {
  modalBindNodeTable.value = document.getElementsByClassName('layoutContent')[0];
  modalBindNodeEdit.value = document.getElementsByClassName('layoutContent')[1];
})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
.rd_chart_osd{width:150px;height: 23px;margin-right: -47px;}
.wr_chart_osd{width:150px;height: 23px;margin-right: -47px;}
.full-modal {
  .ant-modal-body {
    flex: 1;
  }
}
</style>