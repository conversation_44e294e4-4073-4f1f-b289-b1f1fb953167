<template>
    <div class=''>
        <a-modal title="新增OSD" v-model:visible="info.isShow" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" v-if="activeKey[0] == '2'" @click="preview">预览</a-button>
                <a-button type="primary" v-if="activeKey[0] == '1'" @click="createOSD">提交</a-button>
            </template>
            <a-collapse v-model:activeKey="activeKey" @change="changeCollapse" ghost>
                <a-collapse-panel key="1" header="开发选项">
                    <a-radio-group v-model:value="osdform.data.option">
                        <a-radio value="cost_capacity" label="Cost/Capacity-optimized deployment">Cost/Capacity-optimized (Recommended) </a-radio><br>
                        <a-radio value="Throughput-optimized" disabled label="Throughput-optimized">Throughput-optimized</a-radio><br>
                        <a-radio value="IOPS-optimized" disabled label="IOPS-optimized">IOPS-optimized</a-radio>
                    </a-radio-group>
                </a-collapse-panel>
                <a-collapse-panel key="2" header="高级模式">
                <!-- <a-form-item label="常规设备"> -->
                    <a-select v-model:value="advanform.data.data_devices.rotational" placeholder="Type" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear style="width:100px;margin-right:10px">
                        <a-select-option value="hdd">hdd</a-select-option>
                    </a-select>
                    <a-select v-model:value="advanform.data.data_devices.vendor" placeholder="Vendor" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear style="width:100px;margin-right:10px">
                        <a-select-option value="0x1af4">0x1af4</a-select-option>
                    </a-select>
                    <a-select v-model:value="advanform.data.data_devices.size" placeholder="Size" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear style="width:100px">
                        <a-select-option value="20GB">20 GiB</a-select-option>
                    </a-select>
                <!-- </a-form-item>
                <a-form-item label="WAL设备(共享)">
                    
                </a-form-item>
                <a-form-item label="DB设备(共享)">
                    
                </a-form-item> -->
                </a-collapse-panel>
                <a-collapse-panel key="3" header="特性" disabled>
                    <a-checkbox v-model:checked="osdform.data.encrypted" >Encryption</a-checkbox>
                </a-collapse-panel>
            </a-collapse>
            <!-- <a-form :model="osdform" :label-col="{span:5}">
                <a-form-item label="开发选项">
                    <a-radio-group v-model:value="osdform.option">
                        <a-radio value="Cost/Capacity-optimized">Cost/Capacity-optimized (Recommended) </a-radio>
                        <a-radio value="Throughput-optimized">Throughput-optimized</a-radio><br>
                        <a-radio value="IOPS-optimized">IOPS-optimized</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="常规设备">
                    <a-select placeholder="Type">
                        <a-select-option>hdd</a-select-option>
                    </a-select>
                    <a-select placeholder="Vendor">
                        <a-select-option>0x1af4</a-select-option>
                    </a-select>
                    <a-select placeholder="Size">
                        <a-select-option>20 GiB</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="WAL设备(共享)">
                    
                </a-form-item>
                <a-form-item label="DB设备(共享)">
                    
                </a-form-item>
                <a-form-item label="Features">
                    <a-checkbox-group v-model:value="osdform.features" name="checkboxgroup" :options="featuresList" />
                </a-form-item>
            </a-form> -->
        </a-modal>
        <Preview ref="preDialog" :info="preinfo" @cancel="cancel" />
    </div>
</template>
<script lang='ts' setup>
import Preview from "./preview.vue";
import { addOSDAPI } from '@/api/backend/ceph/ceph_origin';
import { message, Modal } from 'ant-design-vue';
import { createVNode, getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
import router from "@/router";
const {proxy} = getCurrentInstance()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
            }
        }
    }
})

const preinfo = reactive({
    isShow:false
})
const preDialog = ref();
const activeKey = ref(['1','3']);
const osdform = reactive({
    method:'predefined',
    data:{option:'cost_capacity',encrypted:false},
    tracking_id:'Cost/Capacity-optimized deployment',
    cloudId:router.currentRoute.value.query.cloudId
})
const defaultadvan = {
    method:'drive_groups',
    data:{
        data_devices:{rotational:undefined,vendor:undefined,size:undefined},
        host_pattern:'*',
        service_id:'dashboard-xyh-1679292737781',
        service_type:'osd',
        encrypted:false
    },
    tracking_id:'dashboard-xyh-1679292737781',
}
const advanform = reactive({
    method:'drive_groups',
    data:{
        data_devices:{rotational:undefined,vendor:undefined,size:undefined},
        host_pattern:'*',
        service_id:'dashboard-xyh-1679292737781',
        service_type:'osd',
        encrypted:false
    },
    tracking_id:'dashboard-xyh-1679292737781',
})
// const featuresList = ref(['1','2','3'])
// const primaryList = ref(['1','2','3'])
// const walList = ref(['1','2','3'])
// const dbList = ref(['1','2','3'])
const state = reactive({
    inputVisible1: false,
    inputVisible2: false,
    inputVisible3: false,
    primaryList:['1','2','3'],
    walList:['1','2','3'],
    dbList:['1','2','3'],

})
const {primaryList,walList,dbList,inputVisible1,inputVisible2,inputVisible3} = toRefs(state)
const inputRef1 = ref()
const inputRef2 = ref()
const inputRef3 = ref()
// const inputVisible1 = ref(false)
// const inputVisible2 = ref(false)
// const inputVisible3 = ref(false)
const changeCollapse = (key) => {
    console.log('key',key)
    if(key[2] == 1){
        activeKey.value = [1,3];
    }
    if(key[2] == 2){
        activeKey.value = [2,3];
    }
}
// const changeOption = (value, option) => {
//     console.log('option.label',option)
//     // if(value == 'cost_capacity'){
//     //     osdform.tracking_id = option.label;
//     // }
// }
const handleClose = (removedTag: string,value) => {
    const tags = value.filter(tag => tag !== removedTag);
    console.log(tags);
    value = tags;
};
const handleInputConfirm = (value,value1,value2) => {
    const inputValue = osdform[value2];
    let tags = state[value1];
    if (inputValue && tags.indexOf(inputValue) === -1) {
        tags = [...tags, inputValue];
    }
    console.log(tags);
    state[value] = false;
    state[value1] = tags;
    osdform[value2] = '';
    // Object.assign(state, {
    //     tags,
    //     inputVisible: false,
    //     inputValue: '',
    // });
};
const showInput = (value,value1) => {
    if(value1 == 'inputRef1'){
        state[value] = true;
        proxy.$nextTick(() => {
            inputRef1.value.focus();
        })
    }
    if(value1 == 'inputRef2'){
        state[value] = true;
        proxy.$nextTick(() => {
            inputRef2.value.focus();
        })
    }
    if(value1 == 'inputRef3'){
        state[value] = true;
        proxy.$nextTick(() => {
            inputRef3.value.focus();
        })
    }
};
const preview = () => {
    let rotational = false;
    let vendor = false;
    let size = false;
    let encrypted = false;
    if(osdform.data.encrypted){
        advanform.data.encrypted = osdform.data.encrypted;
        encrypted = true;
    }else{
        advanform.data.encrypted = undefined;
    }
    preinfo.isShow = true;
    proxy.$nextTick(()=>{
        preDialog.value.setInfo(advanform)
    })
    // Modal.confirm({
    //     title: () => 'OSD创建预览',
    //     content: () => createVNode('pre', {},
    //     `[
    //         {
    //             "service_type": "osd",
    //             "service_id": "dashboard-xyh-1679293907747",
    //             "host_pattern": "*",
    //             "data_devices": {
    //                 ${rotational ? '"rotational": true' : ''}
    //                 ${vendor ? `"vendor": ${advanform.data.data_devices.vendor}` : ''}
    //                 ${size ? `"size": ${advanform.data.data_devices.size}` : ''}
    //             },
    //             ${encrypted ? `"encrypted": true` : ''}
    //         }
    //     ]`),
    //     onOk() {
    //       console.log('OK');
    //     },
    //     onCancel() {
    //       console.log('Cancel');
    //     },
    //     class: 'test',
    //   });
}
const cancel = () => {
    Object.assign(advanform, defaultadvan)
    osdform.data.encrypted = false;
    props.info.isShow = false;
}
const createOSD = async () => {
    let osdform1 = {...osdform};
    if(osdform.data.option == 'cost_capacity'){
        osdform1.tracking_id = 'Cost/Capacity-optimized deployment';
    }
    osdform1.data = [osdform.data];
    let res = await addOSDAPI(osdform1);
    if(!res){
        message.success('操作成功');
        cancel()
        emit('getlist')
    }
    // console.log('res',res)
}
const setInfo = (data) => {
    Object.assign(osdform,data)
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>