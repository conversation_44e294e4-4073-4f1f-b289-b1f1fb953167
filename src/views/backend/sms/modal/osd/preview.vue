<!-- 本页请勿格式化，指<pre>部分-->
<template>
    <div class=''>
        <a-modal title="OSD新增预览" v-model:visible="info.isShow" @ok="createOSD" @cancel="cancel" :maskClosable="true" centered>
            <a-typography-paragraph >
                <div v-html="
                `<pre>
[
    {
        'service_type': 'osd',
        'service_id': 'dashboard-xyh-1679293907747',
        'host_pattern': '*',
        'data_devices': {${advanform.data.data_devices.rotational ? `
            'rotational': true` : ``}${advanform.data.data_devices.vendor ? `,
            'vendor': ${advanform.data.data_devices.vendor}` : ``}${advanform.data.data_devices.size ? `,
            'size': ${advanform.data.data_devices.size}` : ``}
        }${advanform.data.encrypted ? `,
        'encrypted': true` : ``}
    }
]
                </pre>`">
            </div>
            </a-typography-paragraph>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { addOSDAPI } from '@/api/backend/ceph/ceph_origin';
import router from '@/router';
import { message } from 'ant-design-vue';
import { onMounted, reactive } from 'vue';
const emit = defineEmits(['cancel'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false
            }
        }
    }
})
const advanform = reactive({
    method:'drive_groups',
    data:{
        data_devices:{rotational:false,vendor:'',size:''},
        host_pattern:'',
        service_id:'dashboard-xyh-1679292737781',
        service_type:'',
        encrypted:false
    },
    tracking_id:'dashboard-xyh-1679292737781',
})
const cancel = () => {
    props.info.isShow = false;
    emit('cancel');
}
const createOSD = async () => {
    let advanform1 = {data:[{data_devices:{}}],cloudId:router.currentRoute.value.query.cloudId};
    if(advanform.data.data_devices.rotational){
        advanform1.data[0].data_devices.rotational = true;
    }
    if(advanform.data.data_devices.vendor){
        advanform1.data[0].data_devices.vendor = advanform.data.data_devices.vendor;
    }
    if(advanform.data.data_devices.size){
        advanform1.data[0].data_devices.size = advanform.data.data_devices.size;
    }
    if(advanform.data.encrypted){
        advanform1.data[0].encrypted = advanform.data.encrypted;
    }
    if(advanform.data.host_pattern){
        advanform1.data[0].host_pattern = advanform.data.host_pattern;
    }
    if(advanform.data.service_id){
        advanform1.data[0].service_id = advanform.data.service_id;
    }
    if(advanform.data.service_type){
        advanform1.data[0].service_type = advanform.data.service_type;
    }
    advanform1.method = advanform.method;
    advanform1.tracking_id = advanform.tracking_id;
    // osdform1.data = [osdform.data];
    let res = await addOSDAPI(advanform1);
    if(!res){
        message.success('操作成功');
        cancel();
    }
    // console.log('res',res)
}
const setInfo = (data) => {
    Object.assign(advanform,data)
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>