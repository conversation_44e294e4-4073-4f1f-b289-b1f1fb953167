<template>
    <div class=''>
        <a-modal :title="info.isAdd?'新增主机':'修改主机'" v-model:visible="info.isShow" @cancel="cancel" @ok="addHost" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form :model="hostform" :label-col="{span:4}" ref="hostForm">
                <a-form-item label="主机名称" name="hostname" v-if="info.isAdd" :rules="[{ required: true, message: '请输入主机名称' }]">
                    <a-input v-model:value="hostform.hostname" placeholder="mon-123"></a-input>
                </a-form-item>
                <a-form-item label="网络地址" v-if="info.isAdd">
                    <a-input v-model:value="hostform.addr" placeholder="***********"></a-input>
                </a-form-item>
                <a-form-item label="标签">
                    <a-select mode="multiple" v-model:value="hostform.labels" :options="labelOptions" @search="searchLabels" :search-value="labelinputText" :getPopupContainer="triggerNode => triggerNode.parentNode" placeholder="输入手动添加新标签">
                        <template #dropdownRender="{ menuNode: menu }">
                            <v-nodes :vnodes="menu" />
                            <a-divider style="margin: 4px 0" />
                            <div
                                v-if="labelinputText && !labelOptions.some(item => item.value == labelinputText)"
                                style="padding: 4px 8px; cursor: pointer"
                                @mousedown="e => e.preventDefault()"
                                @click="addLabelItem(labelinputText)"
                            >
                                <plus-outlined />
                                新增“{{labelinputText}}”标签
                            </div>
                        </template>
                    </a-select>
                </a-form-item>
                <a-form-item label="Status" v-if="info.isAdd">
                    <a-checkbox-group v-model:value="hostform.status" :options="[{label:'Maintenance Mode',value:'maintenance'}]"></a-checkbox-group>
                </a-form-item>
                
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { addHostAPI, editHostAPI } from '@/api/backend/ceph/ceph_origin';
import router from '@/router';
import { message } from 'ant-design-vue';
import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
const VNodes = (_, { attrs }) => {
    return attrs.vnodes;
}
const {proxy} = getCurrentInstance()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})

const hostForm = ref()
const defaultform = {
    hostname:'',
    addr:'',
    labels:[],
    status:[''],
    cloudId:router.currentRoute.value.query.cloudId
}
const hostform = reactive({
    hostname:'',
    addr:'',
    labels:[],
    status:[''],
    cloudId:router.currentRoute.value.query.cloudId
})
// const featuresList = ref(['1','2','3'])
// const primaryList = ref(['1','2','3'])
// const walList = ref(['1','2','3'])
// const dbList = ref(['1','2','3'])
const state = reactive({
    inputVisible1: false,
    inputVisible2: false,
    inputVisible3: false,
    featuresList:['1','2','3'],
    primaryList:['1','2','3'],
    walList:['1','2','3'],
    dbList:['1','2','3'],

})
const {featuresList,primaryList,walList,dbList,inputVisible1,inputVisible2,inputVisible3} = toRefs(state)
const inputRef1 = ref()
const inputRef2 = ref()
const inputRef3 = ref()
const labelinputText = ref('')
const labelOptions = ref([{value:'_admin'},{value:'grafana'},{value:'iscsi'},{value:'mds'},{value:'mgr'},{value:'mon'},{value:'nfs'},{value:'osd'},{value:'rbd'},{value:'rgw'}])
// const inputVisible1 = ref(false)
// const inputVisible2 = ref(false)
// const inputVisible3 = ref(false)
const searchLabels = (value) => {
    labelinputText.value = value;
}
const addLabelItem = (text) => {
    labelOptions.value.push({value:text});
    hostform.labels.push(text)
    labelinputText.value = ''
}
const handleClose = (removedTag: string,value) => {
    const tags = value.filter(tag => tag !== removedTag);
    console.log(tags);
    value = tags;
};
const handleInputConfirm = (value,value1,value2) => {
    const inputValue = hostform[value2];
    let tags = state[value1];
    if (inputValue && tags.indexOf(inputValue) === -1) {
        tags = [...tags, inputValue];
    }
    console.log(tags);
    state[value] = false;
    state[value1] = tags;
    hostform[value2] = '';
    // Object.assign(state, {
    //     tags,
    //     inputVisible: false,
    //     inputValue: '',
    // });
};
const showInput = (value,value1) => {
    if(value1 == 'inputRef1'){
        state[value] = true;
        proxy.$nextTick(() => {
            inputRef1.value.focus();
        })
    }
    if(value1 == 'inputRef2'){
        state[value] = true;
        proxy.$nextTick(() => {
            inputRef2.value.focus();
        })
    }
    if(value1 == 'inputRef3'){
        state[value] = true;
        proxy.$nextTick(() => {
            inputRef3.value.focus();
        })
    }
};
const cancel = () => {
    Object.assign(hostform, defaultform)
    props.info.isShow = false;
    hostForm.value.resetFields();
}
const addHost = () => {
    console.log('hostform',hostform)
    hostForm.value.validate().then(async ()=>{
        let hostform1 = {...hostform}
        hostform1.status = hostform.status[0];
        let res;
        if(props.info.isAdd)
            res = await addHostAPI(hostform1)
        else
            res = await editHostAPI(hostform1.hostname,{labels:hostform1.labels})
        if(!res){
            console.log('add',res)
            message.success(props.info.isAdd ? '添加成功' : '操作成功');
            cancel();
            emit('getlist');
        }
    })
    
}
const setInfo = (data) => {
    Object.assign(hostform,data)
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>