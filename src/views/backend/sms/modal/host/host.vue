<template>
    <!-- <div class=''> -->
        <!-- cancel-text=" " ok-text="关闭" -->
        <a-modal title="主机管理" v-model:visible="info.isHost" @cancel="cancel" width="100% !important" wrap-class-name="full-modal" :footer="null" centered :getContainer="modalBindNode">
            <!-- <a-row>
                <a-button type="primary" @click="addHost"><PlusOutlined />新增</a-button>
            </a-row><br> -->
            <a-table :columns="columns" :data-source="hostlist" row-key="hostname" :loading="loading">
              <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                <div style="padding: 8px">
                  <a-input
                    ref="searchInput"
                    :placeholder="`搜索 ${column.title}`"
                    :value="selectedKeys[0]"
                    style="width: 188px; margin-bottom: 8px; display: block"
                    @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
                    @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
                  />
                  <a-button
                    type="primary"
                    size="small"
                    style="width: 90px; margin-right: 8px"
                    @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
                  >
                    <template #icon><SearchOutlined /></template>
                    搜索
                  </a-button>
                  <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                    重置
                  </a-button>
                </div>
              </template>
              <template #filterIcon="filtered">
                <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
              </template>
              <template #customRender="{ text, column }">
                <span v-if="searchText && searchedColumn === column.dataIndex">
                  <template
                    v-for="(fragment, i) in text
                      .toString()
                      .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
                  >
                    <mark
                      v-if="fragment.toLowerCase() === searchText.toLowerCase()"
                      class="highlight"
                      :key="i"
                    >
                      {{ fragment }}
                    </mark>
                    <template v-else>{{ fragment }}</template>
                  </template>
                </span>
                <template v-else>
                  {{ text }}
                </template>
              </template>
              <template #instance="{record}">
                <a-tag color="#108ee9" v-for="(item,index) in record.instance" :key="index">{{item}}</a-tag>
              </template>
              <template #labels="{record}">
                <a-tag v-for="(item,index) in record.labels" :key="index">{{item}}</a-tag>
              </template>
              <template #memory_total_kb="{record}">
                {{$ByteFormat(record.memory_total_kb * 1024,'i','str')}}
              </template>
              <template #ceph_version="{record}">
                {{record.ceph_version?.split(' ')[2]}}
              </template>
              <!-- <template #action="{record}">
                <a-button class="button_E" @click="editHost(record)">修改</a-button>
                <a-button class="button_D" @click="delHost(record.hostname)">删除</a-button>
              </template> -->
            </a-table>
        </a-modal>
    <!-- </div> -->
    <Info ref="hostInfoDialog" :info="hosteditinfo" @getlist="setInfo" />
</template>
<script lang='ts' setup>
import { getHostList } from '@/api/backend/ceph/ceph';
import router from '@/router';
import { message } from 'ant-design-vue';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import Info from "./info.vue";
const {proxy} = getCurrentInstance()
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isHost:false
            }
        }
    }
})
const loading = ref(false)
const searchInput = ref()
const searchText = ref('')
const searchedColumn = ref()
const hosteditinfo = reactive({isShow:false,isAdd:true})
const hostInfoDialog = ref()
const hostlist = ref([])
const columns = [
    {title:'主机名称',dataIndex:'hostname',key:'hostname',slots: {
          filterDropdown: 'filterDropdown',
          filterIcon: 'filterIcon',
          customRender: 'customRender',
        },
        onFilter: (value, record) =>
          record.hostname.toString().toLowerCase().includes(value.toLowerCase()),
        onFilterDropdownVisibleChange: visible => {
          if (visible) {
            setTimeout(() => {
              console.log(searchInput.value);
              searchInput.value.focus();
            }, 100);
          }
        },
      },
    {title:'服务虚机',dataIndex:'instance',slots:{customRender:'instance'}},
    {title:'标签',dataIndex:'labels',slots:{customRender:'labels'}},
    {title:'状态',dataIndex:'status'},
    {title:'Model',dataIndex:'model'},
    {title:'CPUs',dataIndex:'cpu_count'},
    {title:'Cores',dataIndex:'cpu_cores'},
    {title:'Total Memory',dataIndex:'memory_total_kb',slots:{customRender:'memory_total_kb'}},
    {title:'Raw Capacity',dataIndex:'hdd_capacity'},
    {title:'HDDs',dataIndex:'hdd_count'},
    {title:'Flash',dataIndex:'flash_count'},
    {title:'NICs',dataIndex:'nic_count'},
    // {title:'版本',dataIndex:'ceph_version',slots:{customRender:'ceph_version'}},
    // {title:'操作',dataIndex:'action',slots:{customRender:'action'}},
];
const handleSearch = (selectedKeys, confirm, dataIndex) => {
  confirm();
  searchText.value = selectedKeys[0];
  searchedColumn.value = dataIndex;
};
const handleReset = clearFilters => {
  clearFilters();
  searchText.value = '';
};
const addHost = () => {
  hosteditinfo.isShow = true
  hosteditinfo.isAdd = true
}
const editHost = (record) => {
  hosteditinfo.isAdd = false;
  hosteditinfo.isShow = true;
  proxy.$nextTick(()=>{
    hostInfoDialog.value.setInfo(record)
  })
}
const setInfo = async (isLoop) => {
  loading.value = !isLoop;
  let res = await getHostList({cloudId:router.currentRoute.value.query.cloudId});
  loading.value = false;
  console.log("loading.value",loading.value)
  if(res.data && Array.isArray(JSON.parse(res.data))){
    console.log('res',JSON.parse(res.data))
    
    hostlist.value = JSON.parse(res.data);
    hostlist.value.forEach((item,index) => {
      item.instance = [];
      item.services.forEach((t,i)=>{
        item.instance.push(t.type+'.'+t.id);
      })
    });
    if(!isLoop){
      beginInterval()
    }
    // if(ii >= res.length - 1)
  }
}
var timerHost = null;
const beginInterval = () => {
  clearInterval(timerHost)
  timerHost = setInterval(()=>{
    setInfo(true);
  },60000)
}
const cancel = () => {
  clearInterval(timerHost)
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
.full-modal {
  .ant-modal-body {
    flex: 1;
  }
}
</style>