<template>
    <!-- <div class=''> -->
        <!-- cancel-text=" " ok-text="关闭" -->
        <a-modal title="存储池管理" v-model:visible="info.isPool" @cancel="cancel" width="100% !important" wrap-class-name="full-modal" :footer="null" centered :getContainer="modalBindNode">
            <a-row>
                <a-button type="primary" @click="addPool"><PlusOutlined />新增</a-button>
            </a-row><br>
            <a-table :columns="columns" :data-source="poollist" row-key="pool" :loading="loading" @change="changeTable">
                <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
      <div style="padding: 8px">
        <a-input
          ref="searchInput"
          :placeholder="`搜索 ${column.title}`"
          :value="selectedKeys[0]"
          style="width: 188px; margin-bottom: 8px; display: block"
          @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
          @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
        />
        <a-button
          type="primary"
          size="small"
          style="width: 90px; margin-right: 8px"
          @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
        >
          <template #icon><SearchOutlined /></template>
          搜索
        </a-button>
        <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
          重置
        </a-button>
      </div>
    </template>
    <template #filterIcon="filtered">
      <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
    </template>
    <template #customRender="{ text, column }">
      <span v-if="searchText && searchedColumn === column.dataIndex">
        <template
          v-for="(fragment, i) in text
            .toString()
            .split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))"
        >
          <mark
            v-if="fragment.toLowerCase() === searchText.toLowerCase()"
            class="highlight"
            :key="i"
          >
            {{ fragment }}
          </mark>
          <template v-else>{{ fragment }}</template>
        </template>
      </span>
      <template v-else>
        {{ text }}
      </template>
    </template>
    <template #type="{record}">
      <!-- <a-tag>{{record.crush_rule.substr(0,7)+':×'+record.size}}</a-tag> -->
      <a-tag>{{record.type}} × {{record.size}}</a-tag>
    </template>
    <template #application_metadata="{record}">
      <!-- <a-tag color="#108ee9">{{record.application_metadata[0]}}</a-tag> -->
      <a-tag color="#108ee9" v-for="(item,index) in record.application_metadata" :key="index">{{item}}</a-tag>
    </template>
    <template #pg_status="{record}">
      <div v-if="record.pg_status">
        <span v-for="(item,key,index) in record.pg_status" :key="key">{{index > 0 ? '，':''}} {{item}} {{key}}</span>
      </div>
      <span v-else>--</span>
    </template>
    <template #rd_bytes>
      <div class="rd_chart"></div>
    </template>
    <template #wr_bytes>
      <div class="wr_chart"></div>
    </template>
    <template #rd="{record}">
      <span v-if="record.stats">
        {{record.stats.rd ? $filterNumber(record.stats.rd.rate) : 0}} /s
      </span>
      <span v-else>--</span>
    </template>
    <template #wr="{record}">
      <span v-if="record.stats">
        {{record.stats.wr ? $filterNumber(record.stats.wr.rate) : 0}} /s
      </span>
      <span v-else>--</span>
    </template>
    <template #action="{record}">
      <a-button class="button_E" @click="editPool(record)">修改</a-button>
    </template>
    </a-table>
  </a-modal>
    <!-- </div> -->
    <Info ref="poolInfoDialog" :info="pooleditinfo" @getlist="setInfo" />
</template>
<script lang='ts' setup>
import { getPoolList } from '@/api/backend/ceph/ceph';
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import Info from "./info.vue";
import * as echarts from "echarts"
import router from '@/router';
const {proxy} = getCurrentInstance()
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isPool:false
            }
        }
    }
})
const loading = ref(false)
const searchInput = ref()
const searchText = ref('')
const searchedColumn = ref()
const pooleditinfo = reactive({isShow:false,isAdd:true}) 
const poolInfoDialog = ref()
const poollist = ref([])
const columns = [
    {title:'名称',dataIndex:'pool_name',key:'pool_name',slots: {
          filterDropdown: 'filterDropdown',
          filterIcon: 'filterIcon',
          customRender: 'customRender',
        },
        onFilter: (value, record) =>
          record.pool_name.toString().toLowerCase().includes(value.toLowerCase()),
        onFilterDropdownVisibleChange: visible => {
          if (visible) {
            setTimeout(() => {
              console.log(searchInput.value);
              searchInput.value.focus();
            }, 100);
          }
        },
      },
    // {title:'数据保护',dataIndex:'crush_rule',slots:{customRender:'crush_rule'}},
    {title:'数据类型',dataIndex:'type',slots:{customRender:'type'}},
    {title:'应用',dataIndex:'application_metadata',slots:{customRender:'application_metadata'}},
    {title:'PG状态',dataIndex:'pg_status',slots:{customRender:'pg_status'}},
    // {title:'使用率',dataIndex:'usage'},
    {title:'读取字节',dataIndex:'stats',slots:{customRender:'rd_bytes'},align:'center'},
    {title:'写入字节',dataIndex:'stats',slots:{customRender:'wr_bytes'},align:'center'},
    {title:'读取操作',dataIndex:'stats',slots:{customRender:'rd'},align:'center'},
    {title:'写入操作',dataIndex:'stats',slots:{customRender:'wr'},align:'center'},
    {title:'操作',dataIndex:'action',slots:{customRender:'action'}},
]
const addPool = () => {
  pooleditinfo.isShow = true
  pooleditinfo.isAdd = true
}
const editPool = (record) => {
  pooleditinfo.isShow = true;
  pooleditinfo.isAdd = false;
  record.pool_type = record.type;
  poolInfoDialog.value.setInfo(record);
}
const changeTable = (pagination, filters, sorter, { currentDataSource }) => {
  nextTick(()=>{
    beginInterval(pagination)
    init(poollist.value,pagination)
  })
}
const setInfo = async (isLoop,pagination) => {
  loading.value = !isLoop;
  let res = await getPoolList({stats:true,cloudId:router.currentRoute.value.query.cloudId});
  loading.value = false;
  if(res.data){
    console.log('res',JSON.parse(res.data))
    poollist.value = JSON.parse(res.data);
    setTimeout(()=>{
      if(!isLoop){
        beginInterval({current:1,pageSize:10})
        init(poollist.value,pagination)
      }else{
        loop(poollist.value,pagination)
      }
    })
  }
}

var myChart1;
var myChart2;
const option1 = [];
const option2 = [];
var timerPool = null;
const beginInterval = (pagination) => {
  clearInterval(timerPool)
  timerPool = setInterval(()=>{
    setInfo(true,pagination);
  },60000)
}
const loop = (pool,pagination) => {
  pool.forEach((item,index)=>{
    if(index >= (pagination.current - 1) * pagination.pageSize && index < pagination.current * pagination.pageSize){
      let formatIndex = index - (pagination.current - 1) * pagination.pageSize;
      let sdata = [];
      let xdata = [];
      if(item.stats){
        item.stats.rd_bytes.rates.forEach((t,i)=>{
          sdata.push(t[1]);
          xdata.push(t[0]);
        })
      }
      option1[index].xAxis.data = xdata;
      option1[index].series[0].data = sdata;
      myChart1[formatIndex].setOption(option1[index]);
      let sdata1 = [];
      let xdata1 = [];
      if(item.stats){
        item.stats.wr_bytes.rates.forEach((t,i)=>{
          sdata1.push(t[1]);
          xdata1.push(t[0]);
        })
      }
      option2[index].xAxis.data = xdata1;
      option2[index].series[0].data = sdata1;
      myChart2[formatIndex].setOption(option2[index]);
    }
  })
}
const init = (pool,pagination) => {
  myChart1 = [];
  myChart2 = [];
  pool.forEach((item,index)=>{
    if(index >= (pagination.current - 1) * pagination.pageSize && index < pagination.current * pagination.pageSize){
      let formatIndex = index - (pagination.current - 1) * pagination.pageSize;
      myChart1.push(undefined)
      let sdata = [];
      let xdata = [];
      if(item.stats){
        item.stats.rd_bytes.rates.forEach((t,i)=>{
          sdata.push(t[1]);
          xdata.push(t[0]);
        })
      }
      option1.push({
        xAxis: {
          type: 'category',
          data: [],
          show:false
        },
        yAxis: {
          type: 'value',
          show:false,
        },
        grid: {
          top:0,
          bottom:0,
        },
        tooltip:{
          show:true,
          formatter: ({value})=>proxy.$ByteFormat(value,'i','str'),
        },
        series: [
          {
            data: [],
            type: 'line',
            symbolSize: 2,
            areaStyle:'#e6f7ff',
            lineStyle:{width:1}
          }
        ]
      })
      if (document.getElementsByClassName('rd_chart')[formatIndex] == null) {
        return
      }
      echarts.dispose(document.getElementsByClassName('rd_chart')[formatIndex])
      myChart1[formatIndex]=echarts.init(document.getElementsByClassName('rd_chart')[formatIndex]);
      option1[index].xAxis.data = xdata;
      option1[index].series[0].data = sdata;
      myChart1[formatIndex].setOption(option1[index]);
      myChart2.push(undefined)
      let sdata1 = [];
      let xdata1 = [];
      if(item.stats){
        item.stats.wr_bytes.rates.forEach((t,i)=>{
          sdata1.push(t[1]);
          xdata1.push(t[0]);
        })
      }
      option2.push({
        xAxis: {
          type: 'category',
          data: [],
          show:false
        },
        yAxis: {
          type: 'value',
          show:false,
          
        },
        grid: {
          top:0,
          // left: '-3%',
          bottom:0,
          // containLabel: true
        },
        tooltip:{
          show:true,
          formatter: ({value})=>proxy.$ByteFormat(value,'i','str'),
        },
        series: [
          {
            data: [],
            type: 'line',
            symbolSize: 2,
            areaStyle:'#e6f7ff',
            lineStyle:{width:1}
          }
        ]
      })
      if (document.getElementsByClassName('wr_chart')[formatIndex] == null) {
        return
      }
      echarts.dispose(document.getElementsByClassName('wr_chart')[formatIndex])
      myChart2[formatIndex]=echarts.init(document.getElementsByClassName('wr_chart')[formatIndex]);
      option2[index].xAxis.data = xdata1;
      option2[index].series[0].data = sdata1;
      myChart2[formatIndex].setOption(option2[index]);
      window.addEventListener('resize',function(){
        myChart2[formatIndex].resize()
        myChart1[formatIndex].resize()
      })
    }
  })
}
const cancel = () => {
  clearInterval(timerPool)
}
onMounted(() => {
  
})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
.rd_chart{width:150px;height: 23px;margin-right: -47px;}
.wr_chart{width:150px;height: 23px;margin-right: -47px;}
.full-modal {

  .ant-modal-body {
    flex: 1;
  }
}
</style>