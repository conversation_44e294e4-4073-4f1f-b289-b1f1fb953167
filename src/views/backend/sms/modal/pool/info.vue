<template>
    <div class=''>
        <a-modal :title="info.isAdd ? '新增池' : '修改池'" v-model:visible="info.isShow" @ok="createPool" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form :model="poolform" ref="poolForm" :label-col="{span:6}" :rules="rules">
                <a-form-item label="名称" name="pool_name">
                    <a-input v-model:value="poolform.pool_name"></a-input>
                </a-form-item>
                <a-form-item label="类型" name="pool_type">
                    <a-select v-model:value="poolform.pool_type" placeholder="请选择池类型" :disabled="!info.isAdd" :getPopupContainer="triggerNode => triggerNode.parentNode">
                        <a-select-option value="erasure">erasure</a-select-option>
                        <a-select-option value="replicated">replicated</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="PG自动缩放" name="pg_autoscale_mode" v-if="poolform.pool_type">
                    <a-radio-group v-model:value="poolform.pg_autoscale_mode">
                        <a-radio-button value="on">on</a-radio-button>
                        <a-radio-button value="warn">warn</a-radio-button>
                        <a-radio-button value="off">off</a-radio-button>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="Placement groups" name="pg_num" v-if="poolform.pg_autoscale_mode != 'on'">
                    <a-input-number v-model:value="poolform.pg_num" />
                </a-form-item>
                <a-form-item label="Flags" v-if="poolform.pool_type == 'erasure'">
                    <a-checkbox-group v-model:value="poolform.flags" :options="flagOptions" />
                </a-form-item>
                <a-form-item label="Replcated size" name="size" v-if="poolform.pool_type == 'replicated'" :disabled="!info.isAdd">
                    <a-input-number v-model:value="poolform.size" />
                </a-form-item>
                <a-form-item label="应用">
                    <a-select mode="multiple" v-model:value="poolform.application_metadata" :options="appOptions" @search="searchApp" :search-value="appinputText" :getPopupContainer="triggerNode => triggerNode.parentNode" placeholder="输入手动添加应用元数据">
                        <template #dropdownRender="{ menuNode: menu }">
                            <v-nodes :vnodes="menu" />
                            <a-divider style="margin: 4px 0" />
                            <div
                                v-if="appinputText && !appOptions.some(item => item.value == appinputText)"
                                style="padding: 4px 8px; cursor: pointer"
                                @mousedown="e => e.preventDefault()"
                                @click="addAppItem(appinputText)"
                            >
                                <plus-outlined />
                                新增“{{appinputText}}”标签
                            </div>
                        </template>
                    </a-select>
                    <!-- <a-select mode="multiple" v-model:value="poolform.application_metadata"></a-select> -->
                </a-form-item>
                <a-form-item label="压缩模式">
                    <a-radio-group v-model:value="poolform.compression_mode">
                        <a-radio-button value="none">none</a-radio-button>
                        <a-radio-button value="passive">passive</a-radio-button>
                        <a-radio-button value="agressive">agressive</a-radio-button>
                        <a-radio-button value="force">force</a-radio-button>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="算法" v-if="poolform.compression_mode != 'none'">
                    <a-radio-group v-model:value="poolform.compression_algorithm">
                        <a-radio-button value="snappy">snappy</a-radio-button>
                        <a-radio-button value="zlib">zlib</a-radio-button>
                        <a-radio-button value="zstd">zstd</a-radio-button>
                        <a-radio-button value="lz4">lz4</a-radio-button>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="Blob最小值(KiB)" v-if="poolform.compression_mode != 'none'">
                    <a-input-number v-model:value="poolform.compression_min_blob_size"></a-input-number>
                </a-form-item>
                <a-form-item label="Blob最大值(KiB)" v-if="poolform.compression_mode != 'none'">
                    <a-input-number v-model:value="poolform.compression_max_blob_size"></a-input-number>
                </a-form-item>
                <a-form-item label="比率" v-if="poolform.compression_mode != 'none'">
                    <a-input-number v-model:value="poolform.compression_required_ratio"></a-input-number>
                </a-form-item>
                <a-form-item label="最大字节数(GiB)">
                    <a-input-number v-model:value="poolform.quota_max_bytes"></a-input-number>
                </a-form-item>
                <a-form-item label="最大对象数">
                    <a-input-number v-model:value="poolform.quota_max_objects"></a-input-number>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { addPoolAPI, editPoolAPI } from '@/api/backend/ceph/ceph_origin';
import { message } from 'ant-design-vue';
import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
const VNodes = (_, { attrs }) => {
    return attrs.vnodes;
}
const {proxy} = getCurrentInstance()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})

const appOptions = ref([{value:'cephfs'},{value:'rbd'},{value:'rgw'}])
const appinputText = ref('')
const flagOptions = ['ec_overwrites']
const poolForm = ref()
const defaultform = {
    pool:'',
    pool_name:'',
    pool_type:undefined,
    pg_autoscale_mode:'on',
    application_metadata:[],
    erasure_code_profile:'default',
    pg_num:1,
    compression_mode:'none',
    compression_algorithm:'snappy',
    compression_max_blob_size:0,
    compression_min_blob_size:0,
    compression_required_ratio:0,
    quota_max_bytes:undefined,
    quota_max_objects:undefined,
    size:undefined
}
const poolform = reactive({
    pool:'',
    pool_name:'',
    pool_type:undefined,
    pg_autoscale_mode:'on',
    application_metadata:[],
    erasure_code_profile:'default',
    pg_num:1,
    compression_mode:'none',
    compression_algorithm:'snappy',
    compression_max_blob_size:0,
    compression_min_blob_size:0,
    compression_required_ratio:0,
    quota_max_bytes:undefined,
    quota_max_objects:undefined,
    size:undefined
})
const rules = reactive({
    pool_name:[{ required: true, message: '请输入存储池名称' }],
    pool_type:[{ required: true, message: '请选择存储池类型' }],
    pg_autoscale_mode:[{ required: true, message: '请选择' }],
    pg_num:[{required: true, type:'number', message: '请输入'}],
    size:[{required: true, type:'number', message: '请输入'}],

})
// const featuresList = ref(['1','2','3'])
// const primaryList = ref(['1','2','3'])
// const walList = ref(['1','2','3'])
// const dbList = ref(['1','2','3'])
const state = reactive({
    inputVisible1: false,
    inputVisible2: false,
    inputVisible3: false,
    featuresList:['1','2','3'],
    primaryList:['1','2','3'],
    walList:['1','2','3'],
    dbList:['1','2','3'],

})
const {featuresList,primaryList,walList,dbList,inputVisible1,inputVisible2,inputVisible3} = toRefs(state)
const inputRef1 = ref()
const inputRef2 = ref()
const inputRef3 = ref()
// const inputVisible1 = ref(false)
// const inputVisible2 = ref(false)
// const inputVisible3 = ref(false)
const searchApp = (value) => {
    appinputText.value = value;
}
const addAppItem = (text) => {
    appOptions.value.push({value:text});
    poolform.application_metadata.push(text)
    appinputText.value = ''
}
const handleClose = (removedTag: string,value) => {
    const tags = value.filter(tag => tag !== removedTag);
    console.log(tags);
    value = tags;
};
const handleInputConfirm = (value,value1,value2) => {
    const inputValue = poolform[value2];
    let tags = state[value1];
    if (inputValue && tags.indexOf(inputValue) === -1) {
        tags = [...tags, inputValue];
    }
    console.log(tags);
    state[value] = false;
    state[value1] = tags;
    poolform[value2] = '';
    // Object.assign(state, {
    //     tags,
    //     inputVisible: false,
    //     inputValue: '',
    // });
};
const showInput = (value,value1) => {
    if(value1 == 'inputRef1'){
        state[value] = true;
        proxy.$nextTick(() => {
            inputRef1.value.focus();
        })
    }
    if(value1 == 'inputRef2'){
        state[value] = true;
        proxy.$nextTick(() => {
            inputRef2.value.focus();
        })
    }
    if(value1 == 'inputRef3'){
        state[value] = true;
        proxy.$nextTick(() => {
            inputRef3.value.focus();
        })
    }
};
const cancel = () => {
    props.info.isShow = false;
    poolForm.value.resetFields()
    Object.assign(poolform,defaultform)
}
const createPool = () => {
    poolForm.value.validate().then(async ()=>{
        let poolform1 = {...poolform}
        if(poolform.application_metadata.length <= 0){
            poolform1.application_metadata = undefined;
        }
        if(poolform.pool_type == 'replicated'){
            poolform1.rule_name = 'replicated_rule';
            poolform1.erasure_code_profile = undefined;
            poolform1.flags = undefined;
        }else if(poolform.pool_type == 'erasure'){
            poolform1.erasure_code_profile = 'default';
            poolform1.rule_name = undefined;
            poolform1.size = undefined;
        }
        if(poolform.pg_autoscale_mode == 'on'){
            poolform1.pg_num = 1;
        }
        if(poolform.compression_mode == 'none'){
            poolform1.compression_mode = undefined;
            poolform1.compression_algorithm = undefined;
        }
        if(poolform.quota_max_bytes){
            poolform1.quota_max_bytes = poolform.quota_max_bytes * (1024*1024*1024);
        }
        if(poolform.quota_max_bytes == 0){
            poolform1.quota_max_bytes = undefined;
        }
        if(poolform.quota_max_objects == 0){
            poolform1.quota_max_objects = undefined;
        }
        console.log('poolform',poolform1)
        let res
        if(props.info.isAdd)
            res = await addPoolAPI(poolform1)
        else
            res = await editPoolAPI(poolform1.pool_name,poolform1)
        if(res.metadata){
            message.success(props.info.isAdd ? '操作成功': '操作成功')
            cancel()
            emit('getlist')
        }
    })
    
}
const setInfo = (record) => {
    Object.assign(poolform,record);
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>