<template>
<div class="performance-row">
    <a-row class="performance-row">
      <a-card title="客户端读/写" size="small">
        <div class="charts" id="chart4" ref="chart4"></div>
      </a-card>
      <a-card title="客户端吞吐量" size="small">
        <div class="charts" id="chart5" ref="chart5"></div>
      </a-card>
      <a-card title="恢复吞吐量" size="small" class="pool-card">
        {{recoveryCount}} B/S
      </a-card>
      <a-card title="Scrubbing" size="small" class="pool-card">
        {{scrubStatus}}
      </a-card>
    </a-row>
   </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from "vue";
import * as echarts from "echarts";
import { Options } from "@/common/chartoption";
const {proxy} = getCurrentInstance()
var myChart4;
var myChart5;
const chart4 = ref();
const chart5 = ref();
let option4 = reactive(Options.ceph_perform_4)
let option5 = reactive(Options.ceph_perform_5)

const init = () => {
  if (!chart4.value || !chart4.value)
    return;
  echarts.dispose(chart4.value);
  echarts.dispose(chart5.value);
  myChart4=echarts.init(chart4.value);
  myChart5=echarts.init(chart5.value);
  window.addEventListener('resize',function(){
    myChart4.resize()
    myChart5.resize()
  })
}
const recoveryCount = ref(0)
const scrubStatus = ref(0)
const setInfo = ({client_perf,scrub_status}) => {
  option4.title.text = client_perf.read_op_per_sec + client_perf.write_op_per_sec;
  option4.series[0].data = [
    {name:'读取请求：'+client_perf.read_op_per_sec+' /s',value:client_perf.read_op_per_sec},
    {name:'写入请求：'+client_perf.write_op_per_sec+' /s',value:client_perf.write_op_per_sec},
  ]
  myChart4.setOption(option4);
  option5.title.text = proxy.$ByteFormat(client_perf.read_bytes_sec + client_perf.write_bytes_sec,'i','num');
  option5.title.subtext = proxy.$ByteFormat(client_perf.read_bytes_sec + client_perf.write_bytes_sec,'i','unit')+ '/s';
  option5.series[0].data = [
    {name:'读取请求：'+proxy.$ByteFormat(client_perf.read_bytes_sec,'i','str')+'/s',value:client_perf.read_bytes_sec},
    {name:'写入请求：'+proxy.$ByteFormat(client_perf.write_bytes_sec,'i','str')+'/s',value:client_perf.write_bytes_sec},
  ]
  myChart5.setOption(option5);
  recoveryCount.value = client_perf.recovering_bytes_per_sec;
  scrubStatus.value = scrub_status;
}
onMounted(() => {
})
defineExpose({setInfo,init})
</script>
<style lang="scss" scoped>
.performance-row{
    justify-content: space-between;
    .ant-card{
        width: 23.5%;
        &:hover {
            border-color: #00000017;
            box-shadow: 0 2px 8px #00000017;
        }
        transition:all .3s;
    };
    .pool-card{:deep(.ant-card-body){height: calc(100% - 38px);display: flex;flex-direction: column;justify-content: center;}}
    :deep(.ant-card-head-title){font-weight: bold};
    :deep(.ant-card-body){
        padding-top: 0;text-align: center;
        
        p{margin-bottom: 5px;}
    }
}
.charts{width:100%;height: 120px;}
</style>