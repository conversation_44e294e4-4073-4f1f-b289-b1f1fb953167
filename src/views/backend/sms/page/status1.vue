<template>
<div>
    <div class="status_head">
        <div class="card_st">
            <p class="title-st">集群状态</p>
            <div>
                 <a-button class="title-st-button">HEALTH_WARN</a-button>
            </div>
        </div>
        <div class="card_st">
            <p class="title-st title-color">主机</p>
            <div class="center-st">
                 1 total
            </div>
        </div>
        <div class="card_st">
              <p class="title-st title-color">Monitor</p>
            <div  class="center-st">
                 1 (quorum 0)
            </div>
        </div>
        <div class="card_st" @click="handleOSDs">
             <p class="title-st title-color">OSD</p>
             <div  class="center-st">
               <p>3 总数</p>
               <p>0 启用中, 2 加入</p>
            </div>
        </div>
    </div>

    <div class="status_head status_font">
        <div class="card_st">
            <p class="title-st">Managers</p>
            <div  class="center-st">
                <p>1 工作</p>
                <p>0 待机</p>
            </div>
        </div>
        <div class="card_st">
            <p class="title-st title-color">对象网关</p>
            <div class="center-st">
                0 total
            </div>
        </div>
        <div class="card_st">
            <p class="title-st">metadata服务器</p>
            <div class="center-st">
                <!-- <span>
                    <p>1 active</p>
                    <p>0 standby</p>
                </span> -->
                <p>无文件系统</p>
            </div>
        </div>
        <div class="card_st">
            <p class="title-st title-color">iSCSI网关</p>
            <div class="center-st">
                <p>0 total</p>
                <p>0 up, 0 down</p>
            </div>
        </div>
    </div>
    <OSD ref="osdDialog" :info="osdinfo" />
</div> 
</template>
<script lang='ts' setup>
import { onMounted, reactive, ref } from 'vue';
import OSD from "../modal/osd/OSDs.vue";
const osdinfo = reactive({isOSD:false});
const osdDialog = ref()
const handleOSDs = () => {
    osdinfo.isOSD = true;
    osdDialog.value.setInfo()
}
onMounted(() => {})
</script>
<style lang="scss" scoped>
.status_head{
  
  display: flex;
  justify-content: space-between;
}
.status_font{
    margin-top:12px ;
}
.card_st{
  background-color: #fff;
  padding: 5px;
  width: 24%;
  height: 147px;
  cursor: pointer;
  transition: all .2s cubic-bezier(.645,.045,.355,1);
	-webkit-transition: all .2s cubic-bezier(.645,.045,.355,1);
}
.card_st:hover{
  box-shadow: 0 2px 8px #00000026;
}
.title-st{
    font-size: 18px;
    font-weight:400 ;
}
.title-color{
    color: #2B99A8;
}
.center-st{
     text-align: center;
     font-size: 18px;
    font-weight:800 ;
}

.center-st p{
    margin: 0;
}


.title-st-button{
    background: white;
    border: none;
    font-size: 18px;
    font-weight:800 ;
    color: #FFA500;
    margin-left:100px ;
}
</style>