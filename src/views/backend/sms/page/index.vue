<template>
  <div class="head">
    <a-card size="small" :bordered="false" class="status_card" :loading="cephLoading">
      <template #title>
        <div class="title-card">状态</div>
      </template>
      <Status ref="statusRef"/>
    </a-card>
    <a-card size="small" :bordered="false" class="status_card" :loading="cephLoading">
      <template #title>
        <div class="title-card">容量</div>
      </template>
      <Capacity ref="capRef"/>
    </a-card>
    <a-card size="small" :bordered="false" class="status_card" :loading="cephLoading">
      <template #title>
        <div class="title-card">性能</div>
      </template>
      <Performance ref="perRef"/>
    </a-card>
  </div>
</template>
<script lang="ts" setup>
import Status from './status.vue'
import Capacity from './Capacity.vue'
import Performance from './Performance.vue'
import { auth, dashboard } from '@/api/backend/ceph/ceph'
import { getCurrentInstance, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import router from '@/router';
import { onBeforeRouteLeave } from 'vue-router';
import { message } from 'ant-design-vue';
import { setCephToken } from '@/utils/auth';
const emit = defineEmits(["setOpenCeph"])
const {proxy} = getCurrentInstance()
const statusRef = ref()
const capRef = ref()
const perRef = ref()
const cephLoading = ref(false);
const statusData = reactive({})
const CapData = reactive({})
const PerData = reactive({})
var times = 0;
var timer4 = null;
const begin1 = () => {
  clearInterval(timer4)
  timer4 = setInterval(()=>{
    if(times < 3){
      getDashboard(true)
    }
  },60000)
}
const getDashboard = (isRefresh,cloudId) => {
  console.log("isRefresh",isRefresh)
  if(!isRefresh)
    cephLoading.value = true;
  times++;
  auth({cloudId:router.currentRoute.value.query.cloudId ? router.currentRoute.value.query.cloudId : cloudId}).then(async (res1)=>{
    if(res1.data){
      await dashboard({cloudId:router.currentRoute.value.query.cloudId}).then((res)=>{
        if(res.data){
          emit("setOpenCeph",1)
          proxy.$nextTick(()=>{
            setTimeout(()=>{
              if(!isRefresh){
                capRef.value.init();
                perRef.value.init();
              }
              localStorage.setItem('openCeph',1)
              statusRef.value.setInfo(JSON.parse(res.data));
              capRef.value.setInfo(JSON.parse(res.data));
              perRef.value.setInfo(JSON.parse(res.data));
            })
          })
        }
      }).catch((err)=>{
        if(times < 3)
          getDashboard()
        else{
          message.error("存储服务连接失败，请及时排查问题。");
          localStorage.setItem('openCeph',0)
        }
      })
    }else{
      message.error(res.msg ? res.msg : '存储服务连接失败，请及时排查问题。')
      localStorage.setItem('openCeph',0)
    }
    cephLoading.value = false;
  }).catch((err)=>{
    console.log('err',err.response)
    if(times >= 3){
      message.error('存储服务连接失败，请及时排查问题。')
      localStorage.setItem('openCeph',0)
      cephLoading.value = false;
    }
    if(times < 3)
      getDashboard()
  })
  
}
onMounted(()=>{
  // proxy.$mitt.on('cephRefresh',(e)=>{
  //     getDashboard(true);
  //     begin1()
  //   })
})
onBeforeRouteLeave(()=>{
  clearInterval(timer4)
})
onBeforeUnmount(()=>{
  clearInterval(timer4)
})
defineExpose({getDashboard,begin1})
</script>
<style lang="scss" scoped>
.head{
    margin: 0 16px
}
.status_card{
  .title-card::before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 17px;
    margin-right: 6px;
    vertical-align: text-bottom;
    background-color: #1890ff;
  }
}
</style>