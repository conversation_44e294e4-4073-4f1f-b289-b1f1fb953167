<template>
    <div>
      <a-row class="status-row">
            <a-card title="基本容量" size="small">
              <div class="charts" id="chart11" ref="chart11"></div>
            </a-card>
            <a-card title="对象数" size="small">
              <div class="charts" id="chart12" ref="chart12"></div>
            </a-card>
            <a-card title="PG 状态" size="small">
              <div class="charts" id="chart13" ref="chart13"></div>
            </a-card>
            <a-card size="small" class="pool-card">
              <template #title>
                  <a @click="goList('isPool')">存储池</a>
              </template>
                {{poolcount}}
            </a-card>
            <a-card title="每个OSD的PG数" size="small" class="pool-card">
              {{perosd}}
            </a-card>
        </a-row>
       <Pool ref="poolDialog" :info="modalinfo" />
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from "vue";
import * as echarts from "echarts";
import Pool from "../modal/pool/pool.vue";
import { Options } from "@/common/chartoption";
const {proxy} = getCurrentInstance()
const poolDialog = ref()
var myChart1;
var myChart2;
var myChart3;
const chart11 = ref();
const chart12 = ref();
const chart13=ref();
let option1 = reactive(Options.ceph_capacity_1)

let option2 = reactive(Options.ceph_capacity_2)

let option3 = reactive(Options.ceph_capacity_3)
const poolcount = ref(0)
const perosd = ref(0)
const modalinfo = reactive({
  isPool:false
})
const goList = (isWhat) => {
  modalinfo[isWhat] = true;
  poolDialog.value.setInfo(false,{current:1,pageSize:10})
}
const init = () => {
  if (!chart11.value || !chart12.value || !chart13.value)
    return;
  echarts.dispose(chart11.value);
  echarts.dispose(chart12.value);
  echarts.dispose(chart13.value);
  myChart1=echarts.init(chart11.value);
  myChart2=echarts.init(chart12.value);
  myChart3=echarts.init(chart13.value);
  //  myChart1.setOption(option1);
  //  myChart2.setOption(option2);
   myChart1.dispatchAction({
    type: 'highlight',
    seriesIndex: 0,
    dataIndex: 0
  });
  console.log("resize")
  myChart1.resize()
  myChart2.resize()
  myChart3.resize()
  window.addEventListener('resize',function(){
    myChart1.resize()
    myChart2.resize()
    myChart3.resize()
  })
}
const setInfo = ({df,pg_info,pools}) => {
  if(df.stats.total_used_raw_bytes == 0){
    option1.title.text = '0%';
    option1.series[0].data = [
      {name:'已使用：'+proxy.$ByteFormat(df.stats.total_used_raw_bytes,'i','str'),value:df.stats.total_used_raw_bytes},
      {name:'空闲：'+proxy.$ByteFormat(df.stats.total_avail_bytes,'i','str'),value:1,itemStyle: {
          color: "#f0f2f5"
        }},
    ]
  }else{
    option1.title.text = (df.stats.total_used_raw_bytes * 100/df.stats.total_bytes).toFixed(2) + '%';
    option1.series[0].data = [
      {name:'已使用：'+proxy.$ByteFormat(df.stats.total_used_raw_bytes,'i','str'),value:df.stats.total_used_raw_bytes},
      {name:'空闲：'+proxy.$ByteFormat(df.stats.total_avail_bytes,'i','str'),value:df.stats.total_avail_bytes,itemStyle: {
          color: "#f0f2f5"
        }},
    ]
  }
  option1.title.subtext = 'of '+proxy.$ByteFormat(df.stats.total_bytes,'i','str');
  
  myChart1.setOption(option1);
  let num_objects_total = pg_info.object_stats.num_objects + pg_info.object_stats.num_objects_misplaced + pg_info.object_stats.num_objects_degraded + pg_info.object_stats.num_objects_unfound;
  option2.series[0].data = [
    {name:'良好：'+(pg_info.object_stats.num_objects * 100 / num_objects_total).toFixed() + '%',value:pg_info.object_stats.num_objects},
    {name:'一般：'+(pg_info.object_stats.num_objects_misplaced * 100 / num_objects_total).toFixed() + '%',value:pg_info.object_stats.num_objects_misplaced},
    {name:'低级：'+(pg_info.object_stats.num_objects_degraded * 100 / num_objects_total).toFixed() + '%',value:pg_info.object_stats.num_objects_degraded},
    {name:'未知：'+(pg_info.object_stats.num_objects_unfound * 100 / num_objects_total).toFixed() + '%',value:pg_info.object_stats.num_objects_unfound},
  ];
  option2.title.text = proxy.$filterNumber(pg_info.object_stats.num_objects);
  myChart2.setOption(option2);
  option3.series[0].data = [
    {name:'正常：'+(pg_info.statuses['active+clean']?pg_info.statuses['active+clean']:0),value:pg_info.statuses['active+clean']},
    {name:'运行中：'+(pg_info.statuses['active+clean+scrubbing+deep']?pg_info.statuses['active+clean+scrubbing+deep']:0),value:pg_info.statuses['active+clean+scrubbing+deep']},
    {name:'警报：'+(pg_info.statuses.warning?pg_info.statuses.warning:0),value:pg_info.statuses.warning},
    {name:'未知：'+(pg_info.statuses.unknown?pg_info.statuses.unknown:0),value:pg_info.statuses.unknown},
  ];
  if(Object.values(pg_info.statuses).length > 0)
    option3.title.text = Object.values(pg_info.statuses).reduce((prev,curr)=>prev+curr);
  else{
    option3.title.text = 0;
  }
   myChart3.setOption(option3);
   poolcount.value = pools.length;
   perosd.value = (pg_info?.pgs_per_osd).toFixed(2);
}
onMounted(()=>{
  
})
defineExpose({setInfo,init})
</script>
<style lang="scss" scoped>
.status-row{
    justify-content: space-between;
    .ant-card{
        width: 23.5%;
        &:hover {
            border-color: #00000017;
            box-shadow: 0 2px 8px #00000017;
        }
        transition:all .3s;
    };
    .pool-card{width: 10.7%;:deep(.ant-card-body){height: calc(100% - 38px);display: flex;flex-direction: column;justify-content: center;}}
    :deep(.ant-card-head-title){font-weight: bold};
    :deep(.ant-card-body){
        padding-top: 0;text-align: center;
        
        p{margin-bottom: 5px;}
    }
}
// .status-row{justify-content: space-between;.ant-card{width: 23.5%;};:deep(.ant-card-body){padding: 12px 0;text-align: center;}}
.charts{width:100%;height: 120px;}
</style>