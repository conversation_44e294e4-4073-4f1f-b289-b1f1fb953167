<template>
    <div>
        <a-row class="status-row">
            <a-card size="small" title="集群状态" class="cluster-card">
                    <a-popover trigger="hover" v-if="statusData.health.status == 'HEALTH_WARN'">
                        <template #title>
                            详情
                            <!-- <InfoCircleFilled />点击<a href="">日志</a>查看详细信息 -->
                        </template>
                        <template #content>
                            <p v-for="(item,index) in statusData.health.checks" :key="index">
                                <span style="color:#ffa500">{{item.type}}</span>
                                ：{{item.summary.message}}
                            </p>
                        </template>
                        <a-button type="text" style="color: #ffa500;">{{statusData.health.status}}<WarningFilled  /></a-button>
                    </a-popover>
                    <a-button v-else-if="statusData.health.status == 'HEALTH_OK'" type="text" style="color:#52c41a">{{statusData.health.status}}</a-button>
                    <a-popover trigger="hover" v-else>
                        <template #title>
                            详情
                            <!-- <InfoCircleFilled />点击<a href="">日志</a>查看详细信息 -->
                        </template>
                        <template #content>
                            <p v-for="(item,index) in statusData.health.checks" :key="index">
                                <span style="color:#ff4d4f">{{item.type}}</span>
                                ：{{item.summary.message}}
                            </p>
                        </template>
                        <a-button type="text" style="color: #ff4d4f;">{{statusData.health.status}}<ExclamationCircleOutlined /></a-button>
                    </a-popover>
                </a-card>
            <a-card size="small">
                <template #title>
                    <a @click="handleHost">主机</a>
                </template>
                {{statusData.hosts}}
            </a-card>
            <a-card size="small" title="Monitor"><span>{{statusData.mon_status.monmap.mons.length}} <span v-if="statusData.mon_status.quorum">（quorum {{statusData.mon_status.quorum[0]}}）</span></span></a-card>
            <a-card size="small">
                <template #title>
                    <a @click="handleOSDs">OSD</a>
                </template>
                <span>{{statusData.osd_map.osds.length}} 总数</span><span>{{statusData.osd_map.up}} 启用中， {{statusData.osd_map.in}} 加入</span><span>{{statusData.osd_map.down}} 宕机， {{statusData.osd_map.out}} 退出</span>
            </a-card>
        </a-row><br>
        <a-row class="status-row">
            <a-card size="small" title="Managers"><p>{{statusData.mgr_map.active_name ? 1 : 0}} 工作</p><span>{{statusData.mgr_map.standbys.length}} 待机</span></a-card>
            <a-card size="small" title="对象网关">{{statusData.rgw}}</a-card>
            <a-card size="small" title="metadata服务器"><p>{{statusData.fs_map.filesystems.length}} 工作</p><span>{{statusData.fs_map.standbys.length}} 待机</span></a-card>
            <a-card size="small" title="iSCSI网关"><p>{{statusData.iscsi_daemons.down + statusData.iscsi_daemons.up}} 总数</p><span>{{statusData.iscsi_daemons.up}} 启用中， {{statusData.iscsi_daemons.down}} 宕机</span></a-card>
        </a-row>
    <OSD ref="osdDialog" :info="osdinfo" />
    <Host ref="hostDialog" :info="hostinfo" />
</div> 
</template>
<script lang='ts' setup>
import { onMounted, reactive, ref } from 'vue';
import OSD from "../modal/osd/OSDs.vue";
import Host from "../modal/host/host.vue";
const statusData = reactive({
    health:{},
    hosts:0,
    mon_status:{monmap:{mons:[]}},
    osd_map:{osds:[],up:0,in:0,down:0,out:0},
    mgr_map:{standbys:[]},
    rgw:0,
    fs_map:{filesystems:[],standbys:[]},
    iscsi_daemons:{}
    });
const osdinfo = reactive({isOSD:false});
const hostinfo = reactive({isHost:false});
const osdDialog = ref()
const hostDialog = ref()
const handleOSDs = () => {
    osdinfo.isOSD = true;
    osdDialog.value.setInfo(false,{current:1,pageSize:10})
}
const handleHost = () => {
    hostinfo.isHost = true;
    hostDialog.value.setInfo()
}
const setInfo = ({health,hosts,mon_status,osd_map,mgr_map,rgw,fs_map,iscsi_daemons}) => {
    osd_map.up = 0;
    osd_map.in = 0;
    osd_map.down = 0;
    osd_map.out = 0;
    osd_map.osds.forEach(item => {
        osd_map.up += item.up;
        osd_map.in += item.in;
        osd_map.down += item.state ? Number(item.state.some((t,i)=>t == 'exists')) : 0;
        osd_map.out += item.state ? Number(item.state.some((t,i)=>t == 'autoout')) : 0;
    });
    Object.assign(statusData,{health,hosts,mon_status,osd_map,mgr_map,rgw,fs_map,iscsi_daemons})
    // console.log('statusData',statusData)
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang="scss" scoped>
.status-row{
    justify-content: space-between;
    .ant-card{
        width: 23.5%;
        &:hover {
            border-color: #00000017;
            box-shadow: 0 2px 8px #00000017;
        }
        transition:all .3s;
    };
    // .cluster-card{:deep(.ant-card-body .ant-btn-text){color: #ffa500;}}
    :deep(.ant-card-head-title){font-weight: bold};
    :deep(.ant-card-body){
        height: calc(100% - 38px);
        padding-top: 0;text-align: center;
        display: flex;flex-direction: column;justify-content: center;
        p{margin-bottom: 5px;}
    }
}
</style>