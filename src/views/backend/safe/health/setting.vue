<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" title="巡检设置" ok-text="提交" :body-style="{height:'570px',overflowY:'auto',padding:'24px 39px 24px'}" @cancel="cancel" :getContainer="modalBindNode">
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button @click="setDefault">默认配置</a-button>
                <a-button type="primary" @click="handleSave">提交</a-button>
            </template>
            <a-form size="small" ref="settingForm" :model="formobj">
                <a-form-item label="定时巡检" name="isopen" :rules="[{ required: true, type:'boolean', message:'请选择是否开启', trigger: 'change' }]" class="isopen">
                    <a-switch v-model:checked="formobj.isopen" checked-children="开" un-checked-children="关" />
                </a-form-item>
                <a-form-item label="表达式" name="corn" :rules="[{ required: true, message:'请输入定时巡检表达式', trigger: 'change' }]" v-if="formobj.isopen" class="corn">
                    <a-input v-model:value="formobj.corn" placeholder="请输入定时巡检表达式，如:0 0 0/12 * * ?" @click="openModal" readonly/>
                </a-form-item>
                <a-divider></a-divider>
                <a-tag size="small" color="#52c41a">正常</a-tag>
                <a-tag size="small" color="rgb(250, 173, 20)">警告</a-tag>
                <a-tag size="small" color="rgb(255, 77, 79)">异常</a-tag>
                <br><br>
                <template v-for="(item,index) in formobj.formlist" :key="index">
                    <!-- <a-divider></a-divider> -->
                    <p class="form-title">{{item.categoryName+' ：'}}</p>
                    <!-- <div style="display:flex;justify-content:space-around;">
                        <span>正常</span>
                        <span>警告</span>
                        <span>严重</span>
                    </div> -->
                    <a-form-item class="lowDivide" :name="['formlist',index,'settingMin']" :rules="rules1" :style="{'--left':`calc(${(item.settingMin1 > 100 ? 100 : item.settingMin1)}% - 20px)`}">
                        <a-input-number size="small" :min="0" :max="100" :step="1" :precision="0" :style="{width:'40px'}" v-model:value="item.settingMin" @change="changeInput(['formlist',index,'settingMax'],item,'settingMin','settingMax')" /> %
                    </a-form-item>
                    <a-progress :percent="item.settingMax1" :success-percent="item.settingMin1" strokeColor="#faad14" trailColor="#ff4d4f" strokeLinecap="square" :show-info="false" />
                    <a-form-item class="highDivide" :name="['formlist',index,'settingMax']" :rules="rules2" :style="{'--right':`calc(${(item.settingMax1 > 100 ? 100 : item.settingMax1)}% - 20px)`}">
                        <a-input-number size="small" :min="0" :max="100" :step="1" :precision="0" :style="{width:'40px'}" v-model:value="item.settingMax" @change="changeInput(['formlist',index,'settingMin'],item,'settingMax','settingMin')" /> %
                    </a-form-item>
                </template>
            </a-form>
        </a-modal>
        <JCronModal ref="innerVueCron" :data="formobj.corn" @okk="handleOK"></JCronModal>
    </div>
</template>
<script lang='ts' setup>
import JCronModal from '@/components/cron/JCronModal.vue'
import { saveSetings, selectHealthCheckSettingList } from '@/api/backend/safe/health';
import { message } from 'ant-design-vue';
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import emiter from '@/utils/Bus';
const {proxy} = getCurrentInstance()
const emit = defineEmits(['change'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false
            }
        }
    }
})

const settingForm = ref();
const innerVueCron = ref();
// const cron = ref('')

const checked1 = ref(false);
const defaultform = {
    isopen:false,
    corn:'',
    formlist:[]
}
const formobj = reactive({
    isopen:false,
    corn:'',
    formlist:[]
})
const openModal = () => {
    nextTick(()=>{
        console.log("innerVueCron.value",innerVueCron.value)
        innerVueCron.value.show()
    })
}
const handleOK = (val) => {
    if(typeof val == 'string'){
        formobj.corn = val;
        // emit('change', val)
    }
}
let validateMin = async (_rule, value) => {
  if (!value && value !== 0) {
    return Promise.reject('请填写');
  } else {
    if(formobj.formlist[_rule.field.split('.')[1]].settingMax || formobj.formlist[_rule.field.split('.')[1]].settingMax === 0){
        if(value >= formobj.formlist[_rule.field.split('.')[1]].settingMax) {
            return Promise.reject("需<"+formobj.formlist[_rule.field.split('.')[1]].settingMax);
        }
    }else
        return Promise.resolve();
    return Promise.resolve();
  }
};
let validateMax = async (_rule, value) => {
  if (!value && value !== 0) {
    return Promise.reject('请填写');
  }else{
    if(formobj.formlist[_rule.field.split('.')[1]].settingMin || formobj.formlist[_rule.field.split('.')[1]].settingMin === 0){
        if (value <= formobj.formlist[_rule.field.split('.')[1]].settingMin) {
            return Promise.reject("需>"+formobj.formlist[_rule.field.split('.')[1]].settingMin);
        } else {
            return Promise.resolve();
        }
    }else
        return Promise.resolve();
  }
};
const changeInput = (rule,item,key,keyOther) => {
    settingForm.value.validateFields([rule])
    .then(()=>{
        // if(Math.abs(item[key] - item[keyOther]) < 22){
        //     if(key == 'settingMin')
        //         item[key+1] = item[keyOther] - 12;
        //     if(key == 'settingMax')
        //         item[key+1] = item[keyOther] + 12;
        // }else
        if(key == 'settingMin'){
            if(item[key] || item[key] === 0){
                item[key+1] = item[key];
                item[keyOther+1] = item[keyOther]
            }
        }else{
            if(item[key]){
                item[key+1] = item[key];
                item[keyOther+1] = item[keyOther]
            }
        }
        // console.log("d",key)
    }).catch(()=>{
        if(key == 'settingMin'){
            if((item[key] || item[key] === 0) && item[key] < item[keyOther]){
                item[key+1] = item[key];
                item[keyOther+1] = item[keyOther]
            }
        }else{
            if(item[key] && item[keyOther] < item[key]){
                item[key+1] = item[key];
                item[keyOther+1] = item[keyOther]
            }
        }
    })
}
const rules1 = [{ required: true, type:'number', validator: validateMin, trigger: "change" }];
const rules2 = [{ required: true, type:'number', validator: validateMax, trigger: "change" }];
const setDefault = () => {
    formobj.isopen = false;
    formobj.corn = '';
    formobj.formlist.forEach((item,index)=>{
        if(item.categoryCode == 'HOSTOS_DISK_USED_PERCENT' || item.categoryCode == 'HOSTOS_CPU_USED_PERCENT' || item.categoryCode == 'HOSTOS_MEMORY_USED_PERCENT'){
            item.settingMax1 = item.settingMax = 90;
            item.settingMin1 = item.settingMin = 80;
        }else{
            item.settingMin1 = item.settingMin = 70;
            item.settingMax1 = item.settingMax = 90;
        }
        
    })
}
const setInfo = async () => {
    emiter.emit("allLoading",true)
    let res = await selectHealthCheckSettingList();
    emiter.emit("allLoading",false)
    if(res.code == 0){
        formobj.formlist = res.data.setingsList ? res.data.setingsList : [];
        formobj.isopen = res.data.taskBean ? Boolean(res.data.taskBean.isOpen) : false;
        formobj.corn = res.data.taskBean ? res.data.taskBean.corn : '';
        formobj.formlist.forEach((item,index)=>{
            if(item.settingMin || item.settingMin === 0){
                item.settingMin1 = item.settingMin;
            }else{
                item.settingMin1 = 33;
            }
            if(item.settingMax){
                item.settingMax1 = item.settingMax;
            }else{
                item.settingMax1 = 6;
            }
        })
    }
}
const handleSave = () => {
    settingForm.value.validate()
    .then(async()=>{
        emiter.emit("allLoading",true)
        if(!formobj.isopen)
            formobj.corn = undefined;
        let templist = [];
        formobj.formlist.forEach((item,index)=>{
            if(item.settingMin && item.settingMax)
            templist.push({categoryCode:item.categoryCode,categoryId:item.id,min:item.settingMin,max:item.settingMax})
        })
        if(templist.length >= formobj.formlist.length){
            let res = await saveSetings(templist,{isopen:Number(formobj.isopen),corn:formobj.corn})
            emiter.emit("allLoading",false)
            if(res.code == 0 && res.data !== false){
                message.success('设置成功');
                props.info.isShow = false;
                cancel()
            }else if(res.code == 0){
                message.error((!res.msg || res.msg == 'success') ? '设置失败' : res.msg);
            }
        }else{
            message.warning('请填写完整');
        }
    })
    .catch((err)=>{
        console.log("err",err)
        proxy.$nextTick(() => {
            let err1 = document.getElementsByClassName('ant-form-item-has-error')
            //实现滚动定位到验证位置
            if (err1.length) {
                err1[0].scrollIntoView({
                    block: 'center',
                    behavior: 'smooth',
                })
            }
        })
    })
    
}
const cancel = () => {
    props.info.isShow = false;
    settingForm.value.resetFields();
    Object.assign(formobj,defaultform)
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
// :deep(.ant-form-item-control){position: relative;}
:deep(.lowDivide){
    display: inline-block;position: relative;top: 0;left: var(--left);margin-bottom: 0;
    :deep(.ant-form-item-control){position: relative;}
    .ant-form-item-control{position: relative;}
    .ant-form-item-explain{position: absolute;top: 0;right: -45px;}
}
:deep(.highDivide){
    display: inline-block;position: relative;top: 0;left: var(--right);
    // .ant-form-item-control{position: relative;}
    // .ant-form-item-explain{position: absolute;top: 0;right: -42px;}
}
// .isopen{height: 32px;}
// .corn{display:inline-block;width:300px}
.ant-divider{
    margin-top: 0px;
}
.form-title::before {
    display: inline-block;
    margin-inline-end: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun,sans-serif;
    line-height: 1;
    content: "*";
}
:deep(.ant-form-item .ant-input-number-handler-wrap){display: none;}
// :deep(.ant-form-horizontal .ant-form-item-control){display: inline-block;}
// :deep(.ant-form-item-explain.ant-form-item-explain-error.v-enter-active.ant-show-help-leave-active){position: absolute;top:0;right: -42px;}
// :deep(.ant-form-item-explain.ant-form-item-explain-error){position: absolute;top:0;right: -42px;}
</style>