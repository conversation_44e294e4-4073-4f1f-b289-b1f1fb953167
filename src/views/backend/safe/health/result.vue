<template>
    <div class='innerPadding'>
        <div class="card-container">
            <a-tabs type="card" v-model:activeKey="activeKey" size="small" @change="changeTab">
                <a-tab-pane key="1" id="tab1" :tab="`汇总(${countData.total})`">
                    <a-tree 
                    v-if="treeData.length"
                    :tree-data="treeData" 
                    show-icon 
                    default-expand-all
                    v-model:expandedKeys="expandedKeys"
                    v-model:selectedKeys="selectedKeys"
                    :replace-fields="replaceFields"
                    @select="onSelect"
                    >
                        <template #normal="{info}">
                            <CheckCircleFilled style="color:#52c41a" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                        <template #error="{info}">
                            <CloseCircleFilled style="color:#ff4d4f" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                        <template #warn="{info}">
                            <ExclamationCircleFilled style="color:#faad14" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                    </a-tree>
                    <a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                </a-tab-pane>
                <a-tab-pane key="2" id="tab2" :tab="`正常(${countData.normal})`" force-render>
                    <a-tree 
                    v-if="treeData1.length"
                    :tree-data="treeData1" 
                    show-icon 
                    default-expand-all
                    v-model:selectedKeys="selectedKeys1"
                    :replace-fields="replaceFields"
                    @select="onSelect"
                    >
                        <template #normal="{info}">
                            <CheckCircleFilled style="color:#52c41a" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                        <template #error="{info}">
                            <CloseCircleFilled style="color:#ff4d4f" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                        <template #warn="{info}">
                            <ExclamationCircleFilled style="color:#faad14" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                    </a-tree>
                    <a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                </a-tab-pane>
                <a-tab-pane key="3" id="tab3" :tab="`警告(${countData.warn})`">
                    <a-tree 
                    v-if="treeData2.length"
                    :tree-data="treeData2" 
                    show-icon 
                    default-expand-all
                    v-model:selectedKeys="selectedKeys2"
                    :replace-fields="replaceFields"
                    @select="onSelect"
                    >
                        <template #normal="{info}">
                            <CheckCircleFilled style="color:#52c41a" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                        <template #error="{info}">
                            <CloseCircleFilled style="color:#ff4d4f" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                        <template #warn="{info}">
                            <ExclamationCircleFilled style="color:#faad14" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                    </a-tree>
                    <a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                </a-tab-pane>
                <a-tab-pane key="4" id="tab4" :tab="`异常(${countData.error})`">
                    <a-tree 
                    v-if="treeData3.length"
                    :tree-data="treeData3" 
                    show-icon 
                    default-expand-all
                    v-model:selectedKeys="selectedKeys3"
                    :replace-fields="replaceFields"
                    @select="onSelect3"
                    >
                        <template #normal="{info}">
                            <CheckCircleFilled style="color:#52c41a" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                        <template #error="{info}">
                            <CloseCircleFilled style="color:#ff4d4f" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                        <template #warn="{info}">
                            <ExclamationCircleFilled style="color:#faad14" />
                            <InfoCircleOutlined v-if="info" :title="info" class="label-question" />
                        </template>
                    </a-tree>
                    <a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                </a-tab-pane>
            </a-tabs>
            <div style="flex:9;margin-top:39px;padding:20px;background:#fff">
                <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">检测结果</div>
                </div>
                <a-table :columns="columns" :data-source="resultlist" row-key="id" :scroll="{y:530}" :pagination="false" size="small">
                    <!-- <template #footer v-if="resultlist.length > 0 && resultlist?.[0].result != '0' && resultlist?.[0].proposalInfo">
                        <span>
                            修复建议：{{resultlist?.[0].proposalInfo}}
                        </span>
                    </template> -->
                    <template #result="{record}">
                        <CheckCircleFilled v-if="record.result == '0'" style="color:#52c41a"/>
                        <CloseCircleFilled v-else-if="record.result == '-1'" style="color:#ff4d4f" />
                        <ExclamationCircleFilled v-else style="color:#faad14" />
                    </template>
                    <template #resultInfo="{record}">
                        <div v-if="record.resultInfo" v-html="record.resultInfo"></div>
                        <span v-else>--</span>
                    </template>
                </a-table>
                <!-- <div class="ant-descriptions-header">
                    <div class="ant-descriptions-title">基础信息</div>
                </div> -->
                <br><br>
                <a-descriptions title="修复建议" v-if="resultlist.length > 0 && resultlist?.[0].result != '0' && resultlist?.[0].proposalInfo">
                    <a-descriptions-item>
                        {{resultlist?.[0].proposalInfo}}
                    </a-descriptions-item>
                </a-descriptions>
                <!-- <span v-if="record.proposalInfo && record.result != '0'">
                    修复建议：{{record.proposalInfo}}
                </span> -->
            </div>
        </div>
        
    </div>
</template>
<script lang='ts' setup>
import { selectHealthCheckResult } from '@/api/backend/safe/health';
import { Empty } from 'ant-design-vue';
import { onMounted, ref, h, getCurrentInstance } from 'vue';
import $ from "jquery";
const {proxy} = getCurrentInstance();
const props = defineProps({
  countData:{
    type:Object,
    default(){
      return {
        normal:0,
        error:0,
        warn:0,
        total:0
      }
    }
  }
})
const expandedKeys = ref([]);
const selectedKeys = ref([]);
const selectedKeys1 = ref([]);
const selectedKeys2 = ref([]);
const selectedKeys3 = ref([]);
const replaceFields = {
      children: 'childList',
      title: 'categoryName',
      key: 'id'
    };
const treeData = ref([]);
const treeData1 = ref([]);
const treeData2 = ref([]);
const treeData3 = ref([]);
const columns = [
     {title: '检测项', dataIndex: 'categoryName', key: 'id',width:'30%'},
     {title: '状态', dataIndex: 'result', slots: { customRender: 'result' }, align:'center', key: 'id',width:'20%'},
     {title: '检测结果详情', dataIndex: 'resultInfo', slots: { customRender: 'resultInfo' }, key: 'id'},
]
const resultlist = ref([]);
const changeTab = (e) => {
    proxy.$nextTick(()=>{
        $(`.ant-tabs-top-content`).scrollTop(0);
    })
    if(e == '1'){
        if(treeData.value.length > 0){
            selectedKeys.value = [treeData.value[0].childList[0]?.id];

            resultlist.value = [treeData.value[0].childList[0]];
        }else{
            resultlist.value = [];
        }
    }
    else if(e == '2'){
        if(treeData1.value.length > 0){
            selectedKeys1.value = [treeData1.value[0].childList[0]?.id];
            resultlist.value = [treeData1.value[0].childList[0]];
        }else{
            resultlist.value = [];
        }
    }
    else if(e == '3'){
        if(treeData2.value.length > 0){
            selectedKeys2.value = [treeData2.value[0].childList[0]?.id];
            resultlist.value = [treeData2.value[0].childList[0]];
        }else{
            resultlist.value = [];
        }
    }
    else if(e == '4'){
        if(treeData3.value.length > 0){
            selectedKeys3.value = [treeData3.value[0].childList[0]?.id];
            resultlist.value = [treeData3.value[0].childList[0]];
        }else{
            resultlist.value = [];
        }
    }
}
const onSelect = (selectedKeys, e:{selected: bool, selectedNodes, node, event}) => {
    console.log("keys",selectedKeys, e)
    resultlist.value[0] = e.node.dataRef;
}
const onSelect1 = (selectedKeys, e:{selected: bool, selectedNodes, node, event}) => {
    console.log("keys",selectedKeys, e)
    resultlist.value[0] = e.node.dataRef;
}
const onSelect2 = (selectedKeys, e:{selected: bool, selectedNodes, node, event}) => {
    console.log("keys",selectedKeys, e)
    resultlist.value[0] = e.node.dataRef;
}
const onSelect3 = (selectedKeys, e:{selected: bool, selectedNodes, node, event}) => {
    console.log("keys",selectedKeys, e)
    resultlist.value[0] = e.node.dataRef;
}
const setInfo = async (historyId) => {
    let res = await selectHealthCheckResult({historyId})
    if(res.code == 0){
        if(res.data){
            console.log('res',res)
            treeData.value = res.data;
            if(treeData.value.length > 0){
                if(treeData.value[0].childList.length > 0){
                    selectedKeys.value = [treeData.value[0].childList[0]?.id];
                    resultlist.value[0] = treeData.value[0].childList[0];
                }
                let treeIndex1 = 0;
                let treeIndex2 = 0;
                let treeIndex3 = 0;
                let expand = [];
                treeData.value.forEach((item,index)=>{
                    item.selectable = false;
                    expand.push(item.id);
                    item.childList.forEach((t, i) => {
                        t.slots = {};
                        t.slots.id = t.id;
                        if(t.result == '0'){
                            t.slots.icon = 'normal';
                            if(!treeData1.value[treeIndex1]){
                                treeData1.value.push({...item});
                                treeData1.value[treeIndex1].childList = [];
                            }
                            treeData1.value[treeIndex1].childList.push(t);
                            
                        }else if(t.result == '-1'){
                            t.slots.icon = 'error';
                            if(!treeData3.value[treeIndex3]){
                                treeData3.value.push({...item});
                                treeData3.value[treeIndex3].childList = [];
                            }
                            treeData3.value[treeIndex3].childList.push(t);
                        }else{
                            t.slots.icon = 'warn';
                            if(!treeData2.value[treeIndex2]){
                                treeData2.value.push({...item});
                                treeData2.value[treeIndex2].childList = [];
                            }
                            treeData2.value[treeIndex2].childList.push(t);
                        }
                    });
                    if(treeData1.value[treeIndex1]){
                        treeIndex1++;
                    }
                    if(treeData2.value[treeIndex2]){
                        treeIndex2++;
                    }
                    if(treeData3.value[treeIndex3]){
                        treeIndex3++;
                    }
                })
                expandedKeys.value = expand;
                console.log("treeData1",treeData1.value)
            }
            
        }
    }
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
// .innerPadding{display: flex;}
// :deep(.ant-tabs .ant-tabs-small-bar .ant-tabs-tab){padding: 2px 0;margin: 0 10px;}
.card-container {
    min-width: 1148px;
    display: flex;
  background: #f0f2f5;
border: 1px solid #f0f2f5;
  overflow: hidden;
//   padding: 1px;
  height: 100%;
}
.ant-tabs{background: #fff;border-right: 1px solid #f0f2f5;overflow: initial;}
:deep(.card-container > .ant-tabs-card > .ant-tabs-content) {
  height: calc(100% - 40px);
  overflow-y: auto;
  margin-top: -16px;
}

:deep(.card-container > .ant-tabs-card > .ant-tabs-content > .ant-tabs-tabpane) {
  background: #fff;
  padding: 16px;
}

:deep(.card-container > .ant-tabs-card > .ant-tabs-bar) {
  border-color: #fff;
  background: #f0f2f5;
}

:deep(.card-container > .ant-tabs-card > .ant-tabs-bar .ant-tabs-tab) {
  border-color: transparent;
  background: transparent;
  margin-right: 0;
}

:deep(.card-container > .ant-tabs-card > .ant-tabs-bar .ant-tabs-tab-active) {
  border-color: #fff;
  background: #fff;
}
:deep(.ant-tree-child-tree > li){position: relative;}
// :deep(.anticon-question-circle){position: absolute;top: 13px;right: 0;}
.label-question{position: absolute;top: 14px;right: 0;font-size: 12px;color: #666;}
:deep(.ant-descriptions-title){
    // border-bottom: 1px solid #f0f2f5;
    // width: 50%;
    &::before{
        content: '';
        display: inline-block;
        width: 4px;
        height: 17px;
        margin-right: 6px;
        vertical-align: text-bottom;
        background-color: #1890ff;
    } 
}
</style>