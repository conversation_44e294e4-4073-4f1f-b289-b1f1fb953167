<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" title="巡检日志" width="100% !important" wrap-class-name="full-modal">
            <template #footer>
                <a-button @click="cancel">关闭</a-button>
            </template>
            <a-table :columns="columns" :data-source="loglist" row-key="id" :pagination="pagination" @change="changeTable" :loading="loading">
                <template #index="{record,index}">
                    {{index+1+(pagination.pageSize * (pagination.current-1))}}
                </template>
                <template #createTime="{record,index}">
                    {{record.createTime ? record.createTime : '--'}}
                </template>
                <template #endTime="{record,index}">
                    {{record.endTime ? record.endTime : '--'}}
                </template>
                <template #state="{record,index}">
                    {{record.state == 0 ? '巡检中' : (record.state == 1 ? '巡检完成' : '巡检被终止')}}
                </template>
                <template #action="{record}">
                    <a-button class="button_V" @click="emit('logToResult',record.id)">查看</a-button>
                    <a-button class="button_V" @click="exportt(record.id)">下载巡检报告</a-button>
                    <!-- <a-button class="button_D" @click="$handleDel([record.id])">删除</a-button> -->
                </template>
            </a-table>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { selectHealthCheckHistoryList } from '@/api/backend/safe/health';
import { getToken } from '@/utils/auth';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['logToResult'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false
            }
        }
    }
})

const parentHistoryId = ref(0);
const loading = ref(false);
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const searchform = reactive({
    pageIndex:1,
    pageSize:10
})
const loglist = ref([])
const columns = [
     {title: '序号', dataIndex: 'index', slots:{customRender: 'index'}, key: 'id',align:'center'},
     {title: '开始检测时间', dataIndex: 'createTime', slots:{customRender: 'createTime'}, key: 'id',align:'center'},
     {title: '结束检测时间', dataIndex: 'endTime', slots:{customRender: 'endTime'}, key: 'id',align:'center'},
     {title: '结果', dataIndex: 'state', slots:{customRender: 'state'}, key: 'id',align:'center'},
     {title: '操作', dataIndex: 'action', slots:{customRender: 'action'}, key: 'id'},
]
const exportt = (historyId) => {
    const token = getToken();
    location.href = import.meta.env.VITE_BASE_API+'/sys/safe/downloadResult?historyId='+historyId+'&token='+token;
    // downloadResult({historyId:globalHistoryId.value});
}
const getList = async () => {
    // let res = await selectHealthCheckHistoryList(searchform);
    // if(res.code == 0){
    loglist.value = await proxy.$getList(loading, selectHealthCheckHistoryList, searchform, pagination, getList);
    // }
}
const cancel = () => {
    props.info.isShow = false;
}
onMounted(() => {})
defineExpose({getList})
</script>
<style lang='scss' scoped>
.full-modal {
  .ant-modal-body {
    flex: 1;
  }
}
</style>