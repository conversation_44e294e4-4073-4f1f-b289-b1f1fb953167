<template>
    <div class='back-page'>
        <a-page-header
          class="health-header"
          title="健康巡检"
          v-show="!Running && Before"
          sub-title="帮助您深入了解当前云平台关键指标状态，确保云平台持续健康工作和业务稳定顺利运行。"
        >
            <a-row justify="space-between">
                <a-button type="primary" size="small" class="btnMargin" @click="handleCheck(true)" :disabled="!(globalState.state.indeterminate || globalState.state.checkAll)">开始检测</a-button>
                <div>
                    <a-button type="text" size="small" @click="openSet"><SettingOutlined />巡检设置</a-button>
                    <a-button type="text" size="small" @click="openLog"><FileTextOutlined />巡检日志</a-button>
                </div>
            </a-row>
        </a-page-header>
        <!-- 开发下个页面换成v-show="Running" -->
        <a-page-header
          v-show="!Before && Running"
          class="health-header run-header"
          title="正在对您的云平台进行检测，请稍候..."
        >
            <a-row justify="space-between">
                <div class="progress-outer">
                    <a-progress :percent="displayPercent" status="active" />
                    <!-- <a-progress :percent="percent" :stroke-color="['#52c41a', '#52c41a', '#f5222d']" /> -->
                <i style="font-size:12px;color:rgba(0, 0, 0, 0.45);">耗时：{{deadline}}
                </i></div>
                <a-button @click="handleCheck(false)">停止检测</a-button>
            </a-row>
        </a-page-header>
        <!-- 勿删 -->
        <a-page-header
          v-show="!Running && !Before"
          class="health-header run-header"
          :title="`检测结果：共${countData.total}项，其中${countData.normal}项正常，${countData.warn}项警告，${countData.error}项异常`"
        >
            <a-row justify="space-between">
                <i style="font-size:12px;color:rgba(0, 0, 0, 0.45);">巡检时间：{{createTime}} 耗时：{{deadline}}</i>
                <a-space>
                    <a-button @click="back" size="small">重新检测</a-button>
                    <a-button @click="exportt" size="small">导出巡检报告</a-button>
                    <a-button @click="openLog" size="small">巡检日志</a-button>
                </a-space>
            </a-row>
        </a-page-header>
        <!-- 勿删 -->
        <Result ref="resultRef" v-if="!Running && !Before" :countData="countData" />
        <!-- 开发下个页面加上v-else -->
        <!-- v-else -->
        <div v-else class="innerPadding">
          <div style="min-width: 1148px;">
            <a-row class="buttonGroup" justify="space-between" v-show="!Running">
                    <a-checkbox
                        v-model:checked="globalState.state.checkAll"
                        :indeterminate="globalState.state.indeterminate"
                        @change.stop="onCheckAllChange"
                        @click.stop="()=>null"
                    >
                    {{ !globalState.state.checkAll ? '勾选所有' : '取消勾选' }}
                    </a-checkbox>
                <a-button type="text" size="small" v-if="activeKey && activeKey.length <= 0" @click="()=>activeKey=[0,1,2,3,4,5]">展开所有<DownCircleOutlined /></a-button>
                <a-button type="text" size="small" v-else @click="()=>activeKey=[]">收起所有<UpCircleOutlined /></a-button>
            </a-row>
            <a-collapse
                v-model:activeKey="activeKey"
                :bordered="false"
                style="background: rgb(255, 255, 255)"
                expandIconPosition="right"
            >   
                <a-collapse-panel v-for="(item,index) in firstLevelList" :key="index" :id="'p'+(index+1)" :header="h('b',`&nbsp;&nbsp;${item.categoryName}`)" :style="customStyle">
                    <template #extra>
                        <a-checkbox
                            v-model:checked="globalState['state'+(index+1)].checkAll"
                            :indeterminate="globalState['state'+(index+1)].indeterminate"
                            @change.stop="(e)=>onGlobalChange(e, index+1)"
                            @click.stop="()=>null"
                            />
                    </template>
                    <a-checkbox-group v-model:value="globalState['state'+(index+1)].checkedList" :options="globalOptions['plainOptions'+(index+1)]" style="width:100%" >
                      <template #label="{value,id}">
                        <span>{{ value }}</span>
                        <span :id="'c'+id">
                            <CheckCircleFilled class="label-status label-status-correct" />
                            <CloseCircleFilled class="label-status label-status-error" />
                            <ExclamationCircleFilled class="label-status label-status-warn" />
                            <InfoCircleOutlined v-if="item.info" :title="item.info" class="label-question" />
                            <LoadingOutlined class="label-status label-status-load" />
                        </span>
                        
                        </template>
                    </a-checkbox-group>
                </a-collapse-panel>
                
            </a-collapse>
          </div>
            
        </div>
        <Setting ref="setRef" :info="setinfo" />
        <Log ref="logRef" :info="loginfo" @logToResult="logToResult" />
    </div>
</template>
<script lang='ts' setup>
import Result from "./result.vue";
import Setting from "./setting.vue";
import Log from "./healthlog.vue";
import { computed, getCurrentInstance, h, onMounted, reactive, ref, watch } from 'vue';
import {checkHealth, createHealthCheck, downloadResult, selectCategoryList, selectHealthCheckResultByHistoryIdAndCategoryId, statisticsHealthResult, updateHealthCheckState} from "@/api/backend/safe/health"
import $ from "jquery";
import moment from "moment";
import { getToken } from "@/utils/auth";
const {proxy} = getCurrentInstance();
const resultRef = ref();
const setRef = ref();
const logRef = ref();
const Running = ref(false);
const Before = ref(true);
const isHandleStop = ref(false);
const createTime = ref('');
const timer = ref();
const timeRaw = ref(-1000 * 60 * 60 * 8);
const deadline = computed(()=>{return moment(timeRaw.value).utcOffset("+08:00").format('HH:mm:ss')})
// ref(Date.now() + 1000 * 60 * 60 * 24 * 2 + 1000 * 30);
// const Steps = ref(0);
const activeKey = ref([0, 1, 2, 3, 4, 5]);
// const plainOptions = [];
const firstLevelList = ref([])
const globalHistoryId = ref(0);
const percent = ref(0);
const displayPercent = computed(()=>Number(percent.value.toFixed()))
const countData = reactive({
    normal:0,
    error:0,
    warn:0,
    total:0
})
const globalOptions = reactive({
  plainOptions1:[],
  plainOptions2:[],
  plainOptions3:[],
  plainOptions4:[],
  plainOptions5:[],
  plainOptions6:[],
})
const allOptionsObj = () => globalOptions.plainOptions1.concat(globalOptions.plainOptions2).concat(globalOptions.plainOptions3).concat(globalOptions.plainOptions4).concat(globalOptions.plainOptions5).concat(globalOptions.plainOptions6);
const globalDefaultOption = (gindex) => {let res = []; globalOptions["plainOptions"+gindex].forEach((item,index)=>{res.push(item.value)});return res;}
const defaultOptions = () => globalDefaultOption(1).concat(globalDefaultOption(2)).concat(globalDefaultOption(3)).concat(globalDefaultOption(4)).concat(globalDefaultOption(5)).concat(globalDefaultOption(6));
const customStyle = 'background: #f7f7f7;border-radius: 4px;margin-bottom: 24px;border: 0;overflow: hidden';
const setinfo = reactive({isShow:false})
const loginfo = reactive({isShow:false})
const globalState = reactive({
  state: {
    indeterminate: true,
    checkAll: true,
    checkedList: [],
  },
  state1: {
    indeterminate: true,
    checkAll: true,
    checkedList: [],
  },
  state2: {
    indeterminate: true,
    checkAll: true,
    checkedList: [],
  },
  state3: {
    indeterminate: true,
    checkAll: true,
    checkedList: [],
  },
  state4: {
    indeterminate: true,
    checkAll: true,
    checkedList: [],
  },
  state5: {
    indeterminate: true,
    checkAll: true,
    checkedList: [],
  },
  state6: {
    indeterminate: true,
    checkAll: true,
    checkedList: [],
  }
})
const checkedList = () => globalState.state1.checkedList.concat(globalState.state2.checkedList).concat(globalState.state3.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList);

const onGlobalChange = (e, gindex) => {
  Object.assign(globalState["state"+gindex], {
    checkedList: e.target.checked ? globalDefaultOption(gindex) : [],
    checkAll:e.target.checked,
    indeterminate: false,
  });
}
const onCheckAllChange = (e: any) => {
  onGlobalChange(e, 1)
  onGlobalChange(e, 2)
  onGlobalChange(e, 3)
  onGlobalChange(e, 4)
  onGlobalChange(e, 5)
  onGlobalChange(e, 6)
  Object.assign(globalState.state, {
    checkedList: e.target.checked ? defaultOptions() : [],
    indeterminate: false,
  });
};
const realCheck = async (historyId, checkedobjlist, percentStep, listIndex) => {
    // console.log("listINdex",listIndex,timer.value)
    let {id, categoryCode} = checkedobjlist[listIndex];
    let categoryId = id;
    if(globalHistoryId.value != historyId)
    return;
    checkHealth({historyId, categoryId, categoryCode}).then((res)=>{



        // console.log('time',timer.value,listIndex,checkedobjlist.length)
        if(res.code == 0 && res.data){
            secondCheck(historyId, checkedobjlist, percentStep, listIndex)
        }else{
            console.log("else",categoryCode)
            // 500的情况
            if(listIndex >= checkedobjlist.length - 1){
                if(timer.value){
                    clearInterval(timer.value);
                }
                percent.value = 100;
                console.log("checkHealth100",listIndex,checkedobjlist.length)
            }else{
                percent.value += percentStep;
            }
            $(`#c${categoryId} .label-status-load`).css('display','none')
            $(`#c${categoryId} .label-status-error`).css('display','inline-block')
            if(Running.value && listIndex < checkedobjlist.length - 1)
                realCheck(historyId, checkedobjlist, percentStep, ++listIndex)
            else if(!isHandleStop.value){
                // console.log("result")
                if(timer.value){
                    clearInterval(timer.value);
                }
                Running.value = false;
                getResult(historyId)
            }
            // realCheck(historyId, checkedobjlist, percentStep, ++listIndex)
        }
    }).catch(()=>{
        console.log("catch",categoryCode)
        // 500的情况
        if(listIndex >= checkedobjlist.length - 1){
            if(timer.value){
                clearInterval(timer.value);
            }
            percent.value = 100;
            console.log("checkHealthcatch100",listIndex,checkedobjlist.length)
        }else
            percent.value += percentStep;
        $(`#c${categoryId} .label-status-load`).css('display','none')
        $(`#c${categoryId} .label-status-error`).css('display','inline-block')
        if(Running.value && listIndex < checkedobjlist.length - 1)
            realCheck(historyId, checkedobjlist, percentStep, ++listIndex)
        else if(!isHandleStop.value){
            // console.log("result")
            if(timer.value){
                clearInterval(timer.value);
            }
            Running.value = false;
            getResult(historyId)
        }
    })
}
const secondCheck = (historyId, checkedobjlist, percentStep, listIndex) => {
    let {id, categoryCode} = checkedobjlist[listIndex];
    let categoryId = id;
    if(globalHistoryId.value != historyId)
    return;
    selectHealthCheckResultByHistoryIdAndCategoryId({historyId, categoryId}).then((res)=>{
        if(Running.value && !res.data){
            console.log("Selse",categoryCode)
            setTimeout(()=>{
                secondCheck(historyId, checkedobjlist, percentStep, listIndex)
            },30000)
        }else{
            if(listIndex >= checkedobjlist.length - 1){
                if(timer.value){
                    
                    clearInterval(timer.value);
                }
                percent.value = 100;
                console.log("secondCheck100",listIndex,checkedobjlist.length)
            }else
                percent.value += percentStep;
            if(Running.value && globalHistoryId.value == historyId){
                if(res.code == 0){
                    if(res.data.result == 0){
                        $(`#c${categoryId} .label-status-load`).css('display','none')
                        $(`#c${categoryId} .label-status-correct`).css('display','inline-block')
                    }else if(res.data.result == 1){
                        $(`#c${categoryId} .label-status-load`).css('display','none')
                        $(`#c${categoryId} .label-status-warn`).css('display','inline-block')
                    }else{
                        $(`#c${categoryId} .label-status-load`).css('display','none')
                        $(`#c${categoryId} .label-status-error`).css('display','inline-block')
                    }
                }else{
                        $(`#c${categoryId} .label-status-load`).css('display','none')
                        $(`#c${categoryId} .label-status-error`).css('display','inline-block')
                    
                }
            }
            
            // console.log("rrrr",Running.value,listIndex,timeRaw.value,percent.value,isHandleStop)
            if(Running.value && listIndex < checkedobjlist.length - 1)
                realCheck(historyId, checkedobjlist, percentStep, ++listIndex)
            else if(!isHandleStop.value){
                // console.log("okresult")
                if(timer.value){
                    clearInterval(timer.value);
                }
                getResult(historyId)
                Running.value = false;
            }
        }
    }).catch(()=>{
        console.log("Scatch",categoryCode)
        if(listIndex >= checkedobjlist.length - 1){
            if(timer.value){
                clearInterval(timer.value);
            }
            percent.value = 100;
            console.log("secondCheckcatch100",listIndex,checkedobjlist.length)
        }else
            percent.value += percentStep;
        $(`#c${categoryId} .label-status-load`).css('display','none')
        $(`#c${categoryId} .label-status-error`).css('display','inline-block')
        if(Running.value && listIndex < checkedobjlist.length - 1)
            realCheck(historyId, checkedobjlist, percentStep, ++listIndex)
        else if(!isHandleStop.value){
            // console.log("result")
            if(timer.value){
                clearInterval(timer.value);
            }
            Running.value = false;
            getResult(historyId)
        }
    })
}
const handleCheck = async (e) => {
    // percent.value = 0;
    // Steps.value = globalState.state.checkedList.length;
    let percentStep = 100 / globalState.state.checkedList.length;
    // let step = 100 / globalState.state.checkedList;
    Running.value = e;
    Before.value = false;
    if(Running.value){
        isHandleStop.value = false;
        percent.value = 0;
        timeRaw.value = -1000 * 60 * 60 * 8;
        globalState.state.checkedList = checkedList();
        for(let i = 0; i < firstLevelList.value.length; i++){
            if(!globalState['state'+(i+1)].indeterminate && !globalState['state'+(i+1)].checkAll)
                $('#p'+(i+1)).css('display','none')
            else{
                globalOptions['plainOptions'+(i+1)].forEach((item,index)=>{
                    item.disabled = true;
                })

            }
        }
        proxy.$nextTick(()=>{
            $('.ant-checkbox').css('display','none')
        })
        $('.ant-checkbox-group-item').not('.ant-checkbox-wrapper-checked').css('display','none')
        $('.ant-checkbox-wrapper-checked .label-status-load').css('display','inline-block')
        let res = await createHealthCheck()
        if(res.code == 0 && res.data.id){
        createTime.value = res.data.createTime ? res.data.createTime : '-';

        let checkedobjlist = [];
            globalState.state.checkedList.forEach((item,index) => {
                allOptionsObj().forEach((t, i)=>{
                    if(t.categoryName == item)
                        checkedobjlist.push(t);
                    // if(t.categoryName == item)
                    //     realCheck(res.data.id, t.id, t.categoryCode, percentStep)
                })
            })
            timer.value = setInterval(()=>{
                timeRaw.value += 1000;
            }, 1000)
            // console.log('timer',timer.value,checkedobjlist)
            globalHistoryId.value = res.data.id;
            realCheck(res.data.id, checkedobjlist, percentStep, 0)
        }
    }else{
        isHandleStop.value = true;
        if(timer.value){
            clearInterval(timer.value);
        }
        getResult(globalHistoryId.value)
        Running.value = false;
    }
    
}
const logToResult = (historyId) => {
    loginfo.isShow = false;
    Before.value = false;
    getCountData(historyId)
    proxy.$nextTick(()=>{
        resultRef.value.setInfo(historyId)
    })
}
const back = () => {
    activeKey.value = [0, 1, 2, 3, 4, 5];
    Running.value = false;
    Before.value = true;
    proxy.$nextTick(()=>{
        $('.label-status').css('display','none')
    //     $('.ant-checkbox-group-item').css('display','inline-block')
    //     $('.ant-checkbox').css('display','inline-block')
    //     $('.label-status').css('display','none')
    //     $('.label-question').css('display','inline-block')
    //     $('.ant-collapse-item').css('display','block')
    //     $('.label-status-load').css('display','none')
    //     for(let i = 0; i < firstLevelList.value.length; i++){
    //         globalOptions['plainOptions'+(i+1)].forEach((item,index)=>{
    //             item.disabled = false;
    //         })

    //     }
    })
    SelectCategoryList();
}
const exportt = () => {
    const token = getToken();
    location.href = import.meta.env.VITE_BASE_API+'/sys/safe/downloadResult?historyId='+globalHistoryId.value+'&token='+token;
    // downloadResult({historyId:globalHistoryId.value});
}
const getResult = (historyId) => {
    changeCheckState(historyId);
}
const changeCheckState = async (id) => {
    let res = await updateHealthCheckState({id,state:Number(isHandleStop.value)+1})
    if(res.code == 0){
        getCountData(id)
        proxy.$nextTick(()=>{
            resultRef.value.setInfo(id)
        })
    }
}
const getCountData = async (historyId) => {
    let res = await statisticsHealthResult({historyId})
    if(res.code == 0){
        console.log("res",res)
        if(res.data){
            res.data.forEach((item,index) => {
                if(item.result == '0'){
                    countData.normal = Number(item.sumNum);
                }else if(item.result == '-1'){
                    countData.error = Number(item.sumNum);
                }else{
                    countData.warn = Number(item.sumNum);
                }
            })
            countData.total = countData.error + countData.normal + countData.warn;
        }
    }
}
const handleCheck1 = (e) => {
    percent.value = 0;
  Running.value = e;
  Before.value = !e;
  if(Running.value == true){
    globalState.state.checkedList = checkedList();
    defaultOptions().forEach((item,index)=>{
      if(globalOptions.plainOptions1.length >= index + 1)
      globalOptions.plainOptions1[index].disabled = true;
      if(globalOptions.plainOptions2.length >= index + 1)
      globalOptions.plainOptions2[index].disabled = true;
      if(globalOptions.plainOptions3.length >= index + 1)
      globalOptions.plainOptions3[index].disabled = true;
      if(globalOptions.plainOptions4.length >= index + 1)
      globalOptions.plainOptions4[index].disabled = true;
      if(globalOptions.plainOptions5.length >= index + 1)
      globalOptions.plainOptions5[index].disabled = true;
      if(globalOptions.plainOptions6.length >= index + 1)
      globalOptions.plainOptions6[index].disabled = true;
      if(!globalState.state.checkedList.includes(item)){
        $('.ant-checkbox-group-item').not('.ant-checkbox-wrapper-checked').css('display','none')
      }
    })
    console.log("重新")
    proxy.$nextTick(()=>{
        $('.ant-checkbox').css('display','none')
        $('.label-status').css('display','inline-block')
        $('.label-question').css('display','none')
    })
    
    setTimeout(()=>{
        percent.value = 30;
    },1000)
    setTimeout(()=>{
        percent.value = 50;
    },2000)
    setTimeout(()=>{
        percent.value = 80;
    },4000)
    setTimeout(()=>{
        percent.value = 100;
        if(percent.value >= 100){
            Running.value = false;
        }
    },6000)
    
  }else{
    defaultOptions().forEach((item,index)=>{
      if(globalOptions.plainOptions1.length >= index + 1)
      globalOptions.plainOptions1[index].disabled = false;
      if(globalOptions.plainOptions2.length >= index + 1)
      globalOptions.plainOptions2[index].disabled = false;
      if(globalOptions.plainOptions3.length >= index + 1)
      globalOptions.plainOptions3[index].disabled = false;
      if(globalOptions.plainOptions4.length >= index + 1)
      globalOptions.plainOptions4[index].disabled = false;
      if(globalOptions.plainOptions5.length >= index + 1)
      globalOptions.plainOptions5[index].disabled = false;
      if(globalOptions.plainOptions6.length >= index + 1)
      globalOptions.plainOptions6[index].disabled = false;
    })
    proxy.$nextTick(()=>{
      $('.ant-checkbox-group-item').css('display','inline-block')
        $('.ant-checkbox').css('display','inline-block')
        $('.label-status').css('display','none')
        $('.label-question').css('display','inline-block')
    })
  }
}
const openSet = () => {
    proxy.$nextTick(()=>{
        setRef.value.setInfo();
        setinfo.isShow = true;
    })
}
const openLog = () => {
    loginfo.isShow = true;
    proxy.$nextTick(()=>{
        logRef.value.getList();
    })
}
const initState = (length) => {
  globalState.state.checkAll = true
  Object.assign(globalState.state, {indeterminate: false,checkedList: defaultOptions()});
//   for(let i = 0; i < length; i++){
//     Object.assign(globalState["state"+(i+1)], {indeterminate: false,checkedList: globalDefaultOption(i+1)});
//   }
  proxy.$nextTick(()=>{
    $('.label-status').css('display','none')
  })
}
const SelectCategoryList = async () => {
  let res = await selectCategoryList()
  if(res.code == 0){
    console.log("res",res)
    firstLevelList.value = res.data.filter(item => item.categoryName != '安全检测');
    firstLevelList.value.forEach((item, index) => {
      item.childList.forEach((t,i) => {
        t.value = t.categoryName;
      })
      globalOptions["plainOptions"+(index+1)] = firstLevelList.value[index].childList;
      Object.assign(globalState["state"+(index+1)], {indeterminate: false,checkedList: globalDefaultOption(index+1)});
      if(index >= firstLevelList.value.length - 1){
        initState(firstLevelList.value.length);
      }
    })
    
  }
}
watch(
    () => globalState.state1.checkedList,
    val => {
        globalState.state1.indeterminate = !!val.length && val.length < globalOptions.plainOptions1.length;
        globalState.state1.checkAll = val.length === globalOptions.plainOptions1.length;
        globalState.state.indeterminate = !!val.concat(globalState.state3.checkedList).concat(globalState.state2.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length && val.concat(globalState.state3.checkedList).concat(globalState.state2.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length < defaultOptions().length;
        globalState.state.checkAll = val.concat(globalState.state3.checkedList).concat(globalState.state2.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length === defaultOptions().length;
        // globalState.state.checkedList = checkedList();
        // console.log("list",globalState.state1.checkedList, globalState.state.checkedList)
    },
);
watch(
    () => globalState.state2.checkedList,
    val => {
        globalState.state2.indeterminate = !!val.length && val.length < globalOptions.plainOptions2.length;
        globalState.state2.checkAll = val.length === globalOptions.plainOptions2.length;
        globalState.state.indeterminate = !!val.concat(globalState.state3.checkedList).concat(globalState.state1.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length && val.concat(globalState.state3.checkedList).concat(globalState.state1.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length < defaultOptions().length;
        globalState.state.checkAll = val.concat(globalState.state3.checkedList).concat(globalState.state1.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length === defaultOptions().length;
        // globalState.state.checkedList = checkedList();
    },
);
watch(
    () => globalState.state3.checkedList,
    val => {
        globalState.state3.indeterminate = !!val.length && val.length < globalOptions.plainOptions3.length;
        globalState.state3.checkAll = val.length === globalOptions.plainOptions3.length;
        globalState.state.indeterminate = !!val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length && val.concat(globalState.state3.checkedList).concat(globalState.state1.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length < defaultOptions().length;
        globalState.state.checkAll = val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length === defaultOptions().length;
        // globalState.state.checkedList = checkedList();
    },
);
watch(
    () => globalState.state4.checkedList,
    val => {
        globalState.state4.indeterminate = !!val.length && val.length < globalOptions.plainOptions4.length;
        globalState.state4.checkAll = val.length === globalOptions.plainOptions4.length;
        globalState.state.indeterminate = !!val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state3.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length && val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state3.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length < defaultOptions().length;
        globalState.state.checkAll = val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state3.checkedList).concat(globalState.state5.checkedList).concat(globalState.state6.checkedList).length === defaultOptions().length;
        // globalState.state.checkedList = checkedList();
    },
);
watch(
    () => globalState.state5.checkedList,
    val => {
        globalState.state5.indeterminate = !!val.length && val.length < globalOptions.plainOptions5.length;
        globalState.state5.checkAll = val.length === globalOptions.plainOptions5.length;
        globalState.state.indeterminate = !!val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state3.checkedList).concat(globalState.state4.checkedList).concat(globalState.state6.checkedList).length && val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state3.checkedList).concat(globalState.state4.checkedList).concat(globalState.state6.checkedList).length < defaultOptions().length;
        globalState.state.checkAll = val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state3.checkedList).concat(globalState.state4.checkedList).concat(globalState.state6.checkedList).length === defaultOptions().length;
        // globalState.state.checkedList = checkedList();
    },
);
watch(
    () => globalState.state6.checkedList,
    val => {
        globalState.state6.indeterminate = !!val.length && val.length < globalOptions.plainOptions6.length;
        globalState.state6.checkAll = val.length === globalOptions.plainOptions6.length;
        globalState.state.indeterminate = !!val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state3.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).length && val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state3.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).length < defaultOptions().length;
        globalState.state.checkAll = val.concat(globalState.state2.checkedList).concat(globalState.state1.checkedList).concat(globalState.state3.checkedList).concat(globalState.state4.checkedList).concat(globalState.state5.checkedList).length === defaultOptions().length;
        // globalState.state.checkedList = checkedList();
    },
);
onMounted(() => {
  SelectCategoryList()
  
})
</script>
<style lang='scss' scoped>
// .back-page{overflow-x: auto;}
.health-header{background-color: #fff;border-bottom: 2px solid #f0f2f5;}
.innerPadding{height: calc(100vh - 200px);overflow:auto;min-width: auto;}
:deep(.ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-extra){float: left;}
:deep(.ant-checkbox-group-item){white-space: nowrap;border: 1px solid #f0f2f5;background-color: #fff; padding: 4px 12px;width: calc((100% - 40px)/5);margin-top: 8px;}
.progress-outer{width: calc((100% - 88px) * 0.8);}
:deep(.ant-page-header-heading-title){overflow:initial;text-overflow:initial;}
:deep(.run-header .ant-page-header-heading-title){line-height: 22px;}
:deep(.run-header .ant-page-header-content){padding-top:2px;}
.label-status{margin-top: 4px;float: right;}
.label-question{margin-top: 5px;float: right;font-size: 12px;color: #666;}
.label-status-warn{color: #faad14;}
.label-status-error{color: #ff4d4f;}
.label-status-correct{color: #52c41a;}
.label-status-load{font-weight: bold;}
.unchecked{display: none;}
:deep(.ant-checkbox-disabled + span){color:inherit}
</style>