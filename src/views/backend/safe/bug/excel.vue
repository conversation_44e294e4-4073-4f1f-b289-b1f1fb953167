<template>
    <div class=''>
        <!-- 导入文件modal -->
        <a-modal
        title="导入信息"
        v-model:visible="excel.fileFlag"
        :maskClosable="false"
        :footer="null"
        :centered="true"
         width="1008.8px !important"
        >
        
        <div style="margin: 20px"></div>
        <div class="cardBox">
            <div class="leftDiv"></div>
            <div class="rightDiv">
            <h3>上传文件</h3>
            <span>
                上传文件格式仅支持json，且文件大小不得超过5M
            </span>
            <div>
                <!-- <CloudUploadOutlined
                v-if="!uploadLoadingFlag"
                style="color: #1990ff"
                /> -->
                <!-- <LoadingOutlined v-if="uploadLoadingFlag" style="color: #1990ff" /> -->
                <a-input type="file" style="border:none" @change="importScript" />
            </div>
            </div>
        </div>
        <p>{{data}}</p>
        <div style="text-align: center; margin-top: 20px">
            <a-button type="primary" size="large" @click="()=>{excel.fileFlag = false}">
            完成
            </a-button>
        </div>
        </a-modal>
    </div>
</template>
<script lang="ts" setup>
// import { getToken } from '@/backstage/utils/auth';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { getLength, jsReadFiles } from '@/utils/tool';
import {getcveResultState, postimportCVE } from "@/api/backend/systems/auth";
import { message } from 'ant-design-vue';
import emiter from '@/utils/Bus';
const {proxy} = getCurrentInstance()
const data = reactive()
const emit = defineEmits(['setRun','getlist'])
const props = defineProps({
    excel:{
        type:Object,
        default(){
            return {
                fileFlag:false
            }
        }
    },
})
const Jsdata=ref([])

 const importScript =async (e) => {
    emiter.emit("allLoading", true)
     jsReadFiles(e.target.files,'textareaNode').then((res)=>{
        Jsdata.value=res;
        finishBtn(res)
     }).catch((err)=>{
        emiter.emit("allLoading", false)
        message.error('上传失败')
     })
    
     
    // if(res){
    //     serverform.userData = res
    // }
  
}
 const finishBtn = async(val) =>{
        // props.excel.fileFlag = false;
        if(val.length>0){
            postimportCVE(JSON.parse(val)).then((res)=>{
                if(res.data.uuid){
                    requestState(res.data.uuid)
                }else{
                    emiter.emit("allLoading", false)
                    message.error('上传失败')
                }
            }).catch((err)=>{
                emiter.emit("allLoading", false)
                message.error('上传失败')
            })
        }else{
            emiter.emit("allLoading", false)
            message.error('未读取内容');
        }
      
    }

const requestState = async (uuid) => {
    emit('setRun',true);
    getcveResultState({uuid}).then((res1)=>{
        if(res1.code == 0){
            // console.log('res1',res1)
            if(res1.data.importResult == 'running'){
                setTimeout(()=>{
                    requestState(uuid)
                },1500)
            }else if(res1.data.importResult == 'success'){
                emiter.emit("allLoading", false)
                message.success('批量导入成功');
                emit('getlist');
                emit('setRun',false);
            }else{
                emiter.emit("allLoading", false)
                emit('setRun',false);
                message.error('批量导入失败：'+res1.data.resultInfo)
            }
        }
    }).catch((err)=>{
        emiter.emit("allLoading", false)
        message.error(!res1.msg || res1.msg == 'success' ? '批量导入失败' : res1.msg)
    })
    
}
    
//   const data={userData:""}
//     jsReadFiles(e.target.files,'textareaNode',data)
//     console.log('imop',data.userData)
   
   

</script>
<style lang='scss' scoped>
.cardBox {
    width: 80%;
    border-radius: 5px;
    margin: 0 auto;
    border: 1px solid #ccc;
    min-height: 132px;
    vertical-align: middle;
    display: flex;
}
.leftDiv {
    display: inline-block;
    background: #1990ff;
    width: 15px;
    min-height: 130px;
    border-radius: 4px 0 0 4px;
}

.rightDiv {
    display: inline-block;
    min-height: 130px;
    margin-left: 23px;
    box-sizing: border-box;
    padding-top: 20px;
}

.alertBox {
    width: 80%;
    margin: 0 auto;
}

.successBox {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    padding: 10px 20px;
}

.errorBox {
    background-color: #fff1f0;
    border: 1px solid #ffa39e;
    padding: 10px 20px;
}
.ant-modal{width: 1000.8px !important;}
</style>