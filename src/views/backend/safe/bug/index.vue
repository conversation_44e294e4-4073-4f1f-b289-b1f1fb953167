<template>
    <div class="cloudContent">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                <a-form-item label="标题">
                    <a-input placeholder="请输入标题" @pressEnter="handleSearch" v-model:value="searchform.cveId" allowClear />
                </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                     <a-button type="primary" class="btnMargin" @click="handleAdd1"> 批量导入</a-button>
                </a-row>
                <a-table
                 :columns="columns" 
                 row-key="cveId" 
                 :data-source="imagelist" 
                 :pagination="pagination" 
                 @change="changeTable" 
                 :loading="{tip:'导入中...',spinning:isRunning}"
                 :scroll="{x:true}">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                     <template #cveId={record}>
                          <a  @click="getcveId(record)">{{record.cveId}}</a>
                     </template>
                    <template #vulLevel={record}>
                         <span v-if="record.vulLevel=='low'">低危</span>
                         <span v-else-if="record.vulLevel=='medium'">中危</span>
                         <span v-else-if="record.vulLevel=='high'">高危</span>
                         <span v-else-if="record.vulLevel=='critical'">致命</span>
                         <span v-else>--</span>
                    </template>
                    <template #score={record}>
                        <span v-if="record.score==null">--</span>
                         <span v-else>{{record.score}}</span>
                    </template>
                    <template #architecture={record}>
                        <span v-if="record.architecture==null">--</span>
                        <span v-else>{{record.architecture}}</span>
                    </template>
                 </a-table>
                 <Cveinfo  :info="info2" />
                <!-- <Info ref="imageDialog" :info="info" @getlist="getList" /> -->
                 <Excels  ref="excel1"  :excel="info1" @getlist="getList" @setRun="(e)=>{isRunning = e}" />
            </div>
        </div>
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Excels from "./excel.vue";
import Cveinfo from './cveinfo.vue'
import Menu from "@/components/cloudmenu/cloudmenu.vue";

import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { getcveList} from "@/api/backend/systems/auth";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { PlusOutlined, ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { useRoute } from "vue-router";
const { proxy } = getCurrentInstance();
const route = useRoute()
const imageDialog = ref(null);
const excel1 = ref(null);
const isRunning = ref(false)
const loading = ref(false);
const imagelist = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false
})
const info1 = reactive({
    fileFlag:false
    })

const info2 = reactive({
    isAdd:true,
    isShow:false,
    cveId:"",
    vulLevel:"",
    architecture:"",
    source:"",
    cveDescription:""
})
   

const searchform = reactive({
    cloudId:localStorage.getItem('cloudId'),
    cveId:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
     {title: 'ID', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:90,align:'center'},
    {title: 'CVE编号', dataIndex: 'cveId',slots: { customRender: 'cveId' }, key: 'id',align:'center'},
    {title: '等级', dataIndex: 'vulLevel', slots: { customRender: 'vulLevel' }, key: 'id',align:'left'},
    {title: '状态', dataIndex: 'status', key: 'id',align:'center'},
    {title: '评分', dataIndex: 'score',slots:{customRender: 'score'}, key: 'id',align:'center'},
    {title: '架构', dataIndex: 'architecture',slots:{customRender: 'architecture'}, key: 'id',align:'center'},
    {title: '发布时间', dataIndex: 'pubTime', key: 'id',align:'center'},
   
];
const getList = async () => {
    searchform.cloudId = localStorage.getItem('cloudId');
    imagelist.value = await proxy.$getList(loading, getcveList, searchform, pagination, getList )
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    cveId: ""
  })
//   getList();
}

const handleAdd1 = () => {
      info1.fileFlag = true;
}
const getcveId=(record)=>{
     info2.isShow=true
     info2.cveId=record.cveId
    info2.vulLevel=record.vulLevel
    info2.architecture=record.architecture
    info2.source=record.source
    info2.cveDescription=record.cveDescription
    
}

const toAddExcel = () => {
  excel.fileFlag = true;
}
onMounted(() => {
    getList()
    if(route.path == '/admin/devops/openstack')
        proxy.$mitt.on('getlist',getList)
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
// .cloudRight{min-width: 750px;}
</style>