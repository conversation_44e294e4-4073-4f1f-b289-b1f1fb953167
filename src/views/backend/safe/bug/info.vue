<template>
    <div class=''>
        <a-modal 
        :title="info.isInfo ? '查看' : (info.isAdd ? '新增':'修改')"
        v-model:visible="info.isShow"
        @cancel="cancel"
        :maskClosable="info.isInfo"
        centered
        :getContainer="modalBindNode"
        >
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="projectform" ref="projectForm" :rules="rules" :labelCol="{ span: 4}">
                <!-- <a-form-item label="域名" name="domainId">
                    <a-select v-model:value="projectform.domainId" placeholder="请选择" :disabled="info.isInfo || !info.isAdd" allow-clear> 
                        <a-select-option v-for="(item,index) in options3" :key="index" :value="item.id+''" >{{item.domainName}}</a-select-option>
                    </a-select>
                </a-form-item> -->
                <a-form-item label="标题" name="noticeTitle">
                    <a-input v-model:value.trim="projectform.noticeTitle" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="公告ID" name="noticeId">
                    <a-input v-model:value.trim="projectform.noticeId" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item name="noticeType" label="公告类型"  >
                        <a-radio-group v-model:value="projectform.noticeType"  :disabled="info.isInfo">
                        <a-radio :value="1">安全公告</a-radio>
                        <a-radio :value="2">BUG公告</a-radio>
                        <a-radio :value="3">特性公告</a-radio>
                        </a-radio-group>
             </a-form-item >
             <a-form-item name="dangerLevel" label="紧急程度" >
                        <a-radio-group v-model:value="projectform.dangerLevel"  :disabled="info.isInfo">
                        <a-radio :value="1">低级</a-radio>
                        <a-radio :value="2">中等</a-radio>
                        <a-radio :value="3">重要</a-radio>
                        <a-radio :value="4">危急</a-radio>
                        </a-radio-group>
            </a-form-item >
                <a-form-item label="修复bug" name="bugInfo">
                    <a-textarea v-model:value.trim="projectform.bugInfo" placeholder="请输入" allow-clear  :disabled="info.isInfo" />
                </a-form-item>
                <a-form-item label="修复CVE" name="cveInfo">
                    <a-textarea v-model:value.trim="projectform.cveInfo" placeholder="请输入" allow-clear   :disabled="info.isInfo"/>
                </a-form-item>
                <a-form-item label="影响产品" name="ruleName5"  >
                      <a-select v-model:value="projectform.ruleName5" :options="options1" mode="multiple" placeholder="请选择" :getPopupContainer="triggerNode => triggerNode.parentNode" :disabled="info.isInfo" allow-clear> 
                         </a-select>
                </a-form-item>
                <a-form-item label="公告说明" name="noticeInfo">
                    <a-textarea v-model:value.trim="projectform.noticeInfo" placeholder="请输入"  :disabled="info.isInfo" allow-clear />
                </a-form-item>
                <a-form-item label="特别说明" name="peculiarityInfo">
                    <a-input v-model:value.trim="projectform.peculiarityInfo" placeholder="请输入"  :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="修复方法" name="updateInfo">
                    <a-textarea v-model:value.trim="projectform.updateInfo" placeholder="请输入"  :disabled="info.isInfo" allow-clear />
                </a-form-item>
                 <a-form-item label="软件包" name="softwareAddress">
                    <a-textarea v-model:value.trim="projectform.softwareAddress" placeholder="请输入"  :disabled="info.isInfo" allow-clear />
                </a-form-item>
                  <a-form-item label="CVE链接" name="cveAddress">
                    <a-textarea v-model:value.trim="projectform.cveAddress" placeholder="请输入"  :disabled="info.isInfo" allow-clear />
                </a-form-item>
                
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {saveRuleserve,updateRuleserve } from "@/api/backend/devops/config"
import {selectServerList } from "@/api/backend/devops/server"
import { selectDictList } from '@/api/backend/systems/dictionary';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})

const projectForm = ref();
const serverlist = ref([]);
 const options1 = ref([{
      value: '1050a',
      label: '1050a',
    },
    {
      value: '1050e',
      label: '1050e',
    }]);
const defaultform = {
    cloudId:localStorage.getItem('cloudId'),
    noticeTitle:'',
            noticeId:'',
            noticeType:1,
            dangerLevel:1,
            bugInfo:'',
            cveInfo:'',
            noticeProductEntityList:[],
            noticeInfo:'',
            peculiarityInfo:'',
            updateInfo:'',
            softwareAddress:'',
            cveAddress:''
}
const projectform = reactive({
    cloudId:localStorage.getItem('cloudId'),
     noticeTitle:'',
            noticeId:'',
            noticeType:1,
            dangerLevel:1,
            bugInfo:'',
            cveInfo:'',
            noticeProductEntityList:[],
            noticeInfo:'',
            peculiarityInfo:'',
            updateInfo:'',
            softwareAddress:'',
            cveAddress:''
})
const rules = {
   noticeTitle: [{ required: true, message:'请填写标题', trigger: "blur" },
            { max:90, message:'标题最多90个字符', trigger: ["blur","change"]  }],
            noticeId: [{ required: true, message:'请填写公告ID', trigger: "blur" },
            {pattern:/^[\u0021-\u007E]*$/, message:"不允许中文字符和空格",trigger: ["blur","change"]}],
            noticeType: [{ required: true, type:'number', message:'请选择公告类型', trigger: "change" }],
            dangerLevel: [{ required: true, type:'number', message:'请选择紧急程度', trigger: "change" }],
            noticeInfo: [{ required: true, message:'请填写公告说明', trigger: "blur" }],
    //  maxCpu:[{required:true, type:'number', message:'请选择',trigger:'change'}],

    // maxCpu:[{required:true, type:'number', message:'仅支持输入1-100之间的整数',trigger:'change',transform(value){ 
    //                     if(value){
    //                         // 将输入的值转为数字
    //                         var val = Number(value)
    //                         // 正则表达式校验输入的数字是否在0-100之内并且属于整数
    //                         if(/^(?:[1-9]?\d|100)$/.test(val)) return val
    //                         // 返回false即为校验失败
    //                         return false
    //                     }
    //                 }}],
 
    serverIds:[{required:true, message:'请选择',trigger:'change'}],
   

   
}
const options3 = ref([])
const getServerList = async () => {
    let res = await selectServerList()
    if(res){
        if(res.code == 0){
            // Object.assign(serverlist,res.data);
            serverlist.value = res.data;
        }
    }
}
const handleSave = () => {
    // let projectform1 = {...projectform}
    // proxy.$handleSave(projectForm.value, saveRuleserve, updateRuleserve, props,projectform1, ()=>{cancel();emit('getlist');},null,()=>{
    //     if(projectform1.allServer == true){
    //       projectform1.allServer = 1;
    //     }
       
    //     else{
    //       projectform1.allServer = 0;
    //       projectform1.servers=[];
    //     if(projectform1.serverIds && projectform1.serverIds.length > 0)
    //     projectform1.serverIds.forEach((id,index)=>{
    //         projectform1.servers.push({id})
    //     })
    //     }
      
    // })
     cancel();
}
const cancel = () => {

   props.info.isShow = false
    props.info.isInfo = false
    // isInfo.value = false
    projectForm.value.resetFields()
    Object.assign(projectform,defaultform)
}
onMounted(() => {})
defineExpose({projectform})
</script>
<style lang='scss' scoped>
</style>