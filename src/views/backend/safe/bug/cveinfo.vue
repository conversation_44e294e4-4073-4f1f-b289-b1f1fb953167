<template>
    <div class='box'>
    <a-modal 
       width="600px !important"
        :title="null"
        v-model:visible="info.isShow"
        :maskClosable="true"
        centered
        :footer="null">
        <div class="hosModal">
             <div class="cvehead">{{info.cveId}}</div>
             <p class="fontsize">漏洞详情</p>
            <div class="table">
                <div class="table_head">
                    <span class="spansize">漏洞编号</span>
                    <div class="textsize">{{info.cveId}}</div>
                </div>
                <div  class="table_head">
                    <span class="spansize">漏洞等级</span>
                    <div class="textsize">
                         <span v-if="info.vulLevel=='low'">低危</span>
                         <span v-else-if="info.vulLevel=='medium'">中危</span>
                         <span v-else-if="info.vulLevel=='high'">高危</span>
                         <span v-else-if="info.vulLevel=='critical'">致命</span>
                         <span v-else>--</span>
                    </div>
                </div>
                <div  class="table_head">
                    <span class="spansize">软件包名</span>
                    <div class="textsize">{{info.architecture}}</div>
                </div>
                <div  class="table_head">
                    <span class="spansize">CVSS v3.1评分</span>
                    <div class="textsize">{{info.source}}</div>
                </div>
            </div>
            <p class="mar">漏洞描述</p>
            <textarea class="tarea" :value='info.cveDescription'/>
           </div>
           
    </a-modal>
    </div>
</template>

<script  lang='ts' setup>
import { getCurrentInstance, onMounted,computed, reactive, ref } from 'vue';
import { getCveHostList,postfixCVE,getprogress } from "@/api/backend/systems/auth";
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false,
                cveId:"",
                vulLevel:"",
                architecture:"",
                source:"",
                cveDescription:""
                
            }
        }
    }
})
</script>
<style lang="scss" scoped>
  .hosModal{
      width: 100%;
      height: 320px;
  }
  .cvehead{
      padding-bottom:18px ;
      margin-bottom:14px ;
      font-size:17px ;
      font-weight:600 ;
      border-bottom:2px solid #ccc ;
  }
  .fontsize{
      font-weight:600 ;
      margin: 0;
      margin-bottom:6px ;
  }
  .textsize{
      margin-top:5px ;
      color:#333;
  }
  .table_head{
      text-align: center;
  }
  .spansize{
      font-size:14px ;
      color: #999;
  }
  .table{
      display: flex;
      justify-content: space-around;
  }
  .mar{
      margin:0 ;
      margin-top:10px ;
      margin-bottom:7px ;
      font-weight:600 ;
  }
  .tarea{
       width: 100%;
  min-height: 130px;
  padding: 10px;
  background: #f2f3f5;
  border: 1px solid #d6dbe3;
  border-radius: 5px;
  outline: none;
  // 右下角斜杠不显示
  resize: none;
  &:focus-visible {
    border: 1px solid #d6dbe3;
  }
  &:hover {
    border: 1px solid #d6dbe3;
  }
  &:active {
    border: 1px solid #d6dbe3;
  }
  &:focus {
    border: 1px solid #d6dbe3;
  }
  }  
</style>