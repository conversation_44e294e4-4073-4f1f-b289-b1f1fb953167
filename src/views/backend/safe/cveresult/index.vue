<template>
    <div class="uncloudRight">
        <div style="min-width:950px">
            <a-card class="head_titlecve">
                <a-row>
                    <a-col :span="4">
                        <a-statistic class="border_cve" title="存在漏洞" :value="cvelist.cve_count" />
                    </a-col>
                    <a-col :span="4">
                        <a-statistic class="border_cve" title="存在高危及以上漏洞"  :value=' cvelist.high_cve_count '/>
                    </a-col>
                    <a-col :span="4" >
                        <a-statistic  class="border_cve" title="存在漏洞主机"  :value='cvelist.affect_host_count'  />
                    </a-col>
                    <a-col :span="4">
                        <a-statistic  class="border_cve" title="今日修复"  :value=' cvelist.cvefix_today_count' />
                    </a-col>
                    <a-col :span="4">
                        <a-statistic  class="border_cve" title="累计修复"  :value=' cvelist.cvefix_all_count' />
                    </a-col>
                    <a-col :span="4">
                        <div class="head_cve">
                            <p class="title_cve">漏洞扫描</p>
                            <a-button class="btnMargin" type="primary"  :loading="iconLoading" @click="Onescan">
                                {{text.info ? '扫描中':'一键扫描'}}
                            </a-button>
                        </div>
                    </a-col>
                </a-row>
            </a-card>
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform">
                    <a-form-item label="CVE编号">
                        <a-input placeholder="请输入CVE编号" @pressEnter="handleSearch" v-model:value="searchform.cveId" allowClear />
                    </a-form-item>
                        <a-form-item label="CVE等级" name="consumer" >
                        <a-select v-model:value="searchform.level"  placeholder="请选择" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear class="select_cve"> 
                            <a-select-option value="medium">中危</a-select-option>
                            <a-select-option value="high">高危</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item>
                        <a-button type="primary" @click="handleSearch"> {{ $t("m.search") }} </a-button>
                    </a-form-item>
                    <a-form-item>
                        <a-button @click="handleAllReset"> {{ $t("m.reset") }} </a-button>
                    </a-form-item>
                </a-form>
            </div>
            <div class='innerPadding'>
                <a-table :columns="columns" row-key="id" :data-source="sultlist" :pagination="pagination" @change="changeTable">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #vul_level={record}>
                        <!-- {{record.vul_level}} -->
                        <span v-if="record.vul_level == 'low'">低危</span>
                        <span v-else-if="record.vul_level == 'medium'">中危</span>
                        <span v-else-if="record.vul_level == 'high'">高危</span>
                        <span v-else-if="record.vul_level=='critical'">致命</span>
                        <span v-else>--</span>
                    </template>
                    <template #affect_host_count={record}>
                            <a @click="getHostId(record)">{{record.affect_host_count}}</a>
                    </template>
                    <template #action={record}>
                        <a-button class="button_V" @click="handleView(record)" >修复</a-button>
                    
                    </template>
                </a-table>
                <Hostinfo  ref='hostlist' :info="info"  /> 
                <Hostfix  ref="fixlist" :info='info1' @getlist="Onestatus"/>
            </div>
        </div>
    </div>
</template>
<script lang='ts' setup>
import Hostinfo from './hostinfo.vue'
import Hostfix from './fix.vue'
import { getCurrentInstance, onMounted, reactive, ref,defineComponent } from 'vue';
import { getRuleservelist, deleteRuleserve } from "@/api/backend/devops/config";
import { getScanResultList, getStatisticsList,getexecuteScan,getselectExecuteScanStatus } from "@/api/backend/systems/auth";
import { PlusOutlined, ExclamationCircleOutlined,QuestionCircleTwoTone,LoadingOutlined } from "@ant-design/icons-vue";
import { useRoute } from "vue-router";
import { time } from 'echarts';
import { message, Modal } from 'ant-design-vue';
import emiter from "@/utils/Bus";
const { proxy } = getCurrentInstance();
const route = useRoute()
const imageDialog = ref(null);
const excel1 = ref(null);
const hostlist=ref()
const fixlist=ref()
const loading = ref(false);
const sultlist = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false,
    title:""
    // affect_host_count:""
})
const info1=reactive({
    isAdd:true,
    isShow:false,
})
const iconLoading=ref(false)

const searchform = reactive({
    // cloudId:localStorage.getItem('cloudId'),
    pageIndex:1,
    pageSize:10,
    level:undefined,
    cveId:''

})
const cvelist=reactive({
    cve_count:'',
    high_cve_count:'',
    affect_host_count:'',
    cvefix_today_count:'',
    cvefix_all_count:''
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
        {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
        {title: 'CVE编号', dataIndex: 'cve_id', key: 'id',align:'left'},
        {title: '漏洞等级', dataIndex: 'vul_level',  slots: { customRender: 'vul_level' }, key: 'id',align:'left'},
        {title: '涉及主机', dataIndex: 'affect_host_count',slots: { customRender: 'affect_host_count' }, key: 'id',align:'center'},
        {title: '发布时间', dataIndex: 'pub_time',slots: { customRender: 'pub_time' },  key: 'id',align:'center'},
        {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
const getList = async () => {
    sultlist.value = await proxy.$getList(loading, getScanResultList, searchform, pagination, getList )
   
}
var timer=null
const text=reactive({
    info:false
})
const Onestatus = async (isButton)=>{
    // timer=setInterval(async()=>{
      let res=await getselectExecuteScanStatus()
      if(res.code==0){
            if(res.data.status=="success"){
                // clearInterval(timer)
                if(isButton){
                    Modal.destroyAll();
                    Modal.success({
                        content: () => '已完成扫描'
                    });
                }
                iconLoading.value=false;
                emiter.emit('allLoading',false)
                text.info=false;
                getcvelist()
                getList()
            }else if(res.data.status == 'running'){
                iconLoading.value=true;
                emiter.emit('allLoading',true)
                setTimeout(()=>{
                    Onestatus(isButton)
                },2000)
            }else{
                message.error('扫描失败')
                iconLoading.value=false
                emiter.emit('allLoading',false)
                text.info=false
            }
      }
  
    
    // },3000)
    // let res=await getexecuteScan()
   
}
const Onescan=() =>{
    iconLoading.value=true;
    emiter.emit('allLoading',true)
    getexecuteScan().then((res)=>{
        console.log('res',res)
        if(res.code == 0){
            if(res.data === true){
                Onestatus(true)
            }else{
                if(res.code == 0)
                    message.error((!res.msg || res.msg == 'success') ? '扫描失败' : res.msg);
                iconLoading.value=false;
                emiter.emit('allLoading',false)
                text.info=false;
            }
        }else{
            if(res.code == 0)
                message.error((!res.msg || res.msg == 'success') ? '扫描失败' : res.msg);
            iconLoading.value=false;
            emiter.emit('allLoading',false)
            text.info=false;
        }
    }).catch((err)=>{
        message.error('扫描失败');
        iconLoading.value=false;
        emiter.emit('allLoading',false)
        text.info=false;
    })
}

const getcvelist=async()=>{
    let res=await getStatisticsList();
    if(res){
        //  cvelist=res.data
         Object.assign(cvelist,res.data)
         
     }
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageIndex:1,
    pageSize:10,
    level:undefined,
    cveId:''
  })
  getList();
};
const getHostId=(record)=>{
    info.isShow=true
    info.title=record.cve_id
    proxy.$nextTick(()=>{

        hostlist.value.getList(record)
    })

}
const handleView=(record)=>{
    info1.isShow=true

   proxy.$nextTick(()=>{
        fixlist.value.getList(record)
    })
}


onMounted(() => {
    // getList()
     Onestatus()
    // getcvelist()
    // if(route.path == '/admin/devops/openstack')
    // proxy.$mitt.on('getlist',getList)
    })
</script>
<style lang="scss" scoped>
.head{
    
    padding: 0px 15px 0 15px;
    height: calc(100vh - 136px)
}
.cloudRight{
    width: calc(100%);
    overflow-y: auto;
    background-color: #fff;
}
.head_titlecve{
    margin-bottom:5px ;
}
.buttonPadding{
    margin-top:5px ;
}
.head_cve{
    margin-top:-3px ;
}
.title_cve{
       color: rgba(0, 0, 0, 0.45);
}
.border_cve{
   border-right:1px solid rgba(0, 0, 0, 0.06);
   margin-right:20px ;
}
.select_cve{
    width: 150px;
}
</style>