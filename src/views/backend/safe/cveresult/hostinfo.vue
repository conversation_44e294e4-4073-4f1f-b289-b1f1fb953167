<template>
    <div class='box'>
    <a-modal 
       width="1200px !important"
        :title="null"
        v-model:visible="info.isShow"
        @cancel="Cvefn"
        :maskClosable="false"
        centered
        :footer="null"
        :getContainer="modalBindNode">
        <div class="infohead">
          <div class="head_left">{{info.title}}-涉及主机</div>
          <div class=head_right>
              <a-button @click="Repain(selectedRowKeys)">一键修复</a-button>
          </div>
        </div>
        
        <div class="infocenter" v-if="isAll.info">
            <a-progress  v-if="isAll.isprogress" :percent="list.progress" status="active" class="progress"/>
            <a-alert  v-if="isAll.issuccess"  :message="`修复成功，共${tack_data.task_host_count}台主机，成功${tack_data.task_fail_host_count}台`" show-icon  type="success"  class="progress"/>
            <a-alert v-if="isAll.iserr" :message="`修复失败，共${tack_data.task_host_count}台主机，失败${tack_data.task_fail_host_count}台`" show-icon  type="error"  class="progress"/>
            <a-alert message="修复完成后，请重启相关主机或者相关服务保证修复生效。" show-icon  type="info" />
        </div>
           <div class="hosModal">
                    <a-table :row-selection="rowSelection" :columns="columns" row-key="id" :data-source="HostDataList"  :pagination="pagination" @change="changeTable">
                        <template #index={record,index}>
                            {{index+1+(pagination.pageSize * (pagination.current-1))}}
                        </template>
                        <template #status={record}>
                            <span v-if='record.status==0'>可连接</span>
                            <span v-else>不可连接</span>
                        </template>
                    </a-table>


           </div>
           
    </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted,computed, reactive, ref } from 'vue';
import { getCveHostList,postfixCVE,getprogress } from "@/api/backend/systems/auth";
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false,
                title:""
            }
        }
    }
})
const searchform = reactive({
    cloudId:localStorage.getItem('cloudId'),
    pageIndex:1,
    pageSize:10,
    level:undefined,
    cve_id:'',
    id:'',
})

const HostDataList = ref([]);
const loading = ref(false);
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
        {title: '主机名称', dataIndex: 'hostname', key: 'id' ,align:'center'},
        {title: '主机IP', dataIndex: 'ip', key: 'id',align:'left'},
        {title: '主机状态', dataIndex: 'status',  slots: { customRender: 'status' }, key: 'id',align:'left'},
        {title: '描述', dataIndex: 'description', key: 'id',align:'center'},
        
];

const getList = async (record) => {
    list.progress=0
    searchform.id = record.id;
    searchform.cloudId = localStorage.getItem('cloudId');
    let  res=await getCveHostList(searchform)
   if(res){
       HostDataList.value=res.data.hosts
    }
   
}

const selectedRowKeys = ref([]);

const rowSelection = computed(() => {
  return {
       selectedRowKeys: selectedRowKeys.value,
    onChange: (selectedRowKey: any, selectedRows: any) => {
        selectedRowKeys.value = [...selectedRowKey];
    }
     };
});
const isAll=reactive({
    info:false,
    iserr:false,
    isprogress:false,
    issuccess:false,
})

 const tack_data=reactive({
     task_host_count:0,
     task_fail_host_count:0,
     task_success_host_count:0
 })

const  list=reactive({
    progress:0,
})

var timer=null
const Repain=async(val)=>{
     list.progress=0
     isAll.info=true
    console.log("val",val)
     const hostIds=val
      let res=await postfixCVE(hostIds,{id:searchform.id})
      if(res){
        
        // console.log("修复",res.data.uuid)
         list.progress=0
        isAll.isprogress=true
         isAll.iserr=false
         isAll.issuccess=false
       getlist(res.data.uuid)
    }}

const getlist=(id)=>{
     
    timer=setInterval(async()=>{
          let  resdata=await getprogress({uuid:id})
                if(resdata){
                           list.progress=0
                        if(resdata.data.status=="running"){
                           
                            isAll.isprogress=true
                            list.progress=resdata.data.progress
                            
                         }
                        if(resdata.data.status=="failed"){
                              list.progress=100
                               isAll.isprogress=false
                               isAll.iserr=true
                          tack_data.task_fail_host_count=resdata.data.task_fail_host_count
                          tack_data.task_host_count=resdata.data.task_host_count
                         clearInterval(timer);
                             
                        }
                        if(resdata.data.status=="success"){
                              list.progress=100
                                isAll.isprogress=false
                               isAll.issuccess=true
                               tack_data.task_host_count=resdata.data.task_host_count
                               tack_data.task_success_host_count=resdata.data.task_success_host_count
                              clearInterval(timer);
                        } 
                         
                }
    },2000)
  
}
const Cvefn=()=>{
      selectedRowKeys.value=[];
      isAll.isprogress=true
      isAll.iserr=false
      isAll.issuccess=false
      isAll.info=false
      list.progress=0
      clearInterval(timer);
}
onMounted(()=>{})

defineExpose({getList})
</script>
<style lang='scss' scoped>
.hosModal{
    height: 600px;
    overflow-y: auto;
}
.infohead{
    display: flex;
    justify-content: space-between;
    padding-bottom:20px ;
    border-bottom:1px solid gray ;
    margin-bottom:20px ;
}
.head_left{
    font-size: 18px;
    font-weight:600;
}
.head_right{
    margin-right:100px ;
}
.progress{
    margin-bottom:20px ;
}
.infocenter{
     margin-bottom:20px ;
}
</style>