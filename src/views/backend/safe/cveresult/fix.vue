<template>
    <div class='box'>
    <a-modal 
       width="460px !important"
       v-model:visible="info.isShow"
        centered
        @cancel="cvefn"
        :footer="null"
        title="修复"
        :maskClosable="true"
        :getContainer="modalBindNode"
        >
           <div class="hosModal">   
              <a-progress v-if="isAll.isprogress" :percent="list.progress" status="active" class="progress"/>
             <a-alert v-if="isAll.iserr" :message="`修复失败，共${tack_data.task_host_count}台主机，失败${tack_data.task_fail_host_count}台`" show-icon  type="error"  class="progress"/>
              <a-alert v-if="isAll.issuccess" :message="`修复成功，共${tack_data.task_host_count}台主机，成功${tack_data.task_success_host_count}台`" show-icon  type="success"  class="progress"/>
                <a-alert message="修复完成后，请重启相关主机或者相关服务保证修复生效。" show-icon  type="info" />
           </div>
    </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { InfoCircleOutlined  } from "@ant-design/icons-vue";
import { postfixCVE,getprogress } from "@/api/backend/systems/auth";
import { get } from 'js-cookie';
import { time } from 'echarts';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false,
                title:""
            }
        }
    }
})

const tack_data=reactive({
    task_host_count:0,
    task_fail_host_count:0,
    task_success_host_count:0
})


const isAll=reactive({
    iserr:false,
    isprogress:true,
    issuccess:false,
})
const   list=reactive({
    progress:0,
})
var timer=null
// const getlist=reactive({
//     uuid:""
// })
const   getList = async(record) => {
    const hostIds=[]
    let res=await postfixCVE(hostIds,{id:record.id})
    if(res){
        console.log("修复",res.data.uuid)
         list.progress=0
         isAll.isprogress=true
         isAll.iserr=false
         isAll.issuccess=false
         
      
        getlist(res.data.uuid)
        
    }
    

   
}

const getlist=(id)=>{
    timer=setInterval(async()=>{
          let  resdata=await getprogress({uuid:id})
                if(resdata){
                           
                        if(resdata.data.status=="running"){
                            isAll.isprogress=true
                            list.progress=resdata.data.progress
                            
                         }
                        if(resdata.data.status=="failed"){
                              list.progress=100
                              isAll.isprogress=false
                              isAll.iserr=true
                          tack_data.task_fail_host_count=resdata.data.task_fail_host_count
                          tack_data.task_host_count=resdata.data.task_host_count
                         clearInterval(timer);
                             emit("getlist")
                        }
                        if(resdata.data.status=="success"){
                              list.progress=100
                               isAll.isprogress=false
                               isAll.issuccess=true
                               tack_data.task_host_count=resdata.data.task_host_count
                               tack_data.task_success_host_count=resdata.data.task_success_host_count
                              clearInterval(timer);
                              emit("getlist")
                        } 
                         
                }
    },2000)
  
}
const cvefn=()=>{
        isAll.isprogress=false
         isAll.iserr=false
         isAll.issuccess=false
          clearInterval(timer);
}

const loading = ref(false);
onMounted(()=>{})
defineExpose({getList})
</script>
<style lang='scss' scoped>
.hosModal{
    height: 130px;
  
}
.progress{
    margin-bottom:20px ;
}
</style>