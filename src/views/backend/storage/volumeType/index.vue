<template>
    <div class='uncloudRight'>
        <div class="buttonPadding">
            <a-form layout="inline" :model="searchform" class="searchform">
                <a-form-item label="卷类型名称">
                    <a-input placeholder="请输入卷类型名称" @pressEnter="handleSearch" v-model:value="searchform.typeName" allowClear />
                </a-form-item>
            </a-form>
            <MoreSearch @search="handleSearch" @reset="handleAllReset" />
        </div>
        <div class="innerPadding">
            <a-row class="buttonGroup">
                <a-button type="primary" class="btnMargin" @click="handleAdd"> 新增 </a-button>
                <a-button @click="$handleDel(selectRowIds,deleteType,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                    批量删除
                </a-button>
            </a-row>
            <a-table :row-selection="rowSelection" :columns="columns" :data-source="volumelist" row-key="id" :pagination="pagination" @change="changeTable" :scroll="{x:true}">
                <template #index={record,index}>
                    {{index+1+(pagination.pageSize * (pagination.current-1))}}
                </template>
                <template #extraSpecs="{record}">
                    {{record.extraSpecs ? record.extraSpecs : '-'}}
                </template>
                <template #action="{record}">
                    <a-button class="button_E" v-if="!record.qosSpecsId" @click="handleQOS(record)">关联QOS规格</a-button>
                    <a-button class="button_E" v-else @click="handleQOS(record)">解除QOS规格</a-button>
                    <a-button class="button_D" @click="$handleDel([record.id],deleteType,getList)">删除</a-button>
                </template>
            </a-table>
        </div>
        <a-modal v-model:visible="isShowSpecs" title="关联QOS规格" @cancel="cancelSpec" :maskClosable="false" :getContainer="modalBindNode">
            <template #footer>
                <a-button @click="cancelSpec">取消</a-button>
                <a-button type="primary" @click="saveSpecs" :disabled="!specform.qosId">提交</a-button>
            </template>
            <a-form>
                <a-form-item label="QOS规格">
                    <a-select v-model:value="specform.qosId" :getPopupContainer="triggerNode => triggerNode.parentNode" placeholder="请选择QOS规格">
                        <a-select-option v-for="(item,index) in qosList" :key="index" :value="item.id">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
        
        </a-modal>
        <Info :info="info" ref="typeDialog" @getlist="getList" />
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import { computed, createVNode, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import Info from "./info.vue";
import { getVolumeTypeList, deleteType, bindQOS, unbindQOS } from "@/api/backend/storage";
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { selectQoSList } from '@/api/backend/devops/config';
import { buttonExpand, handleWidth } from "@/utils/moreform";
import router from "@/router";
const {proxy} = getCurrentInstance();

let selectRowIds: string[] = ref([]);
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    pageIndex:1,
    pageSize:10
})
const info = reactive({
    isShow:false,
    isAdd:true
})
const typeDialog = ref()
const isShowSpecs = ref()
const specform = reactive({
    typeId:0,
    qosId:undefined
})
const qosId = ref(undefined)
const qosList = ref([])
const loading = ref(false);
const volumelist = ref([])
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '名称', dataIndex: 'typeName', key: 'id' ,align:'left'},
    {title: 'ID', dataIndex: 'thirdTypeId', key: 'id', slots: { customRender: 'thirdTypeId' } ,align:'center'},
    {title: 'QoS规范', dataIndex: 'qosSpecsName', key: 'id', align:'center'},
    {title: 'ExtraSpecs', dataIndex: 'extraSpecs', key: 'id', slots: { customRender: 'extraSpecs' } ,align:'center'},
    // {title: '状态', dataIndex: 'status', slots: { customRender: 'status' }, key: 'id',align:'center'},
    // {title: '描述', dataIndex: 'description', key: 'id', slots: { customRender: 'description' }, align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleSearch = () => {
    searchform.pageIndex = 1;
    getList();
}
const handleAllReset = () => {
    searchform.typeName = '';
  searchform.pageIndex = 1;
//   getList();
}
const handleQOS = async (record) => {
    specform.typeId = record.id;
    if(record.qosSpecsId){
        Modal.confirm({
            title: '提示',
            icon: createVNode(ExclamationCircleOutlined),
            content: '请确认是否解除关联QOS规格',
            okText: '是',
            cancelText: '否',
            maskClosable: true,
            async onOk(){
                specform.qosId = record.qosSpecsId;
                let res = await unbindQOS(specform);
                if(res.code == 0 && res.data !== false){
                    message.success('解除关联成功');
                    getList();
                }else if(res.code == 0){
                    message.error((!res.msg || res.msg == 'success') ? '解除关联失败' : res.msg);
                }
            }
        });
    }else{
        let res = await selectQoSList();
        if(res.code == 0){
            console.log('res',res)
            qosList.value = res.data;
            isShowSpecs.value = true;
        }
    }

}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
}
const cancelSpec = () => {
    isShowSpecs.value = false;
    specform.qosId = undefined;
}
const saveSpecs = async () => {
    let res = await bindQOS(specform)
    if(res.code == 0 && res.data !== false){
        message.success('关联成功');
        cancelSpec();
        getList();
    }else{
        message.error((!res.msg || res.msg == 'success') ? '关联失败' : res.msg);
    }
}
const getList = async () => {
    volumelist.value = await proxy.$getList(loading,getVolumeTypeList,searchform,pagination,getList)
    console.log('kkist', volumelist.value)
}
onMounted(() => {
    
    getList();
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
// .buttonPadding,.innerPadding{min-width: 890px;}
</style>