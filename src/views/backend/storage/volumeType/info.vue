<template>
    <div class=''>
        <a-modal :title="info.isAdd ? '新增' : '修改'" v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form ref="typeForm" :model="typeform" :labelCol="{span:4}" :rules="rules">
                <a-form-item label="类型名称" name="typeName">
                    <a-input v-model:value="typeform.typeName" placeholder="请输入卷名称"></a-input>
                </a-form-item>
                <!-- <a-form-item label="描述" name="description">
                    <a-textarea v-model:value="typeform.description" placeholder="请输入描述"></a-textarea>
                </a-form-item> -->
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { saveType, updateType } from "@/api/backend/storage";
import { selectZoneList } from '@/api/backend/devops';
import router from '@/router';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})

const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    typeName:'',
    extraSpecs:''
}
const typeForm = ref()
const typeform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    typeName:'',
    extraSpecs:''
})
const rules = {
    typeName:[{required:true,message:'请输入卷类型名称',trigger:'change'}],
}
const typelist = ref([])
const zonelist = ref([])
const setInfo = (data) => {
    console.log('daya',data)
    Object.assign(typeform,data);
}
const cancel = () => {
    props.info.isShow = false;
    typeForm.value.resetFields();
    Object.assign(typeform,defaultform)
}
const save = () => {
    proxy.$handleSave(typeForm.value, saveType, updateType, props, typeform, ()=>{emit('getlist');cancel();})
}
const SelectVolumeTypeList = async () => {
    let cloudId = localStorage.getItem('cloudId');
    let res = await selectVolumeTypeList({cloudId});
    if(res.code == 0){
        typelist.value = res.data;
    }
}
const selectZonelist = async () => {
    let res = await selectZoneList({cloudId:localStorage.getItem('cloudId'),module:'cinder'});
    if(res.code == 0){
        zonelist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>