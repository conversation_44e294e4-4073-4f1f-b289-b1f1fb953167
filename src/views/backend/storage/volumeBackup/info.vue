<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <template #title>
                <a-tooltip :overlayStyle="{minWidth:'521px'}">
                    <template #title>
                       <pre style="white-space:pre-wrap;" v-html="content.volumebackup"></pre>
                    </template>
                    {{info.isAdd ? '创建卷备份' : '修改卷备份'}} <QuestionCircleOutlined />
                </a-tooltip>
            </template>
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" @click="save" v-if="!info.isInfo">提交</a-button>
                <a-button type="primary" @click="cancel" v-else>关闭</a-button>
            </template>
            <a-form ref="backupForm" :model="backupform" :labelCol="{span:4}" :rules="rules">
                <a-form-item label="备份名称" name="name">
                    <a-input v-model:value="backupform.name" placeholder="请输入备份名称" :disabled="info.isInfo"></a-input>
                </a-form-item>
                <a-form-item label="容器名称" name="container">
                    <a-input v-model:value="backupform.container" placeholder="请输入容器名称" :disabled="info.isInfo"></a-input>
                </a-form-item>
                <a-form-item label="描述" name="description">
                    <a-textarea v-model:value="backupform.description" placeholder="请输入描述" :disabled="info.isInfo"></a-textarea>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { createBackup, updateBackup } from "@/api/backend/storage";
import { selectZoneList } from '@/api/backend/devops';
import {content} from "@/common/explain/modal";
import router from '@/router';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true,
                isInfo:false
            }
        }
    }
})

const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    container:'backups',
    description:'',
}
const backupForm = ref()
const backupform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    container:'backups',
    description:'',
})
const rules = {
    name:[{required:true,message:'请输入快照名称',trigger:'change'}],
    container:[{required:true,message:'请输入容器名称',trigger:'change'}],
    // type:[{required:true,message:'请选择卷类型',trigger:'change'}],
    // size:[{required:true,type:'integer',message:'请输入卷大小',trigger:'change'}],
    // availabilityZone:[{required:true,message:'请输入卷名称',trigger:'change'}],
}
const typelist = ref([])
const zonelist = ref([])
const setInfo = (data) => {
    console.log('daya',data)
    Object.assign(backupform,data);
}
const cancel = () => {
    props.info.isShow = false;
    backupForm.value.resetFields();
    Object.assign(backupform,defaultform)
}
const save = () => {
    proxy.$handleSave(backupForm.value, createBackup, updateBackup, props, backupform, ()=>{emit('getlist');cancel();})
}
const SelectVolumeTypeList = async () => {
    let cloudId = localStorage.getItem('cloudId');
    let res = await selectVolumeTypeList({cloudId});
    if(res.code == 0){
        typelist.value = res.data;
    }
}
const selectZonelist = async () => {
    let res = await selectZoneList({cloudId:localStorage.getItem('cloudId'),module:'cinder'});
    if(res.code == 0){
        zonelist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({setInfo,backupform})
</script>
<style lang='scss' scoped>
</style>