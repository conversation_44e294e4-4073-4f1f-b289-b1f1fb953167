<template>
    <div class=''>
        <a-modal 
        :title="info.isInfo ? '查看' : (info.isAdd ? '新增':'修改')"
        v-model:visible="info.isShow"
        @cancel="cancel"
        :maskClosable="info.isInfo"
        centered
        :getContainer="modalBindNode"
        >
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="projectform" ref="projectForm" :rules="rules" :labelCol="{span:6}">
                <!-- <a-form-item label="域名" name="domainId">
                    <a-select v-model:value="projectform.domainId" placeholder="请选择" :disabled="info.isInfo || !info.isAdd" allow-clear> 
                        <a-select-option v-for="(item,index) in options3" :key="index" :value="item.id+''" >{{item.domainName}}</a-select-option>
                    </a-select>
                </a-form-item> -->
                <a-form-item label="策略名称" name="ruleName">
                    <a-input v-model:value.trim="projectform.ruleName" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                  <a-form-item label="磁盘类型" name="maxCpu">
                    <a-select v-model:value="projectform.maxCpu" :options="options1" placeholder="请选择" :getPopupContainer="triggerNode => triggerNode.parentNode" :disabled="info.isInfo" allow-clear> 
      
                       
                    </a-select>
                </a-form-item>
                <a-form-item label="IOPS上线" name="maxMemory">
                     <a-input-number v-model:value.trim="projectform.maxMemory" :min="1"  :step="1" :precision="0" style="width:100%" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input-number>
                </a-form-item>
                 <a-form-item label="带宽" name="minCpu">
                     <a-input-number v-model:value.trim="projectform.minCpu" :min="1"  :step="1" :precision="0" style="width:100%" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input-number>
                </a-form-item>
                <a-form-item label="IOPS优先级" name="minMemory">
                     <a-input-number v-model:value.trim="projectform.minMemory" :min="0"  :step="1" :precision="0" style="width:100%" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input-number>
                </a-form-item>
                
               
               
                
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {saveRuleserve,updateRuleserve } from "@/api/backend/devops/config"
import {selectServerList } from "@/api/backend/devops/server"
import { selectDictList } from '@/api/backend/systems/dictionary';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})

 const options1 = ref([{
      value: 'HDD',
      label: 'HDD硬盘',
    }, {
      value: 'SSD',
      label: 'SSD硬盘',
    }, {
      value: 'RAID',
      label: 'RAID磁盘',
     
    }, {
      value: 'SAN',
      label: 'SAN磁盘',
    }]);
const projectForm = ref();
const serverlist = ref([]);
const defaultform = {
    cloudId:localStorage.getItem('cloudId'),
    ruleName:'',
    domainId:undefined,
    serverIds:undefined,
    maxCpu:'',
    maxMemory:'',
    minCpu:'',
    type:undefined,
    minMemory:'',
    description:'',
    allServer:false
}
const projectform = reactive({
    cloudId:localStorage.getItem('cloudId'),
    ruleName:'',
    domainId:undefined,
    serverIds:undefined,
    maxCpu:'',
    maxMemory:'',
    minCpu:'',
    minMemory:'',
    allServer:false,
     type:undefined,
})
const rules = {
    ruleName:[{required:true, message:'请输入',trigger:'change'}],
    maxCpu:[{required:true, type:'number', message:'请选择',trigger:'change'}],
    maxMemory:[{required:true, type:'number', message:'仅支持输入0-100之间的整数',trigger:'change',transform(value){ 
                        if(value){
                            // 将输入的值转为数字
                            var val = Number(value)
                            // 正则表达式校验输入的数字是否在0-100之内并且属于整数
                            if(/^(?:[1-9]?\d|100)$/.test(val)) return val
                            // 返回false即为校验失败
                            return false
                        }
                    }}],
    minCpu:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    minMemory:[{type:'number',required:true, message:'请输入',trigger:'change'}],
   

   
}
const options3 = ref([])

const handleSave = () => {
    // let projectform1 = {...projectform}
    // proxy.$handleSave(projectForm.value, saveRuleserve, updateRuleserve, props,projectform1, ()=>{cancel();emit('getlist')},null,()=>{
    //     if(projectform1.allServer == true)
    //     projectform1.allServer = 1;
    //     else
    //     projectform1.allServer = 0;
    // })
    cancel();
}
const cancel = () => {

   props.info.isShow = false
    props.info.isInfo = false
    // isInfo.value = false
    projectForm.value.resetFields()
    Object.assign(projectform,defaultform)
}
onMounted(() => {})
defineExpose({projectform})
</script>
<style lang='scss' scoped>
</style>