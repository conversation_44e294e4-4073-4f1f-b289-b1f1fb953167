<template>
    <div class="cloudContent">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                <a-form-item label="规则名称">
                    <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.ruleName" allowClear />
                </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd">新增 </a-button>
                </a-row>
                <a-table :columns="columns" row-key="id" :data-source="imagelist" :pagination="pagination" @change="changeTable">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #imageSize={record}>
                        {{(record.imageSize/(1024*1024*1024)).toFixed(2)}}
                    </template>
                    <template #action={record}>
                         <a-button class="button_V" @click="handleView(record)">查看</a-button>
                        <a-button type="link" class="button_E"   @click="handleEdit(record)">修改</a-button>
                        <a-button type="link" class="button_D" @click="$handleDel([record.id],deleteRuleserve,getList)">删除</a-button>
                    </template>
                </a-table>
                <Info ref="imageDialog" :info="info" @getlist="getList" />
            </div>
        </div>
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { getRuleservelist, deleteRuleserve } from "@/api/backend/devops/config";
import { useRoute } from "vue-router";
import { handleWidth } from "@/utils/moreform";
const { proxy } = getCurrentInstance();
const route = useRoute()
const imageDialog = ref(null);
const loading = ref(false);
const imagelist = ref([{
            id: '1',
            ruleName: '测试数据',
            maxCpu: "SSD",
            maxCpuText: "SSD硬盘",
            maxMemory: '1',
            minCpu:"1",
            minMemory:"1",
           },
           {
            id: '2',
            ruleName: 'text',
            maxCpu: "HDD",
            maxCpuText: "HDD硬盘",
            maxMemory: '6',
            minCpu:"2",
            minMemory:"1",
           },
          
        ]);

       
const info = reactive({
    isAdd:true,
    isShow:false
})
const searchform = reactive({
    cloudId:localStorage.getItem('cloudId'),
    ruleName:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '策略名称', dataIndex: 'ruleName', key: 'id',align:'left'},
    {title: '磁盘类型', dataIndex: 'maxCpuText', key: 'id',align:'center'},
    {title: 'IOPS上线', dataIndex: 'maxMemory', key: 'id',align:'center'},
    {title: '带宽', dataIndex: 'minCpu', key: 'id',align:'center'},
    {title: 'IOPS优先级', dataIndex: 'minMemory', key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
const getList = async () => {
    searchform.cloudId = localStorage.getItem('cloudId');
    // imagelist.value = await proxy.$getList(loading, getRuleservelist, searchform, pagination, getList )
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    ruleName: ""
  })
//   getList();
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
    info.isInfo = false;
}

const handleEdit = (record) => {
 info.isAdd = false;
    info.isShow = true;
    info.isInfo = false;
    let record1 = {...record}
    if(record1.allServer == 1)
    record1.allServer = true;
    else
    record1.allServer = false;
    // console.log('修改',imageDialog.value.cloudform)

    Object.assign(imageDialog.value.projectform, record1)
}
const handleView = (record) => {
    info.isInfo = true;
    info.isShow = true;
    let record1 = {...record}
    if(record1.allServer == 1)
    record1.allServer = true;
    else
    record1.allServer = false;
    Object.assign(imageDialog.value.projectform, record1)
}

onMounted(() => {
    getList()
    if(route.path == '/admin/devops/openstack')
    proxy.$mitt.on('getlist',getList)
    })
    nextTick(()=>{
        handleWidth()
    })
</script>
<style lang='scss' scoped>
</style>