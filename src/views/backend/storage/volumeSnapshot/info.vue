<template>
    <div class=''>
        <a-modal :title="info.isAdd ? '创建卷快照' : '修改卷快照'" v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form ref="snapshotForm" :model="snapshotform" :labelCol="{span:4}" :rules="rules">
                <a-form-item label="快照名称" name="name">
                    <a-input v-model:value="snapshotform.name" placeholder="请输入快照名称"></a-input>
                </a-form-item>
                <a-form-item label="描述" name="description">
                    <a-textarea v-model:value="snapshotform.description" placeholder="请输入描述"></a-textarea>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { createSnapshot, updateSnapshot } from "@/api/backend/storage";
import { selectZoneList } from '@/api/backend/devops';
import router from '@/router';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})

const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    description:'',
}
const snapshotForm = ref()
const snapshotform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    description:'',
})
const rules = {
    name:[{required:true,message:'请输入快照名称',trigger:'change'}],
    // type:[{required:true,message:'请选择卷类型',trigger:'change'}],
    // size:[{required:true,type:'integer',message:'请输入卷大小',trigger:'change'}],
    // availabilityZone:[{required:true,message:'请输入卷名称',trigger:'change'}],
}
const typelist = ref([])
const zonelist = ref([])
const setInfo = (data) => {
    console.log('daya',data)
    Object.assign(snapshotform,data);
}
const cancel = () => {
    props.info.isShow = false;
    snapshotForm.value.resetFields();
    Object.assign(snapshotform,defaultform)
}
const save = () => {
    proxy.$handleSave(snapshotForm.value, createSnapshot, updateSnapshot, props, snapshotform, ()=>{emit('getlist');cancel();})
}
const SelectVolumeTypeList = async () => {
    let cloudId = localStorage.getItem('cloudId');
    let res = await selectVolumeTypeList({cloudId});
    if(res.code == 0){
        typelist.value = res.data;
    }
}
const selectZonelist = async () => {
    let res = await selectZoneList({cloudId:localStorage.getItem('cloudId'),module:'cinder'});
    if(res.code == 0){
        zonelist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({setInfo,snapshotform})
</script>
<style lang='scss' scoped>
</style>