<template>
    <div class='uncloudRight'>
        <div class="buttonPadding">
            <a-form layout="inline" :model="searchform" class="searchform">
                <a-form-item label="快照名称">
                    <a-input placeholder="请输入名称" @pressEnter="handleSearch" v-model:value="searchform.name" allowClear />
                </a-form-item>
            </a-form>
            <MoreSearch @search="handleSearch" @reset="handleAllReset" />
        </div>
        <div class="innerPadding">
            <a-row class="buttonGroup">
                <!-- <a-button type="primary" class="btnMargin" @click="handleAdd"> 新增 </a-button> -->
                <a-button @click="$handleDel(selectRowIds,deleteSnapshot,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                    批量删除
                </a-button>
            </a-row>
            <a-table :row-selection="rowSelection" :columns="columns" :data-source="snapshotlist" row-key="id" :scroll="{ x: true }" :pagination="pagination" @change="changeTable">
                <template #index={record,index}>
                    {{index+1+(pagination.pageSize * (pagination.current-1))}}
                </template>
                <template #statusText="{record}">
                    {{record.statusText?record.statusText : '-'}}
                </template>
                <template #description="{record}">
                    {{record.description ? record.description : '-'}}
                </template>
                <template #action="{record}">
                    <!-- <a-button class="button_V">查看</a-button> -->
                    <a-button class="button_E" @click="handleEdit(record)">修改</a-button>
                    <a-button class="button_D" @click="$handleDel([record.id],deleteSnapshot,getList)">删除</a-button>
                </template>
            </a-table>
        </div>
        <Info :info="info" ref="snapshotDialog" @getlist="getList" />
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import { computed, getCurrentInstance, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import Info from "./info.vue";
import { getVolumeSnapShotList, deleteSnapshot } from "@/api/backend/storage";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import emiter from "@/utils/Bus";
import router from "@/router";
const {proxy} = getCurrentInstance();
let selectRowIds: string[] = ref([]);
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    pageIndex:1,
    pageSize:10
})
const info = reactive({
    isShow:false,
    isAdd:true
})
const snapshotDialog =ref();
const loading = ref(false);
const snapshotlist = ref([])
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '名称', dataIndex: 'name', key: 'id' ,align:'left'},
    {title: 'ID', dataIndex: 'thirdSnapshotId', key: 'id' ,align:'left'},
    {title: '大小(GiB)', dataIndex: 'size', key: 'id', slots: { customRender: 'size' } ,align:'center'},
    {title: '状态', dataIndex: 'statusText', slots: { customRender: 'statusText' }, key: 'id',align:'center'},
    {title: '卷名称', dataIndex: 'volumeName', key: 'id' ,align:'center'},
    {title: '描述', dataIndex: 'description', key: 'id', slots: { customRender: 'description' }, align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
  begin();
};
const handleSearch = () => {
    searchform.pageIndex = 1;
  getList();
  begin();
}
const handleAllReset = () => {
    searchform.name = '';
  searchform.pageIndex = 1;
//   getList();

}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
}
const handleEdit = (data) => {
    let {
    cloudId,
    projectId,
    name,
    description,
    id
} = data;
    snapshotDialog.value.setInfo({
    cloudId,
    projectId,
    name,
    description,
    id
})
info.isAdd = false;
info.isShow = true;
}
const getList = async (isUnLoading) => {
    if(!isUnLoading)
        emiter.emit("loading",true)
    snapshotlist.value = await proxy.$getList(loading,getVolumeSnapShotList,searchform,pagination,getList)
    emiter.emit("loading",false)
}
var timerVolumeSnap = null;
const begin = () => {
    clearInterval(timerVolumeSnap);
    if(!searchform.name){
        timerVolumeSnap = setInterval(()=>{
            getList(true);
        },60000)
    }
}
onMounted(() => {
    getList();
    begin();
    nextTick(()=>{
        handleWidth()
    })
})
onUnmounted(()=>{
    clearInterval(timerVolumeSnap);
})
</script>
<style lang='scss' scoped>
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
// .buttonPadding,.innerPadding{min-width: 781px;}
</style>