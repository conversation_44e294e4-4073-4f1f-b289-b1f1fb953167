<template>
    <div class=''>
        <a-modal centered
            v-model:visible="info.isShow"
            title="卷快照回滚"
            @cancel="cancel"
            width="720.8px"
            :maskClosable="false"
            :getContainer="modalBindNode"
            >
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" @click="save" :disabled="!rollform.snapshotId">提交</a-button>
            </template>
                <a-form :model="rollform" :label-col="{span:4}">
                    <a-form-item label="卷快照名称" name="snapshotId">
                        <a-select
                            v-model:value="rollform.snapshotId"
                            placeholder="请选择"
                            allowClear>
                            <a-select-option v-for="(item,index) in snapList" :key="index" :value="item.id">{{item.name}}</a-select-option>
                        </a-select>
                    </a-form-item>
                
                 </a-form>
            </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { selectImageList } from '@/api/backend/devops/image';
import { rollBackSnap } from '@/api/backend/devops/server';
import { rollVolumeSnap, selectVolumeSnapShotList } from '@/api/backend/storage';
import { message } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute()
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
            }
        }
    }
})

const rollform = reactive({
    volumeId:"",
    snapshotId:undefined
})
const snapList = ref([])
const cancel = () => {
    props.info.isShow = false;
    rollform.volumeId = "";
    rollform.snapshotId = undefined;
}
const save = async () => {
    let res = await rollVolumeSnap(rollform)
    if(res.code == 0 && res.data !== false){
        // console.log('snaop',res.data)
        message.success('回滚成功')
        cancel()
        emit('getlist')
    }else{
        message.error((!res.msg || res.msg == 'success') ? '回滚失败' : res.msg);
    }
}
const getSnaplist = async (record) => {
    rollform.volumeId = record.id;
    let res = await selectVolumeSnapShotList({peojectId:record.projectId,volumeId:record.id})
    if(res.code == 0){
        // console.log('image',res.data)
        snapList.value = res.data;
    }
}
onMounted(() => {})
defineExpose({getSnaplist})
</script>
<style lang='scss' scoped>
</style>