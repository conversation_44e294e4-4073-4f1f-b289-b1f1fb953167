<template>
    <div class=''>
        <a-modal centered
            v-model:visible="info.isShow"
            title="上传镜像"
            @cancel="cancel"
            width="720.8px"
            :maskClosable="false"
            :getContainer="modalBindNode"
            >
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" @click="save">提交</a-button>
            </template>
                <a-form :model="uploadform" ref="uploadForm" :label-col="{span:4}">
                    <a-form-item label="卷名称" name="volumeName">
                        <a-input v-model:value="uploadform.volumeName" disabled></a-input>
                    </a-form-item>
                    <a-form-item label="镜像名称" name="imageName" :rules="[{required:true}]">
                        <a-input v-model:value="uploadform.imageName" placeholder="请输入"></a-input>
                    </a-form-item>
                    <a-form-item label="磁盘格式" name="diskFormat">
                        <a-select
                            v-model:value="uploadform.diskFormat"
                            placeholder="请选择"
                            allowClear>
                            <template v-for="(item,index) in disklist" :key="item.dictValue">
                                <a-select-option :value="item.dictValue" :label="item.dictLabel" v-if="diskPool.includes(item.dictValue)">{{item.dictLabel}}</a-select-option>
                            </template>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="强制上传" name="isForce">
                        <a-switch v-model:checked="uploadform.isForce" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" />
                    </a-form-item>
                 </a-form>
            </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { selectImageList } from '@/api/backend/devops/image';
import { rollBackSnap } from '@/api/backend/devops/server';
import { rollVolumeSnap, selectVolumeSnapShotList, uploadToImage } from '@/api/backend/storage';
import { selectDictList } from '@/api/backend/systems/dictionary';
import router from '@/router';
import { message } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute()
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true
            }
        }
    }
})
const diskPool = ['raw', 'vmdk', 'vdi', 'qcow2', 'vhd'];

const uploadForm = ref();
const defaultform = {
  "diskFormat": undefined,
  "imageName": "",
  "projectId": router.currentRoute.value.query.projectId,
  "volumeId": 0,
  "isForce":0
}
const uploadform = reactive({
  "diskFormat": undefined,
  "imageName": "",
  "projectId": router.currentRoute.value.query.projectId,
  "volumeId": 0,
  "isForce":0
})
const disklist = ref([])
const rules = {

}
const cancel = () => {
    props.info.isShow = false;
    uploadForm.value.resetFields();
    Object.assign(uploadform,defaultform);
}
const save = () => {
    uploadForm.value.validate().then(async()=>{
        let res = await uploadToImage(uploadform)
        if(res.code == 0 && res.data !== false){
            // console.log('snaop',res.data)
            message.success('上传到镜像成功');
            cancel()
            emit('getlist');
        }
    })
}
const setInfo = (record) => {
    uploadform.volumeId = record.id;
    getDictList(record);
}
const getDictList = async (record) => {
  let res = await selectDictList({dictType:'DISK_FORMAT'})
  if(res){
    if(res.code == 0){
      disklist.value = res.data;
      uploadform.volumeName = record.name;
        uploadform.diskFormat = "raw";
    }
  }
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>