<template>
    <div class=''>
        <a-modal :title="info.isAdd ? '新增' : '修改卷容量'" v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form ref="volumeForm" :model="volumeform" :labelCol="{span:5}" :rules="rules">
                <a-form-item label="当前大小(GiB)" name="size">
                {{volumeform.nowsize}}
                </a-form-item>
                <a-form-item label="新大小(GiB)" name="size">
                    <a-input-number v-model:value="volumeform.size" :min="1" placeholder="请输入卷大小" style="width:100%"></a-input-number>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { selectVolumeTypeList, saveVolume, updateSize } from "@/api/backend/storage";
import { selectZoneList } from '@/api/backend/devops.ts';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})

const defaultform = {
    volumeId:'',
    nowsize:'',
    size:'',
}
const volumeForm = ref()
const volumeform = reactive({
    volumeId:'',
    nowsize:'',
    size:'',
})
const rules = {
    // name:[{required:true,message:'请输入卷名称',trigger:'change'}],
    // type:[{required:true,message:'请选择卷类型',trigger:'change'}],
    size:[{required:true,type:'integer',message:'请输入卷大小',trigger:'change'}],
    // availabilityZone:[{required:true,message:'请输入卷名称',trigger:'change'}],
}
const typelist = ref([])
const zonelist = ref([])
const setInfo = (data) => {
    console.log('daya',data)
    Object.assign(volumeform,data);
}
const cancel = () => {
    props.info.isShow = false;
    volumeForm.value.resetFields();
    Object.assign(volumeform,defaultform)
}
const save = () => {
    proxy.$handleSave(volumeForm.value, saveVolume, updateSize, props, volumeform, ()=>{emit('getlist');cancel();})
}
const SelectVolumeTypeList = async () => {
    let cloudId = localStorage.getItem('cloudId');
    let res = await selectVolumeTypeList({cloudId});
    if(res.code == 0){
        typelist.value = res.data;
    }
}
const selectZonelist = async () => {
    let res = await selectZoneList({cloudId:localStorage.getItem('cloudId'),module:'cinder'});
    if(res.code == 0){
        zonelist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>