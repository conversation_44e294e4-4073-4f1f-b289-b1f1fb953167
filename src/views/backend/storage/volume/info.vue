<template>
    <div class=''>
        <a-modal :title="info.isAdd ? '新增' : '修改'" v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form ref="volumeForm" :model="volumeform" :labelCol="{span:4}" :rules="rules">
                <a-form-item label="卷名称" name="name">
                    <a-input v-model:value="volumeform.name" placeholder="请输入卷名称"></a-input>
                </a-form-item>
                <a-form-item label="卷来源" name="source" v-if="info.isAdd">
                    <a-select v-model:value="volumeform.source" placeholder="请选择卷来源" @change="changeSource" :getPopupContainer="triggerNode => triggerNode.parentNode" :disabled="!info.isAdd">
                        <a-select-option :value="0">没有源，空白卷。</a-select-option>
                        <a-select-option :value="1">快照</a-select-option>
                        <a-select-option :value="3">镜像</a-select-option>
                        <a-select-option :value="2">卷</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="快照" name="snapshotId" v-if="volumeform.source == 1">
                    <a-select v-model:value="volumeform.snapshotId" placeholder="请选择快照" @change="changeSnap" :getPopupContainer="triggerNode => triggerNode.parentNode">
                        <a-select-option v-for="(item,index) in snapshotlist" :key="index" :value="item.id" :size="item.size">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="镜像" name="imageId" v-if="volumeform.source == 3">
                    <a-select v-model:value="volumeform.imageId" placeholder="请选择镜像" @change="changeSnap" :getPopupContainer="triggerNode => triggerNode.parentNode">
                        <a-select-option v-for="(item,index) in imagelist" :key="index" :value="item.id" :size="item.size">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="卷" name="volumeId" v-if="volumeform.source == 2">
                    <a-select v-model:value="volumeform.volumeId" placeholder="请选择卷" @change="changeSnap" :getPopupContainer="triggerNode => triggerNode.parentNode">
                        <a-select-option v-for="(item,index) in volumelist" :key="index" :value="item.id" :size="item.size">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="类型" name="type" v-if="info.isAdd">
                    <a-select v-model:value="volumeform.type" placeholder="请选择卷类型" :getPopupContainer="triggerNode => triggerNode.parentNode">
                        <a-select-option v-for="(item,index) in typelist" :key="index" :value="item.typeName">{{item.typeName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="大小(GiB)" name="size" v-if="info.isAdd">
                    <a-input-number v-model:value="volumeform.size" :min="1" placeholder="请输入卷大小" style="width:100%" :disabled="!info.isAdd"></a-input-number>
                </a-form-item>
                <a-form-item label="可用域" name="availabilityZone" v-if="info.isAdd">
                    <a-select v-model:value="volumeform.availabilityZone" placeholder="请选择可用域" :disabled="!info.isAdd" :getPopupContainer="triggerNode => triggerNode.parentNode">
                        <a-select-option v-for="(item,index) in zonelist" :key="index" :value="item.zoneName">{{item.zoneName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <!-- <a-form-item label="组" name="group">
                    <a-select v-model:value="volumeform.group" placeholder="请选择组"></a-select>
                </a-form-item> -->
                <a-form-item label="描述" name="description">
                    <a-textarea v-model:value="volumeform.description" placeholder="请输入描述"></a-textarea>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { selectVolumeTypeList, saveVolume, updateVolume, selectVolumeList, selectVolumeSnapShotList } from "@/api/backend/storage";
import { selectImageList } from '@/api/backend/devops/image';
import { selectZoneList } from '@/api/backend/devops.ts';
import router from '@/router';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})
const defaultform = {
    id:'',
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    source:0,
    type:'',
    size:1,
    availabilityZone:'nova',
    group:undefined,
    description:''
}
const modalBindNode = ref();
const volumeForm = ref()
const volumeform = reactive({
    id:'',
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    source:0,
    type:'',
    size:1,
    availabilityZone:'nova',
    group:undefined,
    description:''
})
const rules = {
    name:[{required:true,message:'请输入卷名称',trigger:'change'}],
    source:[{required:true,type:'number',message:'请输入卷来源',trigger:'change'}],
    snapshotId:[{required:true,type:'number',message:'请选择快照',trigger:'change'}],
    imageId:[{required:true,type:'number',message:'请选择镜像',trigger:'change'}],
    volumeId:[{required:true,type:'number',message:'请选择卷',trigger:'change'}],
    type:[{required:true,message:'请选择卷类型',trigger:'change'}],
    size:[{required:true,type:'integer',message:'请输入正整数',trigger:'change'}],
    availabilityZone:[{required:true,message:'请输入卷名称',trigger:'change'}],
}
const typelist = ref([])
const zonelist = ref([])
const snapshotlist = ref([])
const imagelist = ref([])
const volumelist = ref([])
const setInfo = (data) => {
    console.log('daya',data)
    Object.assign(volumeform,data);
}
const cancel = () => {
    props.info.isShow = false;
    volumeForm.value.resetFields();
    Object.assign(volumeform,defaultform)
}
const save = () => {
    proxy.$handleSave(volumeForm.value, saveVolume, updateVolume, props, volumeform, ()=>{emit('getlist');cancel();})
}
const changeSource = (e) => {
    if(e == 0){
        volumeform.type = '';
    }else if(e == 1){
        selectSnaplist()
    }else if(e == 2){
        selectVolumelist();
    }else if(e == 3){
        selectImagelist();
    }else{
        if(volumeform.type == '')
        volumeform.type = '__DEFAULT__';
    }
}
const changeSnap = (e,b) => {
    console.log('s0',b)
    volumeform.size = b.size;
}
const SelectVolumeTypeList = async () => {
    let cloudId = router.currentRoute.value.query.cloudId;
    let res = await selectVolumeTypeList({cloudId});
    if(res.code == 0){
        typelist.value = res.data;
    }
}
const selectZonelist = async () => {
    let res = await selectZoneList({cloudId:router.currentRoute.value.query.cloudId,module:'cinder'});
    if(res.code == 0){
        zonelist.value = res.data;
    }
}
const selectSnaplist = async () => {
    let res = await selectVolumeSnapShotList({projectId:router.currentRoute.value.query.projectId})
    if(res.code == 0){
        snapshotlist.value = res.data;
    }
}
const selectImagelist = async () => {
    let res = await selectImageList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId})
    if(res.code == 0){
        imagelist.value = res.data;
    }
}
const selectVolumelist = async () => {
    let res = await selectVolumeList({projectId:router.currentRoute.value.query.projectId,bootable:1,status:'available'});
    if(res.code == 0){
        volumelist.value = res.data;
    }
}
const helper = () => {
    SelectVolumeTypeList();selectZonelist();
}
onMounted(() => {modalBindNode.value = document.getElementsByClassName('full-modal')[0] ? document.getElementsByClassName('full-modal ant-modal-body')[0] : proxy.modalBindNode;})
defineExpose({setInfo,helper})
</script>
<style lang='scss' scoped>
</style>