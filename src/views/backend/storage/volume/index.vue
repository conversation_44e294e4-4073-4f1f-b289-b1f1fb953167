<template>
    <div class='uncloudRight'>
        <div class="buttonPadding">
            <a-form layout="inline" :model="searchform" class="searchform">
                <a-form-item label="卷名称">
                    <a-input placeholder="请输入卷名称" @pressEnter="handleSearch" v-model:value="searchform.volumeName" allowClear />
                </a-form-item>
            </a-form>
            <MoreSearch @search="handleSearch" @reset="handleAllReset" />
        </div>
        <div class="innerPadding">
            <a-row class="buttonGroup">
                <a-button type="primary" class="btnMargin" @click="handleAdd"> 新增 </a-button>
                <a-button @click="$handleDel(selectRowIds,deleteVolume,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                    批量删除
                </a-button>
            </a-row>
            <a-table :row-selection="rowSelection" :columns="columns" :data-source="volumelist" row-key="id" :pagination="pagination" @change="changeTable" :scroll="{x:true}">
                <template #index={record,index}>
                    {{index+1+(pagination.pageSize * (pagination.current-1))}}
                </template>
                <template #name="{record}">
                    {{record.name ? record.name :'-'}}
                </template>
                <template #encrypted="{record}">
                    {{record.encrypted == 0 ? '否':'是'}}
                </template>
                <template #sysOpenstackVolumeAttachmentEntityList="{record}">
                    {{(record.sysOpenstackVolumeAttachmentEntityList && record.sysOpenstackVolumeAttachmentEntityList.length > 0 && record.sysOpenstackVolumeAttachmentEntityList[0].serverName && record.sysOpenstackVolumeAttachmentEntityList[0].device) ? 
                    (record.sysOpenstackVolumeAttachmentEntityList[0].serverName +' 上的 ' + record.sysOpenstackVolumeAttachmentEntityList[0].device) : '-'}}
                    <!-- {{(record.sysOpenstackVolumeAttachmentEntityList?.[0]?.serverName ? record.sysOpenstackVolumeAttachmentEntityList?.[0]?.serverName : '-') +' 的 '+record.sysOpenstackVolumeAttachmentEntityList?.[0]?.device}} -->
                </template>
                <template #bootable="{record}">
                    {{record.bootable == 1 ? 'Yes':'No'}}
                </template>
                <template #statusText="{record}">
                    {{record.statusText ? record.statusText : '-'}}
                </template>
                <template #action="{record}">
                    <a-select ref="selectRef"  style="width: 100px" placeholder="其他操作"
                          :dropdownMatchSelectWidth="false"
                          @select="(value,option)=>setOptions(record,value,option)"
                          >
                            <a-select-option :value="0">修改</a-select-option>
                            <a-select-option :value="1">删除</a-select-option>
                            <a-select-option v-if="record.bootable == 0" :value="2">设置可启动</a-select-option>
                            <a-select-option v-if="record.bootable == 1" :value="3">设置不可启动</a-select-option>
                            <a-select-option :value="4">修改卷容量</a-select-option>
                            <a-select-option :value="5">创建卷快照</a-select-option>
                            <a-select-option :value="6">创建卷备份</a-select-option>
                            <a-select-option :value="7">卷快照回滚</a-select-option>
                            <a-select-option :value="8">上传镜像</a-select-option>
                            <!-- <a-select-option :value="5">压缩卷容量</a-select-option> -->
                    </a-select>
                    <!-- <a-button class="button_V">查看</a-button> -->
                    <!-- <a-button class="button_E" @click="handleEdit(record)">修改</a-button> -->
                    <!-- <a-button class="button_D" @click="">删除</a-button> -->
                </template>
            </a-table>
        </div>
        <Info :info="info" ref="volumeDialog" @getlist="getList" />
        <Size :info="info1" ref="sizeDialog" @getlist="getList" />
        <Snapshot :info="snapinfo" ref="snapshotDialog" @getlist="getList" />
        <Backup :info="backupinfo" ref="backupDialog" @getlist="getList" />
        <Roll :info="rollinfo" ref="rollDialog" @getlist="getList" />
        <Image :info="imageinfo" ref="imageDialog" @getlist="getList" />
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import { computed, getCurrentInstance, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import Info from "./info.vue";
import Size from "./size.vue";
import Snapshot from "../volumeSnapshot/info.vue";
import Backup from "../volumeBackup/info.vue";
import Roll from "./roll.vue";
import Image from "./image.vue";
import { getVolumeList, deleteVolume, updateBootable } from "@/api/backend/storage";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { message } from 'ant-design-vue';
import emiter from "@/utils/Bus";
import router from "@/router";
const {proxy} = getCurrentInstance();
let selectRowIds: string[] = ref([]);
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    volumeName:'',
    pageIndex:1,
    pageSize:10
})
const info = reactive({
    isShow:false,
    isAdd:true
})
const info1 = reactive({
    isShow:false,
    isAdd:true
})
const snapinfo = reactive({
    isAdd: true,
    isShow:false
})
const backupinfo = reactive({
    isAdd: true,
    isShow:false
})
const rollinfo = reactive({
    isAdd: true,
    isShow:false
})
const imageinfo = reactive({
    isShow:false
})
const volumeDialog = ref()
const backupDialog = ref()
const sizeDialog = ref()
const rollDialog = ref()
const imageDialog = ref()
const snapshotDialog = ref()
const tempOption = ref(undefined);
const loading = ref(false);
const volumelist = ref([])
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '名称', dataIndex: 'name', key: 'id', slots: { customRender: 'name' }, align:'left'},
    {title: '大小(GiB)', dataIndex: 'size', key: 'id', slots: { customRender: 'size' } ,align:'center'},
    {title: '类型', dataIndex: 'type', key: 'id' ,align:'center'},
    // {title: '组', dataIndex: 'group', slots: { customRender: 'group' }, key: 'id' ,align:'center'},
    {title: '可用域', dataIndex: 'availabilityZone', slots: { customRender: 'availabilityZone' }, key: 'id' ,align:'center'},
    {title: 'Attacted To', dataIndex: 'sysOpenstackVolumeAttachmentEntityList', slots: { customRender: 'sysOpenstackVolumeAttachmentEntityList' }, key: 'id' ,align:'center'},
    {title: 'Bootable', dataIndex: 'bootable', slots: { customRender: 'bootable' }, key: 'id' ,align:'center'},
    {title: '是否加密', dataIndex: 'encrypted', slots: { customRender: 'encrypted' }, key: 'id' ,align:'center'},
    {title: '状态', dataIndex: 'statusText', slots: { customRender: 'statusText' }, key: 'id' ,align:'center'},
    // {title: '创建人', dataIndex: 'createUserName', key: 'id' ,align:'center'},
    {title: '创建时间', dataIndex: 'createTime', key: 'id' ,align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
  begin();
};
const handleSearch = () => {
    searchform.pageIndex = 1;
    getList();
    begin();
}
const handleAllReset = () => {
    searchform.volumeName = '';
  searchform.pageIndex = 1;
//   getList();
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
    proxy.$nextTick(()=>{
        volumeDialog.value.helper()
    })
}
const handleEdit = (record) => {
    info.isAdd = false;
    info.isShow = true;
    let {
    cloudId,
    projectId,
    name,
    source,
    type,
    size,
    availibilityZone,
    group,
    description,
    id
} = record;
    volumeDialog.value.setInfo({
    cloudId,
    projectId,
    name,
    source,
    type,
    size,
    availibilityZone,
    group,
    description,
    id
});
}
const UpdateBootable = async (volumeId,state) => {
    let res = await updateBootable({volumeId,state})
    if(res.code == 0 && res.data !== false){
        message.success('设置成功')
        getList()
    }else if(res.code == 0){
        message.error((!res.msg || res.msg == 'success') ? '设置失败' : res.msg);
    }
}
const UpdateSize = (record) => {
    info1.isShow = true;
    info1.isAdd = false;
    sizeDialog.value.setInfo({volumeId:record.id,nowsize:record.size})
}
const setOptions = (record, value, option) => {
    console.log('li',record, value, option)
    if(value == 0){
        handleEdit(record);
    }
    if(value == 1){
        proxy.$handleDel([record.id],deleteVolume,getList);
    }
    if(value == 2){
        UpdateBootable(record.id,true)
    }
    if(value == 3){
        UpdateBootable(record.id,false)
    }
    if(value == 4){
        UpdateSize(record)
    }
    if(value == 5){
        snapinfo.isAdd = snapinfo.isShow = true;
        snapshotDialog.value.snapshotform.volumeId = record.id;
    }
    if(value == 6){
        backupinfo.isAdd = backupinfo.isShow = true;
        backupDialog.value.backupform.volumeId = record.id;
    }
    if(value == 7){
        rollinfo.isShow = true;
        rollDialog.value.getSnaplist(record)
    }
    if(value == 8){
        imageinfo.isShow = true;
        nextTick(()=>{
            imageDialog.value.setInfo(record)
        })
    }
}
const getList = async (isUnLoading) => {
    tempOption.value = undefined;
    if(!isUnLoading)
        emiter.emit("loading",true)
    volumelist.value = await proxy.$getList(loading,getVolumeList,searchform,pagination,getList)
    emiter.emit("loading",false)
}
var timerVolume = null;
const begin = () => {
    clearInterval(timerVolume);
    if(!searchform.volumeName){
        timerVolume = setInterval(()=>{
            getList(true);
        },60000)
    }
}
onMounted(() => {
    getList();
    begin();
    nextTick(()=>{
        handleWidth()
    })
})
onUnmounted(()=>{
    clearInterval(timerVolume);
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
// .buttonPadding,.innerPadding{min-width: 1522px;}
</style>