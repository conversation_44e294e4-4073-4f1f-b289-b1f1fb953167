<template>
    <a-table :columns="columns" :data-source="netlist" row-key="id" :pagination="false" :loading="netloading">
        <template #index="{record,index}">
            {{index+1}}
        </template>
        <template #alive="{record,index}">
            {{record.alive ? '激活':'未激活'}}
        </template>
        <template #admin_state_up="{record,index}">
            {{record.admin_state_up ? '启动':'未启动'}}
        </template>
    </a-table>
</template>
<script lang='ts' setup>
import { getNetworkAgentList } from '@/api/backend/devops/poly';
import router from '@/router';
import { computed, onMounted, ref } from 'vue';
// const cloudId = computed(()=>router.currentRoute.value.query.cloudId);
const netloading = ref(false);
const netlist = ref([]);
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id',width:60,align:'center'},
    {title: '代理类型', dataIndex: 'agent_type', key: 'id' ,align:'center'},
    {title: '名称', dataIndex: 'binary', key: 'id' ,align:'center'},
    {title: '主机', dataIndex: 'host', key: 'id' ,align:'center'},
    // {title: '可用域', dataIndex: 'zone', key: 'id' ,align:'center'},
    {title: '服务状态(Status)', dataIndex: 'alive', slots: { customRender: 'alive' }, key: 'id' ,align:'center'},
    {title: '主机状态(State)', dataIndex: 'admin_state_up', slots: { customRender: 'admin_state_up' }, key: 'id' ,align:'center'},
    {title: '更新时间', dataIndex: 'heartbeat_timestamp', key: 'id' ,align:'center'},
]
const getThisList = async () => {
    netloading.value = true;
    let res = await getNetworkAgentList({cloudId:router.currentRoute.value.query.cloudId})
    netloading.value = false;
    if(res.code == 0){
        netlist.value = res.data;
    }
}
onMounted(() => {getThisList()})
defineExpose({getThisList})
</script>
<style lang='scss' scoped>
</style>