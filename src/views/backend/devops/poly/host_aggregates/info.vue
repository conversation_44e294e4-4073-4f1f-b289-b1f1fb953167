<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isAdd ? '新增主机聚合' : '修改主机聚合'}}</span>
                    <a-popover trigger="click" placement="leftTop">
                        <template #content>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto;margin-bottom:0">{{content.host_aggr}}</pre>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :disabled="!polyform.name || !polyform.zone" @click="save">提交</a-button>
            </template>
            <a-form :model="polyform" ref="polyForm" :labelCol="{span:3}">
                <a-form-item label="名称" >
                    <a-input v-model:value="polyform.name" placeholder="请输入名称" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="可用域">
                    <a-input v-model:value="polyform.zone" placeholder="请输入可用域" allow-clear></a-input>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { addPoly, updatePoly } from '@/api/backend/devops/poly';
import {content} from "@/common/explain/modal"
import router from '@/router';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const {proxy} = getCurrentInstance()
const polyForm = ref()
const emit = defineEmits('getlist')
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: false
      };
    }
  },
})
const polyform = reactive({
    name:'',
    zone:'',
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    aggregateId:undefined
})
const save = async () => {
    proxy.$handleSave(polyForm.value,addPoly,updatePoly,props,polyform,()=>{emit('getlist');cancel();})
    // let res = await addPoly({name:'a',zone:'default',cloudId:2})
    // if(res.code == 0){
    //     console.log('re',res.data)
    // }
}
const cancel = () => {
    polyform.name = '';polyform.zone = '';
    polyform.aggregateId = undefined;
    props.info.isShow = false;
}
onMounted(() => {})
defineExpose({polyform})
</script>
<style lang='scss' scoped>
</style>