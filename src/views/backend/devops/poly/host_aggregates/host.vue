<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isAdd ? '选择添加到聚合的主机' : '删除主机'}}</span>
                    <a-popover trigger="click" placement="leftTop">
                        <template #content>
                            <span>{{info.isAdd ? content.host_aggr_add : content.host_aggr_update}}</span>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :disabled="!hostform.hostIds || hostform.hostIds.length <= 0" @click="save">提交</a-button>
            </template>
            <a-form :model="hostform">
                <a-form-item label="选择主机">
                    <a-select mode="multiple" v-model:value="hostform.hostIds" placeholder="请选择主机" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <template v-for="(item,index) in hostlist" :key="index">
                        <a-select-option :value="item.hypervisor_hostname">{{item.hypervisor_hostname}}</a-select-option>
                        </template>
                    </a-select>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import qs from  'qs'
import { queryHypervisor } from '@/api/backend/devops/index';
import { addPolyHost, delPolyHost } from '@/api/backend/devops/poly';
import {content} from "@/common/explain/modal"
import { message } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
import emiter from '@/utils/Bus';
const emit = defineEmits(['getlist'])
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isShow: false,
        isAdd:true,
      };
    }
  },
})
const hostlist = ref([])
const hostform = reactive({
    hostIds:[],
    cloudId:localStorage.getItem('cloudId'),
    hostAggregateId:'',

})
const cancel = () => {
    hostlist.value = [];
    hostform.hostIds = []
    hostform.hostAggregateId = ''
    props.info.isShow = false;
}
const save = async () => {
    let hostform1 = {...hostform}
    // hostform1.hostIds = JSON.stringify(hostform.hostIds);
    hostform1.hostIds = hostform.hostIds.join(',');
    let res; 
    if(props.info.isAdd)
    res =await addPolyHost(hostform1)
    else
    res =await delPolyHost(hostform1)
    if(res.code == 0 && res.data !== false){
        message.success('保存成功')
        cancel()
        emit('getlist')
    }else
    message.error((!res.msg || res.msg == 'success') ? '保存失败' : res.msg);
}
// const del = async () => {
//     let hostform1 = {...hostform}
//     // hostform1.hostIds = JSON.stringify(hostform.hostIds);
//     hostform1.hostIds = hostform.hostIds.join(',');
//     let res =await delPolyHost(hostform1)
//     if(res.code == 0){
//         message.success('保存成功')
//         cancel()
//     }else
//     message.error('保存失败')
// }
const getHost = async (record,isAdd) => {
    hostform.hostAggregateId = record.id;
    // hostform.hostIds = record.hosts;
    let res = await queryHypervisor({cloudId:localStorage.getItem('cloudId')})
    if(res.code == 0){
        // hostlist.value = res.data;
        res.data.forEach((item,index)=>{
            if(isAdd && !record.hosts.includes(item.hypervisor_hostname))
                hostlist.value.push(item)
            if(!isAdd && record.hosts.includes(item.hypervisor_hostname))
                hostlist.value.push(item)
        })
    }
}
onMounted(() => {})
defineExpose({hostform,getHost})
</script>
<style lang='scss' scoped>
</style>