<template>
    <div class=''>
        <a-row v-if="userlimit">
            <a-button type="primary" @click="handleAdd">新增</a-button>
        </a-row>
        <br>
        <a-table :columns="columns" :data-source="polylist" row-key="id" :pagination="false" :scroll="{x:1090}" :loading="polyloading">
            <template #index="{record,index}">
                {{index+1}}
            </template>
            
            <template #hosts="{record,index}">
                {{record.hosts.join(',')}}
            </template>
            <template #metadata="{record,index}">
                <div class="meta_column">
                    <a-tooltip>
                        <template #title>
                            <span v-for="(item,index) in Object.keys(record.metadata)" :key="index">
                                {{item+'='+record.metadata[item]}}<br>
                            </span>
                        </template>
                        <span v-for="(item,index) in Object.keys(record.metadata)" :key="index">
                            {{item+'='+record.metadata[item]}}<br>
                        </span>
                    </a-tooltip>
                </div>
            </template>
            <template #action="{record,index}">
                <a-button class="button_E" @click="handleEdit(record)">修改</a-button>
                <a-button class="button_E" @click="handleHost(record,true)">添加主机</a-button>
                <a-button class="button_E" @click="handleHost(record,false)">删除主机</a-button>
                <a-button class="button_D" @click="$handleDel({ids:record.id,cloudId},deletePoly,getThisList)">删除</a-button>
            </template>
        </a-table>
        <Info ref="polyDialog" :info="polyinfo" @getlist="getThisList" />
        <Host ref="hostDialog" :info="hostinfo" @getlist="getThisList" />
    </div>
</template>
<script lang='ts' setup>
import Info from "./info.vue"
import Host from "./host.vue"
import { deletePoly, getHostAggregateList } from '@/api/backend/devops/poly';
import router from '@/router';
import { userStore } from '@/store/user';
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
const {proxy} = getCurrentInstance()
// const cloudId = computed(()=>router.currentRoute.value.query.cloudId);
const userlimit = computed(()=>userStore().userInfo.roleIdList.includes(1));
const polyinfo = reactive({
    isAdd:true,
    isShow:false
})
const hostinfo = reactive({
    isShow: false,
    isAdd:true,
})
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id',width:60,align:'center'},
    {title: '名称', dataIndex: 'name', key: 'id' ,align:'center'},
    {title: '可用域', dataIndex: 'availability_zone', key: 'id' ,align:'center'},
    {title: '主机', dataIndex: 'hosts', slots: { customRender: 'hosts' }, key: 'id' ,align:'center'},
    {title: '元数据', dataIndex: 'metadata', slots: { customRender: 'metadata' }, key: 'id' ,align:'center'},
    {title: '操作', dataIndex: 'action', slots: { customRender: 'action' }, key: 'id' ,width:264},
]
const polylist = ref([]);
const polyloading = ref(false);
const polyDialog = ref();
const hostDialog = ref();
const handleAdd = () => {
    polyinfo.isAdd = polyinfo.isShow = true;
}
const handleEdit = (record) => {
    polyinfo.isAdd = false;
    polyinfo.isShow = true;
    nextTick(()=>{
        polyDialog.value.polyform.name = record.name;
        polyDialog.value.polyform.zone = record.availability_zone;
        polyDialog.value.polyform.aggregateId = record.id;
        // Object.assign(polyDialog.value.polyform,record)
    })
}
const handleHost = (record,isAdd) => {
    // hostDialog.value.hostform.hostAggregateId = record.id;
    hostDialog.value.getHost(record,isAdd);
    hostinfo.isShow = true;
    hostinfo.isAdd = isAdd;
}
const getThisList = async () => {
    polyloading.value = true;
    let res = await getHostAggregateList({cloudId:router.currentRoute.value.query.cloudId})
    polyloading.value = false;
    if(res.code == 0){
        polylist.value = res.data;
    }
}
onMounted(() => {
    getThisList();
})
defineExpose({getThisList})
</script>
<style lang='scss' scoped>
.meta_column{
    //超出两行省略号
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2; //显示几行

}
</style>