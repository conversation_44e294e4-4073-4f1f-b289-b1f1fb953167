<template>
    <div class='back-page'>
        <a-menu v-model:selectedKeys="activeKey" mode="horizontal" @select="changeTab">
            <a-menu-item :key="1">
                主机聚合
            </a-menu-item>
            <a-menu-item :key="2">
                虚拟机管理器
            </a-menu-item>
            <a-menu-item :key="3">
                计算服务
            </a-menu-item>
            <a-menu-item :key="4">
                块存储服务
            </a-menu-item>
            <a-menu-item :key="5">
                网络代理
            </a-menu-item>
            <a-sub-menu :key="6" v-if="isOpenHA">
                <template #title>
                    高可用{{activeKey[0] >= 7 ? (' > '+haobj[activeKey[0]]) : ''}}
                </template>
                <a-menu-item :key="7">
                    {{haobj[7]}}
                </a-menu-item>
                <a-menu-item :key="8">
                    {{haobj[8]}}
                </a-menu-item>
                <!-- <a-menu-item :key="9">
                    {{haobj[9]}}
                </a-menu-item> -->
            </a-sub-menu>
            <a-menu-item :key="10">
                计算主机
            </a-menu-item>
        </a-menu>
        <div class="ant-tabs-tabpane-active">
            <component :is='componentObj[activeKey[0]]'></component>
        </div>
    </div>
</template>
<script lang='ts' setup>
import HostAggregates from "./host_aggregates/index.vue";
import Hypervisors from "./hypervisors/index.vue";
import ComputeServices from "./compute_services/index.vue";
import BlockStorageServices from "./block_storage_services/index.vue";
import NetworkAgents from "./network_agents/index.vue";
import Segment from "@/views/backend/devops/ha/segment/index.vue";
import Hosts from "@/views/backend/devops/ha/hosts/index.vue";
import Notifications from "@/views/backend/devops/ha/notifications/index.vue";
import HostServices from "./host_services/index.vue";
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import {getVisualSatistic,getVisualList, getComputeServiceList, getBlockServiceList, getHostAggregateList, getNetworkAgentList,deletePoly, supportsMasakari} from "@/api/backend/devops/poly";
import { useRoute } from 'vue-router';
import { menuStore } from "@/store/menu";
import { userStore } from "@/store/user";
const { proxy }  = getCurrentInstance();
const props = defineProps({isDomain:false})
const menu_store = menuStore();
const haobj = {
    7:'Segments',
    8:'宿主机',
    9:'Notifications'
}
const componentObj = {
    1:HostAggregates,
    2:Hypervisors,
    3:ComputeServices,
    4:BlockStorageServices,
    5:NetworkAgents,
    7:Segment,
    8:Hosts,
    9:Notifications,
    10:HostServices
}
const haRef = ref();
const hRef = ref();
const csRef = ref();
const bssRef = ref();
const naRef = ref();
const segmentRef = ref();
const hostRef = ref();
const noticeRef = ref();
const cloudId = localStorage.getItem('cloudId')
const route = useRoute();
const activeKey = ref([1]);
const isOpenHA = ref(true);
const changeTab = ({key}) => {
    activeKey.value = [key];
}
const getSupportsMasakari = () => {
    supportsMasakari({cloudId:route.query.cloudId}).then((res)=>{
        if(res.code == 0){
            isOpenHA.value = res.data;
        }
    })
}
const polyexpose = () => {
    activeKey.value = [1];
    getSupportsMasakari();
};
onMounted(()=>{
    getSupportsMasakari();
})
defineExpose({polyexpose})
</script>
<style lang='scss' scoped>
.back-page{background-color:#fff;height: auto;}

.ant-tabs-tabpane-active{
    padding: 16px;
}
:deep(.ant-table-thead > tr > th){white-space: nowrap;}

:deep(.ant-tabs .ant-tabs-top-content){height: calc(100vh - 196px);overflow-y: auto;}
:deep(.ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td){background-color: transparent;}

.contentPadding{min-width: 950px;}
:deep(.ant-spin-container){overflow-x: auto;}
</style>