<template>
    <a-table :columns="columns" :data-source="blocklist" row-key="id" :pagination="false" :loading="blockloading">
        <template #index="{record,index}">
            {{index+1}}
        </template>
        <template #status="{record,index}">
            {{record.status.toUpperCase() == 'ENABLED' ? '激活':'未激活'}}
        </template>
        <template #state="{record,index}">
            {{record.state.toUpperCase() == 'UP' ? '启动':'未启动'}}
        </template>
    </a-table>
</template>
<script lang='ts' setup>
import { getBlockServiceList } from '@/api/backend/devops/poly';
import router from '@/router';
import { computed, onMounted, ref } from 'vue';
// const cloudId = computed(()=>router.currentRoute.value.query.cloudId);
const blockloading = ref(false);
const blocklist = ref([]);
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id',width:60,align:'center'},
    {title: '名称', dataIndex: 'binary', key: 'id' ,align:'center'},
    {title: '主机', dataIndex: 'host', key: 'id' ,align:'center'},
    {title: '可用域', dataIndex: 'zone', key: 'id' ,align:'center'},
    {title: '服务状态(Status)', dataIndex: 'status', slots: { customRender: 'status' }, key: 'id' ,align:'center'},
    {title: '主机状态(State)', dataIndex: 'state', slots: { customRender: 'state' }, key: 'id' ,align:'center'},
    {title: '更新时间', dataIndex: 'updated_at', key: 'id' ,align:'center'},
]
const getThisList = async () => {
    blockloading.value = true;
    let res = await getBlockServiceList({cloudId:router.currentRoute.value.query.cloudId})
    blockloading.value = false;
    if(res.code == 0){
        blocklist.value = res.data;
    }
}
onMounted(() => {getThisList()})
defineExpose({getThisList})
</script>
<style lang='scss' scoped>
</style>