<template>
    <a-table :columns="columns" :data-source="calclist" row-key="id" :pagination="false" :loading="calcloading">
        <template #index="{record,index}">
            {{index+1}}
        </template>
        <template #status="{record,index}">
            {{record.status.toUpperCase() == 'ENABLED' ? '激活':'未激活'}}
        </template>
        <template #state="{record,index}">
            {{record.state.toUpperCase() == 'UP' ? '启动':'未启动'}}
        </template>
        <template #action="{record}">
            <a-button class="button_E" @click="handleSwitch(record)">{{record.state.toUpperCase() == 'UP' ? '关闭服务':'开启服务'}}</a-button>
            <!-- <a-button class="button_E" @click="handleMigrate(record)">迁移主机</a-button> -->
        </template>
    </a-table>
    <a-modal v-model:visible="modalinfo.visibleMigrate" title="迁移主机" @ok="handleMigrateSave">
      <a-form :model="migrateform" ref="migrateForm">
        <a-form-item label="当前主机" name="host">
            {{migrateform.host}}
        </a-form-item>
        <a-form-item label="运行虚机迁移类型" name="type">
            <a-select v-model:value="migrateform.type">
                <a-select-option value="hot">热迁移</a-select-option>
            </a-select>
        </a-form-item>
        <a-form-item label="允许磁盘超量" name="allowOversize">
            <a-switch v-model:checked="migrateform.allowOverweight" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" ></a-switch>
        </a-form-item>
        <a-form-item label="块设备迁移" name="block">
            <a-switch v-model:checked="migrateform.block" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" ></a-switch>
        </a-form-item>
      </a-form>
    </a-modal>
</template>
<script lang='ts' setup>
import { closeComputeServiceByCloudId, getComputeServiceList, startComputeServiceByCloudId } from '@/api/backend/devops/poly';
import router from '@/router';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { computed, createVNode, onMounted, reactive, ref } from 'vue';
// const cloudId = computed(()=>router.currentRoute.value.query.cloudId);
const calcloading = ref(false);
const calclist = ref([]);
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id',width:60,align:'center'},
    {title: '主机', dataIndex: 'host', key: 'id' ,align:'center'},
    {title: '可用域', dataIndex: 'zone', key: 'id' ,align:'center'},
    {title: '服务状态(Status)', dataIndex: 'status', slots: { customRender: 'status' }, key: 'id' ,align:'center'},
    {title: '主机状态(State)', dataIndex: 'state', slots: { customRender: 'state' }, key: 'id' ,align:'center'},
    {title: '更新时间', dataIndex: 'updated_at', key: 'id' ,align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
]
const modalinfo = reactive({visibleMigrate:false});
const migrateForm = ref();
const migrateform = reactive({host:'',type:'hot',allowOversize:false,block:false});
const handleSwitch = (record) => {
    let {binary,host,state} = record;
    if(state.toUpperCase() == 'UP'){
        Modal.confirm({
            title: () => '确认关闭计算服务?',
            icon: () => createVNode(ExclamationCircleOutlined),
            onOk() {
                closeComputeServiceByCloudId({binary,host,cloudId:router.currentRoute.value.query.cloudId}).then((res)=>{
                    if(res.code == 0){
                        message.success("关闭服务成功");
                        getThisList();
                    }
                })
            }
        });
    }else{
        startComputeServiceByCloudId({binary,host,cloudId:router.currentRoute.value.query.cloudId}).then((res)=>{
            if(res.code == 0){
                message.success("开启服务成功");
                getThisList();
            }
        })
    }
    
    
}
const handleMigrate = (record) => {
    modalinfo.visibleMigrate = true;
    migrateform.host = record.host;
}
const getThisList = async () => {
    calcloading.value = true;
    let res = await getComputeServiceList({cloudId:router.currentRoute.value.query.cloudId})
    calcloading.value = false;
    if(res.code == 0){
        calclist.value = res.data.filter((item,index)=>item.binary == 'nova-compute');
    }
}
onMounted(() => {getThisList()})
defineExpose({getThisList})
</script>
<style lang='scss' scoped>
</style>