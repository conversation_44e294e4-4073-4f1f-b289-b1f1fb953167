<template>
    <div class=''>
        <a-card :loading="chartloading">
            <a-row class="dashboard">
                <div class="item">
                    <a-progress type="dashboard" :width="80" :format="()=>((chartlist.vcpus_used / chartlist.vcpus) * 100).toFixed(2) + '%'" :percent="Number(((chartlist.vcpus_used / chartlist.vcpus) * 100).toFixed(2))" :stroke-color="chartlist.vcpus_used / chartlist.vcpus > 0.7 ? '#EE6666' : ''" />
                    <p></p><span>VCPU使用量</span><p>已使用 {{chartlist.vcpus_used}} / {{chartlist.vcpus}}</p>
                </div> 
                <div class="item">
                    <a-progress type="dashboard" :width="80" :format="(percent)=>percent + '%'" :percent="Number(((chartlist.memory_mb_used/chartlist.memory_mb) * 100).toFixed(2))" :stroke-color="chartlist.memory_mb_used / chartlist.memory_mb > 0.75 ? '#EE6666' : ''" />
                    <p></p><span>内存使用量</span><p>已使用 {{(chartlist.memory_mb_used/(1024*1024)).toFixed(2)}} TB / {{(chartlist.memory_mb/(1024*1024)).toFixed(2)}} TB</p>
                </div> 
                <div class="item">
                    <a-progress type="dashboard" :width="80" :format="(percent)=>percent + '%'" :percent="Number(((chartlist.local_gb_used/chartlist.local_gb) * 100).toFixed(2))" :stroke-color="chartlist.local_gb_used / chartlist.local_gb > 0.75 ? '#EE6666' : ''" />
                    <p></p><span>本地磁盘使用量</span><p>已使用 {{(chartlist.local_gb_used/1024).toFixed(2)}} TB / {{local_gb_used}}</p>
                </div> 
            </a-row>
        </a-card>
        <br>
        <a-table :columns="columns" :data-source="vmlist" row-key="id" :pagination="false" :loading="vmloading">
            <template #index="{record,index}">
                {{index+1}}
            </template>
            <!-- <template #memory_mb_used="{record,index}">
                {{record.memory_mb_used >= 1024 ? (record.memory_mb_used / 1024).toFixed(2) + 'GB' : 'MB'}}
            </template> -->
            <template v-for="col in ['memory_mb_used', 'memory_mb']" #[col]="{ text, record }" :key="col">
                {{$filterMemory(record[col])}}
            </template>
            <template #local_gb_used="{record,index}">
                {{$filterGB(record.local_gb_used)}}
            </template>
            <template #local_gb="{record,index}">
                {{$filterGB(record.local_gb / menu_store.cloudInfo.storageCopyNum)}}
            </template>
        </a-table>
    </div>
</template>
<script lang='ts' setup>
import { getVisualList, getVisualSatistic } from '@/api/backend/devops/poly';
import router from '@/router';
import { menuStore } from '@/store/menu';
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
const {proxy} = getCurrentInstance();
const menu_store = menuStore();
// const cloudId = computed(()=>router.currentRoute.value.query.cloudId);
const chartloading = ref(false);
const chartlist = reactive({});
const vmloading = ref(false);
const vmlist = ref([]);
const local_gb_used = ref();
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id',width:60,align:'center'},
    {title: '主机名称', dataIndex: 'hypervisor_hostname', key: 'id' ,align:'center'},
    {title: '主机类型', dataIndex: 'hypervisor_type', key: 'id' ,align:'center'},
    {title: 'VCPUs(used)', dataIndex: 'vcpus_used', key: 'id' ,align:'center'},
    {title: 'VCPUs(total)', dataIndex: 'vcpus', key: 'id' ,align:'center'},
    {title: 'RAM(used)', dataIndex: 'memory_mb_used', slots: { customRender: 'memory_mb_used' }, key: 'id' ,align:'center'},
    {title: 'RAM(total)', dataIndex: 'memory_mb', slots: { customRender: 'memory_mb' }, key: 'id' ,align:'center'},
    {title: 'Local Storage(used)', dataIndex: 'local_gb_used', slots: { customRender: 'local_gb_used' }, key: 'id' ,align:'center'},
    {title: 'Local Storage(total)', dataIndex: 'local_gb', slots: { customRender: 'local_gb' }, key: 'id' ,align:'center'},
    {title: 'Instances', dataIndex: 'running_vms', key: 'id' ,align:'center'},
]
const getVisualSatisticChart = async () => {
    chartloading.value = true;
    let res = await getVisualSatistic({cloudId:router.currentRoute.value.query.cloudId})
    if(res.code == 0){
        // chartlist.value = res.data;
        Object.assign(chartlist,res.data)
    }
    chartloading.value = false;
}
const getThisList = async () => {
    vmloading.value = true;
    let res = await getVisualList({cloudId:router.currentRoute.value.query.cloudId})
    vmloading.value = false;
    console.log("res",res)
    if(res.code == 0){
        vmlist.value = res.data;
        local_gb_used.value = proxy.$filterGB((chartlist.local_gb * 0.95) / (menu_store.cloudInfo.storageCopyNum * vmlist.value.length));
        console.log("local_gb_used.value",local_gb_used.value)
    }
}
onMounted(() => {
    getVisualSatisticChart();
    getThisList();
})
defineExpose({getVisualSatisticChart,getThisList})
</script>
<style lang='scss' scoped>
.dashboard{
    justify-content: space-evenly;flex-wrap: wrap;
    .item{min-width: 165px;}
}
:deep(.ant-progress-circle.ant-progress-status-success .ant-progress-text){color: #000000D9;}
</style>