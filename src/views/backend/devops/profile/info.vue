<template>
    <div class=''>
        <a-modal :title="info.isInfo ? '查看集群配置' : (info.isAdd ? '添加集群配置' : '修改集群配置')" v-model:visible="info.isShow" @cancel="cancel" @ok="save" :maskClosable="info.isInfo" centered :getContainer="modalBindNode">
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="save">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="configform" ref="configForm" :rules="rules" :label-col="{span:4}">
                <a-form-item label="配置名称" name="profileName">
                    <a-input v-model:value="configform.profileName" placeholder="请输入集群配置名称" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="类型" name="profileType" v-if="info.isInfo">
                    <a-input v-model:value="configform.profileType" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="版本" name="profileVersion" v-if="info.isInfo">
                    <a-input v-model:value="configform.profileVersion" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="虚机名称" name="serverName">
                    <a-input v-model:value="configform.serverName" placeholder="请输入虚机名称" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="镜像" name="imageId">
                    <a-select v-model:value="configform.imageId" placeholder="请选择镜像" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in imagelist" :key="index" :value="item.id">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="虚机类型" name="flavorId">
                    <a-select v-model:value="configform.flavorId" placeholder="请选择虚机类型" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in flavorlist" :key="index" :value="item.id">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="安全组" name="securityGroups">
                    <a-select v-model:value="configform.securityGroups" mode="multiple" placeholder="请选择安全组" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in grouplist" :key="index" :value="item.id+''">{{item.groupName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="网络" name="networkIds">
                    <a-select v-model:value="configform.networkIds" mode="multiple" placeholder="请选择网络" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in networklist" :key="index" :value="item.id+''">{{item.networkName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="密钥" name="keypairId">
                    <a-select v-model:value="configform.keypairId" placeholder="请选择密钥" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in Keypairlist" :key="index" :value="item.id">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { saveClusterProfile, updateClusterProfile } from '@/api/backend/cluster';
import { selectFlavorList } from '@/api/backend/devops/flavor';
import { selectImageList } from '@/api/backend/devops/image';
import { selectKeypairList } from '@/api/backend/devops/keypair';
import { selectNetworkList } from '@/api/backend/devops/network';
import { selectSecugroupList } from '@/api/backend/devops/security';
import router from '@/router';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info: {
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow: false
            }
        }
    }
})
const configForm = ref();
const imagelist = ref([])
const flavorlist = ref([])
const grouplist = ref([])
const networklist = ref([])
const Keypairlist = ref([])
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    profileName:'',
    serverName:'',
    imageId:undefined,
    flavorId:undefined,
    securityGroups:undefined,
    networkIds:undefined,
    keypairId:undefined
}
const configform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    profileName:'',
    serverName:'',
    imageId:undefined,
    flavorId:undefined,
    securityGroups:undefined,
    networkIds:undefined,
    keypairId:undefined
})
const rules = {
    profileName:[{required:true,message:'请输入集群配置名称',trigger:'change'}],
    serverName:[{required:true,message:'请输入虚机名称',trigger:'change'}],
    imageId:[{required:true,type:'number',message:'请选择镜像',trigger:'change'}],
    flavorId:[{required:true,type:'number',message:'请选择虚机类型',trigger:'change'}],
    securityGroups:[{required:true,type:'array',message:'请选择安全组',trigger:'change'}],
    networkIds:[{required:true,type:'array',message:'请选择网络',trigger:'change'}],
    keypairId:[{required:false,type:'number',message:'请选择网络',trigger:'change'}],
}
const cancel = () => {
    props.info.isShow = false;
    Object.assign(configform,defaultform);
}
const save = () => {
    let configform1 = {...configform};
    proxy.$handleSave(configForm.value, saveClusterProfile, updateClusterProfile, props, configform1, ()=>{emit('getlist');cancel()},null,()=>{
        configform1.securityGroups = configform.securityGroups?.length > 0 ? configform.securityGroups.join(',') : '';
        configform1.networkIds = configform.networkIds?.length > 0 ? configform.networkIds.join(',') : '';
    })
}
const selectImagelist = async () => {
    let res = await selectImageList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId,imageType:'Image',status:'ACTIVE'})
    if(res.code == 0){
        imagelist.value = res.data;
    }
}
const selectFlavorlist = async () => {
    let res = await selectFlavorList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId})
    if(res.code == 0){
        flavorlist.value = res.data;
    }
}
const selectGrouplist = async () => {
    let res = await selectSecugroupList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId})
    if(res.code == 0){
        grouplist.value = res.data;
    }
}
const selectNetworklist = async () => {
    let res = await selectNetworkList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId})
    if(res.code == 0){
        networklist.value = res.data;
    }
}
const selectKeypairlist = async () => {
    let res = await selectKeypairList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId})
    if(res.code == 0){
        Keypairlist.value = res.data;
    }
}
const helper = () => {
    selectImagelist();selectFlavorlist();selectGrouplist();selectNetworklist();selectKeypairlist()
}
onMounted(() => {})
defineExpose({configform,helper})
</script>
<style lang='scss' scoped>
</style>