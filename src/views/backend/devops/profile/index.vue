<template>
    <div class='cloudContent'>
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                    <a-form-item label="集群配置名称">
                        <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.profileName" allowClear />
                    </a-form-item>
                    </a-form>
                    <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class="innerPadding">
                <a-button type="primary" class="btnMargin" @click="handleAdd">新增</a-button>
                <a-button @click="$handleDel(selectRowIds,deleteClusterProfile,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                    批量删除
                </a-button>
            <br/><br/>
                <a-table :row-selection="rowSelection" :columns="columns3" :data-source="clusprofilelist" row-key="id" :pagination="pagination" @change="changeTable">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #action={record}>
                        <a-button class="button_V" @click="handleInfo(record)">查看</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteClusterProfile,getList)">删除</a-button>
                    </template>
                </a-table>
            </div>
        </div>
        <CInfo ref="profileRef" :info="info3" @getlist="getList" />
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import CInfo from "./info.vue";
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { selectClusterProfileList, deleteClusterProfile, getClusterProfileList } from "@/api/backend/cluster";
import { handleWidth } from "@/utils/moreform";
import router from "@/router";
const { proxy } = getCurrentInstance();
const clusprofilelist = ref([]);
const profileRef = ref()
const loading = ref(false);
let selectRowIds: string[] = ref([]);
const info3 = reactive({
    isAdd: true,
    isShow: false,
    isInfo: false
})
const searchform = reactive({
    profileName:'',
    pageIndex:1,
    pageSize:10,
    "cloudId": router.currentRoute.value.query.cloudId,
    "projectId": router.currentRoute.value.query.projectId,
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、排序、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns3 = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '配置名称', dataIndex: 'profileName', key: 'name', align:'center'},
    {title: '类型', dataIndex: 'profileType', key: 'id', align:'center'},
    {title: '镜像', dataIndex: 'imageName', key: 'id'},
    {title: '虚机类型', dataIndex: 'flavorName', key: 'id', align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:130},
];
const handleAdd = () => {
    info3.isShow = info3.isAdd = true;
    info3.isInfo = false;
    proxy.$nextTick(()=>{
        profileRef.value.helper()
    })
}

const handleInfo = (record) => {
    info3.isInfo = info3.isShow = true;
    let record1 = {...record};
    record1.securityGroups = record1.securityGroups?.split(',');
    record1.networkIds = record1.networkIds?.split(',');
    proxy.$nextTick(()=>{
        profileRef.value.helper()
        Object.assign(profileRef.value.configform, record1);
    })
}

const handleSearch = () => {
    searchform.pageIndex = 1;
    getList();
}

const handleAllReset = () => {
    searchform.profileName = '';
    searchform.pageIndex = 1;
    // getList();
}
const getList = async () => {
    clusprofilelist.value = await proxy.$getList(loading, getClusterProfileList, searchform, pagination, getList)
}
onMounted(() => {getList();
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
.cloudRight{min-width:auto}
:deep(.ant-table th) { white-space: nowrap; }
:deep(.ant-table td) { white-space: nowrap; }
.buttonPadding,.innerPadding{min-width: 885px;}
</style>