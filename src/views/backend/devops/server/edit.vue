<template>
    <div class=''>
        <a-modal title="修改虚机" v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form :model="serverform" :label-col="{span:4}" ref="serverForm" :rules="rules">
                <a-form-item label="虚机名称" name="serverName">
                    <a-input v-model:value="serverform.serverName" placeholder="请输入虚机名称" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="描述" name="description">
                    <a-textarea v-model:value="serverform.description" placeholder="请输入描述" allow-clear></a-textarea>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { saveServer, updateServer } from '@/api/backend/devops/server';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
const {proxy} = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
                isInfo:true
            }
        }
    }
})
const serverForm = ref();
const defaultform = {
    cloudId:route.query.cloudId,
    domainId:route.query.domainId,
    projectId:route.query.projectId,
    id:'',
    serverName:'',
    description:''
}
const serverform = reactive({
    cloudId:route.query.cloudId,
    domainId:route.query.domainId,
    projectId:route.query.projectId,
    id:'',
    serverName:'',
    description:''
})
const rules = {
    serverName:[{required:true, message:'请输入名称',trigger:'change'}],
}
const setinfo = (record) => {
    serverform.id = record.id;
    serverform.serverName = record.serverName;
    serverform.description = record.description;
    // Object.assign(serverform, record)
}
const cancel = () => {
    props.info.isShow = false;
    Object.assign(serverform, defaultform);
}
const save = () => {
    proxy.$handleSave(serverForm.value,saveServer,updateServer,props,serverform,()=>{emit('getlist');cancel()})
}
onMounted(() => {})
defineExpose({setinfo})
</script>
<style lang='scss' scoped>
</style>