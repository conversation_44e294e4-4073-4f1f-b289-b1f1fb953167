<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" :title="info.isSync ? '验证host账户并初始化设备' : (info.isInstall ? '验证host账户并挂载' : '验证host账户并卸载')" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" @click="submit" :disabled="!hostform.userName || !hostform.passwd">验证</a-button>
            </template>
            <a-form
                :model="hostform"
                :rules="rules"
                ref="hostForm"
            >
                <a-form-item>
                <a-input v-model:value="hostform.userName" placeholder="当前虚机所在主机的用户如：root">
                    <template #prefix><UserOutlined style="color: rgba(0, 0, 0, 0.25)" /></template>
                </a-input>
                </a-form-item>
                <a-form-item>
                <a-input v-model:value="hostform.passwd" placeholder="当前虚机所在主机的用户密码如：123">
                    <template #prefix><LockOutlined style="color: rgba(0, 0, 0, 0.25)" /></template>
                </a-input>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { deleteUSB, saveUSB, SyncDevice } from '@/api/backend/devops/server';
import emiter from '@/utils/Bus';
import { message } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
const emit = defineEmits(['selectlist','setusbId'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isSync:false,
                isInstall:true,
                isShow:false
            }
        }
    }
})
const hostForm = ref()
const defaultform = {
    userName:'',
    passwd:'',
    hostIp:undefined,
    usbId:undefined,
    serverId:undefined,
    cloudId:undefined
}
const hostform = reactive({
    userName:'',
    passwd:'',
    hostIp:'',
    cloudId:''
})
const cancel = () => {
    props.info.isShow = false;
    Object.assign(hostform,defaultform)
    hostForm.value.resetFields();
}
const submit = async () => {
    if(props.info.isSync){
        let res = await SyncDevice(hostform);
        if(res.code == 0 && res.data !== false){
            emit('selectlist','sync');
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '同步失败' : res.msg);
        }
    }else{
        let apiName = props.info.isInstall ? saveUSB : deleteUSB;
        apiName(hostform).then((res)=>{
        if(res.code == 0 && res.data !== false){
            message.success(props.info.isInstall ? '挂载成功':'卸载成功');
            emit('selectlist');
        }else{
            emit('setusbId')
            // usbform.usbId = tempid.value;
            message.error((!res.msg || res.msg == 'success') ? (props.info.isInstall ? '挂载失败':'卸载失败') : res.msg);
        }
        })
    }
    cancel();
}
onMounted(() => {})
defineExpose({hostform})
</script>
<style lang='scss' scoped>
</style>