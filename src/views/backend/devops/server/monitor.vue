<template>
    <div class='' style="padding:20px">
        <a-form layout="inline">
            <a-form-item>
                <a-radio-group v-model:value="value1" @change="changeRadio">
                    <a-radio-button :value="1000*60*60">最近1小时</a-radio-button>
                    <a-radio-button :value="1000*60*60*2">最近2小时</a-radio-button>
                    <a-radio-button :value="1000*60*60*6">最近6小时</a-radio-button>
                    <a-radio-button :value="1000*60*60*12">最近12小时</a-radio-button>
                </a-radio-group>
            </a-form-item>
            <a-form-item>
                <a-range-picker
                v-model:value="value2"
                :locale="locale"
                :show-time="{ format: 'HH:mm:ss' }"
                format="YYYY-MM-DD HH:mm:ss"
                :placeholder="['开始时间', '结束时间']"
                @change="onChangeTime"
                allow-clear
                />
            </a-form-item>
            <a-form-item>
                <a-button @click="onOk" :disabled="value2.length <= 0">查询</a-button>
            </a-form-item>
            <a-form-item>
                <a-button @click="onReset" :disabled="value2.length <= 0">重置</a-button>
            </a-form-item>
        </a-form>
        <br/>
        <div class="charts" id="chart9" ref="chart9"></div>
        <div class="charts" id="chart10" ref="chart10"></div>
        <!-- <div class="charts" id="chart11" ref="chart11"></div> -->
        <div class="charts" id="chart12" ref="chart12"></div>
        <div class="charts" id="chart13" ref="chart13"></div>
    </div>
</template>
<script lang='ts' setup>
import { onMounted, reactive, ref } from 'vue';
import * as echarts from "echarts";
import moment from "moment";
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import {getMonitorline} from "@/api/backend/devops/agent";
import { setNotopt } from "@/utils/tool";
import { Options } from '@/common/chartoption';
const props = defineProps({
    serverId:{
        type:String,
        default(){
            return ''
        }
    }
})
const value1 = ref(1000*60*60)
const value2 = ref([])
var timer1 = null
var myChart9;
var myChart10;
var myChart11;
var myChart12;
var myChart13;
const chart9 = ref()
const chart10 = ref()
const chart11 = ref()
const chart12 = ref()
const chart13 = ref()
let option9 = Options.server_monitor_9;
let option10 = Options.server_monitor_10;
let option12 = Options.server_monitor_12;
let option13 = Options.server_monitor_13;
const searchform = reactive({
    serverId:props.serverId,
    startTime:'',
    endTime:''
})
const onChangeTime = (a,b,c) => {
    searchform.startTime = b[0];
    searchform.endTime = b[1];
    value1.value = '';
}

const onReset = () => {
    value2.value = []
}
const getMonitorData = (startTime,endTime) => {
  getMonitorLine(startTime,endTime,'cpu',option9,myChart9);
  getMonitorLine(startTime,endTime,'memory',option10,myChart10);
  getMonitorLine(startTime,endTime,'byte',option12,myChart12);
  getMonitorLine(startTime,endTime,'packet',option13,myChart13);
}
const getMonitorLine = async (startTime="",endTime="",type,OPTION,CHART) => {
    let res = await getMonitorline({serverId:props.serverId,startTime,endTime,type:type.toUpperCase()})
    if(res.code == 0){
      if(type == 'cpu' || type == 'memory'){
        OPTION.xAxis.data=res.data[type+'Map'][type+'TimeList']
        OPTION.series[0].data=res.data[type+'Map'][type+'PercentList'];
        if(OPTION.xAxis.data.length > 20){
          OPTION.label.show = false;
          OPTION.series[0].symbolSize = 0;
        }else{
          OPTION.label.show = true;
          OPTION.series[0].symbolSize = 3;
        }
        if(res.data[type+'Map'][type+'TimeList'] && res.data[type+'Map'][type+'TimeList'].length > 0)
        CHART.setOption(OPTION);
        else{
          let Option9 = JSON.parse(JSON.stringify(OPTION));
          setNotopt(CHART,Option9)
        }
      }else{
        if(type == 'packet'){
          OPTION.xAxis.data = res.data[type+'Map'].netwrorkPackageTimeList;
          OPTION.series[0].data = res.data[type+'Map'][type+'RecvList'];
          OPTION.series[1].data = res.data[type+'Map'][type+'SendList'];
        }else{
          OPTION.xAxis.data = res.data[type+'Map'].netwrorkByteTimeList;
          OPTION.series[0].data = res.data[type+'Map'][type+'sRecvList'];
          OPTION.series[1].data = res.data[type+'Map'][type+'sSendList'];
        }
        
        if(OPTION.xAxis.data.length > 20){
          OPTION.series[0].symbolSize = 0;
          OPTION.series[1].symbolSize = 0;
        }else{
          OPTION.series[0].symbolSize = 3;
          OPTION.series[1].symbolSize = 3;
        }
        if(type == 'packet'){
          if(res.data[type+'Map'].netwrorkPackageTimeList && res.data.packetMap.netwrorkPackageTimeList.length>0)
          CHART.setOption(OPTION);
          else{
            let Option13 = JSON.parse(JSON.stringify(OPTION));
            setNotopt(CHART,Option13)
          }
        }else{
          if(res.data[type+'Map'].netwrorkByteTimeList && res.data[[type+'Map']].netwrorkByteTimeList.length>0)
          myChart12.setOption(OPTION);
          else{
            let Option12 = JSON.parse(JSON.stringify(OPTION));
            setNotopt(CHART,Option12)
          }
        }
      } 
    }
}
const changeRadio = async (e,b) => {
    value2.value = []
    clearInterval(timer1)
    begin()
    getMonitorData(moment((new Date()).getTime()-value1.value).utcOffset("+08:00").format('YYYY-MM-DD HH:mm:ss'),moment((new Date()).getTime()).utcOffset("+08:00").format('YYYY-MM-DD HH:mm:ss'))
}
const clear = () => {
    clearInterval(timer1)
}
const begin = () => {
  clearInterval(timer1)
    timer1 = setInterval(()=>{
      let startTime = moment((new Date()).getTime()-value1.value).utcOffset("+08:00").format('YYYY-MM-DD HH:mm:ss')
      let endTime = moment((new Date()).getTime()).utcOffset("+08:00").format('YYYY-MM-DD HH:mm:ss')
        getMonitorData(startTime,endTime)
    },300000)
}
const onOk = (a,b,c)=>{
  if(searchform.startTime && searchform.endTime){
    clearInterval(timer1);
    // value1.value = 1000*60*60;
    value1.value = '';
    getMonitorData(searchform.startTime,searchform.endTime);
  }
}
onMounted(() => {
    myChart9 = echarts.init(chart9.value);
    myChart10 = echarts.init(chart10.value);
    // myChart11 = echarts.init(chart11.value);
    myChart12 = echarts.init(chart12.value);
    myChart13 = echarts.init(chart13.value);
    window.addEventListener('resize',function(){
    myChart9.resize()
    myChart10.resize()
    // myChart11.resize()
    myChart12.resize()
    myChart13.resize()
  })
})
defineExpose({getMonitorData,begin,clear,value1})
</script>
<style lang='scss' scoped>
.charts{width:100%;
height:230px;
padding: 10px 8px;
border: 1px solid #f0f2f5;
margin-bottom: 20px;
&:hover {
    border-color: #00000017;
    box-shadow: 0 2px 8px #00000017;
}
transition:all .3s;
}
</style>