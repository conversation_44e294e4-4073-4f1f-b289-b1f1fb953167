<template>
  
    <div class="back-page">
      <a-page-header
          style="background-color:#fff"
          :title="isEmpty ? ' ' : (serverInfo?.serverName)"
          @back="()=>{if(route.query?.thirdServerId){router.back();}else{info.isInfo = info.isShow = false;emit('getlist')}}"
      />
        <!-- <div class="back-header">
            <a-button @click="()=>{info.isInfo = info.isShow = false}">返回</a-button>
            <div></div>
            <div></div>
        </div> -->
        <a-empty v-if="isEmpty" style="margin:100px auto" />
        <a-tabs v-model:activeKey="activeKey" class="back-content" style="background:#fff" :animated="false" @change="changeTab" v-else>
            <a-tab-pane tab=""></a-tab-pane>
            <a-tab-pane key="2" tab="概览">
                <!-- <div class="back-content"> -->
                    <div class="outline clearfix">
                        <a-descriptions size="middle" class="server-info" title="虚机信息" :column="column">
                            <!-- <a-descriptions-item></a-descriptions-item> -->
                            <a-descriptions-item label="ID">{{serverInfo?.thirdServerId}}</a-descriptions-item>
                            <a-descriptions-item label="虚机名称">{{serverInfo?.serverName}}</a-descriptions-item>
                            <a-descriptions-item label="云平台名称">{{serverInfo?.cloudName}}</a-descriptions-item>
                            <a-descriptions-item label="云平台域">{{serverInfo.domainName}}</a-descriptions-item>
                            <a-descriptions-item label="项目">{{serverInfo?.projectName}}</a-descriptions-item>
                            <a-descriptions-item label="可用域">{{serverInfo?.availabilityZone}}</a-descriptions-item>
                            <a-descriptions-item label="host">{{serverInfo?.host}}</a-descriptions-item>
                            <a-descriptions-item label="IP地址">
                              <div>
                                <span v-for="(item,index) in serverInfo?.serverAddressEntityList" :key="index">{{item.addr}}<br/></span>
                              </div>
                            </a-descriptions-item>
                            <a-descriptions-item label="MAC地址">
                              <div>
                                <span v-for="(item,index) in serverInfo?.serverAddressEntityList" :key="index">{{item.macAddr}}<br/></span>
                              </div>
                            </a-descriptions-item>
                            <a-descriptions-item label="虚机状态">{{serverInfo.statusText}}</a-descriptions-item>
                            <a-descriptions-item label="到期时间">{{serverInfo.endTime ? serverInfo.endTime : "永久"}}</a-descriptions-item>
                            <a-descriptions-item label="创建时间">{{serverInfo.createTime ? serverInfo.createTime : '-'}}</a-descriptions-item>
                            <a-descriptions-item label="最新操作时间">{{serverInfo.updateTime ? serverInfo.updateTime : '-'}}</a-descriptions-item>
                           <a-descriptions-item label="是否开启自动伸缩">
                              <a-switch v-model:checked="serverInfo.enableConfig" checked-children="是" un-checked-children="否" @change="rulechange" />
                           </a-descriptions-item>
                           <a-descriptions-item label="是否开启定时快照">
                              <a-switch v-model:checked="serverInfo.scheduleSnapshot" checked-children="是" un-checked-children="否" @click="handleSnap" @change="(checked)=>schedulechange(checked,true)" />
                                <a-modal title="定时快照" v-model:visible="openCron" @cancel="()=>{openCron = false}" :getContainer="modalBindNode" :bodyStyle="{height:'200px',position: 'relative'}">
                                <template #footer>
                                  <div>
                                    <a-button @click="()=>{openCron = false}">取消</a-button>
                                    <a-button type="primary" @click="saveSchedule" :disabled="!scheduleform.corn">提交</a-button>
                                    
                                  </div>
                                </template>
                                  <a-form layout="inline" ref="scheduleForm" :model="scheduleform">
                                    <a-form-item label="corn表达式" name="corn">
                                      <a-input v-model:value="scheduleform.corn" placeholder="请输入corn表达式" style="width:200px" @click="openModal" allow-clear readonly />
                                    </a-form-item>
                                    <span style="position: absolute; bottom: 0px;color:#ff4d4f">
                                      注意:为了最大程度减少对虚拟机性能的影响，建议在非高峰时段执行快照备份操作。
                                    </span>
                                  </a-form>
                                  <!-- <CronModal ref="cronDialog" :cronprops="cronProps" :scheduleform="scheduleform" @setExpression="(e)=>{console.log('e',e);scheduleform.corn = e}" /> -->
                                  <JCronModal ref="cronDialog" :data="scheduleform.corn" @okk="handleOK"></JCronModal>
                              </a-modal>
                            </a-descriptions-item>
                          <a-descriptions-item label="是否开启CVE扫描">
                            <a-switch v-model:checked="serverInfo.openUmop" checked-children="是" un-checked-children="否" @change="umopchange" />
                          </a-descriptions-item>
                          <a-descriptions-item label="是否开启基于响应内容的检查">
                            <a-switch v-model:checked="serverInfo.isInspection" checked-children="是" un-checked-children="否" :loading="loadingCheck" @click="changeCheck" />
                          </a-descriptions-item>
                          <a-descriptions-item label="标签">
                            <div>
                              <template v-for="(tag, index) in tags" :key="tag.id">
                                <a-tooltip v-if="tag.tagName.length > 20" :title="tag.tagName">
                                  <a-tag>
                                    {{ `${tag.tagName.slice(0, 20)}...` }}<CloseOutlined @click="handleClose(tag.id)" style="color:rgba(0, 0, 0, 0.45);font-size:10px;margin-left:7px" />
                                  </a-tag>
                                </a-tooltip>
                                <a-tag v-else>
                                  {{ tag.tagName }}<CloseOutlined @click="handleClose(tag.id)" style="color:rgba(0, 0, 0, 0.45);font-size:10px;margin-left:7px" />
                                </a-tag>
                              </template>
                              <a-input
                                v-if="inputVisible"
                                ref="inputRef"
                                type="text"
                                size="small"
                                :style="{ width: '78px' }"
                                v-model:value="inputValue"
                                @blur="handleInputConfirm"
                                @pressEnter="handleInputConfirm"
                              />
                              <a-tag v-else @click="showInput" style="background: #fff; border-style: dashed">
                                <plus-outlined />
                                新标签
                              </a-tag>
                            </div>
                          </a-descriptions-item>
                        </a-descriptions>
                        <div class="outline-right clearfix">
                          <div style="width:50%;float: left;" class="clearfix">
                            <span v-if="ballTime[0]">
                              <a-tooltip :title="ballTime[0]?ballTime[0]:null">
                                <a-button type="link" style="padding: 0px 0px"><ClockCircleOutlined /></a-button>
                                监测时间：{{ballTime[0].substr(11,8)}}
                              </a-tooltip>
                              </span>
                            <br><br>
                            <div class="charts" id="chart5" ref="chart5"></div>
                          </div>
                            <div style="width:50%;float: left;" class="clearfix">
                              <span v-if="ballTime[1]">
                                <a-tooltip :title="ballTime[1]?ballTime[1]:null">
                                  <a-button type="link" style="padding: 0px 0px"><ClockCircleOutlined /></a-button>
                                  监测时间：{{ballTime[1].substr(11,8)}}
                                </a-tooltip>
                              </span>
                              <br><br>
                              <div class="charts" id="chart7" ref="chart7"></div>
                            </div>
                        </div>
                    </div>
                    <div class="outline clearfix">
                    <!--</div>
                    <div class="outline"> -->
                        <!-- <a-card title="网络" bordered hoverable > -->
                            <a-descriptions size="small" title="网络" :column="1">
                                <a-descriptions-item label="IP地址"><span v-if="networklist[0]">（固）{{networklist[0]?.addr}} <CopyOutlined style="color:#999" @click="()=>{copy(networklist[0]?.addr)}" /></span>&nbsp;&nbsp;&nbsp;&nbsp;<span v-if="networklist[1]">（浮）{{networklist[1]?.addr}} <CopyOutlined style="color:#999" @click="()=>{copy(networklist[1]?.addr)}" /></span></a-descriptions-item>
                                <a-descriptions-item label="安全规则"><a @click="handleSecurityRule">管理规则</a></a-descriptions-item>
                            </a-descriptions>
                        <!-- </a-card> -->
                        <a-descriptions size="small" title="镜像" :column="1">
                            <a-descriptions-item label="镜像名称">{{imageInfo.name}}</a-descriptions-item>
                            <a-descriptions-item label="镜像类型">{{imageInfo.imageType}}</a-descriptions-item>
                            <a-descriptions-item label="系统类型">{{imageInfo.imageOSText?imageInfo.imageOSText:'-'}}</a-descriptions-item>
                        </a-descriptions>
                        <a-descriptions size="small" title="虚机类型" :column="1">
                            <a-descriptions-item label="类型名称">{{serverInfo.flavorEntity?serverInfo.flavorEntity.name:'-'}}</a-descriptions-item>
                            <a-descriptions-item label="类型ID">{{serverInfo.thirdFlavorId?serverInfo.thirdFlavorId:'-'}}</a-descriptions-item>
                            <a-descriptions-item label="内存">{{serverInfo.flavorEntity?.ram >= 1024 ? (serverInfo.flavorEntity?.ram/1024).toFixed()+'GB':serverInfo.flavorEntity?.ram+'MB'}}</a-descriptions-item>
                            <a-descriptions-item label="vcpu">{{serverInfo.flavorEntity?serverInfo.flavorEntity.vcpus:'-'}}</a-descriptions-item>
                            <a-descriptions-item label="磁盘">{{serverInfo.flavorEntity?serverInfo.flavorEntity.totalDisk+'GB':'-'}}</a-descriptions-item>
                        </a-descriptions>
                        <a-descriptions size="small" title="卷" :column="1" v-if="volumeAttachmentList.length > 0">
                            <a-descriptions-item v-for="(item,inex) in volumeAttachmentList" :key="item.id" label="连接到"><span><router-link :to="{path:'/admin/storage/volume',query:{...route.query}}">{{item.volumeName}}</router-link> 位于 {{item.device}} 上</span></a-descriptions-item>
                        </a-descriptions>
                        <a-modal v-model:visible="visibleSecurity" title="管理规则" ok-text="提交" @ok="handleSecurityOk" @cancel="handleSecurityCancel" :getContainer="modalBindNode">
                          <a-form :model="securityform" ref="securityForm">
                            <a-form-item label="安全组" name="securityGroupIds">
                              <a-select mode="multiple" v-model:value="securityform.securityGroupIds" placeholder="请选择安全组">
                                <a-select-option v-for="(item,index) in securitygrouplist" :key="index" :value="item.id">{{item.groupName}}</a-select-option>
                              </a-select>
                            </a-form-item>
                          </a-form>
                          
                        </a-modal>
                    </div>
                    <div class="outline" v-if="serverInfo.status == 'error'">
                      <a-descriptions size="small" title="故障" :column="1">
                        <a-descriptions-item v-for="(item,key) in fault_msg" :key="key" :label="keyObj[key]">{{item}}</a-descriptions-item>
                      </a-descriptions>
                    </div>
                <!-- </div> -->
                
            </a-tab-pane>
            <a-tab-pane key="3" tab="控制台" v-if="serverInfo.status == 'active'" class="terminal" force-render>
                <div style="height:100%">
                  <a-spin :spinning="termloading" style="height:100%">
                      <div style="display:flex;justify-content:space-between"><a-button type="link" :href="Url" target="_blank"><FullscreenOutlined /></a-button><a-button type="link" :href="Url" target="_blank"><FullscreenOutlined /></a-button></div>
                      <iframe :src="Url" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true" width="100%" style="height: calc(100% - 32px)"> </iframe>
                  </a-spin>
                </div>
                <a-empty v-if="!Url && !termloading" />
            </a-tab-pane>
            <a-tab-pane key="4" tab="控制台日志" v-if="serverInfo.status == 'active'" class="terminal" force-render>
                <!-- <div class="buttonPadding"> -->
                  <a-spin :spinning="logloading">
                    <a-form layout="inline" :model="searchform" >
                      <a-form-item label="日志长度" :wrapper-col="{span:24}">
                          <a-input-number placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.lines" :step="1" :min="1" :precision="0" style="width:200px" allowClear />
                      </a-form-item>
                      <a-form-item>
                          <a-button type="primary" @click="handleSearch" :disabled="!searchform.lines"> {{ $t("m.search") }} </a-button>
                      </a-form-item>
                    </a-form>
                    <a-typography-paragraph style="height:calc(100% - 50px)">
                      <pre style="height:100%">{{logText}}</pre>
                    </a-typography-paragraph>
                  </a-spin>
                  
                  <!-- <a-empty v-else /> -->
              <!-- </div> -->
            </a-tab-pane>
            <a-tab-pane key="5" tab="监控" v-if="openPrometheus == 1 && serverInfo.status == 'active'" force-render>
              <Monitor ref="moniRef" :serverId="searchform.serverId" />
            </a-tab-pane>
            <a-tab-pane key="6" tab="物理透传设备" v-if="serverInfo.status == 'active'" force-render>
              <a-spin :spinning="usbloading">
                <div style="padding:20px">
                  <a-form :model="usbform" :label-col="{span:2}">
                    <a-form-item label="可挂载设备">
                      <a-select v-model:value="usbform.usbId" :getPopupContainer="triggerNode => triggerNode.parentNode" placeholder="请选择设备" style="width:600px">
                        <!-- <a-select-option value="">空</a-select-option> -->
                        <a-select-option v-for="(item,index) in usblist" :key="index" :value="item.id">{{item.deviceName}}</a-select-option>
                      </a-select>
                    </a-form-item>
                    <a-form-item :wrapper-col="{offset:2}">
                      <a-button type="primary" @click="handleSave('install')">挂载设备</a-button>
                      <a-button style="margin-left:10px" @click="refresh">更新设备</a-button>
                      <!-- <a-button style="margin-left:10px" @click="saveUsb('uninstall')">卸载设备</a-button> -->
                    </a-form-item>
                    <a-form-item label="已挂载设备" v-if="installedusblist.length > 0">
                      <div style="display:flex;flex-flow:wrap">
                        <a-card size="small" :body-style="{padding:'0 12px 12px'}" v-for="(item,index) in installedusblist" :key="item.id">
                          <template #extra>
                            <a @click="handleSave('uninstall',item)">卸载设备</a>
                          </template>
                          <div style="width:300px;display:flex">
                            <img :src="USB" width="50px" height="50px" style="background-color:azure;border-radius:10px;padding:5px" alt="">
                            <span style="padding-left:10px">{{item.deviceName}}</span>
                          </div>
                        </a-card>
                      </div>
                    </a-form-item>
                  </a-form>
                </div>
              </a-spin>
              
            </a-tab-pane>
             <a-tab-pane key="7" tab="伸缩规则" v-if="serverInfo.enableConfig && serverInfo.status == 'active'" force-render>
              <a-spin :spinning="ruleloading">
                <div class="rulefromfont">
                  <a-form :model="projectform" ref="projectForm" :rules="rules" :labelCol="{span:11}">
                    <a-form-item label="最大CPU使用率" name="maxCpu">
                      <a-input-number v-model:value.trim="projectform.maxCpu"  :min="1" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入"  allow-clear></a-input-number>
                    </a-form-item>
                    <a-form-item label="最大内存使用率" name="maxMemory">
                        <a-input-number v-model:value.trim="projectform.maxMemory" :min="1" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入"  allow-clear></a-input-number>
                    </a-form-item>
                    <a-form-item label="最小cpu使用率阈值" name="minCpu">
                        <a-input-number v-model:value.trim="projectform.minCpu" :min="-1" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入" allow-clear></a-input-number>
                    </a-form-item>
                    <a-form-item label="最小内存使用率阈值" name="minMemory">
                        <a-input-number v-model:value.trim="projectform.minMemory" :min="-1" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入"  allow-clear></a-input-number>
                    </a-form-item>
                    <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
                        <a-button type="primary"  @click="ruleButton" >保存</a-button>
                    </a-form-item>
                  </a-form>
                </div>
              </a-spin>
            </a-tab-pane>
            <a-tab-pane key="8" tab="虚机快照" force-render>
              <div style="padding:20px;overflow-y:auto">
                <a-form layout="inline" :model="snapsearchform" class="searchform">
                <a-form-item label="快照名称">
                    <a-input placeholder="请输入" @pressEnter="()=>{snapsearchform.pageIndex = 1;getSnapList();}" v-model:value="snapsearchform.name" allowClear />
                </a-form-item>
                <a-form-item>
                  <a-button type="primary" @click="()=>{snapsearchform.pageIndex = 1;getSnapList();}">查询</a-button>
                </a-form-item>
                </a-form>
                <a-table
                 :columns="snapcolumns" 
                 row-key="id" 
                 :data-source="snaplist" 
                 :pagination="pagination" 
                 @change="changeSnapTable"
                 :scroll="{x:true}"
                 :loading="imageloading"
                >
                  <template #index={record,index}>
                      {{index+1+(pagination.pageSize * (pagination.current-1))}}
                  </template>
                  <template #imageSize={record}>
                      {{(record.imageSize/(1024*1024*1024)).toFixed(2)}}
                  </template>
                  <template #visibility={record}>
                      {{record.visibility == 'unknown' ? undefined : record.protect}}
                  </template>
                  <template #protect={record}>
                      {{record.protect == 1 ? '是' : ((record.protect === 0 || record.protect === '0') ? '否' : '-')}}
                  </template>
                  <template #action={record}>
                    <a-button class="button_E" @click="handleRoll(record);" v-if="record.projectId == route.query.projectId">回滚</a-button>
                    <a-button class="button_D" @click="$handleDel([record.id],deleteImage,getSnapList)" v-if="record.protect != 1 && record.projectId == route.query.projectId">删除</a-button>
                  </template>
                </a-table>
              </div>
            </a-tab-pane>
            <a-tab-pane key="9" tab="基于响应内容的检查" v-if="openCheck && serverInfo.status == 'active'">
              <a-spin :spinning="checkloading">
                <div style="width:608.8px;padding:24px">
                  <CheckInfo ref="checkInfo" :info="checkinfo" />
                </div>
              </a-spin>
            </a-tab-pane>
        </a-tabs>
        <SyncModal ref="syncDialog" :info="syncinfo" @selectlist="selectUsbList" @setusbId="()=>{usbform.usbId = tempid;}" />
          <Umopinfo ref="umopDialog" :info="umopinfo" @unstatus="(status)=>{serverInfo.openUmop=status;}" />
            <CheckModal ref="checkDialog" :info="checkinfo" 
            @setCheck="(ischeck)=>{serverInfo.isInspection = ischeck;openCheck = ischeck;loadingCheck = false;checkinfo.isAdd = false}"
            @setCheckStatus="switchCheckStatus" />
    </div>
    
</template>
<script lang='ts' setup>
import CheckInfo from "./check/info.vue"
import Monitor from "./monitor.vue";
// import CronModal from "@/components/cron/cronmodal.vue";
import SyncModal from "./syncmodal.vue";
import Umopinfo from './umopinfo.vue';
import CheckModal from './check/modal.vue';
import JCronModal from '@/components/cron/JCronModal.vue'
import { createVNode, getCurrentInstance, nextTick, onBeforeUnmount, onMounted, onUnmounted, reactive, ref, toRefs } from 'vue';
import * as echarts from "echarts";
import {copy, setNotopt} from "@/utils/tool";
import {getConsoleoutput, getConsoleUrl,saveEnableConfig, scheduleSnapshot, selectServerList, updateScheduleSnapshot, selectUSBList, saveUSB, SyncDevice, updateServer, setMonitor, IsInspection, selectInspectionInfo, selectInspectionList, deleteInspection, saveServertag, deleteServertag, changeSecurityGroup, rollBackSnap, refreshDevice, deleteUSB, selectServerErrorInfo } from "@/api/backend/devops/server"
import {getNowResource } from "@/api/backend/devops/agent";
import { postdeletHost } from "@/api/backend/systems/auth";
import {saveRuleserve,updateRuleserve,getConfigInfo } from "@/api/backend/devops/config";

import { message, Modal } from 'ant-design-vue';
import "echarts-liquidfill";
import { useRoute } from "vue-router";
import router from "@/router";
import moment from "moment";
import { selectScheduleList } from "@/api/backend/systems/schedule";
import { queryHypervisor } from "@/api/backend/devops/index";
import { Options } from "@/common/chartoption";
import emiter from "@/utils/Bus";
import {widthListener} from "@/utils/widthlistener";
import { securitygroup_selectListByServerId, selectSecugroupList } from "@/api/backend/devops/security";
import { deleteImage, getImageList } from "@/api/backend/devops/image";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import USB from "@/assets/vm/usb.svg";
import { getVolumeAttachmentList } from "@/api/backend/storage";
const emit = defineEmits(['getlist'])
const { proxy } = getCurrentInstance();
const route = useRoute();
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
                isInfo:true
            }
        }
    }
})
const snapcolumns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id',align:'center'},
    {title: '名称', dataIndex: 'name', key: 'id',align:'left',ellipsis:true},
    {title: '可见性', dataIndex: 'visibility', key: 'id',align:'center'},
    {title: '受保护的', dataIndex: 'protect', slots: { customRender: 'protect' }, key: 'id',align:'center'},
    {title: '磁盘格式', dataIndex: 'diskFormat', key: 'id',align:'center'},
    {title: '大小(G)', dataIndex: 'imageSize', slots: { customRender: 'imageSize' },　key: 'id',align:'center'},
    {title: '状态', dataIndex: 'statusText', key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' } }
];
const column = ref(3);
const imageloading = ref(false);
const snaplist = ref([]);
const openCron = ref(false);
const activeKey = ref('2');
const checkInfo = ref()
const fault_msg = reactive({});
const keyObj = {"message":"消息","code":"编码","details":"详情","created":"创建时间"};
const ballTime = ref([])
const inputRef = ref();
const state = reactive({
  tags: [],
  inputVisible: false,
  inputValue: '',
});
const visibleSecurity = ref(false);
const securitygrouplist = ref([]);
const securityForm = ref();
const securityform = reactive({
  projectId: route.query.projectId,
  serverId:'',
  securityGroupIds:[]
})
const snapsearchform = reactive({
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    domainId:route.query.domainId,
    name:'',
    serverId:'',
    imageType:"snapshot",
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeSnapTable: any = (pagination: any, filters: any, sorter: any) => {
  snapsearchform.pageIndex = pagination.current;
  snapsearchform.pageSize = pagination.pageSize;
  getSnapList();
};
const getSnapList = async () => {
    // snapsearchform.cloudId = e;
    // snapsearchform.cloudId = localStorage.getItem('cloudId');
    snapsearchform.cloudId = route.query.cloudId;
    snapsearchform.projectId = route.query.projectId;
    snapsearchform.domainId = route.query.domainId;
    snaplist.value = await proxy.$getList(imageloading, getImageList, snapsearchform, pagination, getSnapList )
}
const handleRoll = (record) => {
  Modal.confirm({
    title: () => '确认回滚操作',
    icon: () => createVNode(ExclamationCircleOutlined),
    getContainer:proxy.modalBindNode,
    async onOk() {
      let rollform = {};
      rollform.serverId = serverInfo.id;
      rollform.snapshotId = record.id;
      let res = await rollBackSnap(rollform)
      if(res.code == 0 && res.data !== false){
          // console.log('snaop',res.data)
          message.success('回滚成功')
          rollform.serverId = "";
          rollform.snapshotId = undefined;
          getSnapList();
      }
    }
  });
  
}
const handleClose = (id) => {
  deleteServertag([id]).then((res)=>{
    if(res.code == 0 && res.data !== false){
      message.success('删除标签成功')
      const tags = state.tags.filter(tag => tag.id !== id);selectTagList(tags)
    }else{
      if(res.code == 0)
        message.error('删除标签失败')
      selectTagList(state.tags)
    }
  }).catch(()=>{
    selectTagList(state.tags)
  })
};

const showInput = () => {
  state.inputVisible = true;
  nextTick(() => {
    inputRef.value.focus();
  });
};
const selectTagList = (tags) => {
  Object.assign(state, {
    tags,
    inputVisible: false,
    inputValue: '',
  });
}
const handleInputConfirm = () => {
  if(!state.inputValue || state.inputValue == ' '){
    selectTagList(state.tags)
    return;
  }
  saveServertag({"serverId": serverInfo.id,"tagName": state.inputValue}).then((res)=>{
    if(res.code == 0 && res.data !== false){
      message.success('操作成功');
      let tags = state.tags;
      // if (inputValue && tags.indexOf(inputValue) === -1) {
        tags = [...tags, res.data];
      // }
      selectTagList(tags)
      console.log("res",res)
    }else if(res.code == 0){
      message.error('操作失败');
      selectTagList(state.tags)
    }
  }).catch(()=>{
    selectTagList(state.tags)
  })
  // const inputValue = state.inputValue;
  // let tags = state.tags;
  // if (inputValue && tags.indexOf(inputValue) === -1) {
  //   tags = [...tags, inputValue];
  // }
  // console.log(tags);
  // Object.assign(state, {
  //   tags,
  //   inputVisible: false,
  //   inputValue: '',
  // });
};
let {tags,inputVisible,inputValue} = toRefs(state)
const cronProps = reactive({
  isShow:false
})
const syncinfo = reactive({
  isSync:false,
  isInstall:true,
  isShow:false
});
const umopinfo=reactive({
    isAdd:true,
    isShow:false,
})
const checkinfo=reactive({
  isAdd:false,
  isShow:false
})
const checkloading = ref(false);
const checkIdList = ref([])
const openCheck = ref(false)
const openPrometheus = ref(0);
var timer2 = null;
const isEmpty = ref(false);
const value2 = ref(undefined);
const scheduleForm = ref();
const scheduleform = reactive({
  corn:'',
  serverId:''
});
const usbform = reactive({
  usbId:undefined,
  serverId:''
});
const moniRef = ref()
const umopDialog=ref()
const checkDialog=ref()
const cronDialog = ref()
const openModal = () => {
    nextTick(()=>{
        cronDialog.value.show()
    })
}
const handleOK = (val) => {
    if(typeof val == 'string'){
        scheduleform.corn = val;
    }
}
const syncDialog = ref()
const searchform = reactive({serverId:'',lines:50})
const serverInfo = reactive({serverName:''})
const networklist = ref([])
const usblist = ref([])
const installedusblist = ref([])
const tempid = ref('')
const logText = ref('')
const termloading = ref(false);
const logloading = ref(false);
const usbloading = ref(false);
const Url = ref('')
const currMonitor = ref(false)
const loadingMonitor = ref(false)
const loadingCheck = ref(false)
const tempitem = reactive({})
// const count = ref(0)
const imageInfo = reactive({})
const volumeAttachmentList = ref([]);
const setInfo = (record) => {
  console.log("record",record)
  if(record){
    securityform.serverId = record.id;
      selectTagList(record.tagEntityList)
        findHostIp()
      // getConsoleOutput(record.id)
        searchform.serverId = record.id;
        snapsearchform.serverId = record.id;
        getLiquidData() //获取概览中水球当前数据
        begin1() //定时更新概览中水球
        if(activeKey.value == '7')
          getConfiginfo(record.id); //获取伸缩规则信息
        let record1 = {...record};
        if(record.enableConfig==1){
          record1.enableConfig=true
        }else{
          record1.enableConfig=false
        }
        if(record.scheduleSnapshot==1){
          record1.scheduleSnapshot=true
        }else{
          record1.scheduleSnapshot=false
        }
        if(record.openUmop==1){
          record1.openUmop=true
        }else{
          record1.openUmop=false
        }
        if(record.isMonitor==1){
          record1.isMonitor=true
        }else{
          record1.isMonitor=false
        }
        record1.isInspection = Boolean(record1.isInspection)
        openCheck.value = record1.isInspection;
        currMonitor.value = record1.isMonitor ? true : false;
        Object.assign(serverInfo,record1)
        serverInfo.status = serverInfo.status.toLowerCase();
        networklist.value = record.serverAddressEntityList;
        // Url.value = url;
        Object.assign(imageInfo,record.imageEntity)
        // 进入详情页点击关闭检查时需要列表以作删除
        if(record1.isInspection)
          getCheckInfo()
        if(record1.status.toLowerCase() == 'error')
          selectServerErrorInfoAPI(record.id)
      setVolumeAttachmentList(record.id)
    }else if(route.query?.thirdServerId){
      securityform.serverId = route.query?.thirdServerId;
      selectCurrentServerlist(route.query?.thirdServerId)
    }
    // console.log('re',record)
}
const selectServerErrorInfoAPI = (serverId) => {
  selectServerErrorInfo({serverId}).then((res)=>{
    if(res.data){
      Object.assign(fault_msg,{...res.data});
    }
  })
}
const setVolumeAttachmentList = (serverId) => {
  getVolumeAttachmentList({serverId}).then((res)=>{
    console.log("res",res)
    volumeAttachmentList.value = res.data;
  })
}
const selectedList = () => {
  securitygroup_selectListByServerId({serverId:searchform.serverId}).then((res)=>{
    if(res.code == 0 && res.data && res.data.length > 0){
      let securityGroupIds = [];
      res.data.forEach((item,index)=>{
        securityGroupIds.push(item.id);
      })
      securityform.securityGroupIds = securityGroupIds;
    }
  })
}
const handleSecurityRule = () => {
  selectSecugroupList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId})
  .then((res)=>{
    if(res.code == 0 && res.data && res.data.length > 0){
      securitygrouplist.value = res.data;
      selectedList();
      visibleSecurity.value = true;
    }else if(res.code == 0){
      message.error("无可选安全组")
    }
  })
}
const handleSecurityCancel = () => {
  visibleSecurity.value = false;
  securityform.securityGroupIds = [];
}
const handleSecurityOk = () => {
  let securityform1 = {...securityform};
  securityform1.securityGroupIds = securityform.securityGroupIds.toString();
  console.log("securityform1",securityform1)
  proxy.$handleSave(securityForm.value, changeSecurityGroup, changeSecurityGroup, true, securityform1, ()=>{handleSecurityCancel()},null)
}
const changeMonitor = async (isMonitor) => {
  try{
    loadingMonitor.value = true;
    let res = await setMonitor({serverId:serverInfo.id,isMonitor})
    if(res.code == 0 && res.data !== false){
      serverInfo.isMonitor = isMonitor;
      loadingMonitor.value = false;
    }else{
      if(res.code == 0)
        message.error((!res.msg || res.msg == 'success') ? '开启失败' : res.msg);
      serverInfo.isMonitor = currMonitor.value;
      loadingMonitor.value = false;
    }
  }catch{
    serverInfo.isMonitor = currMonitor.value;
    loadingMonitor.value = false;
  }
}
const selectCurrentServerlist = async (thirdServerId) => {
    let res = await selectServerList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId,thirdServerId})
    if(res.code == 0){
        if(res.data && res.data.length > 0){
            isEmpty.value = false;
            proxy.$nextTick(()=>{
            searchform.serverId = res.data[0].id;
            getLiquidData() //获取概览中水球当前数据
            begin1() //定时更新概览中水球
            if(activeKey.value == '7')
                getConfiginfo(res.data[0].id); //获取伸缩规则信息
            if(res.data[0].enableConfig==1){
            res.data[0].enableConfig=true
            }else{
            res.data[0].enableConfig=false
            }
            Object.assign(serverInfo,res.data[0]);
            
            networklist.value = res.data[0].serverAddressEntityList;
            // Url.value = url;
            Object.assign(imageInfo,res.data[0].imageEntity)
            })
        }else{
            isEmpty.value = true;
        }
    }
}
const ruleInfo = reactive({serverId:'',enableConfig:''})
const rulechange=async()=>{
 
  if(serverInfo.enableConfig==true){
    serverInfo.enableConfig=1
  }else if(serverInfo.enableConfig==false){
       serverInfo.enableConfig=0
    }
    // ruleInfo.serverId=searchform.serverId
    // ruleInfo.enableConfig=serverInfo.enableConfig
    // let ruleInfo1 = {...ruleInfo}
    //  let res = await saveEnableConfig(ruleInfo1)
    let res=await saveEnableConfig({
                serverId:searchform.serverId,
                enableConfig:serverInfo.enableConfig
        })
        if(res.data == true){
            if(serverInfo.enableConfig==1){
                serverInfo.enableConfig=true
                  message.success((!res.msg || res.msg == 'success') ? '开启成功' : res.msg);
            }else{
                serverInfo.enableConfig=false
                 message.success((!res.msg || res.msg == 'success') ? '关闭成功' : res.msg);
            }
            //    emit('getlist')
       }else{
        if(res.code == 0){
          if(serverInfo.enableConfig==1){
            message.error((!res.msg || res.msg == 'success') ? '开启失败' : res.msg);
          }else{
            message.error((!res.msg || res.msg == 'success') ? '关闭失败' : res.msg);
          }
        }
       }
}
const schedulechange = async (checked,isSwitch) => {
  let switchText = checked ? '开启' : '关闭';
  if(!checked || !isSwitch){
    let res=await updateScheduleSnapshot({
    serverId:searchform.serverId,
    scheduleSnapshot:Number(checked)
    })
    if(res.data == true){
      serverInfo.scheduleSnapshot=checked;
      message.success(switchText+"成功");
      emit('getlist');
    }else{
      message.error((!res.msg || res.msg == 'success') ? switchText+'失败' : res.msg);
    }
  }
  // ruleInfo.serverId=searchform.serverId
  // ruleInfo.enableConfig=serverInfo.enableConfig
  // let ruleInfo1 = {...ruleInfo}
  //  let res = await saveEnableConfig(ruleInfo1)
  
}
const handleCron = () => {
  cronProps.isShow = true;
  proxy.$nextTick(()=>{
    console.log("scheduleform.corn",scheduleform.corn)
    cronDialog.value.setExpression(scheduleform.corn);
  })
}

const umopchange=async()=>{
  if(serverInfo.openUmop==false){
    //  message.success("关闭成功");
    let res=await postdeletHost({
      cloudId:localStorage.getItem('cloudId'),
      serverId:serverInfo.id,
      umopHostId:serverInfo.umopHostId
    })
    
    if(res.code==0 & res.data !== false){
        message.success("关闭成功");
        serverInfo.openUmop=false
      
    }else{
      if(res.code==0)
        message.error((!res.msg || res.msg == 'success') ? '关闭失败' : res.msg);
      serverInfo.openUmop=true
    }

  }else if(serverInfo.openUmop==true){

      umopinfo.isShow=true
      umopDialog.value.setInfo(serverInfo)
        umopDialog.value.ipNumber(networklist.value)
  }
}
const changeCheck = (checked) => {
  loadingCheck.value = true;
  // serverInfo.openCheck
  if(checked){
    checkinfo.isShow = checkinfo.isAdd = true;
    proxy.$nextTick(()=>{
      checkDialog.value.setInfo(serverInfo);
    })
  }else{
    // loadingCheck.value = false;
    // openCheck.value = checked;
    deleteCheckList();
  }
}

const switchCheckStatus = (isInspection) => {
  let text = isInspection ? '开启' : '关闭';
  loadingCheck.value = false;
  openCheck.value = isInspection;
  checkinfo.isAdd = false
  message.success(text+'成功')
  if(isInspection){
    // 这里立马调列表是为了开启后不切换页签立刻关闭的情况以作删除
    getCheckInfo()
  }
}
const deleteCheckList = () => {
  proxy.$handleDel(
    checkIdList.value,
    deleteInspection,
    ()=>{
      switchCheckStatus(false)
    },
    null,
    ()=>{
      loadingCheck.value = false;
      serverInfo.isInspection = true;
      // message.error('关闭失败')
    },
    true
  )
}
const getCheckInfo = () => {
  checkloading.value = true;
  selectInspectionList({serverId:serverInfo.id}).then((res)=>{
    checkloading.value = false;
    if(res.code == 0){
      console.log("info",res)
      let idarr = []
      res.data.forEach((item,index)=>{
        idarr.push(item.id);
      })
      checkIdList.value = idarr;
      proxy.$nextTick(()=>{

        checkInfo.value.setInfo(serverInfo,true,res.data[res.data.length - 1])
      })
    }
  })
}

const projectForm = ref();
const ruleloading = ref(false);
const projectform = reactive({
    source:1,
    serverIdList:undefined,
    cloudId:localStorage.getItem('cloudId'),
    projectId:route.query.projectId,
    maxCpu:'',
    maxMemory:'',
    minCpu:-1,
    minMemory:-1,
   
})
const ruleButton=()=>{
  projectform.serverIdList =  [searchform.serverId]
   let projectform1 = {...projectform}
     
    proxy.$handleSave(projectForm.value, saveRuleserve, updateRuleserve, false,projectform1)
}
const save = (hostform,apiName) => {
  // let apiName = syncinfo.isInstall ? saveUSB : deleteUSB;
  return apiName(hostform).then((res)=>{
    if(res.code == 0 && res.data !== false){
        message.success(syncinfo.isInstall ? '挂载成功':'卸载成功');
        refresh();
    }else{
        // refresh();
        // usbform.usbId = tempid.value;
    }
  })
}
const handleSave = (state,otherParams) => {
  if(state == 'install'){
    if(!usbform.usbId){
      message.error('请选择设备')
      return;
    }
    Modal.confirm({
      title: () => '确认挂载?',
      icon: () => createVNode(ExclamationCircleOutlined),
      onOk() {
        syncinfo.isInstall = true;
        syncinfo.isSync = false;
        let usbform1 = {...usbform}
        if(!usbform.serverId)
        usbform1.serverId = serverInfo.id;
        usbform1.usbId = (usbform.usbId == '' ? undefined : usbform.usbId);
        return save(usbform1,saveUSB);
      },
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      onCancel() {},
    });
  }else{
    // if(!usbform.usbId){
    //   message.error('还未挂载设备')
    //   return;
    // }
    let {id,serverId} = otherParams;
    Modal.confirm({
      title: () => '确认卸载?',
      icon: () => createVNode(ExclamationCircleOutlined),
      okType: 'danger',
      onOk() {
        syncinfo.isInstall = false;
        syncinfo.isSync = false;
        let usbform1 = {}
        usbform1.serverId = serverId ? serverId : serverInfo.id;
        usbform1.usbId = id;
        return save(usbform1,deleteUSB);
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }
}
// 为设备查询提供当前host
const findHostIp = async () => {
  let res = await queryHypervisor({cloudId:localStorage.getItem('cloudId'),projectId:route.query.projectId})
  if(res.code == 0){
    Object.assign(tempitem,res.data.find((item,index)=>item.hypervisor_hostname == serverInfo.host))
    // tempitem = res.data.find((item,index)=>item.hypervisor_hostname == serverInfo.host)
  }
}
const syncDevice = () => {
  syncinfo.isShow = true;
  syncinfo.isSync = true;
  proxy.$nextTick(()=>{
    Object.assign(syncDialog.value.hostform,{hostIp:tempitem.host_ip,cloudId:serverInfo.cloudId})
  })
}
const saveSchedule = () => {
  let corn1 = scheduleform.corn;
  proxy.$handleSave(scheduleForm.value, scheduleSnapshot, scheduleSnapshot, false,scheduleform, ()=>{schedulechange(true);serverInfo.scheduleSnapshot = true;openCron.value = false;}, ()=>{scheduleform.corn = corn1;})
}
const rules = {
    maxCpu:[{required:true, type:'number', message:'仅支持输入1-100之间的整数',trigger:'change',transform(value){ 
                        if(value){
                            // 将输入的值转为数字
                            var val = Number(value)
                            // 正则表达式校验输入的数字是否在0-100之内并且属于整数
                            if(/^(?:[1-9]?\d|100)$/.test(val)) return val
                            // 返回false即为校验失败
                            return false
                        }
                    }}],
    maxMemory:[{required:true, type:'number', message:'仅支持输入1-100之间的整数',trigger:'change',transform(value){ 
                        if(value){
                            // 将输入的值转为数字
                            var val = Number(value)
                            // 正则表达式校验输入的数字是否在0-100之内并且属于整数
                            if(/^(?:[1-9]?\d|100)$/.test(val)) return val
                            // 返回false即为校验失败
                            return false
                        }
                    }}],
    minCpu:[{required:true, type:'number', message:'仅支持输入-1~100之间的整数',trigger:'change',transform(value){ 
                        if(value){
                            // 将输入的值转为数字
                            var val = Number(value)
                            // 正则表达式校验输入的数字是否在0-100之内并且属于整数
                            if(/^(?:[-1-9]?\d|100)$/.test(val)) return val
                            // 返回false即为校验失败
                            return false
                        }
                    }}],
    minMemory:[{required:true, type:'number', message:'仅支持输入-1~100之间的整数',trigger:'change',transform(value){ 
                        if(value){
                            // 将输入的值转为数字
                            var val = Number(value)
                            // 正则表达式校验输入的数字是否在0-100之内并且属于整数
                            if(/^(?:[-1-9]?\d|100)$/.test(val)) return val
                            // 返回false即为校验失败
                            return false
                        }
                    }}],
}
const getConsoleOutput = async () => {
  logloading.value = true;
  let res = await getConsoleoutput(searchform)
  logloading.value = false;
  if(res.code == 0 && res.data !== false){
    logText.value = res.data;
  }else{
    logText.value = ""
    message.error((!res.msg || res.msg == 'success') ? '查询失败' : res.msg);
  }
};
const refresh = () => {
  refreshDevice({cloudId:route.query.cloudId}).then((res)=>{
    console.log("res",res)
    if(res.code == 0){
      selectUsbList()
    }
  })
}
// 查询设备列表
const selectUsbList = (sync) => {
  usbloading.value = true;
  selectUSBList({cloudId:route.query.cloudId,projectId:route.query.projectId,comouteNodeId:tempitem.id,status:0}).then((res)=>{
    usbloading.value = false;
    if(res.code == 0){
      if(sync){
        message.success('同步成功');
      }
      usblist.value = res.data;
    }
  })
  selectUSBList({cloudId:route.query.cloudId,serverThirdId:serverInfo.thirdServerId,status:1}).then((res)=>{
    usbloading.value = false;
    if(res.code == 0){
      if(sync){
        message.success('同步成功');
      }
      installedusblist.value = res.data;
    }
  })
}
const handleSnap = (checked) =>{
  serverInfo.scheduleSnapshot = !checked;
  openCron.value = checked;
  scheduleform.serverId = serverInfo.id;
  selectScheduleList({beanName:'scheduleSnapshot',param:serverInfo.id}).then((res)=>{
    // console.log('res',res)
    if(res.page && res.page.length > 0){
      scheduleform.corn = res.page[0].cronExpression;
    }
  })
}
const changeTab = async (key) => {
  if(key == '3'){
    termloading.value = true;
    let res = await getConsoleUrl(serverInfo.id);
    termloading.value = false;
    if(res.code == 0 && res.data !== false){
      Url.value = res.data;
    }else
    message.error((!res.msg || res.msg == 'success') ? '虚机控制台异常' : res.msg);
  }
  if(key == '4'){
    getConsoleOutput()
  }
  if(key == '6'){
    usbform.usbId = undefined;
    usbform.serverId = serverInfo.id;
    tempid.value = usbform.usbId;
    // selectUsbList()
    refresh()
  }
  if(key == '7'){
      getConfiginfo(serverInfo.id)
  }
  if(key == '8'){
    getSnapList();
  }
  if(key == '9'){
    getCheckInfo()
  }
  if(serverInfo.status?.toLowerCase() == 'active'){
    if(key == '2'){
      begin1()
    }else{
      clearInterval(timer2)
    }
    if(key == '5'){
      
      // moniRef.value.begin()
      proxy.$nextTick(()=>{
        moniRef.value.getMonitorData(moment((new Date()).getTime()-moniRef.value.value1).utcOffset("+08:00").format('YYYY-MM-DD HH:mm:ss'),moment((new Date()).getTime()).utcOffset("+08:00").format('YYYY-MM-DD HH:mm:ss'))

      })
    }
    else{
      // moniRef.value.clear()
    }
  }
  
}

const handleSearch = () => {
  getConsoleOutput()
}

const getConfiginfo = async (id) => {
  ruleloading.value = true;
  if(id){
    let res = await getConfigInfo({id})
    ruleloading.value = false;
    if(res.code == 0){
      Object.assign(projectform,res.data);
    }
  }
  
}

var myChart5;
var myChart7;
const chart5 = ref()
const chart7 = ref()
// const liquidColor=ref('#91CC75')
let option5 = reactive(Options.server_5);
let option7 = Options.server_7;
const getLiquidData = () => {
  getNowresource('CPU',option5,myChart5,0)
  getNowresource('MEMORY',option7,myChart7,1)
}
const getNowresource = async (type,OPTION,CHART,INDEX) => {
  let res = await getNowResource({serverId:searchform.serverId,type})
  if(res.code == 0){
    OPTION.series[0].data[0] = res.data[type.toLowerCase()] ? (res.data[type.toLowerCase()][type.toLowerCase()+'Percent']/100).toFixed(4) : null;
    ballTime.value[INDEX] = res.data[type.toLowerCase()] ? res.data[type.toLowerCase()].createTime : null;
      setBallOption(OPTION,CHART)
  }else{
    OPTION.series[0].data[0] = null;
    ballTime.value = [];
    setBallOption(OPTION,CHART)
  }
}
const setBallOption = (OPTION,CHART) => {
  if(OPTION.series[0].data[0]<=0.3){
        OPTION.series[0].color[0] = '#91CC75'
        OPTION.series[0].label.color = '#91CC75'
      }else if(OPTION.series[0].data[0] > 0.3 && OPTION.series[0].data[0] <= 0.8){
        OPTION.series[0].color[0] = '#FAC858'
        OPTION.series[0].label.color = '#FAC858'
      }else{
        OPTION.series[0].color[0] = '#EE6666'
        OPTION.series[0].label.color = '#EE6666'
      }
      CHART.hideLoading();
      CHART.setOption(OPTION);
      myChart5.resize()
      myChart7.resize()
} 
const begin1 = () => {
  clearInterval(timer2)
  timer2 = setInterval(()=>{
      getLiquidData()
    },180000)
}
onBeforeUnmount(()=>{
  clearInterval(timer2)
  // moniRef.value.clear()
})
const widthlistener = (className) => {
  const element = document.getElementsByClassName(className)[0];
  widthListener(element,(width,height)=>{
    if(className == 'back-page'){
      console.log("width",width)
      if((width <= 1367 && width > 1123) ||width <= 807){
        column.value = 2;
      }else{
        column.value = 3;
      }
    }
  },()=>{
    myChart5.resize()
    myChart7.resize()
  })
}
function listenerFUNC(){
  widthlistener('back-page')
}
onMounted(() => {
  if(localStorage.getItem('openPrometheus'))
  openPrometheus.value = localStorage.getItem('openPrometheus');
    myChart5 = echarts.init(chart5.value);
    // myChart6 = echarts.init(chart6.value);
    myChart7 = echarts.init(chart7.value);
    myChart5.showLoading();
    myChart7.showLoading();
    widthlistener('back-page')
    window.addEventListener('resize', listenerFUNC)
  // setTimeout(()=>{
  //   setInfo()
  // })
})
onUnmounted(()=>{
  window.removeEventListener("resize", listenerFUNC);
})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
// .back-header{border-bottom: 4px solid #f0f2f5;}
.back-content{padding: 0;height: 100%;overflow-y: initial;}
.outline{ display: flex;flex-flow: wrap;padding-top: 16px;overflow-x: auto;}
.ant-descriptions{flex: 333px;padding: 20px;margin: 0 16px 16px;border: 1px solid #f0f2f5;transition:all .3s;}
.server-info{flex: 526px;}
.outline-right{flex: 200px;min-width: 526px;margin: 0 16px 16px 16px;border: 1px solid #f0f2f5;padding: 8px;
&:hover {
    border-color: #00000017;
    box-shadow: 0 2px 8px #00000017;
}
transition:all .3s;}
.charts{width:100%;height:213px;
padding: 8px;
// &:hover {
//     border-color: #00000017;
//     box-shadow: 0 2px 8px #00000017;
// }
// transition:all .3s;
}
.ant-card{margin: 16px;}
:deep(.ant-card-head){border-bottom: none;.ant-card-head-title{font-size: 16px;font-weight: bolder;}}
.ant-descriptions:hover {
    border-color: #00000017;
    box-shadow: 0 2px 8px #00000017;
}
:deep(.ant-descriptions-row > td){width: 242px;}
.terminal.ant-tabs-tabpane-active{height:calc(100vh - 240px);padding:0 16px 0 16px}
.ant-typography pre{padding: 1em;margin-top: 0;}
:deep(.ant-tabs .ant-tabs-top-content){
      height: calc(100vh - 230px);
    // overflow-y: scroll;
}
.rulefromfont{
  width: 30%;
  margin-left:20px ;
  margin-top:30px ;
}
:deep(.ant-table th){ white-space: nowrap; }
:deep(.ant-table td){ white-space: nowrap; }
:deep(.ant-spin-nested-loading),:deep(.ant-spin-container){height: 100%;}
@media (max-width: 861px){
  .ant-descriptions{min-width: 526px;}
}
@media (max-width: 1220px){
  .outline-right{width: 100%;}
}
</style>