<template>
    <div class=''>
        <a-modal :title="info.isAdd ? '新增' : '修改'" v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form ref="volumeForm" :model="volumeform" :labelCol="{span:4}" :rules="rules">
                <a-form-item label="虚机名称" name="hostName">
                    <a-input v-model:value="volumeform.hostName" placeholder="请输入虚机名称"></a-input>
                </a-form-item>
                <a-form-item label="用户名称" name="userName">
                    <a-input v-model:value="volumeform.userName" placeholder="请输入用户名称"></a-input>
                </a-form-item>
                 <a-form-item label="用户密码" name="hostPassword">
                    <a-input v-model:value="volumeform.hostPassword" placeholder="请输入用户密码"></a-input>
                </a-form-item>
                  <a-form-item label="IP地址" name="ip">
                    <a-select v-model:value="volumeform.ip" placeholder="请选择IP地址" :getPopupContainer="triggerNode => triggerNode.parentNode">
                        <a-select-option v-for="(item,index) in iplist" :key="index" :value="item.addr">{{item.addr}}</a-select-option>
                    </a-select>
                    <!-- <a-input v-model:value="volumeform.ip" placeholder="请输入IP地址" disabled></a-input> -->
                </a-form-item>
                  <a-form-item label="端口" name="port">
                    <a-input v-model:value="volumeform.port" placeholder="请输入端口"></a-input>
                </a-form-item>
               
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { postsaveHost } from "@/api/backend/systems/auth";
import { selectImageList } from '@/api/backend/devops/image';
import { selectZoneList } from '@/api/backend/devops.ts';
import { message } from 'ant-design-vue';
import emiter from '@/utils/Bus';
import router from '@/router';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['unstatus']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    serverId:'',
    hostName:'',
    userName:'',
    hostPassword:'',
    ip:undefined,
    port:'22'
}
const volumeForm = ref()
const volumeform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    serverId:'',
    hostName:'',
    userName:'',
    hostPassword:'',
    ip:undefined,
    port:'22'
})
const iplist = ref([])
const Opumop=reactive({
    openUmop:false
})
const rules = {
    hostName:[{required:true,message:'请输入虚机名称',trigger:'change'}],
    userName:[{required:true,message:'请输入用户名称',trigger:'change'}],
    hostPassword:[{required:true,message:'请输入用户密码',trigger:'blur'}],
    ip:[{required:true,message:'请输入ip地址',trigger:'change'}],
    port:[{required:true,message:'请输入端口',trigger:'change'}],
    
}
const typelist = ref([])
const zonelist = ref([])
const snapshotlist = ref([])
const imagelist = ref([])
const volumelist = ref([])
const setInfo = (data) => {
  
    volumeform.serverId=data.id
    volumeform.hostName=data.serverName;
    Opumop.openUmop=data.openUmop.value
    iplist.value = data.serverAddressEntityList
    // Object.assign(volumeform,data);
}
const ipNumber=(data)=>{
     volumeform.ip=data[0].addr
   
}
const save=async()=>{
    let res=await postsaveHost(volumeform)
    if(res){
        if(res.code==0 && res.data !== false){
            message.success("开启成功");
            close()
            emit('unstatus',true)
        }else{
            if(res.code == 0)
                message.error((!res.msg || res.msg == 'success') ? '开启失败' : res.msg);
            close()
            emit('unstatus',false)
          }
        
    }else{
         emit('unstatus',false)
         message.error("开启失败");
    }
    
 
}
const close = () => {
    props.info.isShow = false;
    volumeForm.value.resetFields();
    Object.assign(volumeform,defaultform)
    //  emit('unstatus',false)
}
const cancel = () => {
    // props.info.isShow = false;
    // volumeForm.value.resetFields();
    // Object.assign(volumeform,defaultform)
    close()
    emit('unstatus',false)
    //  emit('unstatus',false)
}
onMounted(()=>{})
defineExpose({setInfo,ipNumber})
</script>
<style lang='scss' scoped>
</style>