<template>
    <div class='penetrate'>
        <a-modal v-model:visible="info.isShow" @cancel="cancel" width="100% !important"
        :bodyStyle="{height:'calc(100vh - 108px)',overflow:'auto',padding:0}" :maskClosable="false" :closable="!loading" wrapClassName="full-modal" centered :getContainer="modalBindNode">
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>新增</span>
                    <a-popover trigger="click" placement="leftTop">
                        <template #content>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="current == 0"><span>{{content.server_base}}</span></pre>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="current == 1"><span>{{content.server_network}}</span></pre>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="current == 2"><span>{{content.server_system}}</span></pre>
                            <!-- <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="current == 3"><span>{{content.server_network}}</span></pre>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="current == 4"><span>{{content.server_network}}</span></pre>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="current == 5"><span>{{content.server_securitygroup}}</span></pre>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="current == 6"><span>{{content.server_keypair}}</span></pre>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="current == 7"><span>{{content.server_allocation}}</span></pre> -->
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <template #footer>
                <div class="footer-custom">
                    <div>
                        <!-- <span style="margin-right:16px">数量</span>
                        <a-input-number></a-input-number> -->
                    </div>
                    <div class="steps-action">
                        <a-button style="margin-left: 8px" @click="cancel" :disabled="loading">取消</a-button>
                        <a-button v-if="current > 0" style="margin-left: 8px" @click="prev" :disabled="loading">上一步:{{steps[current - 1].title}}</a-button>
                        <a-button v-if="current < steps.length - 1" type="primary" @click="next" :disabled="loading">下一步:{{steps[current + 1].title}}</a-button>
                        <a-button
                            v-if="current == steps.length - 1"
                            type="primary"
                            @click="handleSave"
                            :disabled="loading"
                        >
                            提交
                        </a-button>
                    </div>
                </div>
            </template>
                <div class="steps-indicator">
                    <a-steps :current="current">
                        <a-step v-for="item in steps" :key="item.title" :title="item.title" />
                    </a-steps>
                </div>
                <div class="steps-content">
            <a-spin :spinning="loading" size="large">
                    <div :class="(isFixed && isInCurrent) ? 'center-content sapce-between' : 'center-content center'" ref="containerRef">
                        <div class="fixed-content">
                            <StepContent ref="stepcontent" :info="{current:current,total:total}" @set_current="(e)=>{current = e;}" />
                        </div>
                        
                        <div class="float-content" v-show="isFixed && isInCurrent">
                            <confirm />
                        </div>
                        <a-drawer
                        v-show="!isFixed"
                        placement="right"
                        :closable="false"
                        v-model:visible="visible"
                        :width="350"
                        >
                        <template #handle>
                            <!-- <div class="drawer-wrapper"> -->
                                <div class="drawer-handle" @click="showDrawer(visible)" v-show="!isFixed">
                                    <a-tooltip title="收起" v-if="visible">
                                        <CloseOutlined />
                                    </a-tooltip>
                                    <a-tooltip title="配置概览" v-else>
                                        <MenuOutlined  />
                                    </a-tooltip>
                                </div>
                            <!-- </div> -->
                            <!-- <div> -->
                                
                            <!-- </div> -->
                        </template>
                        <confirm />
                        </a-drawer>
                    </div>
                    
            </a-spin>
                </div>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import StepContent from "@/components/stepcontent/StepContent.vue";
import confirm from "@/components/stepcontent/confirm.vue";
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import {content} from "@/common/explain/modal";
import router from '@/router';
import { menuStore } from '@/store/menu';
import $ from "jquery";
import { saveServer, updateServer } from "@/api/backend/devops/server";
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:true,
                isInfo:false
            }
        }
    }
});
const containerRef = ref();
const isFixed = ref(true);
const visible = ref(false);
const stepcontent = ref();
const loading = ref(false);
const count = ref(0);
const current = ref(0);
const total = ref(4);
const isInCurrent = computed(()=>((total.value - current.value) == 2 || (total.value - current.value) == 3 || (total.value - current.value) == 4));
const steps = [
    {
        title: '实例规格',
        label: 'base',
    },
    {
        title: '网络和安全组',
        label: 'network',
    },
    {
        title: '管理设置',
        label: 'system',
    }
];
const validatorDataDisk = (rule,value) => {
    if(!value){
        return Promise.reject("请选择数据盘类型");
    }
    return Promise.resolve();
}
const datadisk = ref([]);
const showDrawer = (params) => {
    visible.value = !params;
    if(visible.value){
        nextTick(()=>{
            stepcontent.value.setConfirm();
        })
    }
};
const next = () => {
    nextTick(()=>{
        stepcontent.value.next();
    })
};
const prev = () => {
    current.value--;
};
const cancel = () => {
    props.info.isShow = false;
    nextTick(()=>{
        stepcontent.value.cancel();
        loading.value = false;
    })
}
const handleSave = () => {
    nextTick(()=>{
        stepcontent.value.handleSave(
            (serverform,serverForm)=>{
                let serverform1 = {...serverform.ticketOpenstackServerInfoEntity};
                if(!serverform1.imageOS)
                serverform1.imageOS = 0;
                serverform1.flavorId = serverform1.flavorId.length > 0 ? serverform1.flavorId[0] : null;
                if(serverform1.imageId && serverform1.imageId.length > 0){
                    serverform1.imageId = serverform1.imageId[0];
                    serverform1.sourceId = serverform1.imageId;
                }
                serverform1.imageId = null;
                if(serverform1.volumeId && serverform1.volumeId.length > 0){
                    serverform1.volumeId = serverform1.volumeId[0];
                    serverform1.sourceId = serverform1.volumeId;
                }
                serverform1.volumeId = null;
                if(serverform1.volumeSnapShotId && serverform1.volumeSnapShotId.length > 0){
                    serverform1.volumeSnapShotId = serverform1.volumeSnapShotId[0];
                    serverform1.sourceId = serverform1.volumeSnapShotId;
                }
                serverform1.volumeSnapShotId = null;
                // serverform1.imageId = serverform1.imageId.length > 0 ? serverform1.imageId[0] : null;
                // serverform1.volumeId = serverform1.volumeId.length > 0 ? serverform1.volumeId[0] : null;
                serverform1.source = Number(serverform1.source);
                serverform1.volList.forEach((item,index)=>{
                    item.deleteOnTermination = Number(item.deleteOnTermination)
                })
                if(serverform1.networkIds.length > 0){
                    serverform1.networkIds = serverform1.networkIds.join(',');
                    serverform1.groupIds = serverform1.groupIds.length > 0 ? serverform1.groupIds[0] : null;
                }else{
                    serverform1.networkIds = null;
                    serverform1.groupIds = null;
                }
                serverform1.portIds = serverform1.portIds.length > 0 ? serverform1.portIds.join(',') : null;
                serverform1.keyPairId = serverform1.keyPairId.length > 0 ? serverform1.keyPairId[0] : null;

                serverform1.physicalNode = null;
                serverform1.vnics = null;
                serverform1.userData = null;
                // console.log("serverform1",serverform1)
                proxy.$handleSave(serverForm.value, saveServer, updateServer, props, serverform1, ()=>{emit('getlist');cancel();},()=>{loading.value = false;},()=>{loading.value = true;})
            });
    })
}
const init = () => {
    stepcontent.value.init();
}
onMounted(()=>{
    isFixed.value = (window.innerWidth > 1310);
    if(window.innerWidth > 1310){
        visible.value = false;
    }
    window.addEventListener("resize",(e)=>{
        if(e.target.innerWidth <= 1310){
            isFixed.value = false;
        }else{
            isFixed.value = true;
            visible.value = false;
        }
    })
})
defineExpose({init})
</script>
<style lang="scss" scoped>
:deep(.ant-modal-body){position: relative;}
:deep(.ant-modal-footer){box-shadow: 0 2px 30px 0 rgb(0 0 0 / 9%);}
.steps-indicator{padding: 20px 300px;border-bottom: 1px solid #f0f0f0;background-color: #fff;}
.steps-content{height: calc(100% - 73px);overflow: auto;background-color: #f0f2f5;}
.center-content{min-height: calc(100% - 20px);display: flex;flex-flow: wrap;position: relative;max-width: 1320px;margin: 10px auto 10px;}
.space-between{justify-content: space-between;}
.center{justify-content: center;}
.fixed-content{width: 1000px;padding-top: 20px;background-color: #fff;}
.float-content{position: fixed;top: 138px;right:calc((100% - 1320px) / 2);width: 310px;height: calc(100% - 200px);;overflow-y: auto;padding: 10px;background-color: #fff;}
.drawer-handle {
  position: absolute;
    top: 138px;
    width: 41px;
    height: 40px;
    cursor: pointer;
    z-index: 0;
    text-align: center;
    line-height: 40px;
    font-size: 16px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background: #fff;
  box-shadow: 2px 0 8px #00000026;
    left: -40px;
    border-radius: 4px 0 0 4px;
}
</style>