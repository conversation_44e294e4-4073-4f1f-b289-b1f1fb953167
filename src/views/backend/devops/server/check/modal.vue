<template>
    <div class=''>
        <a-modal :title="info.isAdd ? '新增' : '修改'" v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <Info ref="checkInfo" :info="checkinfo" />
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import Info from "./info.vue"
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['setCheck','setCheckStatus']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow:false
            }
        }
    }
})
const checkinfo=reactive({
  isAdd:true,
  isShow:false
})
const checkInfo = ref()

const save=async()=>{
    // close()
    // emit('setCheck',true)
    proxy.$nextTick(()=>{
        checkInfo.value.save(okclose)
    })
}
const okclose = () => {
    props.info.isShow = false;
    // emit('setCheck',true)
    emit('setCheckStatus',true)

}
const close = () => {
    props.info.isShow = false;
    checkInfo.value.close()
}
const cancel = () => {
    close()
    emit('setCheck',false)
}
const setInfo = (serverInfo) => {
    proxy.$nextTick(()=>{
        checkInfo.value.setInfo(serverInfo)
    })
    // selectInspectionInfo({id}).then((res)=>{
    //     if(res.code == 0){

    //     }
    // })
}
onMounted(()=>{})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>