<template>
    <a-form ref="checkForm" :model="checkform" :labelCol="{span:4}" :rules="rules">
        <a-form-item label="虚机名称" name="serverName">
            {{checkform.serverName}}
        </a-form-item>
        <a-form-item label="请求地址" name="url">
            <a-input v-model:value="checkform.url" placeholder="请输入请求地址"></a-input>
        </a-form-item>
            <a-form-item label="请求方式" name="urlMethod">
            <a-radio-group v-model:value="checkform.urlMethod">
                <a-radio-button value="get">GET</a-radio-button>
                <a-radio-button value="post">POST</a-radio-button>
            </a-radio-group>
        </a-form-item>
            <a-form-item label="匹配方式" name="mateMethod">
            <a-radio-group v-model:value="checkform.mateMethod">
                <a-radio-button value="fuzzy">模糊</a-radio-button>
                <a-radio-button value="precise">精确</a-radio-button>
            </a-radio-group>
            <!-- <a-input v-model:value="checkform.ip" placeholder="请输入IP地址" disabled></a-input> -->
        </a-form-item>
            <a-form-item label="关键词" name="relateKey">
            <a-textarea v-model:value="checkform.relateKey" placeholder="请输入匹配的关键词，多个用英文逗号分隔"></a-textarea>
        </a-form-item>
        <a-form-item label="排除关键词" name="excludeKey">
            <a-textarea v-model:value="checkform.excludeKey" placeholder="请输入匹配排除的关键词，多个用英文逗号分隔"></a-textarea>
        </a-form-item>
        <a-form-item v-if="!info.isAdd" :wrapper-col="{offset:4}">
            <a-button type="primary" @click="save">保存</a-button>
        </a-form-item>
    </a-form>
</template>
<script lang='ts' setup>
import { saveInspection, testConnection, updateInspection } from '@/api/backend/devops/server';
import router from '@/router';
import { message } from 'ant-design-vue';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const {proxy} = getCurrentInstance()
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd: false,
                isShow:false
            }
        }
    }
})
const checkForm = ref()
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    serverId:'',
    serverName:'',
    url:'',
    urlMethod:'get',
    mateMethod: 'fuzzy',
    relateKey:'',
    excludeKey:''
}

const checkform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    serverId:'',
    serverName:'',
    url:'',
    urlMethod:'get',
    mateMethod: 'fuzzy',
    relateKey:'',
    excludeKey:''
})
const rules = {
    url:[{required:true,message:'请输入地址',trigger:'change'}],
    urlMethod:[{required:true,message:'请选择请求方式',trigger:'change'}],
    mateMethod:[{required:true,message:'请选择匹配方式',trigger:'change'}],
    relateKey:[{required:true,message:'请输入关键词',trigger:'change'}],
}
const save = (callback) => {
    let optionText = props.info.isAdd ? '开启' : '修改';
    checkForm.value.validate().then(()=>{
        testConnection(checkform.url).then((res)=>{
            if(res.code == 0 && res.data){
                // console.log("res",res)
                realSave(callback)
            }else{
                if(res.code == 0)
                    message.error(!res.msg || res.msg == 'success' ? '测试链接失败，无法'+optionText : res.msg)
                if(!props.info.isAdd)
                    checkForm.value.resetFields();
            }
        }).catch((err)=>{
            message.error('测试链接失败，无法'+optionText)
            if(!props.info.isAdd)
                checkForm.value.resetFields();
        })
        // console.log("ok")
    })
}
const realSave = (callback) => {
    proxy.$handleSave(checkForm.value, saveInspection, updateInspection, props, checkform, ()=>{callback();close()},()=>{
        message.error('开启失败')
    },null,true)
}
const close = () => {
    checkForm.value.resetFields();
    Object.assign(checkform,defaultform)
}
const setInfo = ({id,serverName},isEdit,checkinfo) => {
    if(isEdit){
        Object.assign(checkform, checkinfo)
    }
    checkform.serverId = id;
    checkform.serverName = serverName;
    
}
onMounted(() => {})
defineExpose({close,setInfo,save})
</script>
<style lang='scss' scoped>
</style>