<template>
    <div class="cloudContent" v-if="!info1.isShow || !info1.isInfo">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight" id="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                    <a-form-item label="虚机名称">
                        <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.serverName" allowClear />
                    </a-form-item>
                    <!-- <a-form-item label="镜像">
                        <a-select placeholder="请选择" allowClear />
                    </a-form-item> -->
                    <a-form-item label="IP地址">
                        <a-input placeholder="请输入" v-model:value="searchform.ip" @pressEnter="handleSearch" allowClear />
                    </a-form-item>
                    <a-form-item label="状态">
                        <a-select placeholder="请选择" v-model:value="searchform.status" :options="statuslist" style="width:100px" allowClear />
                    </a-form-item>
                    <a-form-item label="所有者">
                        <a-select placeholder="请选择" v-model:value="searchform.ownerId" :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}" style="width:200px" show-search allowClear >
                            <a-select-option v-for="(item,index) in userlist" :key="index" :value="item.userId" :title="item.userName+' ('+item.loginName+')'">{{item.userName+' ('+item.loginName+')'}}</a-select-option>
                        </a-select>
                    </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" @export="handleload" />
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd" >新增 </a-button>
                    <a-button class="btnMargin" @click="handleTrans" :disabled="selectedRowKeys.length <= 0" >虚机交接</a-button>
                    <a-button class="btnMargin" @click="handleMultiple('启动','START')" :disabled="selectedRowKeys.length <= 0 || !isMultipleStart" >批量启动</a-button>
                    <a-button class="btnMargin" @click="handleMultiple('关闭','STOP')" :disabled="selectedRowKeys.length <= 0 || !isMultipleStop" >批量关闭</a-button>
                    <a-button class="btnMargin" @click="handleMultiple('重启','REBOOT')" :disabled="selectedRowKeys.length <= 0 || !isMultipleReboot" >批量重启</a-button>
                    <a-button
                    @click="$handleDel(selectedRowKeys,()=>deleteApi,()=>{selectedRowKeys = [];getList();isImmediate = false;},
                        ()=>h('div',[
                                h('p','确认要删除虚机？'),
                                h('p','当计算服务开启回收实例间隔时，删除后云主机会存放在回收站，按对应的时间间隔保留，在此期限内可以选择恢复。恢复成功后的云主机状态为运行中，且相关资源保持不变。'),
                                h(Checkbox,{checked:isImmediate,onChange:changeCheck},'立即删除')
                            ]
                        ),
                        null,null,()=>{isImmediate = false;}
                    )" 
                    :disabled="selectedRowKeys.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table 
                :columns="columns" 
                row-key="id" 
                :data-source="serverlist" 
                :pagination="pagination" 
                @change="changeTable"
                :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                :scroll="{x:true}"
               >
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #serverName={record}>
                        <a @click="goDetail(record)">{{record.serverName}}</a>
                    </template>
                    <template #createTime={record}>
                        <span>{{record.createTime.substr(0,10)}}</span>
                    </template>
                    <template #endTime={record}>
                        <span>{{record.endTime ? record.endTime.substr(0,10) : '永久'}}</span>
                    </template>
                    <template #imageEntity={record}>
                        {{record.imageEntity?.name}}
                    </template>
                    <template #flavorEntity={record}>
                        {{record.flavorEntity?.name}}
                    </template>
                    <template #serverAddressEntityList={record}>
                        <span v-for="(item,index) in record.serverAddressEntityList" :key="item.id">{{item.addr}}<br/></span>
                    </template>
                    <template #action={record,index}>
                        <a-button class="button_V" @click="openTerm(record.id)" v-if="record.status.toLowerCase() == 'active'">控制台</a-button>
                        <a-button class="button_E" @click="setOptions(record.id,'EDIT','修改虚机',record)" v-if="options[24].statusOnly.includes(record.status.toLowerCase())">修改</a-button>
                        <a-button class="button_D" @click="setOptions(record.id,'DELETE','删除',record)">删除</a-button>
                        <a-dropdown :trigger="['click']" :overlay-style="{width:'100px'}" :getPopupContainer="()=>bindNode" v-if="record.status.toLowerCase() != 'error'">
                            <a class="ant-dropdown-link" @click.prevent>
                            更多
                            <DownOutlined />
                            </a>
                            <template #overlay>
                            <a-menu mode="vertical" @click="({ item, key })=>{setOptions(record.id,key,item.title,record)}">
                                <a-sub-menu key="sub1" title="配置变更" v-if="record.status.toLowerCase() != 'error'">
                                    <template v-for="(t,i) in options.slice(0,11)">
                                        <a-menu-item :key="t.value" :title="t.label" v-if="!t.statusOnly || (t.statusOnly && t.statusOnly.includes(record.status.toLowerCase()))" :disabled="t.disabled">
                                            {{t.label}}
                                        </a-menu-item>
                                    </template>
                                </a-sub-menu>
                                <a-sub-menu key="sub2" title="虚机状态" v-if="record.status.toLowerCase() != 'error'">
                                    <template v-for="(t,i) in options.slice(11,22)">
                                        <a-menu-item :key="t.value" :title="t.label" v-if="!t.statusOnly ||  (t.statusOnly && t.statusOnly.includes(record.status.toLowerCase()))" :disabled="t.disabled">
                                            {{t.label}}
                                        </a-menu-item>
                                    </template>
                                </a-sub-menu>
                                <a-sub-menu key="sub3" title="快照备份" v-if="record.status.toLowerCase() != 'error'">
                                    <template v-for="(t,i) in options.slice(22,24)">
                                        <a-menu-item :key="t.value" :title="t.label" v-if="!t.statusOnly ||  (t.statusOnly && t.statusOnly.includes(record.status.toLowerCase()))" :disabled="t.disabled">
                                            {{t.label}}
                                        </a-menu-item>
                                    </template>
                                </a-sub-menu>
                                <!-- <template v-for="(t,i) in options.slice(23,25)">
                                    <a-menu-item :key="t.value" :title="t.label" v-if="computedFloat(null,null,record.serverAddressEntityList)||(t.status && (record.status.toLowerCase() != t.status)) || (!t.status && (record.status == t.statusOnly))" :disabled="t.disabled">
                                        {{t.label}}
                                    </a-menu-item>
                                </template> -->
                            </a-menu>
                            </template>
                        </a-dropdown>
                    </template>
                </a-table>
                <FInfo ref="serverDialog" :info="info" @getlist="getList" />
                <Add ref="serverForm" :info="info1" @getlist="getList" v-if="info1.isAdd" />
                <Edit ref="serverEditForm" :info="info1" @getlist="getList" v-if="!info1.isAdd" />
                <CVOLVMS ref="serverForm2" :info="info2" @getlist="getList"  />
                <DetaVolm ref="serverForm3" :info="info3" @getlist="getList"/>
                <Roll ref="rollDialog" :info="rollinfo" @getlist="getList"/>
                <Size ref="sizeDialog" :info="sizeinfo" @getlist="getList"/>
                <Migrate ref="migrateDialog" :info="migrateinfo" @getlist="getList"/>
                <API ref="apiDialog" :info="apiinfo" @getlist="getList"/>
                <Rebuild ref="rebuildDialog" :info="rebuildinfo" @getlist="getList"/>
                <Password ref="passwordDialog" :modalinfo="passwordinfo" @getlist="getList" />
                <a-modal title="请确认交接信息" v-model:visible="isTrans" ok-text="提交" cancel-text="取消" @ok="saveTrans" @cancel="cancelTrans" :maskClosable="false" centered :getContainer="modalBindNodeTrans">
                    <a-form :model="transform" :rules="rules1" :label-col="{span:5}" ref="transForm">
                        <a-form-item label="交接虚机列表" name="ids">
                            <a-select mode="multiple" v-model:value="transform.ids" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear disabled>
                                <a-select-option v-for="(item,index) in selectedRows" :key="index" :value="item.id" :label="item.serverName">{{item.serverName}}</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="接收人" name="nextOwnerId">
                            <a-select v-model:value="transform.nextOwnerId" placeholder="请选择接收人" show-search 
                            :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}" 
                             :getPopupContainer="triggerNode => triggerNode.parentNode"
                            allow-clear>
                                <a-select-option v-for="(item,index) in userlist" :key="index" :value="item.userId" :label="item.userName">{{item.userName+' ('+item.loginName+')'}}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-form>
                </a-modal>
                <a-modal :title="'确认批量'+multipleInfo.title+'信息'" v-model:visible="multipleInfo.isShow" ok-text="确定" cancel-text="取消" @ok="saveMultiple" @cancel="cancelMultiple" :maskClosable="true" centered :getContainer="modalBindNodeMultiple">
                    <a-form :label-col="{span:5}">
                        <a-form-item label="虚机列表">
                            <a-select mode="multiple" v-model:value="multipleform.serverID" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear disabled>
                                <a-select-option v-for="(item,index) in selectedRows" :key="index" :value="item.id" :label="item.serverName">{{item.serverName}}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-form>
                </a-modal>
            </div>
        </div>
    </div>
    <Info ref="serverForm1" :info="info1" @getlist="getList" v-else />
    <Snapshot :info="snapinfo" ref="snapshotDialog" @getlist="getList" />
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Snapshot from "./options/snap.vue";
import CVOLVMS from "./options/cvolvms.vue"
import DetaVolm from './options/detachvolm.vue';
import Size from './options/size.vue';
import Roll from './options/roll.vue';
import Migrate from './options/migrate.vue';
import API from './options/api.vue';
import Rebuild from './options/rebuild.vue';
import Password from './options/password.vue';
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { ExclamationCircleOutlined, PlusOutlined } from "@ant-design/icons-vue"
import { computed, createVNode, getCurrentInstance, h, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import { getServerList, deleteServer,getConsoleUrl,actionServer, changeOwner, postremoveip, rebootServer, migrateServer,selectExecList, unbindAPI} from "@/api/backend/devops/server";
import { queryWorker } from "@/api/backend/systems/user";
import { message, Modal, Checkbox } from "ant-design-vue";
import FInfo from "./options/flaotinfo.vue";
import Add from "./add.vue";
import Edit from "./edit.vue";
import { useRoute } from "vue-router";
import { getToken} from "@/utils/auth";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { selectImageList } from "@/api/backend/devops/image";
import emiter from "@/utils/Bus";
import { deleteRecycle } from "@/api/backend/devops/recycle";
import { selectDictList } from "@/api/backend/systems/dictionary";
const token=getToken()
const { proxy } = getCurrentInstance();
const route = useRoute()
const modalBindNodeTrans = ref();
const modalBindNodeMultiple = ref();
const isTrans = ref(false)
const isMultipleStart = ref(true);
const isMultipleStop = ref(true);
const isMultipleReboot = ref(true);
const multipleInfo = reactive({
    isShow:false,
    title:'启动'
})
const selectedRowKeys = ref([])
const selectedRows = ref([])
const selectedServerNames = ref([]);
const userlist = ref([]);
const statuslist = ref([]);
const transForm = ref()
const transform = reactive({nextOwnerId:undefined,ids:[]})
const multipleform = reactive({action:undefined,serverID:[]})
const rules1 = {
    nextOwnerId:[{type:'number',required:true,message:'请选择接收人',trigger:'change'}]
}
const bindNode = ref()
const serverDialog = ref(null);
const snapshotDialog = ref()
const migrateDialog = ref()
const serverForm = ref(null);
const serverEditForm = ref(null);
const serverForm1 = ref(null);
const serverForm2 = ref(null);
const serverForm3 = ref(null);
const sizeDialog = ref(null);
const rollDialog = ref(null);
const rebuildDialog = ref(null);
const passwordDialog = ref(null);
const tempOption = ref(undefined)
const loading = ref(false);
const serverlist = ref([]);
const isImmediate = ref(false);
const info = reactive({
    isShow:false,
    isAdd:true
})
const info1 = reactive({
    isAdd: true,
    isShow:false,
    isInfo:false
})

const info2 = reactive({
    isAdd: true,
    isShow:false,
    isInfo:false
})
const info3 = reactive({
    isAdd: true,
    isShow:false,
    isInfo:false
})
const snapinfo = reactive({
    isAdd: true,
    isShow:false
})
const sizeinfo = reactive({
    isAdd: true,
    isShow:false
})
const rollinfo = reactive({
    isAdd: true,
    isShow:false
})
const migrateinfo = reactive({
    isAdd: true,
    isShow:false
})
const rebuildinfo = reactive({
    isAdd:true,
    isShow:false
})
const passwordinfo = reactive({
    isShow:false
})
const searchform = reactive({
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    domainId:route.query.domainId,
    serverName:'',
    ip:'',
    status:undefined,
    ownerId:undefined,
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
  begin();
};
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,align:'center'},
    {title: '虚机名称', dataIndex: 'serverName', slots: { customRender: 'serverName' }, key: 'id',width:160,align:'left'},
    {title: '镜像名称', dataIndex: 'imageEntity',slots: { customRender: 'imageEntity' }, key: 'id',width:318, align:'left'},
    {title: '虚机类型', dataIndex: 'flavorEntity',slots: { customRender: 'flavorEntity' }, key: 'id',align:'left'},
    {title: 'IP地址', dataIndex: 'serverAddressEntityList',slots: { customRender: 'serverAddressEntityList' }, key: 'id',align:'left'},
    {title: '区域', dataIndex: 'availabilityZone', key: 'id',align:'center'},
    {title: '状态', dataIndex: 'statusText', key: 'id',align:'center'},
    {title: '开始日期', dataIndex: 'createTime', slots: { customRender: 'createTime' }, key: 'id',align:'center'},
    {title: '到期日期', dataIndex: 'endTime', slots: { customRender: 'endTime' }, key: 'id',align:'center'},
    {title: '所有者', dataIndex: 'ownerName', key: 'id',align:'center'},
    {title: '主机', dataIndex: 'host', key: 'id',align:'left'},
    {title: '主机IP', dataIndex: 'hostIp', key: 'id',align:'left'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },fixed:'right' }
];
// 必看：*！绑定浮动IP、解除浮动IP前加项目一定要修改computedFloat的传参下标
// 与列表接口可能返回的状态与status一致(该按钮不显示)，与返回状态互斥用statusOnly(显示该按钮)
const options = [
    {"value":"BIND", "label":"绑定浮动IP", "disabled":false,"status":true,"statusOnly":["active", "paused", "suspended", "shutoff"]},
    {"value":"UNBIND", "label":"解除浮动IP", "disabled":false,"status":true,"statusOnly":["active", "paused", "suspended", "shutoff"]},
    {"value":"BINDAPI", "label":"连接接口", "disabled":false,"status":true,"statusOnly":["active", "shutoff"]},
    {"value":"UNBINDAPI", "label":"分离接口", "disabled":false,"status":true,"statusOnly":["active", "shutoff"]},
    {"value":"CVOLVMS", "label":"连接卷", "disabled":false,"status":true,"statusOnly":["active"]},
    {"value":"DETACHVOLM", "label":"分离卷", "disabled":false,"status":true,"statusOnly":["active"]},
    {"value":"RESIZE", "label":"调整虚机大小", "disabled":false,"status":true,"statusOnly":["active", "shutoff"]},
    {"value":"REBUILD", "label":"重建虚机", "disabled":false,"status":true,"statusOnly":["active", "shutoff"]},
    {"value":"MIGRATE", "label":"虚机迁移", "disabled":false,"status":true,"statusOnly":["active","shutoff"]},
    {"value":"LIVEMIGRATE", "label":"虚机热迁移", "disabled":false,"status":true,"statusOnly":["active"]},
    {"value":"PASSWORD", "label":"修改管理员密码", "disabled":false,"statusOnly":["active"]},

    // {"value":"", "label":"更新元数据", "disabled":true,"status":true},
    // {"value":"", "label":"编辑安全组", "disabled":true,"status":true},
    // {"value":"", "label":"调整端口安全组", "disabled":true,"status":true},
    {"value":"PAUSE", "label":"暂停虚机", "disabled":false, "status":"paused", "statusOnly": ["active", "shutoff", "rescue"]},
    {"value":"UNPAUSE", "label":"唤醒虚机", "disabled":false, "statusOnly":["paused"]},
    {"value":"SUSPEND", "label":"挂起虚机", "disabled":false, "status":"suspended", "statusOnly": ["active", "shutoff"]},
    {"value":"RESUME", "label":"恢复(挂起的)虚机", "disabled":false, "statusOnly":["suspended"]},
    {"value":"REBOOT", "label":"重启虚机", "disabled":false,"status":true, "statusOnly": ["active", "shutoff", "rescue"]},
    {"value":"START", "label":"开启虚机", "disabled":false, "status":"active", "statusOnly": ["shutoff", "stopped"]},
    {"value":"STOP", "label":"关闭虚机", "disabled":false, "status":"stop", "statusOnly": ["active", "shutoff", "rescue"]},
    {"value":"RESCUE", "label":"救援虚机", "disabled":false, "status":"rescue", "statusOnly": ["active", "shutoff", "stopped"]},
    {"value":"UNRESCUE", "label":"恢复虚机", "disabled":false, "statusOnly":["rescue"]},
    {"value":"SHELVE", "label":"废弃虚机", "disabled":false, "status":"shelved_offloaded", "statusOnly": ["active", "paused", "suspended", "shutoff"]},
    {"value":"UNSHELVE", "label":"恢复虚机", "disabled":false, "statusOnly":["shelved_offloaded"]},
    
    {"value":"SNAPSHOT", "label":"创建快照", "disabled":false, "status":true, "statusOnly": ["active", "paused", "suspended","shutoff"]},
    {"value":"ROLLBACK", "label":"快照回滚", "disabled":false,"status":true, "statusOnly": ["active"]},
    {"value":"EDIT", "label":"修改虚机", "disabled":false,"status":true,"statusOnly": ["active", "paused", "suspended", "shutoff","error"]},
    {"value":"DELETE", "label":"删除", "disabled":false,"status":true},
    // {"value":"", "label":"重建虚机", "disabled":true,"status":true},
]
let map = new Map();
const onSelectChange = (selectedRowkeys,selectedrows) => {
    console.log("selectedrows",selectedRowkeys,selectedrows)
    selectedRowKeys.value = selectedRowkeys;
    let serverNames = [];
    for (let item of selectedRows.value.concat(selectedrows)) {
        serverNames.push(item.serverName)
        if (!map.has(item.id)) {
            map.set(item.id, item);
        };
    };
    selectedServerNames.value = serverNames;
    selectedRows.value = [...map.values()];
    isMultipleStart.value = !(selectedrows.some((item)=>{
        return (!options[16].statusOnly.includes(item.status.toLowerCase()))
    }))
    isMultipleStop.value = !(selectedrows.some((item)=>{
        return (!options[17].statusOnly.includes(item.status.toLowerCase()))
    }))
    isMultipleReboot.value = !(selectedrows.some((item)=>{
        return (!options[18].statusOnly.includes(item.status.toLowerCase()))
    }))
};
const getList = async (isUnLoading) => {
    nextTick(()=>{
        bindNode.value = document.getElementById('cloudRight');
    })
    tempOption.value = undefined;
    // searchform.cloudId = localStorage.getItem('cloudId');
    searchform.cloudId = route.query.cloudId;
    searchform.projectId = route.query.projectId;
    searchform.domainId = route.query.domainId;
    if(!isUnLoading)
        emiter.emit("loading",true)
    serverlist.value = await proxy.$getList(loading, getServerList, searchform, pagination, getList );
    emiter.emit("loading",false)
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
  begin();
};
const handleload:any =async()=>{
    handleSearch()
    let res=await selectExecList({token:token,projectId:searchform.projectId,cloudId:searchform.cloudId,serverName:searchform.serverName})
    var blob = new Blob([res], { type:"application/vnd.ms-excel" });
    let url=window.URL.createObjectURL(blob)
    let link = document.createElement("a");
    link.style.display='none'
    link.href=url
    link.setAttribute('download',`虚机管理.xlsx`)
    document.body.appendChild(link)
    link.click();
}
const handleAllReset: any = () => {
    Object.assign(searchform,{
        cloudId:route.query.cloudId,
        projectId:route.query.projectId,
        domainId:route.query.domainId,
        serverName:'',
        ip:'',
        status:undefined,
        ownerId:undefined,
        pageIndex:1
    })
//   getList();
}
const handleAdd = () => {
    info1.isAdd = info1.isShow = true;
    info1.isInfo = false;
    nextTick(()=>{
        serverForm.value.init();
    })
}

const handleTrans = () => {
    transform.ids = selectedRowKeys.value;
    isTrans.value = true;
    // proxy.$nextTick(()=>{
    //     queryworker();
    // })
}
const handleMultiple = (title,action) => {
    if(action == 'STOP'){
        Modal.confirm({
            // title: 'Confirm',
            icon: createVNode(ExclamationCircleOutlined),
            content: `确认关闭虚机（虚机名称：${selectedServerNames.value.join('，')})？`,
            okText: '提交',
            cancelText: '取消',
            maskClosable: true,
            getContainer:proxy.modalBindNode,
            async onOk(){
                let res = await actionServer({serverID:selectedRowKeys.value,action})
                if(res.code==0 && res.data== true){
                    message.success('批量'+title+"成功")
                    selectedRowKeys.value = []
                    selectedRows.value = []
                    selectedServerNames.value = []
                    getList()
                }else if(res.code == 0){
                    message.warning('批量'+title+"失败")
                }
            }
        });
    }else{
        multipleform.serverID = selectedRowKeys.value;
        multipleform.action = action;
        multipleInfo.isShow = true;
        multipleInfo.title = title;
    }
    
}
const cancelMultiple = () => {
    multipleInfo.isShow = false;
    multipleform.serverID = [];
}
const saveMultiple = async () => {
    if(multipleInfo.title == '重启'){
        let res = await rebootServer({serverID:multipleform.serverID})
        if(res.code == 0 && res.data !== false){
            message.success('批量重启成功')
            cancelMultiple();
            selectedRowKeys.value = []
            selectedRows.value = []
            getList();
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '批量重启失败' : res.msg);
        }
    }
    else{
        let res = await actionServer(multipleform)
        if(res.code==0 && res.data !== false){
            message.success('批量'+multipleInfo.title+"成功")
            selectedRowKeys.value = []
            selectedRows.value = []
            cancelMultiple();
        }else{
            if(res.msg && res.msg != 'success')
                message.warning(res.msg)
            else
                message.warning('批量'+multipleInfo.title+"失败")
        }
        getList()
    }
}
const saveTrans = () => {
    transForm.value.validate().then(async ()=>{
        let res = await changeOwner(transform)
        if(res.code == 0 && res.data !== false){
            message.success('交接成功');
            cancelTrans();
            selectedRowKeys.value = []
            selectedRows.value = []
            getList();
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '交接失败' : res.msg);
        }
    })
    
}
const cancelTrans = () => {
    isTrans.value = false;
    transform.ids = [];
    transform.nextOwnerId = undefined;
    transForm.value.resetFields();
}
const deleteApi = computed(()=>isImmediate.value ? deleteRecycle : deleteServer);
const changeCheck = (e) => {
    isImmediate.value = e.target.checked;
}
const openTerm = (id) => {
    Modal.confirm({
        title:'跳转到控制台',
        icon: createVNode(ExclamationCircleOutlined),
        content:'您确定要直接跳转到控制台吗？控制台稍后会在新页面中打开。',
        async onOk(){
            let res = await getConsoleUrl(id);
            if(res.code == 0 && res.data !== false){
                window.open(res.data,"_blank");
            }else if(res.code == 0){
                message.error((!res.msg || res.msg == 'success') ? '虚机控制台异常' : res.msg);
                return;
            }
        }
    })
}
// 共用action接口的在最后一个判断条件中，其他接口分别判断
const setOptions = async (serverID, action,label,record) => {
    if(action == 'DELETE'){
        proxy.$handleDel([serverID],()=>deleteApi.value,()=>{getList();isImmediate.value = false;},
            ()=>h('div',[
                    h('p','确认要删除虚机？'),
                    h('p','当计算服务开启回收实例间隔时，删除后云主机会存放在回收站，按对应的时间间隔保留，在此期限内可以选择恢复。恢复成功后的云主机状态为运行中，且相关资源保持不变。'),
                    h(Checkbox,{checked:isImmediate.value,onChange:changeCheck},'立即删除')
                ]
            ),
            null,null,()=>{isImmediate.value = false;}
        )
    }else if(action == 'BIND'){
        addFloatIp(serverID)
    }else if(action == 'CVOLVMS'){
        cbolvnInfo(serverID);
    }else if(action == 'DETACHVOLM'){
         detachInfo(serverID);
    }else if(action == 'EDIT'){
         handleEdit(record);
    }else if(action == 'REBOOT'){
        handleReboot(serverID)
    }else if(action == 'REBUILD'){
        handleRebuild(record)
    }else if(action == 'RESIZE'){
        handleSize(record)
    }else if(action == 'UNBIND'){
        // relieveIP(serverID)
        Modal.confirm({
            // title: 'Confirm',
            icon: createVNode(ExclamationCircleOutlined),
            content: `是否同时释放浮动ip`,
            okText: '是',
            cancelText: '否',
            maskClosable: true,
            onOk(){
                relieveIP(serverID,true)
            },
            onCancel(){
                relieveIP(serverID,false)
            }
        });
    }else if(action == 'SNAPSHOT'){
        snapinfo.isAdd = snapinfo.isShow = true;
        snapshotDialog.value.snapshotform.serverId = serverID;
    }else if(action == 'ROLLBACK'){
        handleRoll(serverID);
    }else if(action == 'MIGRATE'){
        handleMigrate(action,record)
    }else if(action == 'LIVEMIGRATE'){
        handleMigrate(action,record)
    }else if(action == 'BINDAPI' || action == 'UNBINDAPI'){
        console.log('ok')
        handleAPI(action,record)
    }else if(action == 'STOP'){
        Modal.confirm({
            // title: 'Confirm',
            icon: createVNode(ExclamationCircleOutlined),
            content: `确认关闭虚机（虚机名称：${record.serverName})？`,
            okText: '提交',
            cancelText: '取消',
            maskClosable: true,
            getContainer:proxy.modalBindNode,
            async onOk(){
                let res = await actionServer({serverID:[serverID],action})
                if(res.code==0 && res.data== true){
                    message.success(label+"成功")
                    getList()
                }else if(res.code == 0){
                    message.warning(res.msg ? res.msg : label+"失败")
                }
            }
        });
    }else if(action == 'PASSWORD'){
        handlePassword(record)
    }else{
        let res = await actionServer({serverID:[serverID],action})
        if(res.code==0 && res.data== true){
          message.success(label+"成功")
        }else{
            if(!res.msg)
            message.warning(label+"失败")
            else
            message.warning(res.msg)
        }
        getList()
    }
}
// 必看：若options下标13之前的数组数量变化，必须同步改变pre,next的实参
const computedFloat = (pre,next,data)=>{
    let res = data.some((item,index)=>{
        return item.type == 'floating';
    })
    if(pre && next){
        if(res){
            pre.status = false;
            next.status = true;
        }else{
            pre.status = true;
            next.status = false;
        }
    }
    // if(res && t.value == 'BIND'){
    //     console.log('需要接触')
    //     pre = false;
    //     t.status = false;
    //     return false;
    // }else if(!res && t.value == 'UNBIND'){
    //     t.status = false;
    //     return false;
    // }
    // if(!pre && t.value == 'UNBIND'){
    //     t.status = true;
    // }
}
const addFloatIp = (id) => {
    serverDialog.value.getselectFloatIP(()=>{
        info.isShow = info.isAdd = true;
        serverDialog.value.getIpderss(id)
        serverDialog.value.cloudform.serverId=id
    })
}
const relieveIP = (serverID,remove)=>{
    postremoveip({
        serverID,
        remove
    }).then((res)=>{
        if(res.code==0 && res.data !== false){
            message.success("解绑成功")
            // resolve()
            getList()
            props.info.isShow = false;
            cloudform.serverID=""
            cloudform.ipId=""
            cloudform.floatIpId=""
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '解绑失败' : res.msg)
            // reject()
        }
    })
}
const handleReboot = async (serverID) => {
    let res = await rebootServer({serverID:[serverID]})
    if(res.code == 0 && res.data !== false){
        message.success('重启成功')
        getList();
    }else if(res.code == 0){
        message.error((!res.msg || res.msg == 'success') ? '重启失败' : res.msg);
    }
}
const handleRebuild = (record) => {
    rebuildinfo.isShow = true;
    nextTick(()=>{
        rebuildDialog.value.SelectImageList(record)
    })
}
const handleEdit = (record) => {
    info1.isAdd = false;
    info1.isShow = true;
    proxy.$nextTick(()=>{
        serverEditForm.value.setinfo(record)
        // Object.assign(serverEditForm.value.serverform, record)
    })
}

const handleSize = (record) => {
    sizeinfo.isShow = true;
    proxy.$nextTick(()=>{
        sizeDialog.value.getFlavorlist();
        sizeDialog.value.setinfo(record);
    })
}
const handleRoll = (serverId) => {
    rollinfo.isShow = true;
    proxy.$nextTick(()=>{
        rollDialog.value.getSnaplist(serverId);
        // sizeDialog.value.setinfo(record);
    })
    
}
const handleMigrate = (action,record) => {
    if(action == 'LIVEMIGRATE'){
        migrateinfo.isShow = true;
        proxy.$nextTick(()=>{
            migrateDialog.value.setinfo(record);
        })
    }else{
        Modal.confirm({
            title: "确认迁移信息",
            icon: createVNode(ExclamationCircleOutlined),
            content: "您已选择"+record.serverName+"，迁移操作不可恢复，请谨慎操作。",
            okText: "提交",
            cancelText: "取消",
            maskClosable: true,
            onOk() {
                migrateServer({serverID:record.id})
                .then((res: any) => {
                    if (res.code == 0 && res.data !== false) {
                        message.success('迁移成功')
                        getList()
                    } else {
                        message.error((!res.msg || res.msg == 'success') ? '迁移失败' : res.msg);
                    }
                })
                .catch((err: any) => {
                    message.error('迁移失败')
                });
            }
        })
    }
    
}
const apiinfo = reactive({isShow:false,isAdd:true})
const apiDialog = ref()
const handleAPI = (action,record) => {
    if(action == 'BINDAPI')
        apiinfo.isAdd = true;
    else{
        apiinfo.isAdd = false;
        if(!record.serverAddressEntityList || !record.serverAddressEntityList?.[0]?.portId){
            message.error('无可分离接口');
            return;
        }
    }
    apiinfo.isShow = true;
    proxy.$nextTick(()=>{
        apiDialog.value.setInfo(action,record);
    })
}
const handleUnbindAPI = async (record) => {
    let res = await unbindAPI({serverId:record.id,portId:record.portId})
    if(res.code == 0 && res.data !== false){
        message.success('分离接口成功');
        getList()
    }else if(res.code == 0){
        message.error((!res.msg || res.msg == 'success') ? '分离接口失败' : res.msg);
    }
}
const handlePassword = (record) => {
    passwordinfo.isShow = true;
    nextTick(()=>{
        passwordDialog.value.passform.serverId = record.id;
    })
}
const goDetail = async (record) => {
    info1.isInfo = info1.isShow = true;
    proxy.$nextTick(()=>{
        serverForm1.value.setInfo(record)
    })
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
}
const getDictList = async () => {
  let res = await selectDictList({dictType:'SERVER_STATUS'})
  if(res){
    if(res.code == 0){
      statuslist.value = res.data.map((item,index)=>{
        item.label = item.dictLabel;
        item.value = item.dictValue;
        return item;
      });
    }
  }
}
const cbolvnInfo=(id)=>{
     info2.isShow = info2.isAdd = true;
     serverForm2.value.getCvlvmsIP();
       serverForm2.value.cloudform.serverId=id
}
const detachInfo=(id)=>{
     info3.isShow = info3.isAdd = true;
      serverForm3.value.cloudform.serverId=id
     serverForm3.value.getdetachCvlvmsIP();
   
}
var timerServer = null;
const begin = () => {
    clearInterval(timerServer);
    console.log("searchform.status",searchform.status,!searchform.serverName && !searchform.ip && !searchform.status)
    if(!searchform.serverName && !searchform.ip && !searchform.status){
        timerServer = setInterval(()=>{
            getList(true);
        },60000)
    }
}
onMounted(() => {
    modalBindNodeTrans.value = document.getElementsByClassName('cloudContent')[0];
    modalBindNodeMultiple.value = document.getElementsByClassName('cloudContent')[1];
    getList();
    begin();
    queryworker();
    getDictList();
    nextTick(()=>{
        handleWidth()
    })
})
onUnmounted(()=>{
    clearInterval(timerServer);
})
</script>
<style lang='scss' scoped>
// :deep(.ant-table th){ word-break:break-all; }
// :deep(.ant-table td){ word-break:break-all; }
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
// .cloudContent .cloudRight{min-width: 810px;}
.btnMargin {
  margin-right: 10px;
}
#cloudRight{position: relative;}
// .buttonPadding,.innerPadding{min-width: 1597px;}
</style>