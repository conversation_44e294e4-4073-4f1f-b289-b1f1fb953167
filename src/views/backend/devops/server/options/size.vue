<!-- 调整虚机大小 -->
<template>
    <div class=''>
        <a-modal title="调整虚机大小" v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form :model="sizeform" :label-col="{span:5}" ref="sizeForm" :rules="rules">
                <a-form-item label="旧的虚机类型">
                    <a-input v-model:value="flavorId" placeholder="请输入旧的虚机类型" disabled></a-input>
                </a-form-item>
                <a-form-item label="新的虚机类型" name="flavorId">
                    <a-select v-model:value="sizeform.flavorId" placeholder="请输入新的虚机类型" :getPopupContainer="triggerNode => triggerNode.parentNode">
                        <a-select-option v-for="(item,indx) in flavorlist" :key="index" :value="item.id">{{item.name}}</a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { resizeServer } from '@/api/backend/devops/server';
import { selectFlavorList } from '@/api/backend/devops/flavor';
import { message } from 'ant-design-vue';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import emiter from '@/utils/Bus';
import router from '@/router';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
            }
        }
    }
})
const sizeform = reactive({
    serverID:'',
    flavorId:undefined
})
const sizeForm = ref();
const flavorId = ref('');
const flavorlist = ref([]);
const rules = {
    flavorId:[{required:true, type:'number', message:'请输入新的虚机类型',trigger:'change'}],
}
const setinfo = (record) => {
    sizeform.serverID = record.id;
    flavorId.value = record.flavorEntity?.name;
}
const cancel = () => {
    props.info.isShow = false;
    sizeform.serverID = '';
    sizeform.flavorId = undefined;
}
const save = () => {
    sizeForm.value.validate().then(async ()=>{
        let res = await resizeServer(sizeform);
        if(res.code == 0 && res.data !== false){
            message.success('调整大小成功');
            emit('getlist');
            cancel()
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '调整大小失败' : res.msg);
        }
    })
    
}
const getFlavorlist = async () => {
    let res = await selectFlavorList({cloudId:router.currentRoute.value.query.cloudId,projectId: router.currentRoute.value.query.projectId})
    if(res.code == 0)
    flavorlist.value = res.data;
}
onMounted(() => {})
defineExpose({setinfo, getFlavorlist})
</script>
<style lang='scss' scoped>
</style>