<template>
    <div class=''>
        <a-modal centered
            v-model:visible="info.isShow"
            ok-text="提交"
            cancel-text="取消"
            @ok="save"
            @cancel="cancel"
            width="720.8px"
            :maskClosable="false"
            :getContainer="modalBindNode"
            >
                <template #title>
                    <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                        <span>管理浮动IP的关联</span>
                        <a-popover trigger="click" placement="leftTop">
                            <template #content>
                                <span>{{content.server_floatip}}</span>
                            </template>
                            <InfoCircleOutlined  />
                        </a-popover>
                    </div>
                </template>
                <a-form :model="cloudform" ref="cloudformRef" :rules="rules" :label-col="{span:6}">

                     <a-form-item label="IP地址" name="floatIpId">
                        <a-select
                            v-model:value="cloudform.floatIpId"
                            placeholder="请选择"
                            allowClear>
                            <a-select-option v-for="(item,index) in floatList" :key="index" :value="item.id">{{item.floatingIpAddress}}</a-select-option>
                        </a-select>
                    </a-form-item>
                     <a-form-item label="待连接的接口" name="ipId">
                        <a-select
                            v-model:value="cloudform.ipId"
                            placeholder="请选择"
                            allowClear>
                            <a-select-option v-for="(item,index) in addressList " :key="index" :value="item.id">{{item.addr}}</a-select-option>
                        </a-select>
                    </a-form-item>
                
                 </a-form>
            </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {  selectCloudList } from "@/api/backend/cloud";
import { getselectflaotId } from "@/api/backend/devops/floatingip";
import { postaddip,postremoveip } from "@/api/backend/devops/server";
import { getselectserveIP } from "@/api/backend/devops.ts";
import { selectDictList} from "@/api/backend/systems/dictionary";
import {content} from "@/common/explain/modal";
import { message } from 'ant-design-vue';
import emiter from '@/utils/Bus';
import router from '@/router';
const { proxy } = getCurrentInstance()
const emit = defineEmits(['getlist'])
const cloudformRef = ref(null);
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: false
      };
    }
  },
})
const floatList = ref([])
const addressList=ref([])
const isInfo = ref(false)
// const NetworkAvailableList= ref([]); //网络可用
const defaultform = {
   ipId:undefined,
   floatIpId:undefined
   
}
const cloudform = reactive({
    ipId:undefined,
   floatIpId:undefined

})
const rules = {
    floatIpId:[{type:'number',required:true,message:'请选择',trigger:'change'}],
    ipId:[{type:'number',required:true,message:'请选择',trigger:'change'}],
}

const save = () => {
    cloudformRef.value.validate().then(async ()=>{
        if(props.info.isAdd){
            let res=await postaddip({
                serverId:cloudform.serverId,
                ipId:cloudform.ipId,
                floatIpId:cloudform.floatIpId
            })
            if(res.code==0 && res.data !== false){
                message.success("绑定成功")
                emit('getlist')
                props.info.isShow = false;
                cloudform.serverId=""
                cloudform.ipId="";
                cloudform.floatIpId="";
            }else if(res.code == 0){
                message.error((!res.msg || res.msg == 'success') ? '绑定失败' : res.msg, 5)
            }
        }
    })
}
const cancel = () => {
    props.info.isShow=false
    isInfo.value = false
    cloudform.serverID=""
    cloudform.serverId=""
    cloudform.ipId=""

    cloudformRef.value.resetFields();
    Object.assign(cloudform,defaultform)
}
const selectCloudlist = () => {
}
const getselectFloatIP=async(callback)=>{
    let cloudId=router.currentRoute.value.query.cloudId
    let projectId=router.currentRoute.value.query.projectId
    let res=await getselectflaotId({cloudId,projectId,status:'down'})
    if(res.data && res.data.length > 0){
        callback()
        floatList.value=res.data;
    }else{
        message.error('当前项目下无空闲浮动ip')
        return;
    }
}

const getIpderss = async (serverId) => {
    let res = await getselectserveIP({serverId})
    addressList.value=res.data
}
onMounted(() => {
    selectCloudlist();
})
defineExpose({cloudform,isInfo,getselectFloatIP,getIpderss})
</script>
<style lang='scss' scoped>
</style>