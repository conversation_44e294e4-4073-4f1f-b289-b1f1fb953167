<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isAdd ? '新增':'修改'}}</span>
                    <a-popover trigger="click" placement="leftTop">
                        <template #content>
                            <span>{{content.server_snap}}</span>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <a-form ref="snapshotForm" :model="snapshotform" :labelCol="{span:4}" :rules="rules">
                <a-form-item label="快照名称" name="snapshotName">
                    <a-input v-model:value="snapshotform.snapshotName" placeholder="请输入快照名称"></a-input>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { saveSnapshot, updateSnapshot } from "@/api/backend/storage";
import { createSnapshot } from '@/api/backend/devops/server';
import { selectZoneList } from '@/api/backend/devops';
import {content} from "@/common/explain/modal";
import router from '@/router';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    snapshotName:''
}
const snapshotForm = ref()
const snapshotform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    snapshotName:''
})
const rules = {
    snapshotName:[{required:true,message:'请输入快照名称',trigger:'change'}],
    // type:[{required:true,message:'请选择卷类型',trigger:'change'}],
    // size:[{required:true,type:'integer',message:'请输入卷大小',trigger:'change'}],
    // availabilityZone:[{required:true,message:'请输入卷名称',trigger:'change'}],
}
const typelist = ref([])
const zonelist = ref([])
const setInfo = (data) => {
    Object.assign(snapshotform,data);
}
const cancel = () => {
    props.info.isShow = false;
    snapshotForm.value.resetFields();
    Object.assign(snapshotform,defaultform)
}
const save = () => {
    proxy.$handleSave(snapshotForm.value, createSnapshot, updateSnapshot, props, snapshotform, ()=>{emit('getlist');cancel();})
}
const SelectVolumeTypeList = async () => {
    let cloudId = localStorage.getItem('cloudId');
    let res = await selectVolumeTypeList({cloudId});
    if(res.code == 0){
        typelist.value = res.data;
    }
}
const selectZonelist = async () => {
    let res = await selectZoneList({cloudId:localStorage.getItem('cloudId'),module:'nova'});
    if(res.code == 0){
        zonelist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({setInfo,snapshotform})
</script>
<style lang='scss' scoped>
</style>