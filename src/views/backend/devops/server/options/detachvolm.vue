<template>
    <div class=''>
        <a-modal centered
            v-model:visible="info.isShow"
            ok-text="分离卷"
            cancel-text="取消"
            @ok="save"
            @cancel="cancel"
            width="720.8px"
            :maskClosable="false"
            :getContainer="modalBindNode"
            >
                <template #title>
                    <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                        <span>分离卷</span>
                        <a-popover trigger="click" placement="leftTop">
                            <template #content>
                                <span>{{content.server_dvolume}}</span>
                            </template>
                            <InfoCircleOutlined  />
                        </a-popover>
                    </div>
                </template>
                <a-form :model="cloudform" ref="cvolvmsRef" :rules="rules" :label-col="{span:3}">
                    <a-form-item label="卷名称" name="volumeId">
                        <a-select
                            v-model:value="cloudform.volumeId"
                            placeholder="请选择"
                            allowClear>
                            <a-select-option v-for="(item,index) in floatList" :key="index" :value="item.volumeId">{{item.volumeName ? item.volumeName : item.thirdVolumeId}}</a-select-option>
                        </a-select>
                    </a-form-item>
                
                 </a-form>
            </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { selectVolumeList} from '@/api/backend/storage';
import { saveDevolvneIp} from "@/api/backend/devops/server";
import { getDeolvIpList} from "@/api/backend/devops.ts";
import {content} from "@/common/explain/modal";
import { message } from 'ant-design-vue';
import emiter from '@/utils/Bus';
import router from '@/router';
const { proxy } = getCurrentInstance()
const emit = defineEmits(['getlist'])
const cvolvmsRef = ref(null);
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: false
      };
    }
  },
})
const floatList = ref([])
const addressList=ref([])
const isInfo = ref(false)
// const NetworkAvailableList= ref([]); //网络可用
const defaultform = {
   volumeId:undefined,
 
   
}
const cloudform = reactive({
    volumeId:undefined,
 

})
const rules = {
    volumeId:[{type:'number',required:true,message:'请选择',trigger:'change'}],
    
}
// message.success("关联成功")
// message.error(res.msg)
const cancel = () => {
    cloudform.serverId=""
    cloudform.volumeId="";
}

const save = () => {
    cvolvmsRef.value.validate().then(async ()=>{
        if(props.info.isAdd){
            let res=await saveDevolvneIp({
                serverId:cloudform.serverId,
                volumeId:cloudform.volumeId
            })
            if(res.code==0){
                if(res.data===false){
                    message.error((!res.msg || res.msg == 'success') ? '分离卷失败' : res.msg)
                }else{
                  message.success("分离卷成功")
                }
                emit('getlist')
                props.info.isShow = false;
                cancel()
            }
        }
    })
}
const getdetachCvlvmsIP=async()=>{
   
    let projectId=router.currentRoute.value.query.projectId
    let res=await getDeolvIpList({serverId:cloudform.serverId})
    floatList.value=res.data 
}
onMounted(() => {})
defineExpose({cloudform,isInfo,getdetachCvlvmsIP})
</script>
<style lang='scss' scoped>
</style>