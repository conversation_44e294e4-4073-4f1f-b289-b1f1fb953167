<template>
    <div class=''>
        <a-modal title="重建虚机" v-model:visible="info.isShow" :getContainer="modalBindNode" @ok="handleSave" @cancel="cancel">
            <a-form ref="rebuildForm" :model="rebuildform" :label-col="{span:3}" :wrapper-col="{span:21}">
                <a-form-item label="名称" name="serverName" :rules="{required:true,message:'请输入虚机名称'}">
                    <a-input v-model:value="rebuildform.serverName"></a-input>
                </a-form-item>
                <a-form-item label="镜像" name="imageId" :rules="{required:true,message:'请选择镜像'}">
                    <a-select v-model:value="rebuildform.imageId" :getPopupContainer="triggerNode => triggerNode.parentNode" placeholder="请选择镜像">
                        <template v-for="(item,index) in imagelist" :key="index">
                            <a-select-option :value="item.id">
                                <a-tooltip :title="item.name">{{item.name}}</a-tooltip>
                            </a-select-option>
                        </template>
                    </a-select>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { selectImageList } from '@/api/backend/devops/image';
import { rebuildServer } from '@/api/backend/devops/server';
import { message } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
const emit = defineEmits(['getlist']);
const route = useRoute()
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
            }
        }
    }
})
const rebuildForm = ref()
const defaultform = {
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    serverId:0,
    imageId:undefined,
    serverName:''
}
const rebuildform = reactive({
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    serverId:0,
    imageId:undefined,
    serverName:''
})
const imagelist = ref([])
const SelectImageList = (record) => {
    rebuildform.serverId = record.id;
    rebuildform.serverName = record.serverName;
    selectImageList({cloudId:route.query.cloudId,projectId:route.query.projectId,imageType:'Image',status:'ACTIVE'}).then((res)=>{
        if(res.code == 0){
            imagelist.value = res.data;
        }
    })
}
const handleSave = () => {
    rebuildServer(rebuildform).then((res)=>{
        if(res.code == 0 && res.data !== false){
            message.success("虚机重建成功");
            emit('getlist');
            cancel();
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '虚机重建失败' : res.msg);
        }
    }).catch((err)=>{
        message.error(err.message);
    })
}
const cancel = () => {
    props.info.isShow = false;
    rebuildForm.value.resetFields();
    Object.assign(rebuildform,defaultform);
}
onMounted(() => {})
defineExpose({SelectImageList})
</script>
<style lang='scss' scoped>
</style>