<!-- 调整虚机大小 -->
<template>
    <div class=''>
        <a-modal title="热迁移" v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form :model="migrateform" :label-col="{span:5}" ref="migrateForm" :rules="rules">
                <a-form-item label="当前虚机">
                    <a-input v-model:value="migrateform.host" placeholder="请输入旧的虚机" allow-clear disabled></a-input>
                </a-form-item>
                <a-form-item label="新虚机" name="destHost">
                    <a-select v-model:value="migrateform.destHost" placeholder="请选择新的虚机" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <template v-for="(item,index) in hostlist" :key="index">
                            <a-select-option :value="item.hypervisor_hostname" v-if="migrateform.host!=item.hypervisor_hostname">{{item.hypervisor_hostname}}</a-select-option>
                        </template>
                        <!-- <a-select-option value="compute3" v-if="migrateform.host!='compute3'">compute3</a-select-option> -->
                    </a-select>
                </a-form-item>
                <a-form-item label="允许磁盘超量">
                    <a-switch v-model:checked="migrateform.diskOverCommit" checked-children="是" un-checked-children="否" />
                </a-form-item>
                <a-form-item label="块设备迁移">
                    <a-switch v-model:checked="migrateform.blockMigration" checked-children="是" un-checked-children="否" />
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { queryHypervisor } from '@/api/backend/devops/index';
import { selectFlavorList } from '@/api/backend/devops/flavor';
import { liveMigrateServer } from '@/api/backend/devops/server';
import { message } from 'ant-design-vue';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import emiter from '@/utils/Bus';
import router from '@/router';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist']);
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
            }
        }
    }
})
const defaultform = {
    serverID:'',
    host:'',
    destHost:undefined,
    diskOverCommit:false,
    blockMigration:false
};
const migrateform = reactive({
    serverID:'',
    host:'',
    destHost:undefined,
    diskOverCommit:false,
    blockMigration:false
})
const migrateForm = ref();
const flavorId = ref('');
const flavorlist = ref([]);
const hostlist = ref([]);
const rules = {
    destHost:[{required:true, message:'请输入新的虚机类型',trigger:'change'}],
}
const setinfo = (record) => {
    migrateform.serverID = record.id;
    migrateform.host = record.host;
    QueryHypervisor()
    // flavorId.value = record.flavorEntity?.name;
}
const cancel = () => {
    props.info.isShow = false;
    Object.assign(migrateform, defaultform);
}
const save = () => {
    migrateForm.value.validate().then(async ()=>{
        migrateform.diskOverCommit = migrateform.diskOverCommit ? 1 : 0;
        migrateform.blockMigration = migrateform.blockMigration ? 1 : 0;
        let res = await liveMigrateServer(migrateform);
        if(res.code == 0 && res.data !== false){
            message.success('热迁移成功');
            emit('getlist');
            cancel()
        }else if(res.code == 0){
            message.error(((!res.msg || res.msg == 'success') ? '热迁移失败' : res.msg), 5);
        }
    })
    
}
const QueryHypervisor = async () => {
    let res = await queryHypervisor({cloudId:router.currentRoute.value.query.cloudId,projectId: router.currentRoute.value.query.projectId})
    if(res.code == 0)
    hostlist.value = res.data;
}
const getFlavorlist = async () => {
    let res = await selectFlavorList({cloudId:router.currentRoute.value.query.cloudId,projectId: router.currentRoute.value.query.projectId})
    if(res.code == 0)
    flavorlist.value = res.data;
}
onMounted(() => {})
defineExpose({setinfo})
</script>
<style lang='scss' scoped>
</style>