<template>
    <div class=''>
        <a-modal title="修改管理员密码" v-model:visible="modalinfo.isShow" @ok="handleSave" ok-text="提交" @cancel="cancel" :getContainer="modalBindNode">
            <a-form ref="passForm" :model="passform" :rules="rules" :label-col="{span:5}">
                <a-form-item label="管理员密码" name="password">
                    <a-input-password v-model:value="passform.password" placeholder="请输入" />
                </a-form-item>
                <a-form-item label="确认密码" name="confirm">
                    <a-input-password v-model:value="passform.confirm" placeholder="请输入" />
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { sysserver_changeAdminPassword } from '@/api/backend/devops/server';
import { message } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const props = defineProps({
    modalinfo:{
        type:Object,
        default(){
            return {
                isShow:false
            }
        }
    }
})
const passForm = ref();
const defaultform = {
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    serverId:0,
    password:''
}
const passform = reactive({
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    serverId:0,
    password:''
})
const validatepass: any = async (rule: any, value: any) => {
  if (value === undefined || value === '') {
    return Promise.reject("请输入密码");
  }
    const pwdRegex = new RegExp("(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{6,16}$");
    if (value === "") {
      return Promise.reject("请输入密码");
    } else if (value.length < 4 || value.length > 18 || !pwdRegex.test(value)) {
      return Promise.reject("密码复杂度太低（密码中必须包含字母、数字、特殊字符,长度为4-18位），请重新设置密码！");
    } else {
      if (passform.confirm !== '') {
          passForm.value.validateFields('confirm');
        }
      return Promise.resolve();
    }
};
const validateIspass: any = async (rule: any, value: any, callback: any) => {
  if (value === "" || value === undefined) {
    return Promise.reject("请输入确认密码");
  } else if (value !== passform.password) {
    return Promise.reject("确认密码与密码不一致");
  } else {
      return Promise.resolve();
  }
};
const rules = { 
        password:[{ required: true, validator: validatepass, trigger: "change" }],
        confirm: [{required: true, validator: validateIspass, trigger: "change" }],
    
}
const cancel = () => {
    passForm.value.resetFields();
    Object.assign(passform,defaultform);
    props.modalinfo.isShow = false;
}
const handleSave = () => {
    passForm.value.validate().then(()=>{
        let passform1 = {...passform};
        passform1.confirm = null;
        console.log("passform1",passform1)
        sysserver_changeAdminPassword(passform1).then((res)=>{
            if(res.code == 0){
                message.success("管理员密码操作成功");
                cancel();
            }
        })
    })
    
}
onMounted(() => {})
defineExpose({passform})
</script>
<style lang='scss' scoped>
</style>