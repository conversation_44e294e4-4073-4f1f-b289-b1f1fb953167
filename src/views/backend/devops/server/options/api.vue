<template>
    <div class=''>
        <a-modal :title="info.isAdd ? '连接接口': '分离接口'" v-model:visible="info.isShow" ok-text="提交" @ok="save" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form :model="apiform" ref="apiForm" :rules="rules" :label-col="{span:6}">
                <a-form-item label="指定一个端口的方法" name="type" v-if="info.isAdd">
                    <a-select v-model:value="apiform.type" @change="changeType" :getPopupContainer="triggerNode => triggerNode.parentNode">
                        <a-select-option value="0">按端口</a-select-option>
                        <a-select-option value="1">按网络</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="网络" name="networkId" v-if="info.isAdd">
                    <a-select v-model:value="apiform.networkId" @change="changeNet" placeholder="请选择网络" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in netlist" :key="index" :value="item.id">{{item.networkName}}</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="Server Address" name="portId" v-if="info.isAdd && apiform.type == '0'">
                    <a-select v-model:value="apiform.portId" placeholder="请选择 Server Address" :disabled="!apiform.networkId" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in portlist" :key="index" :value="item.id">{{item.portName + ' (' + ((item.portIpEntityList && item.portIpEntityList[0]) ? item.portIpEntityList[0].ipAddress : '') + ')'}}</a-select-option>
                    </a-select>
                </a-form-item>

                <a-form-item label="子网" name="subnetId" v-if="info.isAdd && apiform.type == '1'">
                    <a-select v-model:value="apiform.subnetId" placeholder="请选择 Server Address" :disabled="!apiform.networkId" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in subnetlist" :key="index" :value="item.id">{{item.cidr + ' (' + item.subnetName + ')'}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="Server Address" name="ipAddress" v-if="info.isAdd && apiform.type == '1'">
                    <a-input v-model:value="apiform.ipAddress" placeholder="请输入 IP Address" allow-clear></a-input>
                </a-form-item>

                <a-form-item label="Server Address" name="portId" v-if="!info.isAdd && apiform.type == '0'">
                    <a-select v-model:value="apiform.portId" placeholder="请选择 Server Address" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in portlist" :key="index" :value="item.portId">{{item.addr + ' (' + item.networkName + ')'}}</a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { selectNetworkList, selectPortList, selectSubnetList } from '@/api/backend/devops/network';
import { bindAPI, unbindAPI } from '@/api/backend/devops/server';
import { message } from 'ant-design-vue';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import emiter from "@/utils/Bus";
const route = useRoute()
const {proxy} = getCurrentInstance()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})
const netlist = ref([]);
const portlist = ref([]);
const subnetlist = ref([]);
const apiForm = ref();
const apiform = reactive({
    serverId:0,
    networkId:undefined,
    portId:undefined,
    subnetId:undefined,
    ipAddress:'',
    type: '0'
})
const rules = reactive({
    networkId:[{type:'number',required:true,message:'请选择网络',trigger:'change'}],
    portId:[{type:'number',required:true,message:'请选择端口',trigger:'change'}],
    subnetId:[{type:'number',required:true,message:'请选择子网',trigger:'change'}],
    ipAddress:[{required:false,message:'请输入 IP Address',trigger:'change'}]
})
const changeType = () => {
    apiform.networkId = undefined;
}
const changeNet = (e) => {
    apiform.portId = undefined;
    if(e && apiform.type == '0')
    selectPortlist(e);
    apiform.subnetId = undefined;
    if(e && apiform.type == '1')
    selectSubnetlist(e);
    // if(apiform.type == '0'){
    // }else if(apiform.type == '1'){
    // }
}
const selectNetworklist = async () => {
    let res = await selectNetworkList({cloudId:route.query.cloudId,projectId: route.query.projectId})
    if(res.code == 0){
        netlist.value = res.data;
    }
}
const selectPortlist = async (networkId) => {
    let res = await selectPortList({networkId,cloudId:route.query.cloudId,projectId: route.query.projectId})
    if(res){
        if(res.code == 0){
            portlist.value = res.data.filter((item,index)=>item.deviceOwner == '');
        }
    }
}
const selectSubnetlist = async (networkId) => {
    apiform.subnetId = undefined;
    let res = await selectSubnetList({networkId,cloudId:route.query.cloudId,projectId: route.query.projectId});
    if(res.code == 0){
        subnetlist.value = res.data;
    }
}
const cancel = () => {
    apiForm.value.resetFields();
    props.info.isShow = false;
    Object.assign(apiform,{
        serverId:0,
        networkId:undefined,
        portId:undefined,
        subnetId:undefined,
        ipAddress:'',
        type: '0'
    })
}
const save = () => {    
    apiForm.value.validate().then(async()=>{
        let apiform1 = {...apiform};
        console.log('apiform1.type',apiform1.type)
        if(apiform1.type == '0'){
            apiform1.subnetId = undefined;
            apiform1.ipAddress = undefined;
        }else if(apiform1.type == '1'){
            apiform1.portId = undefined;
        }
        apiform1.type = '0';
        // apiform1.networkId = undefined;
        if(props.info.isAdd){
            let res = await bindAPI(apiform1)
            if(res.code == 0 && res.data !== false){
                message.success('连接接口成功');
                emit('getlist')
                cancel();
            }else if(res.code == 0){
                message.error((!res.msg || res.msg == 'success') ? '连接接口失败' : res.msg);
            }
        }else{
            let res = await unbindAPI(apiform1)
            if(res.code == 0 && res.data !== false){
                message.success('分离接口成功');
                emit('getlist')
                cancel();
            }else if(res.code == 0){
                message.error((!res.msg || res.msg == 'success') ? '分离接口失败' : res.msg);
            }
        }
        
    })
}
const setInfo = (action,record) => {
    apiform.serverId = record.id;
    if(action == 'BINDAPI')
    selectNetworklist()
    else{
        portlist.value = record.serverAddressEntityList;

    }
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>