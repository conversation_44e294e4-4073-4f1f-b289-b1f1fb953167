<template>
    <div class=''>
        <a-modal 
        :title="info.isInfo ? '查看' : (info.isAdd ? '新增':'修改')"
        v-model:visible="info.isShow"
        @cancel="cancel"
        :maskClosable="info.isInfo"
        centered
        body-style="height:590px;overflow-y:auto"
        :getContainer="modalBindNode"
        >
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="subnetform" ref="subnetForm" :rules="rules" :labelCol="{span:5}">
                <a-form-item label="ID" name="thirdSubnetId" v-if="info.isInfo">
                    {{subnetform.thirdSubnetId}}
                </a-form-item>
                <a-form-item label="子网名称" name="subnetName">
                    <a-input v-model:value.trim="subnetform.subnetName" placeholder="请输入" :disabled="info.isInfo" allowClear></a-input>
                </a-form-item>
                <a-form-item name="cidr">
                    <template #label>
                        <a-tooltip title="CIDR格式的网络地址(例如***********/24,2O01:DB8::/48)">
                            网络地址 <QuestionCircleOutlined style="font-size:12px" />
                        </a-tooltip>
                    </template>
                    <a-input v-model:value.trim="subnetform.cidr" placeholder="请输入" :disabled="info.isInfo || !info.isAdd" allowClear></a-input>
                </a-form-item>
                <a-form-item label="IP版本" name="ipVersion">
                    <a-select v-model:value="subnetform.ipVersion" :disabled="info.isInfo" placeholder="请选择" :getPopupContainer="triggerNode => triggerNode.parentNode" allowClear>
                        <a-select-option value="V4">IPv4</a-select-option>
                        <a-select-option value="V6">IPv6</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="是否使用网关" name="disabled">
                    <a-switch v-model:checked="subnetform.disabled" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                </a-form-item>
                <a-form-item label="网关IP" name="gateway" v-if="subnetform.disabled">
                    <a-input v-model:value.trim="subnetform.gateway" :disabled="info.isInfo" placeholder="请输入" allowClear></a-input>
                </a-form-item>
                <a-form-item label="是否激活DHCP" name="enableDhcp">
                    <a-switch v-model:checked="subnetform.enableDhcp" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                </a-form-item>
                <a-form-item name="poolEntityList">
                    <template #label>
                        分配地址池&nbsp;
                        <a-tooltip>
                            <template #title>
                                IP地址分配池，每条记录是：<br>开始IP~结束IP
                            </template>
                            <QuestionCircleOutlined style="font-size:12px" />
                        </a-tooltip>
                    </template>
                    <a-space
                    v-for="(url, index) in subnetform.poolEntityList"
                    :key="index"
                    style="display: flex;"
                    align="baseline"
                    :size="29"
                    >
                        <a-form-item
                            :name="['poolEntityList', index, 'startIp']"
                            :rules="{
                            required: true,
                            message: '未填写开始IP',
                            }"
                        >
                            <a-input v-model:value="url.startIp" size="small" placeholder="开始IP" :disabled="info.isInfo"/>
                        </a-form-item>
                        ~
                        <a-form-item
                            :name="['poolEntityList', index, 'endIp']"
                            :rules="{
                            required: true,
                            message: '未填写结束IP',
                            }"
                        >
                            <a-input v-model:value="url.endIp" size="small" placeholder="结束IP" :disabled="info.isInfo"/>
                        </a-form-item>
                        <MinusCircleOutlined @click="removeUrl(url)" v-if="!info.isInfo" />
                    </a-space>
                    <a-form-item v-if="!info.isInfo">
                        <a-button type="dashed" size="small" block @click="addUrl">
                            <PlusOutlined />
                        </a-button>
                    </a-form-item>
                </a-form-item>
                <a-form-item name="dnsNameservers">
                    <template #label>
                        DNS服务器&nbsp;
                        <a-tooltip title="该子网的DNS服务器IP地址列表，多条数据使用英文逗号分隔">
                            <QuestionCircleOutlined style="font-size:12px" />
                        </a-tooltip>
                    </template>
                    <a-textarea v-model:value="subnetform.dnsNameservers" placeholder="该子网的DNS服务器IP地址列表，多条数据使用英文逗号分隔" :disabled="info.isInfo"></a-textarea>
                </a-form-item>
                <a-form-item name="hostRouteList">
                    <template #label>
                        虚机路由&nbsp;
                        <a-tooltip>
                            <template #title>
                                虚机增加额外的路由，记录格式是：<br>目的CIDR~下一跳
                            </template>
                            <QuestionCircleOutlined style="font-size:12px" />
                        </a-tooltip>
                    </template>
                    <a-space
                    v-for="(router, index) in subnetform.hostRouteList"
                    :key="index"
                    style="display: flex;"
                    align="baseline"
                    :size="29"
                    >
                        <a-form-item
                            :name="['hostRouteList', index, 'destination']"
                            :rules="{
                            required: true,
                            message: '未填写目的CIDR',
                            }"
                        >
                            <a-input v-model:value="router.destination" size="small" placeholder="目的CIDR" :disabled="info.isInfo"/>
                        </a-form-item>
                        ~
                        <a-form-item
                            :name="['hostRouteList', index, 'nextHop']"
                            :rules="{
                            required: true,
                            message: '未填写下一跳',
                            }"
                        >
                            <a-input v-model:value="router.nextHop" size="small" placeholder="下一跳" :disabled="info.isInfo"/>
                        </a-form-item>
                        <MinusCircleOutlined @click="removeRouter(router)" v-if="!info.isInfo" />
                    </a-space>
                    <a-form-item v-if="!info.isInfo">
                        <a-button type="dashed" size="small" block @click="addRouter">
                            <PlusOutlined />
                        </a-button>
                    </a-form-item>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {saveSubnet,updateSubnet } from "@/api/backend/devops/network"
import { selectDictList } from '@/api/backend/systems/dictionary';
import { useRoute } from 'vue-router';
import router from '@/router';
const {proxy} = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    },
    id:{
        type:Number,
        default(){
            return 0
        }
    },
})

const subnetForm = ref();
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    networkId:props.id,
    subnetName:'',
    cidr:'',
    ipVersion:undefined,
    disabled:true,
    gateway:'',
    enableDhcp:true,
    poolEntityList:[],
    hostRouteList:[],
    dnsNameservers:''
}
const subnetform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    networkId:props.id,
    subnetName:'',
    cidr:'',
    ipVersion:undefined,
    disabled:true,
    gateway:'',
    enableDhcp:true,
    poolEntityList:[],
    hostRouteList:[],
    dnsNameservers:''
})
const rules = {
    subnetName:[{required:true, message:'请输入',trigger:'change'}],
    cidr:[{required:true, message:'请输入',trigger:'change'}],
}
const addUrl = () => {
  subnetform.poolEntityList.push({
    startIp: '',
    endIp: '',
    cloudId:route.query.cloudId
  });
};
const removeUrl = item => {
  let index = subnetform.poolEntityList.indexOf(item);
  if (index !== -1) {
    subnetform.poolEntityList.splice(index, 1);
  }
};
const addRouter = () => {
  subnetform.hostRouteList.push({
    destination: '',
    nextHop: '',
    cloudId:route.query.cloudId
  });
};
const removeRouter = item => {
  let index = subnetform.hostRouteList.indexOf(item);
  if (index !== -1) {
    subnetform.hostRouteList.splice(index, 1);
  }
};
const handleSave = () => {
    let subnetform1 = {...subnetform}
    proxy.$handleSave(subnetForm.value, saveSubnet, updateSubnet, props,subnetform1, ()=>{cancel();emit('getlist',subnetform1.networkId)},null,()=>{
        if(subnetform1.disabled == true)
            subnetform1.disabled = 1;
        else{
            subnetform1.disabled = 0;
            subnetform1.gateway = null;
        }
        subnetform1.enableDhcp = Number(subnetform1.enableDhcp)
    })
}
const cancel = () => {
    props.info.isShow = false
    props.info.isInfo = false
    // isInfo.value = false
    let resetlist = ['subnetName'];
    subnetform.poolEntityList.forEach((item,index)=>{
        resetlist.push(['poolEntityList', index, 'startIp'])
        resetlist.push(['poolEntityList', index, 'endIp'])
    })
    subnetform.hostRouteList.forEach((item,index)=>{
        resetlist.push(['hostRouteList', index, 'destination'])
        resetlist.push(['hostRouteList', index, 'nextHop'])
    })
    subnetForm.value.resetFields(resetlist)
    Object.assign(subnetform,JSON.parse(JSON.stringify(defaultform)))
}
onMounted(() => {})
defineExpose({subnetform})
</script>
<style lang='scss' scoped>
:deep(.ant-space-item .ant-form-item){
    margin-bottom: 2px;
}
:deep(.ant-form-item-control-input-content .ant-form-item){
    margin-bottom: 2px;
}
</style>