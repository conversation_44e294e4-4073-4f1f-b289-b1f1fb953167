<template>
    <div class=''>
        <a-modal 
        :title="info.isInfo ? '查看' : (info.isAdd ? '新增':'修改')"
        v-model:visible="info.isShow"
        @cancel="cancel"
        :maskClosable="info.isInfo"
        centered
        :getContainer="modalBindNode"
        >
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="portform" ref="portForm" :rules="rules" :labelCol="{span:6}">
            <a-tabs type="card" v-model:activeKey="activeKey">
                <a-tab-pane key="1" tab="信息">
                        <a-form-item label="端口名称" name="portName">
                            <a-input v-model:value.trim="portform.portName" placeholder="请输入" :disabled="info.isInfo" allowClear></a-input>
                        </a-form-item>
                        <a-form-item label="设备ID" name="deviceId" v-if="info.isInfo || info.isAdd">
                            <a-input v-model:value.trim="portform.deviceId" placeholder="请输入" :disabled="info.isInfo" allowClear></a-input>
                        </a-form-item>
                        <a-form-item label="设备所属者" name="deviceOwner" v-if="info.isInfo || info.isAdd">
                            <a-input v-model:value.trim="portform.deviceOwner" placeholder="请输入" :disabled="info.isInfo" allowClear></a-input>
                        </a-form-item>
                        <a-form-item label="指定IP地址或子网" v-if="info.isInfo || info.isAdd">
                            <a-select v-model:value="flagValue" @change="changeFlag" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" placeholder="请选择" allowClear>
                                <a-select-option :value="0">自动分配IP地址</a-select-option>
                                <a-select-option :value="1">子网</a-select-option>
                                <a-select-option :value="2">IP地址</a-select-option>
                                <a-select-option :value="3">无IP地址</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="子网" name="subnetId" v-if="flagValue == 1">
                            <a-select v-model:value="portform.subnetId" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" placeholder="请选择" allowClear>
                                <a-select-option v-for="(item,index) in sublist" :key="index" :value="item.id">{{item.subnetName}}</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="IP地址" name="ip" v-if="flagValue == 2">
                            <a-input v-model:value.trim="portform.ip" :disabled="info.isInfo" placeholder="请输入" allowClear></a-input>
                        </a-form-item>
                        <a-form-item label="MAC地址" name="macAddress" v-if="info.isInfo || info.isAdd">
                            <a-input v-model:value.trim="portform.macAddress" :disabled="info.isInfo" placeholder="请输入" allowClear></a-input>
                        </a-form-item>
                        <a-form-item label="VNIC类型" name="vnicType">
                            <a-select v-model:value="portform.vnicType" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" placeholder="请选择">
                                <a-select-option value="normal">正常</a-select-option>
                                <a-select-option value="direct">直连</a-select-option>
                                <a-select-option value="direct-physical">直通物理硬件</a-select-option>
                                <a-select-option value="macvtap">MacVTap</a-select-option>
                                <a-select-option value="baremetal">裸机</a-select-option>
                                <a-select-option value="virtio-forwarder">Virtio转发器</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="是否启用管理员状态" name="adminStateUp">
                            <a-switch v-model:checked="portform.adminStateUp" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                        </a-form-item>
                        <a-form-item label="端口安全" name="portSecurityEnabled">
                            <a-switch v-model:checked="portform.portSecurityEnabled" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                        </a-form-item>
                    
                </a-tab-pane>
                <a-tab-pane key="2" tab="安全组" v-if="portform.portSecurityEnabled">
                    <a-form-item name="securityGroupIds" >
                        <a-table :data-source="seculist" :columns="columns4" row-key="id" :scroll="{ y: 400 }" :row-selection="{selectedRowKeys:portform.securityGroupIds,onChange:onSelectChange,columnTitle:'单选',getCheckboxProps: (record) => ({disabled: info.isInfo})}" size="small" :pagination="false" bordered></a-table>
                        <!-- <a-table bordered></a-table> -->
                    </a-form-item>
                </a-tab-pane>
            </a-tabs>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {savePort,selectSubnetList,updatePort } from "@/api/backend/devops/network"
import { selectSecugroupList } from '@/api/backend/devops/security';
import { selectDictList } from '@/api/backend/systems/dictionary';
import { useRoute } from 'vue-router';
import router from '@/router';
const {proxy} = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    },
    id:{
        type:Number,
        default(){
            return -1
        }
    },
})

const activeKey = ref('1')
const flagValue = ref(0);
const sublist = ref([])
const seculist = ref([])
const portForm = ref();
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    networkId:props.id,
    noFixedIp:1,
    portName:'',
    deviceId:'',
    deviceOwner:'',
    subnetId:undefined,
    ip:null,
    macAddress:'',
    vnicType:'normal',
    adminStateUp:true,
    portSecurityEnabled:true,
    securityGroupIds:[],
}
const portform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    networkId:props.id,
    noFixedIp:1,
    portName:'',
    deviceId:'',
    deviceOwner:'',
    subnetId:undefined,
    ip:null,
    macAddress:'',
    vnicType:'normal',
    adminStateUp:true,
    portSecurityEnabled:true,
    securityGroupIds:[],
})
const rules = reactive({
    vnicType:[{required:true, message:'请输入',trigger:'change'}],
    portName:[{required:true, message:'请输入',trigger:'change'}],
    macAddress:[{required:false, message:'请输入',trigger:'change'}],
    subnetId:[{required:true, type:'number', message:'请输入',trigger:'change'}],
    ip:[{required:true, message:'请输入',trigger:'change'}],
})

const columns4 = [
    {title: '名称', dataIndex: 'groupName', key: 'id',align:'left'},
    {title: '描述', dataIndex: 'description', key: 'id',align:'center'},
];

const onSelectChange = (e,b) =>{
    portform.securityGroupIds = [e[e.length-1]];
}

const changeFlag = (e) => {
    console.log('e',flagValue.value)
    if(e==1){
        selectSubnetlist()
    }
    if(e == 3){
        portform.noFixedIp = 0;
    }else{
        portform.noFixedIp = 1;
    }
}
const selectSubnetlist = async () => {
    let res = await selectSubnetList({networkId:props.id})
    if(res.code == 0){
        sublist.value = res.data;
    }
}
const selectSecugrouplist = async () => {
    let res = await selectSecugroupList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId})
    if(res.code == 0)
    seculist.value = res.data;
    // defaultGroup()
}
const defaultGroup = () => {
    if(seculist.value.length > 0){
        seculist.value.forEach((item,index) => {
        if(item.groupName == 'default'){
            portform.securityGroupIds = [];
        }
    })
    }
}
const handleSave = () => {
    let portform1 = {...portform}
    proxy.$handleSave(portForm.value, savePort, updatePort, props,portform1, ()=>{cancel();emit('getlist',portform1.networkId)},null,()=>{
        portform1.adminStateUp = portform1.adminStateUp ? 1 : 0;
        portform1.portSecurityEnabled = portform1.portSecurityEnabled ? 1 : 0;
        if(!portform1.macAddress)
            portform1.macAddress = undefined;
    })
}
const cancel = () => {
    props.info.isShow = false
    props.info.isInfo = false
    flagValue.value = 0;
    // isInfo.value = false
    activeKey.value = '1';
    portForm.value.resetFields()
    Object.assign(portform,defaultform)
}
onMounted(() => {})
defineExpose({portform,selectSecugrouplist})
</script>
<style lang='scss' scoped>
:deep(.ant-checkbox-inner){border-radius: 50%;}
:deep(.ant-checkbox-checked){&::after{border: none;}}
</style>