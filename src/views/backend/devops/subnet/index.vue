<template>
    <div class="back-page" v-if="!info2.isInfo">
            <a-page-header
                style="background-color:#fff"
                :title="netinfo.networkName"
                @back="back"
            />
        <a-tabs :active-key="activeKey" @change="changeTab" class="back-content" style="background:#fff" :animated="false">
            <a-tab-pane tab=""></a-tab-pane>
            <a-tab-pane key="2" tab="概览">
                
                <a-descriptions class='subPadding' :column="1" :label-style="{width:'142px',fontWeight:'bold'}">
                    <a-descriptions-item label="ID">{{netinfo.thirdNetworkId}}</a-descriptions-item>
                    <a-descriptions-item label="网络名称">{{netinfo.networkName}}</a-descriptions-item>
                    <a-descriptions-item label="项目">{{netinfo.projectName}}</a-descriptions-item>
                    <a-descriptions-item label="网络类型">
                        {{netinfo.networkType}}
                    </a-descriptions-item>
                    <a-descriptions-item label="物理网络" v-if="isShowPhysical">
                        {{netinfo.physicalNetwork ? netinfo.physicalNetwork : '-'}}
                    </a-descriptions-item>
                    <a-descriptions-item label="段ID" v-if="isShowSeg">
                        {{netinfo.providerSegId ? netinfo.providerSegId : '-'}}
                    </a-descriptions-item>
                    <a-descriptions-item label="网络可用域">
                        <span v-if="!netinfo.sysOpenstackZoneEntityList || netinfo.sysOpenstackZoneEntityList.length <= 0">-</span>
                        <a-tag v-for="(item,index) in netinfo.sysOpenstackZoneEntityList" :key="index">{{item?.zoneName ? item?.zoneName : ''}}</a-tag>
                    </a-descriptions-item>
                    <a-descriptions-item label="状态">{{netinfo.statusText}}</a-descriptions-item>
                    <a-descriptions-item label="是否启用管理员状态">{{netinfo.adminStateUp == 1 ? '是': '否'}}</a-descriptions-item>
                    <a-descriptions-item label="是否共享">{{netinfo.shared == 1 ? '是': '否'}}</a-descriptions-item>
                    <a-descriptions-item label="是否启用外部网络">{{netinfo.routerExternal == 1 ? '是': '否'}}</a-descriptions-item>
                    <a-descriptions-item label="最大传输单元">{{netinfo.mtu === null ? netinfo.mtu : '-'}}</a-descriptions-item>
                    
                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="3" tab="子网" force-render>
                <div class='subPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" style="margin-right: 10px;" @click="handleAdd"> 新增 </a-button>
                    <a-button @click="$handleDel(selectRowIds,deleteSubnet,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                    <!-- <a-button type="primary" @click="()=>{info.isSub=false}" > 返回 </a-button> -->
                </a-row>
                <a-table :row-selection="rowSelection" :columns="columns" row-key="id" :data-source="subnetlist" :loading="loading" :pagination="pagination" @change="changeTable" :scroll="{x:true}">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #disabled={record}>
                        {{record.disabled == 1 ? '是' : '否'}}
                    </template>
                    <template #gateway={record}>
                        {{record.gateway ? record.gateway : '-'}}
                    </template>
                    <template #action={record}>
                        <a-button class="button_V" @click="handleView(record)">查看</a-button>
                        <a-button class="button_E" @click="handleEdit(record)">修改</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteSubnet,getList)">删除</a-button>
                    </template>
                </a-table>
                <Info ref="subnetDialog" :info="info1" @getlist="getList" :id="netinfo.id" />
                </div>
            </a-tab-pane>
            <a-tab-pane key="4" tab="端口" force-render>
                <div class='subPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" style="margin-right: 10px;" @click="handlePortAdd"> 新增 </a-button>
                    <a-button @click="$handleDel(selectRowIds1,deletePort,()=>{selectRowIds1 = [];getPortList()})" :disabled="selectRowIds1.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table :row-selection="rowSelection1" :columns="columns1" row-key="id" :data-source="portlist" :loading="loadingPort" @change="changeTablePort" :pagination="paginationPort" :scroll="{x:true}" :getPopupContainer="triggerNode=>triggerNode.parentNode">
                    <template #index={record,index}>
                        {{index+1+(paginationPort.pageSize * (paginationPort.current-1))}}
                    </template>
                    <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                        <div style="padding: 8px">
                            <a-input
                            ref="searchInput"
                            :placeholder="`请输入${column.title}搜索`"
                            v-model:value="searchformPort.portName"
                            style="width: 188px; margin-bottom: 8px; display: block"
                            @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
                            />
                            <a-button
                            type="primary"
                            size="small"
                            style="width: 90px; margin-right: 8px"
                            @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
                            >
                            <template #icon><SearchOutlined /></template>
                            搜索
                            </a-button>
                            <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                            重置
                            </a-button>
                        </div>
                    </template>
                    <template #filterIcon="filtered">
                        <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
                    </template>
                    <template #portIpEntityList={record,index}>
                        {{record.portIpEntityList?.[0]?.ipAddress}}
                    </template>
                    <template #deviceId="{record}">
                        {{record.deviceId ? record.deviceId : '分离'}}
                    </template>
                    <template #deviceOwner="{record}">
                        {{record.deviceOwner ? record.deviceOwner : '-'}}
                    </template>
                    <template #adminStateUp={record,index}>
                        {{record.adminStateUp === 'true' ? '激活':'未激活'}}
                    </template>
                    <template #state={record,index}>
                        {{record.state}}
                    </template>
                    <template #action={record}>
                        <a-button class="button_V" @click="handlePortView(record)">查看</a-button>
                        <a-button class="button_E" @click="handlePortEdit(record)">修改</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deletePort,getPortList)">删除</a-button>
                    </template>
                </a-table>
                <Port ref="portDialog" :info="info2" @getlist="getPortList" :id="netinfo.id" />
                </div>
            </a-tab-pane>
        </a-tabs>
    </div>
    <PInfo ref="pinfoDialog" v-else :info="info2" @getlist="getPortList" :id="netinfo.id" />
</template>
<script lang='ts' setup>
import Info from "./info.vue";
import Port from "./port.vue";
import PInfo from "./pinfo.vue";
// import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { computed, getCurrentInstance, onMounted, onUnmounted, reactive, ref } from 'vue';
import { getSubnetList, deleteSubnet, selectSubnetList, selectPortList, getPortlist, deletePort, getNetworkInfo } from "@/api/backend/devops/network";
import router from "@/router";
import emiter from "@/utils/Bus";
const { proxy } = getCurrentInstance();
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: false,
        isSub:true
      };
    }
  },
})
const typeControl = reactive({
  "LOCAL":[],
  "FLAT":["physicalNetwork"],
  "VLAN":["physicalNetwork","providerSegId"],
  "VXLAN":["providerSegId"],
  "GRE":["providerSegId"],
  "Geneve":["providerSegId"]
})
const isShowPhysical = ref();
const isShowSeg = ref();
const searchInput = ref();
const searchText = ref('')
const searchedColumn = ref()
const activeKey = ref('2')
const netinfo = reactive({})
const subnetDialog = ref(null);
const portDialog = ref(null);
const pinfoDialog = ref(null);
const loading = ref(false);
const loadingPort = ref(false);
const subnetlist = ref([]);
const portlist = ref([]);
// const id = ref();
const networkName = ref();
let selectRowIds: string[] = ref([]);
let selectRowIds1: string[] = ref([]);
const info1 = reactive({
    isAdd:true,
    isShow:false
})
const info2 = reactive({
    isAdd:true,
    isShow:false,
    isInfo:false
})
const searchform = reactive({
    networkId:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
  beginPort();
};
const searchformPort = reactive({
    networkId:'',
    portName:'',
    pageIndex:1,
    pageSize:10
})
const paginationPort = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTablePort: any = (pagination: any, filters: any, sorter: any) => {
  searchformPort.pageIndex = pagination.current;
  searchformPort.pageSize = pagination.pageSize;
  getPortList();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const rowSelection1 = computed(() => {
  return {
    selectedRowKeys: selectRowIds1.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds1.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '子网名称', dataIndex: 'subnetName', key: 'id',align:'left'},
    {title: '网络地址', dataIndex: 'cidr', key: 'id',align:'left'},
    {title: 'IP版本', dataIndex: 'ipVersion', key: 'id',align:'left'},
    {title: '是否使用网关', dataIndex: 'disabled', slots: { customRender: 'disabled' }, key: 'id',align:'left'},
    {title: '网关', dataIndex: 'gateway', slots: { customRender: 'gateway' }, key: 'id',align:'left'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
const columns1 = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '端口名称', dataIndex: 'portName', key: 'portName',align:'left',slots: {
          filterDropdown: 'filterDropdown',
          filterIcon: 'filterIcon',
        }},
    {title: '固定IP地址', dataIndex: 'portIpEntityList', slots: { customRender: 'portIpEntityList' }, key: 'id',align:'left'},
    {title: 'MAC地址', dataIndex: 'macAddress', key: 'id',align:'left'},
    {title: 'Device', dataIndex: 'deviceId', slots: { customRender: 'deviceId' }, key: 'id',align:'left'},
    {title: 'Device Owner', dataIndex: 'deviceOwner', slots: { customRender: 'deviceOwner' }, key: 'id',align:'left'},
    {title: '管理员状态', dataIndex: 'adminStateUp', slots: { customRender: 'adminStateUp' }, width:110, key: 'id',align:'center'},
    {title: '状态', dataIndex: 'state', slots: { customRender: 'state' }, width:60, key: 'id',align:'left'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200,fixed: 'right' }
];
const back = () => {props.info.isSub=false;clearInterval(timerPort)}
const changeTab = (e) => {
    activeKey.value = e;
    if(e == '3'){
        getList(netinfo.id)
    }
    if(e == '4'){
        getPortList(netinfo.id)
        beginPort();
    }else{
        clearInterval(timerPort);
    }
}
const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    searchformPort.pageIndex = "1";
    searchformPort.pageSize = pagination.pageSize;
    getPortList();
    beginPort();
};
const handleReset = clearFilters => {
    clearFilters();
    searchformPort.pageIndex = "1";
    searchformPort.pageSize = pagination.pageSize;
    searchformPort.portName = '';
    getPortList();
};
const getList = async (Id) => {
    // loading.value = true;
    if(Id)
    searchform.networkId = Id;
    else
    searchform.networkId = netinfo.id;
    // let res = await selectSubnetList({networkId:searchform.networkId})
    // if(res){
    //     if(res.code == 0)
    //         subnetlist.value = res.data;
    //     loading.value = false;
    // }
    
    subnetlist.value = await proxy.$getList(loading, getSubnetList, searchform, pagination, getList,null)
}
const handleView = (record) => {
    info1.isInfo = info1.isShow = true;
    let record1 = {...record}
    if(record1.disabled == 1)
    record1.disabled = true;
    else
    record1.disabled = false;
    record1.enableDhcp = Boolean(record1.enableDhcp);
    Object.assign(subnetDialog.value.subnetform, record1)
}
const handleAdd = () => {
    info1.isAdd = info1.isShow = true;
    subnetDialog.value.subnetform.networkId = netinfo.id;
}
const handleEdit = (record) => {
    info1.isAdd = false;
    info1.isShow = true;
    let record1 = {...record}
    if(record1.disabled == 1)
    record1.disabled = true;
    else
    record1.disabled = false;
    record1.enableDhcp = Boolean(record1.enableDhcp);
    Object.assign(subnetDialog.value.subnetform, record1)
}


const getPortList = async (Id,isUnLoading) => {
    searchformPort.projectId = router.currentRoute.value.query.projectId;
    if(Id)
    searchformPort.networkId = Id;
    else
    searchformPort.networkId = netinfo.id;
    // let res = await selectPortList({networkId:searchformPort.networkId})
    // if(res){
    //     if(res.code == 0)
    //         portlist.value = res.data;
    // }
    loadingPort.value = !isUnLoading;
    portlist.value = await proxy.$getList({value:false}, getPortlist, searchformPort, paginationPort, getPortList,null )
    loadingPort.value = false;
}
const handlePortView = (record) => {
    info2.isInfo = true;
    info2.isShow = info2.isAdd = false;
    let record1 = {...record}
    record1.securityGroupIds = record1.securityGroupEntityList;
    record1.adminStateUp = (record1.adminStateUp === 'true') ? true : false;
    record1.portSecurityEnabled = (record1.portSecurityEnabled ==1) ? true : false;
    record1.networkName = netinfo.networkName;
    proxy.$nextTick(()=>{
        Object.assign(pinfoDialog.value.portform, record1)
    })
}
const handlePortAdd = () => {
    info2.isAdd = info2.isShow = true;
    info2.isInfo = false;
    proxy.$nextTick(()=>{
        portDialog.value.selectSecugrouplist();
        portDialog.value.portform.networkId = netinfo.id;
    })
    
}
const handlePortEdit = (record) => {
    info2.isAdd = false;
    info2.isShow = true;
    info2.isInfo = false;
    // let record1 = {...record}
    
    let {
        id,
    cloudId,
    networkId,
    portName,
    deviceId,
    deviceOwner,
    subnetId,
    ip,
    macAddress,
    vnicType,
    adminStateUp,
    portSecurityEnabled,
    securityGroupIds,
} = record;
let record1 = {
    id,
    cloudId,
    networkId,
    portName,
    deviceId,
    deviceOwner,
    subnetId,
    ip,
    macAddress,
    vnicType,
    adminStateUp,
    portSecurityEnabled,
    securityGroupIds,
}
    if(record.securityGroupEntityList){
        let securityGroupIdsTemp = [];
        record.securityGroupEntityList.forEach((item,index)=>{
            securityGroupIdsTemp.push(item.id);
        })
        record1.securityGroupIds = securityGroupIdsTemp;
    }
    record1.adminStateUp = (record.adminStateUp === 'true') ? true : false;
    record1.portSecurityEnabled = (record.portSecurityEnabled ==1) ? true : false;
    Object.assign(portDialog.value.portform, record1)
    proxy.$nextTick(()=>{
        portDialog.value.selectSecugrouplist();
        console.log("netinfo.id",netinfo.id)
        portDialog.value.portform.networkId = netinfo.id;
    })
}
const setInfo = async (record,id) => {
    if(id){
        let res = await getNetworkInfo({id})
        if(res.code == 0){
            Object.assign(netinfo,res.data);
        }
    }else if(record){
        Object.assign(netinfo,record);
    }
    isShowPhysical.value = typeControl[netinfo.networkType].includes('physicalNetwork')
    isShowSeg.value = typeControl[netinfo.networkType].includes('providerSegId')

}
var timerPort = null;
const beginPort = () => {
    clearInterval(timerPort);
    if(!searchform.portName){
        timerPort = setInterval(()=>{
            getPortList(searchformPort.networkId,true);
        },60000)
    }
}
onMounted(() => {})
onUnmounted(()=>{
    clearInterval(timerPort);
})
defineExpose({getPortList,netinfo,setInfo})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
.subPadding{
    padding: 0 20px 20px;
    background-color: #ffffff;
    // height: calc(100vh - 136px);
    overflow-y: auto;
    margin: 0 16px;
    .buttonGroup{margin-bottom: 10px;}
    :deep(.ant-table th){ white-space: nowrap; }
    :deep(.ant-table td){ white-space: nowrap; }
}
.back-content{padding: 0;height: 100%;overflow-y: initial;}
:deep(.ant-tabs .ant-tabs-top-content){
      height: calc(100vh - 230px);
    overflow-y: scroll;
}
.ant-descriptions{width: 500px;}
:deep(.ant-descriptions-item-container .ant-descriptions-item-label){justify-content: flex-end;}
:deep(.ant-descriptions-item-content){justify-content: start;}
</style>