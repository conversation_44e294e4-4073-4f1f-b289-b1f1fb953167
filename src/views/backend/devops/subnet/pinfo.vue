<template>
    <div class='back-page'>
        <a-page-header :title="portform.portName" @back="()=>{info.isInfo=false}" style="background-color:#fff"></a-page-header>
        <div class="back-content">
            <a-descriptions class='' :column="1" :label-style="{width:'142px',fontWeight:'bold'}">
                <a-descriptions-item label="端口名称">{{portform.portName}}</a-descriptions-item>
                <!-- <a-descriptions-item label="id">{{portform.projectName}}</a-descriptions-item> -->
                <a-descriptions-item label="网络名称">{{portform.networkName}}</a-descriptions-item>
                <a-descriptions-item label="网络ID">{{portform.thirdNetworkId}}</a-descriptions-item>
                <a-descriptions-item label="MAC地址">{{portform.macAddress}}</a-descriptions-item>
                <a-descriptions-item label="状态">{{portform.state}}</a-descriptions-item>
                <a-descriptions-item label="管理状态">{{portform.adminStateUp == 1 ? '激活': '未激活'}}</a-descriptions-item>
                <a-descriptions-item label="安全性是否开启">{{portform.portSecurityEnabled == 1 ? '是': '否'}}</a-descriptions-item>
                <!-- <a-descriptions-item label="DNS名称">{{portform.routerExternal == 1 ? '是': '否'}}</a-descriptions-item> -->
            </a-descriptions>
            <a-divider></a-divider>
            <a-descriptions title="固定IP" class='' :column="1" :label-style="{width:'142px',fontWeight:'bold'}">
                <a-descriptions-item label="IP地址" v-if="portform.portIpEntityList?.[0]?.ipAddress">{{portform.portIpEntityList?.[0]?.ipAddress}}</a-descriptions-item>
                <a-descriptions-item label="" class="no-content" v-else>无</a-descriptions-item>
                <!-- <a-descriptions-item label="子网ID">{{pportform.portIpEntityList?.}}</a-descriptions-item> -->
            </a-descriptions>
            <a-divider></a-divider>
            <a-descriptions title="连接设备" class='' :column="1" :label-style="{width:'142px',fontWeight:'bold'}">
                <a-descriptions-item label="设备所属者">{{portform.deviceOwner ? portform.deviceOwner : '-'}}</a-descriptions-item>
                <a-descriptions-item label="设备ID">{{portform.deviceId ? portform.deviceId : '分离'}}</a-descriptions-item>
            </a-descriptions>
            <a-divider></a-divider>
            <a-descriptions title="安全组" class='' :column="1" :label-style="{width:'142px',fontWeight:'bold'}">
                <a-descriptions-item :label="portform.securityGroupEntityList?.[0]?.groupName" v-if="portform.securityGroupEntityList?.length > 0">
                    <div v-if="portform.securityGroupEntityList?.[0]?.ruleEntityList && portform.securityGroupEntityList?.[0]?.ruleEntityList.length > 0">
                        <template v-for="(item,index) in portform.securityGroupEntityList?.[0]?.ruleEntityList">
                            <div>允许 {{item.ethertype}} {{item.ipProtocol != 'null' ? (item.ipProtocol + ' from') : 'to'}} {{item.ipProtocol != 'null' ? (item.formPort == 'null' ? ethertypeObj[item.ethertype] : item.formPort) : (item.toPort == 'null' ? ethertypeObj[item.ethertype] : item.toPort) }}</div>
                        </template>
                    </div>
                    <span v-else>无</span>
                </a-descriptions-item>
                <a-descriptions-item label="" class="no-content" v-else>无</a-descriptions-item>
            </a-descriptions>
            <a-divider></a-divider>
            <a-descriptions title="绑定" class='' :column="1" :label-style="{width:'142px',fontWeight:'bold'}">
                <a-descriptions-item label="VNIC类型">{{portform.vnicType}}</a-descriptions-item>
                <!-- <a-descriptions-item label="主机">{{portform.deviceId}}</a-descriptions-item> -->
                <a-descriptions-item label="VIF类型">{{portform.vifType}}</a-descriptions-item>
                <a-descriptions-item label="VIF详情" v-if="portform.vifDetailEntityList?.length > 0">
                    <a-descriptions>
                        <a-descriptions-item label="桥">{{portform.vifDetailEntityList?.[0]?.bridgeName}}</a-descriptions-item>
                        <a-descriptions-item label="连通度">{{portform.vifDetailEntityList?.[0]?.connectivity}}</a-descriptions-item>
                        <a-descriptions-item label="端口拦截">{{portform.vifDetailEntityList?.[0]?.portFilter == 'true' ? '是':'否'}}</a-descriptions-item>
                        <a-descriptions-item label="ovs Hybrid Plug">{{portform.vifDetailEntityList?.[0]?.ovsHybridPlug}}</a-descriptions-item>
                        <a-descriptions-item label="datapath类型">{{portform.vifDetailEntityList?.[0]?.datapathType}}</a-descriptions-item>
                    </a-descriptions>
                </a-descriptions-item>
            </a-descriptions>
        </div>
        
        
    </div>
</template>
<script lang='ts' setup>
import { onMounted, reactive, ref } from 'vue';
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:false,
                isShow:false,
                isInfo:false
            }
        }
    }
})
const ethertypeObj = {
    'IPv4': '0.0.0.0/0',
    'IPv6': '::/0'
}
const portform = reactive({})
onMounted(() => {})
defineExpose({portform})
</script>
<style lang='scss' scoped>
:deep(.no-content .ant-descriptions-item-content){margin-left: 142px;}
.back-content{
    height: calc(100vh - 168px);
}
</style>