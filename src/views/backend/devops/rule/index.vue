<template>
    <!-- <div class="cloudContent"> -->
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="uncloudRight">
            <!-- <div class="buttonPadding">
                <a-form layout="inline" :model="searchform">
                <a-form-item label="安全组名称">
                    <a-input placeholder="请输入" v-model:value="searchform.ruleName" allowClear />
                </a-form-item>
                <a-form-item>
                    <a-button type="primary" @click="handleSearch"> {{ $t("m.search") }} </a-button>
                </a-form-item>
                <a-form-item>
                    <a-button @click="handleAllReset"> {{ $t("m.reset") }} </a-button>
                </a-form-item>
                </a-form>
            </div> -->
            <!-- <div class='innerPadding'> -->
                <!-- <a-page-header :title="groupName"></a-page-header> -->
                <a-page-header
                class="back-header1"
                :title="groupName"
                @back="()=>{info.isRule = false}"
            />
                <!-- <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" style="margin-right:10px" @click="handleAdd">新增 </a-button>
                    <a-button type="primary" @click="()=>{info.isRule = false}" > 返回 </a-button>
                </a-row> -->
                <!-- <a-divider></a-divider> -->
                <div class="back-content">
                    <a-row class="buttonGroup">
                        <a-button type="primary" class="btnMargin" style="margin-right:10px" @click="handleAdd">新增 </a-button>
                        <a-button @click="$handleDel(selectRowIds,deleteSecurule,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                            批量删除
                        </a-button>
                        <!-- <a-button type="primary" @click="()=>{info.isRule = false}" > 返回 </a-button> -->
                    </a-row>
                <a-table :row-selection="rowSelection" :columns="columns" row-key="id" :scroll="{ x: true }" :data-source="securulelist" :pagination="false" @change="changeTable">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #direction={record,index}>
                        {{record.direction.toLowerCase() == 'null' ? '-' : (record.direction.toLowerCase() == 'egress' ? '出口':'入口')}}
                    </template>
                    <template #ipProtocol={record,index}>
                        {{record.ipProtocol == 'null' ? '任何' : record.ipProtocol}}
                    </template>
                    <template #portrange={record,index}>
                        {{(record.formPort == 'null' || record.toPort == 'null') ? '任何' : ((record.formPort == record.toPort) ? record.formPort : (record.formPort + '-' + record.toPort))}}
                    </template>
                    <template #remoteIpPrefix="{record,index}">
                        {{record.remoteIpPrefix == 'null' ? (record.ipProtocol == 'null' ? ethertypeObj[record.ethertype] : '-') : record.remoteIpPrefix}}
                    </template>
                    <template v-for="col in ['remoteGroupId', 'description']" #[col]="{ text, record }" :key="col">
                        {{ (text == 'null' || text == '') ? '-' : text}}
                    </template>
                    <!-- <template #remoteIpPrefix={record,index}>
                        {{record.remoteIpPrefix == 'null' ? '-' : record.remoteIpPrefix }}
                    </template>
                    <template #remoteGroupId={record,index}>
                        {{record.remoteGroupId == 'null' ? '-' : record.remoteGroupId }}
                    </template>
                    <template #description={record,index}>
                        {{record.description == 'null' ? '-' : record.description }}
                    </template> -->
                    <template #action={record}>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteSecurule,getList)">删除</a-button>
                    </template>
                </a-table>
                </div>
                
                <Info ref="securuleDialog" :info="info1" @getlist="getList" />
            </div>
        <!-- </div> -->
    <!-- </div> -->
</template>
<script lang='ts' setup>
import Info from "./info.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { getSecuruleList, deleteSecurule, selectSecuruleList } from "@/api/backend/devops/security";
import { useRoute, useRouter } from "vue-router";
const { proxy } = getCurrentInstance();
const router = useRouter()
const route = useRoute()
const emit = defineEmits(['changes'])
const props = defineProps({
    selectedRowKeys: {
        type:Array,
        default(){
            return []
        }
    },
    info:{
        type:Object,
        default(){
            return {
                isAdd:false,
                isShow:false,
                isRule:true
            }
        }
    }
})
const ethertypeObj = {
    'IPv4': '0.0.0.0/0',
    'IPv6': '::/0'
}
const securuleDialog = ref(null);
const loading = ref(false);
const securulelist = ref([]);
const groupId = ref();
const groupName = ref('');
let selectRowIds: string[] = ref([]);
const info1 = reactive({
    isAdd:true,
    isShow:false
})
const searchform = reactive({
    
    ruleName:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: 'Direction', dataIndex: 'direction', slots: { customRender: 'direction' }, key: 'id',align:'center'},
    {title: 'Ether Type', dataIndex: 'ethertype', key: 'id',align:'center'},
    {title: 'IP 协议', dataIndex: 'ipProtocol', slots: { customRender: 'ipProtocol' }, key: 'id',align:'center'},
    {title: 'Port Range', slots: { customRender: 'portrange' }, key: 'id',align:'center'},
    {title: '远程 IP 前缀', dataIndex: 'remoteIpPrefix',slots: { customRender: 'remoteIpPrefix' }, key: 'id',align:'center'},
    {title: '远程安全组', dataIndex: 'remoteGroupId',slots: { customRender: 'remoteGroupId' }, key: 'id',align:'center'},
    {title: '描述', dataIndex: 'description',slots: { customRender: 'description' }, key: 'id',align:'left'},
    {title: '修改时间', dataIndex: 'updateTime', key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' } }
];
const getList = async (id) => {
    loading.value = true;
    if(id)
    searchform.groupId = id;
    else
    searchform.groupId = groupId.value;
    let res = await selectSecuruleList({groupId:searchform.groupId})
    if(res){
        if(res.code == 0){
            securulelist.value = res.data;
        }
        loading.value = false;
    }
    
    // searchform.cloudId = localStorage.getItem('cloudId');
    // securulelist.value = await proxy.$getList(loading, getSecuruleList, searchform, pagination, getList )
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    ruleName: ""
  })
  getList();
}
const handleAdd = () => {
    info1.isAdd = info1.isShow = true;
    securuleDialog.value.ruleform.groupId = groupId.value;
    proxy.$nextTick(()=>{
        securuleDialog.value.selectGroupList()
    })
}
const handleEdit = (record) => {
    info1.isAdd = false;
    info1.isShow = true;
    Object.assign(securuleDialog.value.ruleform, record)
}
onMounted(() => {})
defineExpose({getList,groupId,groupName})

</script>
<style lang='scss' scoped>
.back-content{padding: 20px;overflow-y: initial;.buttonGroup{margin-bottom: 10px;}}
.back-header1{background-color: #fff;}
:deep(.ant-table th) { white-space: nowrap; }
:deep(.ant-table td) { white-space: nowrap; }
</style>