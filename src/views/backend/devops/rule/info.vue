<template>
    <div class=''>
        <a-modal 
        v-model:visible="info.isShow"
        @ok="handleSave"
        @cancel="cancel"
        ok-text="提交"
        cancel-text="取消"
        :body-style="{height:'550px'}"
        :maskClosable="false"
        centered
        :getContainer="modalBindNode"
        >
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isAdd ? '添加规则':'修改规则'}}</span>
                    <a-popover trigger="click" placement="leftTop">
                        <template #content>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto;margin-bottom:0">{{content.securitygroup_rule}}</pre>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <a-form :model="ruleform" ref="groupForm" :rules="rules" :labelCol="{span:5}">
                <a-form-item label="规则" name="ipProtocolName">
                    <a-select v-model:value.trim="ruleform.ipProtocolName" placeholder="请选择规则" :options="options" @change="selectOption" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                    </a-select>
                </a-form-item>
                <a-form-item label="方向" name="direction" v-if="actionType != 'none'">
                    <a-select v-model:value.trim="ruleform.direction" placeholder="请选择方向" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear >
                        <a-select-option value="ingress">入口</a-select-option>
                        <a-select-option value="egress">出口</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="打开端口" name="portrange" v-if="actionType == 'port'">
                    <a-select v-model:value.trim="ruleform.portrange" placeholder="请选择端口范围" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option value="0">端口</a-select-option>
                        <a-select-option value="1">端口范围</a-select-option>
                        <a-select-option value="2">所有端口</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item name="formPort" v-if="actionType == 'port' && ruleform.portrange == '0'">
                    <template #label>
                        端口
                        <a-tooltip title="输入大于1小于65535的整数"><QuestionCircleOutlined /></a-tooltip>
                    </template>
                    <a-input v-model:value.trim="ruleform.formPort" placeholder="请输入端口" allowClear></a-input>
                </a-form-item>
                <a-form-item name="formPort" v-if="actionType == 'port' && ruleform.portrange == '1'">
                    <template #label>
                       起始端口号
                        <a-tooltip title="输入大于1小于65535的整数"><QuestionCircleOutlined /></a-tooltip>
                    </template>
                    <a-input v-model:value.trim="ruleform.formPort" placeholder="请输入起始端口号" allowClear></a-input>
                </a-form-item>
                <a-form-item name="toPort" v-if="actionType == 'port' && ruleform.portrange == '1'">
                    <template #label>
                       终止端口号
                        <a-tooltip title="输入大于1小于65535的整数"><QuestionCircleOutlined /></a-tooltip>
                    </template>
                    <a-input v-model:value.trim="ruleform.toPort" placeholder="请输入终止端口号" allowClear></a-input>
                </a-form-item>
                <a-form-item name="icmpType" v-if="actionType == 'encode'">
                    <template #label>
                        类型
                        <a-tooltip title="请输入ICMP类型值，范围(-1:255)"><QuestionCircleOutlined /></a-tooltip>
                    </template>
                    <a-input v-model:value.trim="ruleform.icmpType" placeholder="请输入类型" allowClear></a-input>
                </a-form-item>
                <a-form-item name="encode" v-if="actionType == 'encode'">
                    <template #label>
                        编码
                        <a-tooltip title="请输入编码值，范围(-1:255)"><QuestionCircleOutlined /></a-tooltip>
                    </template>
                    <a-input v-model:value.trim="ruleform.encode" placeholder="请输入编码" allowClear></a-input>
                </a-form-item>
                <a-form-item label="远程" name="remote">
                    <a-select v-model:value.trim="ruleform.remote" placeholder="请选择远程" @change="changeRemote" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option value="0">CIDR</a-select-option>
                        <a-select-option value="1">安全组</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="CIDR" name="remoteIpPrefix" v-if="ruleform.remote == '0'">
                    <a-input v-model:value.trim="ruleform.remoteIpPrefix" placeholder="0.0.0.0/0" allowClear></a-input>
                </a-form-item>
                <a-form-item label="安全组" name="remoteGroupId" v-if="ruleform.remote == '1'">
                    <a-select v-model:value.trim="ruleform.remoteGroupId" placeholder="请选择安全组" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear >
                        <a-select-option v-for="(item,index) in grouplist" :key="index" :value="item.id+''">{{item.groupName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="以太网类型" name="ethertype" v-if="ruleform.remote == '1'">
                    <a-select v-model:value.trim="ruleform.ethertype" placeholder="请选择以太网类型" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear >
                        <a-select-option value="IPv4">IPv4</a-select-option>
                        <a-select-option value="IPv6">IPv6</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="描述" name="description">
                    <a-textarea v-model:value.trim="ruleform.description" placeholder="请输入描述" allowClear />
                </a-form-item>
                <!-- <a-form-item label="formPort" name="formPort">
                    <a-input v-model:value.trim="ruleform.formPort" placeholder="请输入" allowClear></a-input>
                </a-form-item>
                <a-form-item label="toPort" name="toPort">
                    <a-input v-model:value.trim="ruleform.toPort" placeholder="请输入" allowClear></a-input>
                </a-form-item>
                
                <a-form-item label="IP协议" name="ipProtocol">
                    <a-select v-model:value.trim="ruleform.ipProtocol" placeholder="请输入" >
                        <a-select-option value="ARP">ARP</a-select-option>
                        <a-select-option value="FTP">FTP</a-select-option>
                        <a-select-option value="ICMP">ICMP</a-select-option>
                        <a-select-option value="IGMP">IGMP</a-select-option>
                        <a-select-option value="RIP">RIP</a-select-option>
                        <a-select-option value="SMTP">SMTP</a-select-option>
                        <a-select-option value="TCP">TCP</a-select-option>
                        <a-select-option value="TELNET">TELNET</a-select-option>
                        <a-select-option value="TFTP">TFTP</a-select-option>
                        <a-select-option value="UDP">UDP</a-select-option>
                    </a-select>
                </a-form-item> -->
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {saveSecurule,selectSecugroupList,updateSecurule } from "@/api/backend/devops/security"
import { selectDictList } from '@/api/backend/systems/dictionary';
import {content} from "@/common/explain/modal";
import { useRoute } from 'vue-router';
const {proxy} = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})
const actionType = ref('')
const options = ref([
    {label:'定制TCP规则',value:'定制TCP规则',type:'port'},
    {label:'定制UDP规则',value:'定制UDP规则',type:'port'},
    {label:'定制ICMP规则',value:'定制ICMP规则',type:'encode'},
    {label:'所有ICMP协议',value:'所有ICMP协议',type:'direction'},
    {label:'所有TCP协议',value:'所有TCP协议',type:'direction'},
    {label:'所有UDP协议',value:'所有UDP协议',type:'direction'},
    {label:'DNS',value:'DNS',type:'none'},
    {label:'HTTP',value:'HTTP',type:'none'},
    {label:'HTTPS',value:'HTTPS',type:'none'},
    {label:'IMAP',value:'IMAP',type:'none'},
    {label:'IMAPS',value:'IMAPS',type:'none'},
    {label:'LDAP',value:'LDAP',type:'none'},
    {label:'MS SQL',value:'MS SQL',type:'none'},
    {label:'MYSQL',value:'MYSQL',type:'none'},
    {label:'POP3',value:'POP3',type:'none'},
    {label:'POP3S',value:'POP3S',type:'none'},
    {label:'RDP',value:'RDP',type:'none'},
    {label:'SMTP',value:'SMTP',type:'none'},
    {label:'SMTPS',value:'SMTPS',type:'none'},
    {label:'SSH',value:'SSH',type:'none'},
])
const grouplist = ref([])
const groupForm = ref();
const defaultform = {
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    groupId:undefined,
    remote:'1',
    ethertype:'IPv4'
}
const ruleform = reactive({
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    groupId:undefined,
    remote:'1',
    ethertype:'IPv4'
})
const rules = {
    ipProtocolName:[{required:true, message:'请选择规则',trigger:'change'}],
    direction:[{required:true, message:'请选择方向',trigger:'change'}],
    portrange:[{required:true, message:'请选择端口范围',trigger:'change'}],
    formPort:[
        {required:true, message:'请输入端口',trigger:'change'},
        {pattern:/^([1-9](\d{0,3}))$|^([1-5]\d{4})$|^(6[0-4]\d{3})$|^(65[0-4]\d{2})$|^(655[0-2]\d)$|^(6553[0-5])$/, message:'请输入大于1小于65535的整数',trigger:'change'}
    ],
    toPort:[
        {required:true, message:'请输入端口',trigger:'change'},
        {pattern:/^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6([0-4][0-9]{3}|5([0-4][0-9]{2}|5([0-2][0-9]|3[0-5]))))$/, message:'请输入大于1小于65535的整数',trigger:'change'}
    ],
    icmpType:[
        {required:true, message:'请输入类型',trigger:'change'},
        {pattern:/^(-1|0|[1-9][0-9]{0,1}|1[0-9]{2}|2[0-5]{2})$/, message:'请输入-1~255整数',trigger:'change'},
    ],
    encode:[
        {required:true, message:'请输入编码',trigger:'change'},
        {pattern:/^(-1|[0-9]|[1-9][0-9]|1[0-9]{2}|2[0-5]{2})$/, message:'请输入-1~255整数',trigger:'change'},
    ],
    remote:[{required:true, message:'请选择远程',trigger:'change'}],
    remoteIpPrefix:[{required:true, message:'请输入CIDR',trigger:'change'}],
    remoteGroupId:[{required:true, message:'请选择安全组',trigger:'change'}],
    ethertype:[{required:true, message:'请选择以太网类型',trigger:'change'}],
}
const selectOption = (value,option) => {
    console.log("option",value,option)
    if(value == '定制TCP规则'){
        ruleform.ipProtocol = 'TCP';
    }else if(value == '定制UDP规则'){
        ruleform.ipProtocol = ' UDP';
    }else if(value == '定制ICMP规则'){
        ruleform.ipProtocol = 'ICMP';
    }else if(value == '所有ICMP协议'){
        ruleform.ipProtocol = 'ICMP';
    }else if(value == '所有TCP协议'){
        ruleform.ipProtocol = 'TCP';
    }else if(value == '所有UDP协议'){
        ruleform.ipProtocol = 'UDP';
    }else{
        ruleform.ipProtocol = value;
    }
    actionType.value = (value ? option.type : '');
}
const changeRemote = (e) => {
    if(e == '1'){
        selectGroupList();
        ruleform.ethertype = 'IPv4';
    }
}
const handleSave = () => {
    if(actionType.value != 'port'){
        ruleform.portrange = undefined;
        ruleform.formPort = undefined;
        ruleform.toPort = undefined;
    }else{
        if(ruleform.portrange == '0'){
            ruleform.toPort = ruleform.formPort;
        }else if(ruleform.portrange == '2'){
            ruleform.formPort = undefined;
            ruleform.toPort = undefined;
        }
    }
    if(actionType.value != 'encode'){
        ruleform.icmpType = undefined;
        ruleform.encode = undefined;
    }
    if(actionType.value == 'none'){
        ruleform.direction = undefined;
        console.log('actionType.value',actionType.value,ruleform.direction)
    }
    if(ruleform.remote == '0'){
        ruleform.remoteGroupId = undefined;
        ruleform.ethertype = undefined;
    }else if(ruleform.remote == '1'){
        ruleform.remoteIpPrefix = undefined;
    }
    // groupForm.value.validate().then(()=>{
    //     console.log("ui",ruleform)
    // })
    let ruleform1 = {...ruleform};
    ruleform1.portrange = undefined;
    ruleform1.remote = undefined;
    ruleform1.ipProtocolName = undefined;
    // if(actionType.value != 'none')
    // ruleform1.ipProtocol = undefined;
    proxy.$handleSave(groupForm.value, saveSecurule, updateSecurule, props,ruleform1, ()=>{cancel();emit('getlist',ruleform.groupId)})
}
const cancel = () => {
    props.info.isShow = false
    // isInfo.value = false
    groupForm.value.resetFields()
    Object.assign(ruleform,defaultform)
}
const selectGroupList = async () => {
    let res = await selectSecugroupList({cloudId:route.query.cloudId,projectId:route.query.projectId})
    if(res.code == 0){
        grouplist.value = res.data;
        ruleform.remoteGroupId = grouplist.value[0].id+'';
    }
}
onMounted(() => {})
defineExpose({ruleform,selectGroupList})
</script>
<style lang='scss' scoped>
</style>