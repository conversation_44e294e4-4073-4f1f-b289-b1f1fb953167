<template>
    <div class=''>
        <a-modal centered
            v-model:visible="info.isShow"
            @cancel="cancel"
            width="608.8px"
            :maskClosable="info.isInfo"
            :getContainer="modalBindNode"
            >
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isInfo ? '查看' :　(info.isAdd ? '新增' : '修改')}}</span>
                    <a-popover trigger="click" placement="leftTop" v-if="!info.isInfo">
                        <template #content>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto;margin-bottom:0" v-if="info.isAdd" v-html="content.network_add"></pre>
                            <span v-else v-html="content.network_update"></span>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <template #footer>
          <a-button style="margin-left: 10px" @click="cancel" v-if="!info.isInfo" >{{ $t("m.cancel") }}</a-button>
          <a-button type="primary" @click="save" v-if="!info.isInfo">{{ $t("m.save") }}</a-button>
          <a-button type="primary" style="margin-left: 10px" @click="cancel" v-if="info.isInfo" >{{ $t("m.close") }}</a-button>
        </template>
                <a-form :model="cloudform" ref="cloudformRef" :rules="rules" :label-col="{span:6}">

                      <a-form-item label="网络名称" name="networkName">
                        <a-input v-model:value.trim="cloudform.networkName" placeholder="请输入" :disabled="info.isInfo" allowClear/>
                    </a-form-item>

                    <a-form-item label="项目" name="projectId" v-if="info.isAdd || info.isInfo">
                        <a-select
                            v-model:value="cloudform.projectId"
                            placeholder="请选择"
                            :disabled="info.isInfo || !info.isAdd"
                            allowClear>
                            <a-select-option v-for="(item,index) in networds" :key="index" :value="item.id">{{item.projectName}}</a-select-option>
                        </a-select>
                    </a-form-item>

                    <a-form-item label="供应商网络类型" name="networkType" v-if="(info.isAdd || info.isInfo) && isManager">
                        <a-select
                            v-model:value="cloudform.networkType"
                            placeholder="请选择"
                            :disabled="info.isInfo || !info.isAdd"
                            allowClear>
                            <a-select-option v-for="(item,index) in networdstype" :key="index" :value="item" :label="item">{{item}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="物理网络" name="physicalNetwork" v-if="cloudform.networkType == 'FLAT' || cloudform.networkType == 'VLAN'">
                      <a-input v-model:value="cloudform.physicalNetwork" placeholder="请输入物理网络" :disabled="info.isInfo" allow-clear></a-input>
                    </a-form-item>
                    <a-form-item label="段ID" name="providerSegId" v-if="cloudform.networkType == 'GRE' || cloudform.networkType == 'VXLAN' || cloudform.networkType == 'Geneve' || cloudform.networkType == 'VLAN'">
                      <a-input-number v-model:value="cloudform.providerSegId" :min="1" :step="1" :precision="0" placeholder="请输入段ID" :disabled="info.isInfo"></a-input-number>
                    </a-form-item>
                    <a-form-item label="网络可用域" name="sysOpenstackZoneEntityList" v-if="info.isAdd || info.isInfo">
                        <a-checkbox-group v-model:value="cloudform.sysOpenstackZoneEntityList" :disabled="info.isInfo || !info.isAdd">
                          <a-checkbox v-for="item in NetworkAvailableList" :key="item.id" :value="item" name="type">
                            {{ item.zoneName }}
                          </a-checkbox>
                        </a-checkbox-group>
                   </a-form-item>
                  
                  <a-form-item label="是否启用管理员状态">
                       <a-switch checked-children="正常" un-checked-children="禁用" v-model:checked="cloudform.adminStateUp" @change="ChangeStateUp"  :disabled="info.isInfo" />
                  </a-form-item>

                   <a-form-item label="是否共享">
                       <a-switch checked-children="正常" un-checked-children="禁用" v-model:checked="cloudform.shared"  @change="Changeshared" :disabled="info.isInfo" />
                  </a-form-item>

                  <a-form-item label="是否启用外部网络" v-if="info.isAdd || info.isInfo">
                       <a-switch checked-children="正常" un-checked-children="禁用" v-model:checked="cloudform.routerExternal"  @change="ChangeExternal" :disabled="info.isInfo || !info.isAdd" />
                  </a-form-item>
                 </a-form>
            </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {  selectCloudList } from "@/api/backend/cloud";
import { saveNetwork, updateNetwork } from "@/api/backend/devops/network";
import { selectProjectList } from "@/api/backend/devops/project";
import { selectZoneList } from "@/api/backend/devops.ts";
import { selectDictList} from "@/api/backend/systems/dictionary";
import { userStore } from '@/store/user';
import {content} from "@/common/explain/modal"
import { useRoute } from 'vue-router';
import router from '@/router';
const { proxy } = getCurrentInstance()
const emit = defineEmits(['getlist'])
const route = useRoute()
const cloudformRef = ref(null);
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: false,
        isInfo:false
      };
    }
  },
})
const modalBindNode = ref();
const networds = ref([])
const networdstype=ref([])
// const isInfo = ref(false)
const NetworkAvailableList= ref([]); //网络可用
const defaultform = {
   "networkName":"",
   "cloudId": route.query.cloudId,
   "projectId": undefined,
    "networkType":undefined,
    "sysOpenstackZoneEntityList":[],
    "adminStateUp":true,
    "shared":true,
    "routerExternal":true,
   "providerSegId":'',
    "physicalNetwork":''
}
const cloudform = reactive({
    "networkName":"",
    "cloudId": route.query.cloudId,
    "projectId": undefined,
    "networkType":undefined,
    "sysOpenstackZoneEntityList":[],
    "adminStateUp":true,
    "shared":true,
    "routerExternal":true,
    "providerSegId":'',
    "physicalNetwork":''
})
const isManager = computed(()=>userStore().userInfo.roleIdList.includes(1))
const validateProhectId = async (rule: any, value: any) => {
  if (!value) {
    return Promise.reject("请选择项目");
  } else {
    return Promise.resolve();
  }
};

const validateNetword = async (rule: any, value: any) => {
  if (!value) {
    return Promise.reject("请选择供应商网络类型");
  } else {
    return Promise.resolve();
  }
};

const validateSysList: any = async (rule: any, value: any) => {
  if (cloudform.sysOpenstackZoneEntityList.length == 0) {
    return Promise.reject("请选择网络可用域");
  } else {
    return Promise.resolve();
  }
};

const rules = reactive({
    networkName:[{ required:true,message:'请输入',trigger:'change'}],
    projectId:[{required:props.info.isAdd, validator:validateProhectId,trigger:'change'}],
    networkType:[{required:props.info.isAdd, validator:validateNetword,trigger:'change'}],
    physicalNetwork:[{required:props.info.isAdd, message:'请输入物理网络',trigger:'change'}],
    providerSegId:[{required:props.info.isAdd,type:'number', message:'请输入段ID',trigger:'change'}],
    sysOpenstackZoneEntityList: [{ required: props.info.isAdd, validator: validateSysList, trigger: "blur" }],
})

// const netSelect = (a,b) => {
//   console.log('a,b',a,b)
//   cloudform.type = b.label.toLowerCase();
// }

const save = () => {
  let cloudform1 = {...cloudform};
     proxy.$handleSave(cloudformRef.value, saveNetwork, updateNetwork, props, cloudform1, ()=>{emit('getlist');cancel();},null,()=>{
      cloudform1.adminStateUp = Number(cloudform.adminStateUp);
      cloudform1.shared = Number(cloudform.shared);
      cloudform1.routerExternal = Number(cloudform.routerExternal);
      if(props.info.isAdd)
      cloudform1.createUserId = userStore().userId;
      if(!(cloudform.networkType == 'FLAT' || cloudform.networkType == 'VLAN'))
      cloudform1.physicalNetwork = null;
      if(!(cloudform.networkType == 'GRE' || cloudform.networkType == 'VXLAN' || cloudform.networkType == 'Geneve' || cloudform.networkType == 'VLAN')){
        cloudform1.providerSegId = null;
      }
    })
}
const cancel = () => {
    props.info.isShow = props.info.isInfo = false
    cloudformRef.value.resetFields();
    Object.assign(cloudform,defaultform)
}
const selectCloudlist = () => {
  selectProjectList({cloudId:router.currentRoute.value.query.cloudId,domainId:router.currentRoute.value.query.domainId,projectId:router.currentRoute.value.query.projectId}).then((res)=>{
    cloudform.projectId = Number(route.query.projectId);
    networds.value = res.data;
  })
}
const selectWordlist=async()=>{

// let res = await selectDictList({dictType:'NETWORD_TYPE'})
//     if(res.code == 0){
//         networdstype.value = res.data;
//      }
networdstype.value = ['LOCAL','FLAT','VLAN','VXLAN','GRE','Geneve']
}
const getNetwordlist=()=>{
  selectZoneList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId,module:'network'}).then((res)=>{
    NetworkAvailableList.value = res.data;
  })
}
const setInitInfo = () => {
  selectCloudlist();selectWordlist();getNetwordlist()
}
onMounted(() => {modalBindNode.value = document.getElementsByClassName('full-modal')[0] ? document.getElementsByClassName('full-modal ant-modal-body')[0] : proxy.modalBindNode;})
defineExpose({cloudform,setInitInfo})
</script>
<style lang='scss' scoped>
</style>