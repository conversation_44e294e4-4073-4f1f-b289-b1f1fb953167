<template>
    <div class="cloudContent" v-if="!info.isSub">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                    <a-form-item label="网络名称">
                        <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.networkName" allowClear />
                    </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd"> 新增 </a-button>
                    <a-button @click="$handleDel(selectRowIds,deleteNetwork,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table :row-selection="rowSelection" :columns="columns" row-key="id" :data-source="networklist" :pagination="pagination" @change="changeTable" :scroll="{x:true}">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #networkName={record}>
                        <a @click="handleSub(record)">{{record.networkName}}</a>
                        <!-- <router-link :to="'/admin/devops/subnet?id='+record.id">{{record.networkName}}</router-link> -->
                    </template>
                    <template #statusText="{record}">
                        {{record.statusText ? record.statusText : '-'}}
                    </template>
                    <template #action={record}>
                        <a-button class="button_V" @click="handleView(record)">查看</a-button>
                        <a-button class="button_E" @click="handleEdit(record)">修改</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteNetwork,getList)">删除</a-button>
                    </template>
                </a-table>
                <Info ref="networkDialog" :info="info" @getlist="getList" />
            </div>
        </div>
    </div>
    <Subnet :info="info" ref="subnetRef" v-else />
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Subnet from "../subnet/index.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { computed, getCurrentInstance, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import { getNetworkList, deleteNetwork } from "@/api/backend/devops/network";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { useRoute } from "vue-router";
import emiter from "@/utils/Bus";
import router from "@/router";
const { proxy } = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(['changes'])
const props = defineProps({
    selectedRowKeys: {
        type:Array,
        default(){
            return []
        }
    }
})
const networkDialog = ref(null);
const subnetRef = ref(null);
const loading = ref(false);
const networklist = ref([]);
let selectRowIds: string[] = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false,
    isInfo:false,
    isSub:false
})
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    networkName:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
  begin();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '名称', dataIndex: 'networkName', slots: { customRender: 'networkName' }, key: 'id',align:'left'},
    {title: '项目', dataIndex: 'projectName', key: 'id',align:'center'},
    {title: '状态', dataIndex: 'statusText', key: 'id',align:'center', slots: { customRender: 'statusText' }},
    {title: '修改人', dataIndex: 'updateUserName', key: 'id',align:'center'},
    {title: '修改时间', dataIndex: 'updateTime', key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
const getList = async (isUnLoading) => {
    // searchform.cloudId = e;
    searchform.cloudId = router.currentRoute.value.query.cloudId;
    searchform.projectId = router.currentRoute.value.query.projectId;
    if(!isUnLoading)
        emiter.emit("loading",true)
    networklist.value = await proxy.$getList(loading, getNetworkList, searchform, pagination, getList )
    emiter.emit("loading",false)
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
  begin();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    networkName: ""
  })
//   getList();
}
const handleSub = (record) => {
    info.isSub = true;
    // const _this = this;
    proxy.$nextTick(()=>{
        // subnetRef.value.getList(record.id)
        // subnetRef.value.getPortList(record.id)
        // subnetRef.value.id = record.id;
        subnetRef.value.setInfo(record);
        // Object.assign(subnetRef.value.netinfo,record);
    })
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
    nextTick(()=>{
        // networkDialog.value.cloudform.projectId = Number(route.query.projectId);
        networkDialog.value.setInitInfo();
    })
}
const handleView = (record) => {
    info.isInfo = info.isShow = true;
    let record1={...record}
    if(record1.adminStateUp==1){
        record1.adminStateUp=true
    }else{
         record1.adminStateUp=false
    }
    if(record1.shared==1){
         record1.shared=true
    }else{
          record1.shared=false
    }

    if(record1.routerExternal==1){
        record1.routerExternal=true
    }else{
          record1.routerExternal=false
    }
    Object.assign(networkDialog.value.cloudform, record1)
    networkDialog.value.setInitInfo();
}
const handleEdit = (record) => {
    info.isAdd = false;
    info.isShow = true;
    let record1={...record}
    if(record1.adminStateUp==1){
        record1.adminStateUp=true
    }else{
         record1.adminStateUp=false
    }
    if(record1.shared==1){
         record1.shared=true
    }else{
          record1.shared=false
    }

    if(record1.routerExternal==1){
        record1.routerExternal=true
    }else{
          record1.routerExternal=false
    }
    record1.providerSegId = Number(record1.providerSegId)
    Object.assign(networkDialog.value.cloudform, record1)
}
var timerNetwork = null;
const begin = () => {
    clearInterval(timerNetwork);
    if(!searchform.networkName){
        timerNetwork = setInterval(()=>{
            getList(true);
        },60000)
    }
}
onMounted(() => {
    getList();
    begin();
    nextTick(()=>{
        handleWidth()
    })
})
onUnmounted(()=>{
    clearInterval(timerNetwork);
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
// .buttonPadding,.innerPadding{min-width: 817px;}
</style>