<template>
    <div class=''>
        <a-modal title="新增路由接口" v-model:visible="info.isShow" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" @click="save" :disabled="!apiform.subnetId">提交</a-button>
            </template>
            <a-form :model="apiform" ref="apiForm" :label-col="{span:4}">

                <a-form-item label="网络">
                    <a-select v-model:value="networkId" @change="selectSubnetlist" placeholder="请选择网络" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in netlist" :key="index" :value="item.id">{{item.networkName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="子网" name="subnetId" :rules="[{type:'number',required:true, message:'请选择子网', trigger:'change'}]">
                    <a-select :disabled="!networkId" v-model:value="apiform.subnetId" placeholder="请选择子网" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in subnetlist" :key="index" :value="item.id">{{item.cidr+' ('+item.subnetName+')'}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="IP地址" name="ipAddress">
                    <a-input v-model:value="apiform.ipAddress" placeholder="请输入ip地址" allow-clear></a-input>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { selectNetworkList, selectSubnetList } from '@/api/backend/devops/network';
import { saveRouteAPI } from '@/api/backend/devops/route';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
const {proxy} = getCurrentInstance();
const route = useRoute();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})
const apiForm = ref()
const netlist = ref()
const networkId = ref(undefined)
console.log('networkId',networkId.value)
const subnetlist = ref()
const apiform = reactive({
    "cloudId": route.query.cloudId,
    "ipAddress": "",
    "projectId": route.query.projectId,
    "routerId": 0,
    "subnetId": undefined,
})
const cancel = () => {
    props.info.isShow = false;
    Object.assign(apiform,{
        "cloudId": route.query.cloudId,
        "ipAddress": "",
        "projectId": route.query.projectId,
        "routeId": 0,
        "subnetId": undefined,
    })
    networkId.value = undefined;
}
const selectNetworklist = async (routeId) => {
    apiform.routerId = routeId;
    let res = await selectNetworkList({cloudId:route.query.cloudId,projectId: route.query.projectId})
    if(res.code == 0){
        netlist.value = res.data;
    }
}
const selectSubnetlist = async (networkId) => {
    apiform.subnetId = undefined;
    let res = await selectSubnetList({networkId});
    if(res.code == 0){
        subnetlist.value = res.data;
    }
}
const save = () => {
    let apiform1 = {...apiform};
    proxy.$handleSave(apiForm.value, saveRouteAPI, saveRouteAPI, props, apiform1, ()=>{emit('getlist');cancel();},null,()=>{
        // routeform1.status = '';
    })
}
onMounted(() => {})
defineExpose({selectNetworklist})
</script>
<style lang='scss' scoped>
</style>