<template>
    <div class='cloudContent' v-if="!info.isInfo">
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                    <a-form-item label="路由名称">
                        <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.routeName" allowClear />
                    </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class="innerPadding">
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="toAdd">新增</a-button>
                    <a-button @click="$handleDel(selectRowIds,deleteRoute,()=>{selectRowIds = [];getRoutelist()})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table :row-selection="rowSelection" :columns="columns" row-key="id" :data-source="routelist" :pagination="pagination" @change="changeTable" :scroll="{x:true}">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #name="{record}">
                        <a @click="handleView(record)">{{record.name}}</a>
                    </template>
                    <template #networkName="{record}">
                        {{record.networkId ? record.networkName : '-'}}
                    </template>
                    <template #enableSnat="{record}">
                        {{record.enableSnat == 1 ? '是':'否'}}
                    </template>
                    <template #adminStateUp="{record}">
                        {{record.adminStateUp == 1 ? '是':'否'}}
                    </template>
                    <template #statusText="{record}">
                        {{record.statusText ? record.statusText : '-'}}
                    </template>
                    <template #action="{record}">
                        <a-button class="button_V" @click="handleView(record)">查看</a-button>
                        <a-button class="button_E" @click="handleEdit(record)">修改</a-button>
                        <a-button class="button_E" v-if="record.networkId" @click="handleClear(record)">清除网关</a-button>
                        <a-button class="button_E" v-else @click="handleSett(record)">设置网关</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteRoute,getRoutelist)">删除</a-button>
                    </template>
                </a-table>
            </div>
            
        </div>
        <a-modal title="设置网关" v-model:visible="info.isSet" ok-text="提交" cancel-text="取消" @ok="set" @cancel="cancelSet" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form ref="netForm" :model="netform" :rules="rules2">
                <a-form-item label="外部网络" name="networkId">
                    <a-select v-model:value="netform.networkId" placeholder="请选择" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in netlist" :key="index" :value="item.id" :label="item.networkName">{{item.networkName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="启用SNAT" name="enableSnat">
                    <a-switch v-model:checked="netform.enableSnat" checked-children="是" un-checked-children="否" />
                </a-form-item>
            </a-form>
        </a-modal>
        <Info :info="info" ref="routeDialog" @getlist="getRoutelist" />
    </div>
    <Detail ref="detailDialog" :info="info" v-else />
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Detail from "./detail.vue";
import { computed, createVNode, getCurrentInstance, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import { getRouteList, deleteRoute } from "@/api/backend/devops/route";
import { selectNetworkList } from "@/api/backend/devops/network";
import { setNetwork, clearNetwork } from "@/api/backend/devops/route";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { message, Modal } from "ant-design-vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import emiter from "@/utils/Bus";
import router from "@/router";
const { proxy } = getCurrentInstance();
const routeDialog = ref();
const detailDialog = ref();
const netForm = ref();
const routelist = ref([]);
const netlist = ref([]);
const loading = ref(false);
let selectRowIds: string[] = ref([]);
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getRoutelist();
  begin();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title:'项目名称',dataIndex:'projectName'},
    {title:'路由名称',dataIndex:'name', slots: { customRender: 'name' }},
    {title:'外部网络',dataIndex:'networkName', slots: { customRender: 'networkName' }},
    {title:'是否启用snat',dataIndex:'enableSnat', slots: { customRender: 'enableSnat' }},
    {title:'是否启用管理员状态',dataIndex:'adminStateUp', slots: { customRender: 'adminStateUp' }},
    {title:'状态',dataIndex:'statusText',slots: { customRender: 'statusText' },width:60},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:320 }
]
const searchform = reactive({
    routeName:'',
    pageIndex:1,
    pageSize:10,
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId
})
const netform = reactive({routeId:'',networkId:undefined,enableSnat:true})
const rules2 = {
    networkId:[{required:true,type:'number',message:'请选择网络',trigger:'change'}],
    enableSnat :[{required:true,type:'boolean',message:'请选择是或否',trigger:'change'}]
}
const info = reactive({
    isAdd:true,
    isInfo:false,
    isShow:false,
    isSet:false
})
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getRoutelist();
  begin();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    routeName: ""
  })
//   getRoutelist();
}
const toAdd = () => {
    info.isAdd = info.isShow = true;
    info.isInfo = false;
    routeDialog.value.selectNetworklist();
    routeDialog.value.getNetwordlist();
}
const getRoutelist = async (isUnLoading) => {
    if(!isUnLoading)
        emiter.emit("loading",true)
    routelist.value = await proxy.$getList(loading, getRouteList, searchform, pagination, getRoutelist)
    emiter.emit("loading",false)
}
const handleView = (record) => {
    info.isSet = false;
    info.isInfo = true;
    proxy.$nextTick(()=>{
        detailDialog.value.setInfo(record);
    })
}
const handleEdit = (record) => {
    info.isSet = false;
    info.isInfo = info.isAdd = false;
    info.isShow = true;
    routeDialog.value.setInfo(record);
}
const handleClear = async (record) => {
    Modal.confirm({
        title: "确认清除网关",
        icon: createVNode(ExclamationCircleOutlined),
        content: "请谨慎操作。",
        okText: "提交",
        cancelText: "取消",
        maskClosable: true,
        onOk() {
            clearNetwork(record.id)
            .then((res: any) => {
                if (res.code == 0 && res.data !== false) {
                    message.success('清除网关成功')
                    getRoutelist();
                } else {
                    message.error((!res.msg || res.msg == 'success') ? '清除网关失败' : res.msg);
                }
            })
            .catch((err: any) => {
                message.error('解绑失败');
            });
        }
    })
}
const handleSett = (record) => {
    info.isSet = true;
    netform.routeId = record.id;
}
const cancelSet = () => {
    info.isSet = false;
    netform.enableSnat = true;
    netform.routeId = '';
    netform.networkId = undefined;
}
const set = () => {
    netForm.value.validate().then(async ()=>{
        let netform1 = {...netform}
        if(netform.enableSnat == true)
        netform1.enableSnat = 1;
        else
        netform1.enableSnat = 0;
        let res = await setNetwork(netform1)
        if(res.code == 0 && res.data !== false){
            message.success('设置网关成功');
            getRoutelist()
            cancelSet();
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '设置网关失败' : res.msg);
        }
    })
}
const selectNetworklist = async () => {
    let cloudId = router.currentRoute.value.query.cloudId
    let projectId = router.currentRoute.value.query.projectId
    let res = await selectNetworkList({cloudId,routerExternal:1})
    if(res.code == 0){
        netlist.value = res.data;
    }
}
var timerRoute = null;
const begin = () => {
    clearInterval(timerRoute);
    if(!searchform.routeName){
        timerRoute = setInterval(()=>{
            getRoutelist(true);
        },60000)
    }
}
onMounted(() => {
    getRoutelist();
    begin();
    nextTick(()=>{
        handleWidth()
    })
})
onUnmounted(()=>{
    clearInterval(timerRoute);
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
// .buttonPadding,.innerPadding{min-width: 951px;}
</style>