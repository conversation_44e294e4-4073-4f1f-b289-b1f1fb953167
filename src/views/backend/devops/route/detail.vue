<template>
    <div class="back-page">
        <a-page-header
          style="background-color:#fff"
          :title="routeinfo?.name"
          @back="()=>{info.isInfo = false}"
      />
      <a-tabs :active-key="activeKey" @change="changeTab" class="back-content" style="background:#fff" :animated="false">
            <a-tab-pane tab=""></a-tab-pane>
            <a-tab-pane key="2" tab="概览">
                <a-descriptions class='subPadding' :column="1" :label-style="{width:'142px',fontWeight:'bold'}" v-if="routeinfo.name">
                    <a-descriptions-item label="名称">{{routeinfo.name}}</a-descriptions-item>
                    <a-descriptions-item label="ID">{{routeinfo.thirdRouteId}}</a-descriptions-item>
                    <a-descriptions-item label="项目ID">
                        {{routeinfo.thirdProjectId}}
                    </a-descriptions-item>
                    <a-descriptions-item label="状态">{{routeinfo.status?.toUpperCase() == 'ACTIVE' ? '运行中': '未运行'}}</a-descriptions-item>
                    <a-descriptions-item label="管理状态">{{routeinfo.adminStateUp == 1 ? 'UP': 'DOWN'}}</a-descriptions-item>
                    <a-descriptions-item label="可用域">{{routeinfo.zone ? routeinfo.zone : '-'}}</a-descriptions-item>
                </a-descriptions>
                <a-divider orientation="left" v-if="routeinfo.networkName">外部网关</a-divider>
                <a-descriptions class='subPadding' :column="1" :label-style="{width:'142px',fontWeight:'bold'}" v-if="routeinfo.networkName">
                    <a-descriptions-item label="网络名称">{{routeinfo.networkName}}</a-descriptions-item>
                    <a-descriptions-item label="网络ID">{{routeinfo.thirdNetworkId}}</a-descriptions-item>
                    <a-descriptions-item label="SNAT">
                        {{routeinfo.enableSnat == 1 ? '激活' : '未激活'}}
                    </a-descriptions-item>
                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="3" tab="接口">
                <div class='subPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" style="margin-right: 10px;" @click="handleAPIAdd"> 新增 </a-button>
                    <a-button @click="$handleDel(selectRowIds,deleteRouteAPI,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table :row-selection="rowSelection" :columns="columns1" row-key="id" :data-source="apilist" :loading="loading" @change="changeTable" :pagination="pagination" :scroll="{x:true}" :getPopupContainer="triggerNode=>triggerNode.parentNode">
                    <template #index={record,index}>
                        {{index+1}}
                    </template>
                    <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                                    <div style="padding: 8px">
                                        <a-input
                                        ref="searchInput"
                                        :placeholder="`请输入${column.title}搜索`"
                                        v-model:value="searchform.ip"
                                        style="width: 188px; margin-bottom: 8px; display: block"
                                        @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
                                        />
                                        <a-button
                                        type="primary"
                                        size="small"
                                        style="width: 90px; margin-right: 8px"
                                        @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
                                        >
                                        <template #icon><SearchOutlined /></template>
                                        搜索
                                        </a-button>
                                        <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                                        重置
                                        </a-button>
                                    </div>
                                </template>
                                <template #filterIcon="filtered">
                                    <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
                                </template>
                    <template #portIpEntityList={record,index}>
                        {{record.portIpEntityList?.[0].ipAddress}}
                    </template>
                    <template #isExternal={record,index}>
                        {{record.isExternal == 1 ? '外部网关' : (record.isExternal === 0 ? '内部接口' : '-')}}
                    </template>
                    <template #adminStateUp={record,index}>
                        {{record.adminStateUp === 'true' ? '激活':'未激活'}}
                    </template>
                    <template #state={record,index}>
                        {{record.state?.toLowerCase() == 'active' ? '正常':'禁用'}}
                    </template>
                    <template #action={record}>
                        <!-- <a-button class="button_V" @click="handlePortView(record)" >查看</a-button> -->
                        <a-button class="button_D" @click="$handleDel([record.id],deleteRouteAPI,getAPIList)" >删除</a-button>
                    </template>
                </a-table>
                <APIadd ref="apiDialog" :info="info2" @getlist="getAPIList" :id="id" />
                </div>
            </a-tab-pane>
      </a-tabs>
    </div>
</template>
<script lang='ts' setup>
import APIadd from "./apiadd.vue"
import { deleteRouteAPI, getRouteAPIList, getRouteInfo, selectAPIList } from '@/api/backend/devops/route';
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { useRoute } from "vue-router";
import emiter from "@/utils/Bus";
const {proxy} = getCurrentInstance();
const route = useRoute()
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: false,
        isInfo:false
      };
    }
  },
})
const activeKey = ref('2');
const apiDialog = ref();
const routeinfo = reactive({
    name:''
});
const apilist = ref([]);
const loading = ref(false)
let selectRowIds: string[] = ref([]);
const searchform = reactive({pageIndex:1,pageSize:10,cloudId:route.query.cloudId,projectId:route.query.projectId,ip:''})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getAPIList();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns1 = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '接口名称', dataIndex: 'thirdPortId', key: 'thirdPortId',align:'left'},
    {title: 'IP地址', dataIndex: 'ipAddress', key: 'id',align:'left',slots: {filterDropdown: 'filterDropdown',filterIcon: 'filterIcon'}},
    {title: '类型', dataIndex: 'isExternal', slots: { customRender: 'isExternal' }, key: 'id',align:'center'},
    {title: '管理员状态', dataIndex: 'adminStateUp', slots: { customRender: 'adminStateUp' }, width:110, key: 'id',align:'center'},
    {title: '状态', dataIndex: 'state', slots: { customRender: 'state' }, width:60, key: 'id',align:'left'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' } }
];
const info2 = reactive({
    isAdd:true,
    isShow:false,
})
const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    searchform.pageIndex = "1";
    searchform.pageSize = pagination.pageSize;
    getAPIList();
};
const handleReset = clearFilters => {
    clearFilters();
    searchform.pageIndex = "1";
    searchform.pageSize = pagination.pageSize;
    searchform.ip = '';
    getAPIList();
};
const changeTab = (e) => {
    activeKey.value = e;
    if(e == '3'){
        getAPIList()
    }
}
const handleAPIAdd = () => {
    info2.isAdd = info2.isShow = true;
    proxy.$nextTick(()=>{
        apiDialog.value.selectNetworklist(routeinfo.id)
    })
}
const getAPIList = async () => {
    searchform.thirdRouterId = routeinfo.thirdRouteId;
    apilist.value = await proxy.$getList(loading, getRouteAPIList, searchform, pagination, getAPIList)
}
const setInfo = async (record) => {
    if(record){
        let res = await getRouteInfo({id:record.id})
        if(res.code == 0){
            Object.assign(routeinfo,res.data);
        }
    }else if(record){
        Object.assign(routeinfo,record);
    }
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
.back-content{padding: 0;height: 100%;overflow-y: initial;}
.subPadding{
    padding: 0 20px 20px;
    background-color: #ffffff;
    // height: calc(100vh - 136px);
    overflow-y: auto;
    margin: 0 16px;
    .buttonGroup{margin-bottom: 10px;}
    :deep(.ant-table th) { white-space: nowrap; }
    :deep(.ant-table td) { white-space: nowrap; }
}
:deep(.ant-tabs .ant-tabs-top-content){
      height: calc(100vh - 230px);
    overflow-y: scroll;
}
.ant-descriptions{width: 500px;}
// :deep(.ant-descriptions-item-container .ant-descriptions-item-label){justify-content: end;}
// :deep(.ant-descriptions-item-content){justify-content: start;}
</style>