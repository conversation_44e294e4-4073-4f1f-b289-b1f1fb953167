<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" @cancel="cancel" :maskClosable="info.isInfo" :getContainer="modalBindNode" centered>
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isInfo ? '查看' : (info.isAdd ? '新增' : '修改')}}</span>
                    <a-popover trigger="click" placement="leftTop">
                        <template #content>
                            <span>{{!info.isAdd ? content.route_update : content.route_add}}</span>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <template #footer>
                <a-button style="margin-left: 10px" @click="cancel" v-if="!info.isInfo" >{{ $t("m.cancel") }}</a-button>
                <a-button type="primary" @click="save" v-if="!info.isInfo">{{ $t("m.save") }}</a-button>
                <a-button type="primary" style="margin-left: 10px" @click="cancel" v-if="info.isInfo" >{{ $t("m.close") }}</a-button>
            </template>
            <a-form ref="routeForm" :model="routeform" :rules="rules" :label-col={span:5}>
                <a-form-item label="路由名称" name="name">
                    <a-input v-model:value="routeform.name" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="外部网络" name="networkId">
                    <!-- <a-input v-model:value="routeform.networkId" :disabled="info.isInfo"></a-input> -->
                    <a-select v-model:value="routeform.networkId" :placeholder="info.isInfo ? '':'请选择'" :disabled="info.isInfo||!info.isAdd" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in netlist" :key="index" :value="item.id" :label="item.networkName">{{item.networkName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="可用域" name="zone">
                    <a-select v-model:value="routeform.zone" :placeholder="info.isInfo ? '':'请选择'" :disabled="info.isInfo||!info.isAdd" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in NetworkAvailableList" :key="index" :value="item.zoneName" :label="item.zoneName">{{item.zoneName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="创建人" name="createUserName" v-if="info.isInfo">
                    <a-input v-model:value="routeform.createUserName" :disabled="info.isInfo" allow-clear />
                </a-form-item>
                <a-form-item label="创建时间" name="createTime" v-if="info.isInfo">
                    <a-input v-model:value="routeform.createTime" :disabled="info.isInfo" allow-clear />
                </a-form-item>
                <a-form-item label="启用管理员状态" name="adminStateUp">
                    <a-switch v-model:checked="routeform.adminStateUp" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                </a-form-item>
                <a-form-item label="启用SNAT" name="enableSnat">
                    <a-switch v-model:checked="routeform.enableSnat" checked-children="是" un-checked-children="否" :disabled="info.isInfo||!info.isAdd" />
                </a-form-item>
                <!-- <a-form-item label="状态" name="status" v-if="info.isInfo">
                    <a-switch v-model:checked="routeform.status" checked-children="正常" un-checked-children="禁用" :disabled="info.isInfo" />
                </a-form-item> -->
                
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getRouteInfo, saveRoute, updateRoute } from '@/api/backend/devops/route';
import { selectNetworkList } from '@/api/backend/devops/network';
import { selectZoneList } from '@/api/backend/devops.ts';
import { userStore } from '@/store/user';
import {content} from "@/common/explain/modal"
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import router from '@/router';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow:false,
                isInfo:false
            }
        }
    }
})
const routeForm = ref()
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    networkId:undefined,
    enableSnat:true,
    adminStateUp:true,
    zone:undefined,
    createUserId:userStore().userId
}
const netlist = ref([])
const NetworkAvailableList = ref([])
const routeform = reactive({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId,createUserId:userStore().userId,enableSnat:true,adminStateUp:true})
const rules = {
    name:[{required:!props.info.isInfo, message:'请输入', trigger:'change'}],
    enableSnat:[{type:'boolean',required:!props.info.isInfo, message:'请选择', trigger:'change'}],
    adminStateUp:[{type:'boolean',required:!props.info.isInfo, message:'请输入', trigger:'change'}],
}
const setInfo = async (record) => {
    selectNetworklist();getNetwordlist()
    // let res = await getRouteInfo({record})
    // if(res.code == 0){
        let routeform1 = {...record};
        // if(res.data.status == 0){
        //     routeform1.status = false;
        // }else{
        //     routeform1.status = true;
        // }
        routeform1.adminStateUp = (record.adminStateUp == 1) ? true : false;
        routeform1.enableSnat = (record.enableSnat == 1) ? true : false;
        // routeform1.status = (res.data.status.toLowerCase() == 'active') ? true : false;
        Object.assign(routeform, routeform1);
    // }
}
const cancel = () => {
    props.info.isShow = false;
    routeForm.value.resetFields();
    Object.assign(routeform, defaultform);
}
const save = () => {
    let routeform1 = {...routeform};
    proxy.$handleSave(routeForm.value, saveRoute, updateRoute, props, routeform1, ()=>{emit('getlist');cancel();},null,()=>{
        routeform1.adminStateUp = routeform.adminStateUp ?  1 : 0;
        routeform1.enableSnat = routeform.enableSnat ?  1 : 0;
        // routeform1.status = '';
    })
}
const selectNetworklist = async () => {
    let cloudId = router.currentRoute.value.query.cloudId
    let projectId = router.currentRoute.value.query.projectId
    let res = await selectNetworkList({cloudId,routerExternal:1})
    if(res.code == 0){
        netlist.value = res.data;
    }
}
const getNetwordlist=async ()=>{
  let res = await selectZoneList({cloudId:localStorage.getItem('cloudId'),module:'router'})
  if(res.code == 0){
    //  console.log("网络端口",res.data)
    NetworkAvailableList.value = res.data;
  }
}
onMounted(() => {})
defineExpose({setInfo,selectNetworklist,getNetwordlist})
</script>
<style lang='scss' scoped>
</style>