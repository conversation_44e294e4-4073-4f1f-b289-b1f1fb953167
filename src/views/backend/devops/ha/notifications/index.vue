<template>
    <div class=''>
        <a-row class="buttonGroup">
            <a-button type="primary" style="margin-right: 10px;" @click="handleAdd"> 新增 </a-button>
        </a-row>
        <a-table :columns="columns" :data-source="hahostlist"></a-table>
    </div>
</template>
<script lang='ts' setup>
import { onMounted, ref } from 'vue';
const hahostlist = ref([]);
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '主机', dataIndex: 'name', key: 'id' ,align:'left'},
    {title: 'UUID', dataIndex: 'totalDisk', key: 'id', slots: { customRender: 'totalDisk' } ,align:'center'},
    {title: '类型', dataIndex: 'ram', slots: { customRender: 'ram' }, key: 'id' ,align:'center'},
    {title: '状态', dataIndex: 'publicStatus', slots: { customRender: 'publicStatus' }, key: 'id' ,align:'center', ellipsis:true},
    {title: 'Payload', dataIndex: 'createTime', key: 'id' ,align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:220 }
];
onMounted(() => {})
</script>
<style lang='scss' scoped>
</style>