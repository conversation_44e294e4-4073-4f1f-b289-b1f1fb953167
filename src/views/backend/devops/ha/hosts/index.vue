<template>
    <div class=''>
        <a-table :columns="columns" :data-source="hahostlist" row-key="id" :pagination="pagination" @change="changeTable" :loading="loading">
            <template #index={record,index}>
                {{index+1+(pagination.pageSize * (pagination.current-1))}}
            </template>
            <template #reserved="{record}">
                {{record.reserved ? '是':'否'}}
            </template>
            <template #onMaintenance="{record}">
                {{record.onMaintenance ? '是':'否'}}
            </template>
            <template #action="{record}">
                <a-button class="button_E" @click="handleEdit({segmentInfo:record,isHostPage:true})">修改</a-button>
                <a-button class="button_D" @click="$handleDel([record.id],deleteMasakariHost,getList)">删除</a-button>
            </template>
        </a-table>
        <Edit ref="editRef" :info="info" @getlist="getList" />
    </div>
</template>
<script lang='ts' setup>
import Edit from "./edit.vue";
import { deleteMasakariHost, getMasakariHostList } from '@/api/backend/devops/ha';
import router from '@/router';
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
const { proxy } = getCurrentInstance();
const loading = ref(false);
const hahostlist = ref([]);
const editRef = ref(null);
const info = reactive({isShow:false,isInfo:false,isAdd:true});
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    segmentId:'',
    pageIndex:1,
    pageSize:10
})
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id',width:60,align:'center'},
    {title: '名称', dataIndex: 'name', key: 'id' ,align:'left'},
    {title: 'Segment', dataIndex: 'segmentName', key: 'id' ,align:'left'},
    {title: 'UUID', dataIndex: 'thirdSegmentUuid', key: 'id',align:'center'},
    {title: '是否保留主机', dataIndex: 'reserved', slots: { customRender: 'reserved' }, key: 'id' ,align:'center'},
    {title: '主机类型', dataIndex: 'type', key: 'id' ,align:'center'},
    {title: '控制属性', dataIndex: 'controlAttributes', key: 'id' ,align:'center', ellipsis:true},
    {title: '是否维护', dataIndex: 'onMaintenance', slots: { customRender: 'onMaintenance' }, key: 'id' ,align:'center'},
    // {title: 'Failover Segment', dataIndex: 'segmentId', key: 'id' ,align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:220 }
];
const pagination = reactive({
    // 分页配置器
    pageSize: 10, // 一页的数据限制
    current: 1, // 当前页
    total: 0, // 总数
    hideOnSinglePage: false, // 只有一页时是否隐藏分页器
    showQuickJumper: true, // 是否可以快速跳转至某页
    showSizeChanger: true, // 是否可以改变 pageSize
    pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
    showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
    searchform.pageIndex = pagination.current;
    searchform.pageSize = pagination.pageSize;
    getList();
};
const handleEdit = (record) => {
    info.isShow = true;
    info.isInfo = info.isAdd = false;
    nextTick(()=>{
        editRef.value.setInfo(record);
    })
}
const getList = async () => {
    // 第一个参数是loading，已设置全局loading，这里可不使用
    hahostlist.value = await proxy.$getList(loading, getMasakariHostList, searchform, pagination, getList);
};
onMounted(() => {getList()})
defineExpose({getList})
</script>
<style lang='scss' scoped>
</style>