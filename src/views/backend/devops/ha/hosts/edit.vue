<template>
    <a-modal :title="info.isAdd ? '新增宿主机' : '修改宿主机'" v-model:visible="info.isShow" :maskClosable="false" :getContainer="modalBindNode" ok-text="提交" @ok="save" @cancel="cancel">
        <a-form :model="segmentform" ref="segmentForm" :labelCol="{span:7}" :rules="rules">
            <a-form-item label="Segment名称" v-if="info.isAdd">
                <a-input v-model:value="segmentform.segmentName" disabled></a-input>
            </a-form-item>
            <a-form-item label="主机名称" name="name">
                <a-select
                    v-model:value="segmentform.name"
                    show-search
                    placeholder="请选择宿主机名称"
                    :options="hostlist"
                    filterable
                    :show-arrow="false"
                >
                </a-select>
            </a-form-item>
            <a-form-item label="是否保留主机" name="reserved">
                <a-switch v-model:checked="segmentform.reserved" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" ></a-switch>
            </a-form-item>
            <a-form-item label="主机类型" name="type">
                <a-input v-model:value="segmentform.type" placeholder="请输入主机类型"></a-input>
            </a-form-item>
            <a-form-item label="控制属性" name="controlAttributes">
                <a-input v-model:value="segmentform.controlAttributes" placeholder="请输入控件属性"></a-input>
            </a-form-item>
            <a-form-item label="是否维护" name="onMaintenance">
                <a-switch v-model:checked="segmentform.onMaintenance" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" ></a-switch>
            </a-form-item>
        </a-form>
    </a-modal>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {content} from "@/common/explain/modal";
import router from '@/router';
import { saveMasakariHost, saveSegment, selectHostListByCloudId, updateMasakariHost, updateSegment } from '@/api/backend/devops/ha';
const emit = defineEmits(['getlist','getHostlist']);
const {proxy} = getCurrentInstance();
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})
const isHostPage = ref(false);
const defaultform = {
  "cloudId": router.currentRoute.value.query.cloudId,
  "domainId": router.currentRoute.value.query.domainId,
  "id": 0,
  "segmentId":'',
  "thirdSegmentUuid":'',
  "name": "",
  "controlAttributes": "",
  "onMaintenance": 0,
  "reserved": 0,
  "type": ""
};
const segmentForm = ref();
const segmentform = reactive({
  "cloudId": router.currentRoute.value.query.cloudId,
  "domainId": router.currentRoute.value.query.domainId,
  "id": 0,
  "segmentId":'',
  "thirdSegmentUuid":'',
  "name": "",
  "controlAttributes": "",
  "onMaintenance": 0,
  "reserved": 0,
  "type": ""
});
const hostlist = ref([]);
const rules = {
    name:[{required:true,message:'请输入段名称'}],
    type:[{required:true,message:'请输入类型'}],
    controlAttributes:[{required:true,message:'请输入控件属性'}]
}
const selectHostList = () => {
    selectHostListByCloudId({cloudId:router.currentRoute.value.query.cloudId}).then((res)=>{
        if(res.code == 0){
            hostlist.value = res.data;
            hostlist.value.map((item,index)=>{
                item.label = item.hostname;
                item.value = item.hostname;
            })
        }
    })
}
const setInfo = (params) => {
    isHostPage.value = params.isHostPage;
    selectHostList();
    if(props.info.isAdd){
        segmentform.segmentId = params.segmentInfo.id;
        segmentform.thirdSegmentUuid = params.segmentInfo.uuid;
        segmentform.segmentName = params.segmentInfo.name;
    }else{
        let record1 = {
            id:params.segmentInfo.id,
            segmentId:params.segmentInfo.segmentId,
            thirdSegmentUuid:params.segmentInfo.thirdSegmentUuid,
            cloudId:params.segmentInfo.cloudId,
            domainId:params.segmentInfo.domainId,
            name:params.segmentInfo.name,
            controlAttributes:params.segmentInfo.controlAttributes,
            onMaintenance:params.segmentInfo.onMaintenance,
            reserved:params.segmentInfo.reserved,
            type:params.segmentInfo.type
        }
        Object.assign(segmentform,record1);
    }
}
const cancel = () => {
    props.info.isShow = false;
    Object.assign(segmentform,defaultform);
}
const save = () => {
    console.log("segmentform",segmentform)
    // formRef, save, update, props, data, callback,error,pre,unMessage
    proxy.$handleSave(segmentForm.value,saveMasakariHost,updateMasakariHost,props,segmentform,()=>{
        cancel();
        emit('getlist');
    })
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>