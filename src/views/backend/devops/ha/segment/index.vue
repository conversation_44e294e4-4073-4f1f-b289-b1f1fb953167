<template>
    <div v-show="!info.isInfo">
        <!-- <div class="cloudRight"> -->
            <!-- <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                    <a-form-item label="Segment 名称">
                        <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.name" allowClear />
                    </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div> -->
            <!-- <div class="innerPadding"> -->
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd"> 新增 </a-button>
                </a-row>
                <a-table :columns="columns" :data-source="halist" row-key="id" :pagination="pagination" @change="changeTable" :loading="loading">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #name="{record}">
                        <a @click="handleView(record)">{{record.name}}</a>
                    </template>
                    <template #action="{record}">
                        <a-button class="button_V" @click="handleView(record)">查看</a-button>
                        <a-button class="button_E" @click="handleAddHost({segmentInfo:record,isHostPage:false})">添加宿主机</a-button>
                        <a-button class="button_E" @click="handleEdit(record)">修改</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteSegment,getList)">删除</a-button>
                    </template>
                </a-table>
            <!-- </div> -->
        <!-- </div> -->
    </div>
    <Info ref="hainfoRef" :info="info" v-if="info.isInfo" @add="handleAddHost" />
    <Edit ref="editRef" :info="info" @getlist="getList" />
    <EditHost ref="editHostRef" :info="hostinfo" @getlist="getList" @getHostlist="()=>{hainfoRef.getList()}" />
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Edit from "./edit.vue";
import EditHost from "../hosts/edit.vue";
import { getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { deleteSegment, getSegmentList } from "@/api/backend/devops/ha";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import router from "@/router";
const { proxy } = getCurrentInstance();
const hainfoRef = ref(null);
const editRef = ref(null);
const editHostRef = ref(null);
const info = reactive({isModal:false,isShow:false,isInfo:false,isAdd:true});
const hostinfo = reactive({isShow:false,isInfo:false,isAdd:true});
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    pageIndex:1,
    pageSize:10
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: 'Segment名称', dataIndex: 'name', slots: { customRender: 'name' }, key: 'id' ,align:'left'},
    {title: 'UUID', dataIndex: 'uuid', key: 'id',align:'center'},
    {title: '恢复类型', dataIndex: 'recoveryMethod', key: 'id' ,align:'center'},
    {title: '服务类型', dataIndex: 'serviceType', key: 'id' ,align:'center'},
    {title: '创建时间', dataIndex: 'createTime', key: 'id' ,align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:260 }
];
const loading = ref(false);
const halist = ref([]);
const pagination = reactive({
    // 分页配置器
    pageSize: 10, // 一页的数据限制
    current: 1, // 当前页
    total: 0, // 总数
    hideOnSinglePage: false, // 只有一页时是否隐藏分页器
    showQuickJumper: true, // 是否可以快速跳转至某页
    showSizeChanger: true, // 是否可以改变 pageSize
    pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
    showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
    searchform.pageIndex = pagination.current;
    searchform.pageSize = pagination.pageSize;
    getList();
};
const handleSearch = () => {
    searchform.pageIndex = "1";
    searchform.pageSize = pagination.pageSize;
    getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
        cloudId:router.currentRoute.value.query.cloudId,
        domainId:router.currentRoute.value.query.domainId,
        pageSize: 10,
        pageIndex: 1,
    })
//   getList();
}
const handleAdd = () => {
    info.isModal = info.isAdd = true;
    info.isInfo = false;
    // editRef.value
}
const handleView = (record) => {
    info.isShow = info.isInfo = true;
    info.isAdd = false;
    nextTick(()=>{
        hainfoRef.value.setInfo(record);
    })
}
const handleEdit = (record) => {
    console.log("record1",record)
    info.isModal = true;
    info.isInfo = info.isAdd = false;
    nextTick(()=>{
        editRef.value.setInfo(record);
    })
}
const handleAddHost = (params) => {
    console.log('params',params);
    hostinfo.isShow = hostinfo.isAdd = true;
    hostinfo.isInfo = false;
    nextTick(()=>{
        editHostRef.value.setInfo(params);
    })
}
const getList = async () => {
    // 第一个参数是loading，已设置全局loading，这里可不使用
    halist.value = await proxy.$getList(loading, getSegmentList, searchform, pagination, getList);
};
onMounted(() => {
    getList()
    nextTick(()=>{
        handleWidth()
    })
});
defineExpose({getList})
</script>
<style lang='scss' scoped>
.buttonPadding{border-bottom-width: 1px;}
</style>