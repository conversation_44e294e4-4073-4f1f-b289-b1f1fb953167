<template>
    <a-modal v-model:visible="info.isModal" :maskClosable="false" ok-text="提交" @ok="save" @cancel="cancel" :getContainer="modalBindNode">
        <template #title>
            <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                <span>{{(info.isAdd ? '新增Segment' : '修改Segment')}}</span>
                <a-popover trigger="click" placement="leftTop">
                    <template #content>
                        <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-html="content.ha_add"></pre>
                    </template>
                    <InfoCircleOutlined v-if="info.isAdd" />
                </a-popover>
            </div>
        </template>
        <a-form :model="segmentform" ref="segmentForm" :labelCol="{span:7}" :rules="rules">
            <a-form-item label="Segment名称" name="name">
                <a-input v-model:value="segmentform.name" placeholder="请输入Segment名称"></a-input>
            </a-form-item>
            <a-form-item name="recoveryMethod">
                <template #label>
                    恢复类型&nbsp;
                    <a-tooltip placement="topRight" title="任何主机出现故障时的恢复类型。">
                    <QuestionCircleOutlined style="font-size:12px" v-if="info.isAdd" />
                    </a-tooltip>
                </template>
                <a-select v-model:value="segmentform.recoveryMethod" placeholder="请选择恢复类型">
                    <a-select-option value="auto">auto</a-select-option>
                    <a-select-option value="auto_priority">auto_priority</a-select-option>
                    <a-select-option value="reserved_host">reserved_host</a-select-option>
                    <a-select-option value="rh_priority">rh_priority</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item name="serviceType" v-if="info.isAdd">
                <template #label>
                    <a-tooltip placement="topRight" title="截至目前，用户可用COMPUTE作为服务类型。">
                    服务类型 <QuestionCircleOutlined style="font-size:12px" />
                    </a-tooltip>
                </template>
                <a-input v-model:value="segmentform.serviceType" placeholder="请输入服务类型" disabled></a-input>
            </a-form-item>
            <a-form-item label="是否激活" name="enabled">
                <a-switch v-model:checked="segmentform.enabled" :checkedValue="1" :unCheckedValue="0" checked-children="是" un-checked-children="否" ></a-switch>
            </a-form-item>
            <a-form-item label="描述" name="description">
                <a-textarea v-model:value="segmentform.description" placeholder="请输入描述"></a-textarea>
            </a-form-item>
        </a-form>
    </a-modal>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {content} from "@/common/explain/modal";
import router from '@/router';
import { saveSegment, updateSegment } from '@/api/backend/devops/ha';
const emit = defineEmits(['getlist']);
const {proxy} = getCurrentInstance();
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false,
                isModal:false
            }
        }
    }
})
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    name:'',
    recoveryMethod:'auto',
    serviceType:'compute',
    enabled:1,
    description:''
};
const segmentForm = ref();
const segmentform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    name:'',
    recoveryMethod:'auto',
    serviceType:'compute',
    enabled:1,
    description:''
});
const rules = {
    name:[{required:true,message:'请输入段名称'}],
    recoveryMethod:[{required:props.info.isAdd,message:'请选择恢复方式'}],
    serviceType:[{required:true,message:'请输入服务类型'}]
}
const setInfo = (record) => {
    console.log("record",record);
    let record1 = {
        id:record.id,
        cloudId:record.cloudId,
        domainId:record.domainId,
        name:record.name,
        recoveryMethod:record.recoveryMethod,
        serviceType:record.serviceType,
        enabled:record.enabled,
        description:record.description
    }
    Object.assign(segmentform,record1);
}
const cancel = () => {
    props.info.isModal = false;
    Object.assign(segmentform,defaultform);
}
const save = () => {
    console.log("segmentform",segmentform)
    // formRef, save, update, props, data, callback,error,pre,unMessage
    proxy.$handleSave(segmentForm.value,saveSegment,updateSegment,props,segmentform,()=>{
        emit('getlist');cancel();
    })
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>