<template>
    <div class='back-page'>
        <!-- <a-page-header
          style="background-color:#fff"
          :title="segmentInfo.name"
          @back="()=>{props.info.isShow = props.info.isInfo = false;}"
      /> -->
      <a @click="()=>{info.isShow = info.isInfo = false;}"><DoubleLeftOutlined />返回</a>
        <a-tabs class="back-content" :animated="false" @change="changeTab">
            <a-tab-pane key="1" tab="概览">
                <a-descriptions :column="1">
                    <a-descriptions-item label="名称">{{segmentInfo.name}}</a-descriptions-item>
                    <!-- <a-descriptions-item label="ID">{{segmentInfo.id}}</a-descriptions-item> -->
                    <a-descriptions-item label="恢复类型">{{segmentInfo.recoveryMethod}}</a-descriptions-item>
                    <a-descriptions-item label="服务类型">{{segmentInfo.serviceType}}</a-descriptions-item>
                    <a-descriptions-item label="是否激活">{{segmentInfo.enabled ? '是' : '否'}}</a-descriptions-item>
                    <a-descriptions-item label="描述">{{segmentInfo.description}}</a-descriptions-item>
                    <a-descriptions-item label="创建时间">{{segmentInfo.createTime}}</a-descriptions-item>
                    <a-descriptions-item label="修改时间">{{segmentInfo.updateTime}}</a-descriptions-item>
                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="2" tab="宿主机">
                <div class="subPadding">
                    <a-row class="buttonGroup">
                        <a-button type="primary" class="btnMargin" @click="()=>{console.log('add',segmentInfo);emit('add',{segmentInfo,isHostPage:true})}"> 新增 </a-button>
                    </a-row>
                    <a-table :columns="columns" :data-source="hahostlist" row-key="id" :loading="loading">
                        <template #index={record,index}>
                            {{index+1}}
                        </template>
                        <template #reserved="{record}">
                            {{record.reserved ? '是':'否'}}
                        </template>
                        <template #onMaintenance="{record}">
                            {{record.onMaintenance ? '是':'否'}}
                        </template>
                        <template #action="{record}">
                            <a-button class="button_E" @click="handleEdit({segmentInfo:record,isHostPage:false})">修改</a-button>
                            <a-button class="button_D" @click="$handleDel([record.id],deleteMasakariHost,getList)">删除</a-button>
                        </template>
                    </a-table>
                </div>
            </a-tab-pane>
        </a-tabs>
        <HostEdit ref="editRef" :info="host" @getlist="getList" />
    </div>
</template>
<script lang='ts' setup>
import HostEdit from "../hosts/edit.vue";
import { deleteMasakariHost, selectMasakariHostList } from '@/api/backend/devops/ha';
import { nextTick, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
const emit = defineEmits(['add']);
const router = useRouter();
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
                isInfo:true
            }
        }
    }
})
const editRef = ref(null);
const host = reactive({isShow:false,isInfo:false,isAdd:true});
const segmentInfo = reactive({
    name:''
})
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, width:60,align:'center'},
    {title: '主机名称', dataIndex: 'name', align:'left'},
    {title: 'UUID', dataIndex: 'thirdSegmentUuid',align:'center'},
    {title: '是否保留主机', dataIndex: 'reserved', slots: { customRender: 'reserved' }, align:'center'},
    {title: '主机类型', dataIndex: 'type', align:'center'},
    {title: '控制属性', dataIndex: 'controlAttributes', align:'center', ellipsis:true},
    {title: '是否维护', dataIndex: 'onMaintenance', slots: { customRender: 'onMaintenance' }, align:'center'},
    // {title: 'Failover Segment', dataIndex: 'createTime', key: 'id' ,align:'center'},
    {title: '操作', dataIndex: 'action', slots: { customRender: 'action' },width:220 }
];
const loading = ref(false);
const hahostlist = ref([]);
const handleEdit = (record) => {
    host.isShow = true;
    host.isInfo = host.isAdd = false;
    nextTick(()=>{
        editRef.value.setInfo(record);
    })
}
const getList = async () => {
    loading.value = true;
    // 第一个参数是loading，已设置全局loading，这里可不使用
    let res;
    try{
        res = await selectMasakariHostList({segmentUuid:segmentInfo.uuid})
    }catch{
        loading.value = false;
    }finally{
        loading.value = false;
        if(res.code == 0){
            hahostlist.value = res.data;
        }
    }
    
};
const changeTab = (key) => {
    if(key == '2'){
        getList();
    }
}
const setInfo = (record) => {
    Object.assign(segmentInfo,record);
}
onMounted(() => {})
defineExpose({setInfo,getList})
</script>
<style lang='scss' scoped>
.back-content{padding: 0;height: 100%;overflow-y: initial;}
.ant-descriptions{padding: 20px;margin: 0 16px 16px;}
.subPadding{
    background-color: #ffffff;
    // height: calc(100vh - 136px);
    overflow-y: auto;
    .buttonGroup{margin-bottom: 10px;}
}
:deep(.ant-tabs .ant-tabs-top-content){
      height: calc(100vh - 230px);
    overflow-y: scroll;
}
.back-content{border-top-width: 0;}
</style>