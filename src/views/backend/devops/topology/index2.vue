<template>
    <div class='contentPadding'>
        <!-- jtopo用于渲染的div -->
        <div id="divId" style="height:600px;width:100%px;border:1px solid gray"></div>
    </div>

</template>
<script lang='ts' setup>
import { onMounted } from 'vue';
import {Stage, Layer, Node, Link, CircleNode} from 'jtopo';
const initCreate = () => {
        var stage = new Stage('divId');
        var layer = new Layer('default');
        stage.addChild(layer);
        stage.hideToolbar()
        stage.show();

        // 方便调测
        window.stage = stage;
        window.layer = layer;

        return {
            stage, layer
        };
    }
    const drawData = (layer) => {
        layer.height = 700;
        var fromNode = new CircleNode('From', 200, 200, 40);
        var toNode = new Node('To',   400, 200, 40, 40);
        var toNode1 = new Node('To1',   400, 400, 40, 40);
        var toNode2 = new Node('To2',   200, 400, 40, 40);
        // 半径
        // fromNode.setRadius(20);
        // 设置节点填充颜色
        fromNode.css({
            backgroundColor: 'red',
            // border: 'solid 1px gray',
            // font: 'bold 12px arial',
        });

        toNode.css({
            background: 'orange',
        });
        toNode1.css({
            background: 'yellow',
        });
        toNode2.css({
            background: 'green',
        });
        var link = new Link('Link',fromNode,toNode);
        link.css({
            border: 'solid 1px gray',
        })
        layer.addChild(link);

        var link1 = new Link('Link1',fromNode,toNode1);
        link1.css({
            border: 'solid 1px gray',
        })
        layer.addChild(link1);

        var link2 = new Link('Link2',fromNode,toNode2);
        link2.css({
            border: 'solid 1px gray',
        })
        layer.addChild(link2);

        // fromNode.on('mousedown', (event)=>{
        //     fromNode.text = 'mousedown';
        // });

        layer.addChild(fromNode);
        layer.addChild(toNode);
        layer.addChild(toNode1);
        layer.addChild(toNode2);
    }
onMounted(() => {
    let {layer} = initCreate();
    drawData(layer);
})
</script>
<style lang='scss' scoped>
canvas:last-child{height: 700px !important;}
</style>