<template>
    <div class='contentPadding'>
    <a-tabs :animated="false">
      <a-tab-pane tab="拓扑" key="1">
        <!-- <Minitopo /> -->
        <Straighttopo />
      </a-tab-pane>
      <a-tab-pane tab="图表" key="2" force-render>
        <Topo />
      </a-tab-pane>
    </a-tabs>
    </div>
</template>

<script lang='ts' setup>
import Topo from "@/components/topo/topo.vue";
import Minitopo from "@/components/minitopo/minitopo.vue";
import Straighttopo from "@/components/minitopo/straighttopo.vue";
import { onMounted } from "vue";
onMounted(() => {
})
</script>
<style lang='scss' scoped>
:deep(.ant-tabs .ant-tabs-top-content){
      height: calc(100vh - 197px);
    overflow-y: scroll;
    position: relative;
}
</style>