<template>
    <div class=''>
        <a-modal 
        :title="info.isInfo ? '查看项目' : (info.isAdd ? '新增项目':'修改项目')"
        v-model:visible="info.isShow"
        @cancel="cancel"
        :maskClosable="info.isInfo"
        centered
        :getContainer="modalBindNode"
        >
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="projectform" ref="projectForm" :rules="rules" :labelCol="{span:4}">
                <!-- <a-form-item label="域名" name="domainId">
                    <a-select v-model:value="projectform.domainId" placeholder="请选择" :disabled="info.isInfo || !info.isAdd" allow-clear> 
                        <a-select-option v-for="(item,index) in options3" :key="index" :value="item.id+''" >{{item.domainName}}</a-select-option>
                    </a-select>
                </a-form-item> -->
                <a-form-item label="名称" name="projectName">
                    <a-input v-model:value.trim="projectform.projectName" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="是否激活" name="enabled">
                    <a-switch v-model:checked="projectform.enabled" :disabled="info.isInfo" />
                </a-form-item>
                <a-form-item label="描述" name="description">
                    <a-textarea v-model:value.trim="projectform.description" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-textarea>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { saveProject,updateProject } from "@/api/backend/devops/project"
import { selectOpenstList } from "@/api/backend/devops/domain"
import { selectDictList } from '@/api/backend/systems/dictionary';
import { useRoute } from 'vue-router';
import { menuStore } from '@/store/menu';
import { storeToRefs } from 'pinia';
import emiter from '@/utils/Bus';
const {proxy} = getCurrentInstance();
const menu_store = menuStore()
const {cloudIndex,domainIndex,projectIndex} = storeToRefs(menu_store);
const route = useRoute()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})
const projectForm = ref();
const defaultform = {
    cloudId:route.query.cloudId,
    domainId:route.query.domainId,
    projectId:route.query.projectId,
    projectName:'',
    enabled:true,
    description:''
}
const projectform = reactive({
    cloudId:route.query.cloudId,
    domainId:route.query.domainId,
    projectId:route.query.projectId,
    projectName:'',
    enabled:true,
    description:''
})
const rules = {
    // domainId:[{required:true, message:'请选择',trigger:'change'}],
    projectName:[{required:true, message:'请输入',trigger:'change'}],
}
const options3 = ref([])
const selectopenstList = async () => {
    let res = await selectOpenstList({cloudId:projectform.cloudId})
    if(res.code == 0){
        options3.value = res.data
    }
} 
const handleSave = () => {
    projectform.cloudId = route.query.cloudId;
    projectform.domainId = route.query.domainId;
    let projectform1 = {...projectform}
    proxy.$handleSave(projectForm.value, saveProject, updateProject, props,projectform1, ()=>{
        cancel();emit('getlist');
        if(route.path == '/admin/devops/menu/domain'){
            proxy.$mitt.emit('getProjectCount',{domainId:route.query.domainId,cloudId:route.query.cloudId})
            emiter.emit("ProjectListAPI",{key:'domain_'+route.query.domainId,cloudKey:'cloud_'+route.query.cloudId,type:'project', action:props.isAdd ? 'add' : 'edit'})
            // ProjectListAPI({key:'domain_'+domainId,cloudKey:'cloud_'+cloudId})
        }else{
            emiter.emit("setProject");
            // emiter.emit("ProjectListAPI",{key:'domain_'+route.query.domainId,cloudKey:'cloud_'+route.query.cloudId,type:'cloud', action:props.isAdd ? 'add' : 'edit'})
            emiter.emit("MenusListSET",{key:'project_'+route.query.projectId,domainKey:'domain_'+route.query.domainId,cloudKey:'cloud_'+route.query.cloudId,type:'project', action:props.isAdd ? 'add' : 'edit'})
            // ProjectListAPI({key:'domain_'+domainId,cloudKey:'cloud_'+cloudId})
            // MenusListSET({key:'project_'+projectId,domainKey:'domain_'+domainId,cloudKey:'cloud_'+cloudId})
        }
        // if(props.info.isAdd){
        // }
    },null,()=>{
        if(projectform1.enabled == true)
        projectform1.enabled = 1;
        else
        projectform1.enabled = 0;
    })
}
const cancel = () => {
    props.info.isShow = false
    props.info.isInfo = false
    // isInfo.value = false
    projectForm.value.resetFields()
    Object.assign(projectform,defaultform)
}
onMounted(() => {
    // selectopenstList();
    // proxy.$mitt.on('selectProjectDomain',selectopenstList)
})
defineExpose({projectform})
</script>
<style lang='scss' scoped>
</style>