<template>
    <div class=''>
        <a-modal title="项目配置" 
        v-model:visible="info.isQuota" 
        :body-style="{minHeight:'501px'}"
        @cancel="cancel"
        :maskClosable="false"
        centered
        :getContainer="modalBindNode">
        <template #footer>
            <a-button @click="cancel">取消</a-button>
            <a-button @click="handleSave" type="primary">提交</a-button>
        </template>
            <a-tabs v-model:activeKey="activeKey" :animated="false">
                <a-tab-pane tab="计算" key="1" class="first-tab">
                    <a-form :model="computeform" :rules="rules1" ref="computeForm" :label-col="{span:7}">
                        <a-form-item label="虚机" name="serverNum"><a-input-number v-model:value="computeform.serverNum" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="CPU数量" name="vpcuNum"><a-input-number v-model:value="computeform.vpcuNum" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="内存(MB)" name="ramSize"><a-input-number v-model:value="computeform.ramSize" :min="0" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="元数据条目" name="metadataNum"><a-input-number v-model:value="computeform.metadataNum" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="密钥对" name="keyNum"><a-input-number v-model:value="computeform.keyNum" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <!-- <a-form-item label="主机组" name="serverGroup"><a-input-number v-model:value="computeform.serverGroup" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item> -->
                        <!-- <a-form-item label="主机组成员" name="serverGroupUser"><a-input-number v-model:value="computeform.serverGroupUser" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item> -->
                        <a-form-item label="注入的文件" name="fileNum"><a-input-number v-model:value="computeform.fileNum" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="已注入文件内容(Bytes)" name="fileSize"><a-input-number v-model:value="computeform.fileSize" :min="0" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="注入文件路径的长度" name="filepathSize"><a-input-number v-model:value="computeform.filepathSize" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                    </a-form>
                </a-tab-pane>
                <a-tab-pane tab="卷" key="2" force-render>
                    <a-form :model="volumeform" :rules="rules2" ref="volumeForm" :label-col="{span:7}">
                        <a-form-item label="卷" name="volumeNum"><a-input-number v-model:value="volumeform.volumeNum" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="卷备份" name="backupNum"><a-input-number v-model:value="volumeform.backupNum" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="卷快照" name="volumeSnapshot"><a-input-number v-model:value="volumeform.volumeSnapshot" :min="0" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="卷及快照总大小(GiB)" name="volumeSize"><a-input-number v-model:value="volumeform.volumeSize" :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                    </a-form>
                </a-tab-pane>
                <a-tab-pane tab="网络" key="3" force-render>
                    <a-form :model="netform" :rules="rules3" ref="netForm" :label-col="{span:7}">
                        <a-form-item label="网络" name="networkNum"><a-input-number v-model:value="netform.networkNum"  :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="子网" name="subnetNum"><a-input-number v-model:value="netform.subnetNum"  :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="端口" name="portNum"><a-input-number v-model:value="netform.portNum"  :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="路由" name="routeNum"><a-input-number v-model:value="netform.routeNum"  :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="浮动IP" name="floatipNum"><a-input-number v-model:value="netform.floatipNum"  :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="安全组" name="safeGroup"><a-input-number v-model:value="netform.safeGroup"  :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                        <a-form-item label="安全组规则" name="safeRules"><a-input-number v-model:value="netform.safeRules"  :min="0" :precision="0" :step="1" placeholder="请输入"></a-input-number></a-form-item>
                    </a-form>
                </a-tab-pane>
                <a-tab-pane tab="项目成员" key="4" force-render>
                    <a-select
                     mode="multiple" 
                     v-model:value="quotaform.userIds" 
                     placeholder="请选择项目成员（多选可搜索）"
                     style="width:100%" 
                     :filter-option="(input, option) => {return option.children[0].children.indexOf(input) !== -1}" 
                     show-search>
                        <a-select-option v-for="(item,index) in userlist" :key="index" :value="item.userId" :label="item.userName">{{item.userName+' ('+item.loginName+')'}}</a-select-option>
                    </a-select>
                </a-tab-pane>
            </a-tabs>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { updateComputeQuota, updateNetworkQuota, updateVolumeQuota, saveComputeQuota, saveNetworkQuota, saveVolumeQuota, getComputeQuotaInfo, getNetworkQuotaInfo, getVolumeQuotaInfo, updateQuota } from "@/api/backend/devops/project";
import { message } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { queryWorker } from '@/api/backend/systems/user';
import emiter from '@/utils/Bus';
const route = useRoute()
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type: Object,
        default(){
            return {
                isQuota: false
            }
        }
    }
})
const activeKey = ref('1')
const isAddCompute = ref(false);
const isAddVolume = ref(false);
const isAddNet = ref(false);
// serverGroup:'', serverGroupUser:'', 
const defaultcomputeform = {serverNum:'', vpcuNum:'', ramSize:'', metadataNum:'', keyNum:'', fileNum:'', fileSize:'', filepathSize:''};
const defaultvolumeform = {volumeNum:'', volumeSize:'', volumeSnapshot:''};
const defaultnetform = {networkNum:'', subnetNum:'', portNum:'', routeNum:'', floatipNum:'', safeGroup:'', safeRules:''};
const computeForm = ref()
const volumeForm = ref()
const netForm = ref()
const quotaform = reactive({userIds:[]})
const computeform = reactive({})
const volumeform = reactive({})
const netform = reactive({})
const userlist = ref([])
const rules1 = {
    serverNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    vpcuNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    ramSize:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    metadataNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    keyNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    fileNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    fileSize:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    filepathSize:[{type:'number',required:true, message:'请输入',trigger:'change'}],
}
const rules2 = {
    volumeNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    backupNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    volumeSize:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    volumeSnapshot:[{type:'number',required:true, message:'请输入',trigger:'change'}],
}
const rules3 = {
    networkNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    subnetNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    portNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    routeNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    floatipNum:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    safeGroup:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    safeRules:[{type:'number',required:true, message:'请输入',trigger:'change'}],
}
const isEmpty = ref(true);
const cancel = () => {
    activeKey.value = '1';
    props.info.isQuota = false;
    Object.assign(computeform,defaultcomputeform)
    Object.assign(volumeform,defaultvolumeform)
    Object.assign(netform,defaultnetform)
    quotaform.userIds = [];
    computeForm.value.resetFields();
    volumeForm.value.resetFields();
    netForm.value.resetFields();
}
const handleSave = async () => {
    let res = await computeForm.value.validate().then().catch((err)=>{message.error('请检查【计算】的必填字段')});
    if(res){
        let res1 = await volumeForm.value.validate().then().catch((err)=>{message.error('请检查【卷】的必填字段')});;
        if(res1){
            let res2 = await netForm.value.validate().then().catch((err)=>{message.error('请检查【网络】的必填字段')});;
            if(res2){
                quotaform.computeEntity = computeform;
                quotaform.volumeEntity = volumeform;
                quotaform.networkEntity = netform;
                let res3 = await updateQuota(quotaform)
                if(res3.code == 0){
                    if(res3.data){
                        message.success((!res3.msg || res3.msg == 'success') ? '项目配额成功' : res3.msg);
                        proxy.$mitt.emit('refreshcloudIndex',quotaform.projectId)
                        if(route.path == '/admin/devops/menu/domain')
                            proxy.$mitt.emit('getProjectCount',{domainId:route.query.domainId,cloudId:route.query.cloudId})
                    }
                    else
                    message.error((!res3.msg || res3.msg == 'success') ? '项目配额失败' : res3.msg);
                    cancel();
                }
            }
        }
    }
}
const setInfo = async (projectId) => {
    queryworker()
    let res1 = await getComputeQuotaInfo({projectId});
        if(res1.code == 0){
            Object.assign(computeform, res1.data.sysOpenstackProjectCompute);
            Object.assign(volumeform, res1.data.sysOpenstackProjectVolume)
            Object.assign(netform, res1.data.sysOpenstackProjectNetwork)
        }
    quotaform.projectId = projectId;
    quotaform.userIds = res1.data.userEntityList ? res1.data.userEntityList.map((item,index)=>{
        return item.userId;
    }) : [];
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
.ant-input-number{width: 98%;}
.first-tab{height: 392px;overflow-y: auto;}
</style>