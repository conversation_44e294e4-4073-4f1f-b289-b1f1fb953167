<template>
    <div class="cloudContent">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform">
                <a-form-item label="项目名称">
                    <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.projectName" allowClear />
                </a-form-item>
                <a-form-item>
                    <a-button type="primary" @click="handleSearch"> {{ $t("m.search") }} </a-button>
                </a-form-item>
                <a-form-item>
                    <a-button @click="handleAllReset"> {{ $t("m.reset") }} </a-button>
                </a-form-item>
                </a-form>
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd">新增 </a-button>
                </a-row>
                <a-table :columns="columns" row-key="id" :data-source="projectlist" :pagination="pagination" @change="changeTable">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #enabled={record}>
                        {{record.enabled == 1 ? '是' : '否'}}
                    </template>
                    <template #action={record}>
                        <a-button class="button_V" @click="handleView(record)">查看</a-button>
                        <a-button class="button_E" @click="handleEdit(record)">修改</a-button>
                        <a-button class="button_E" @click="handleQuota(record)">配额</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteProject,getList)">删除</a-button>
                    </template>
                </a-table>
                <Info ref="projectDialog" :info="info" @getlist="getList" />
                <Quota ref="quotaDialog" :info="quota" @getlist="getList" />
            </div>
        </div>
    </div>
    
</template>
<script lang='ts' setup>
import Info from "./info.vue";
import Quota from "./quota.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { getProjectList, deleteProject } from "@/api/backend/devops/project";
import { useRoute } from "vue-router";
const { proxy } = getCurrentInstance();
const route = useRoute()
const projectDialog = ref(null);
const quotaDialog = ref(null);
const loading = ref(false);
const projectlist = ref([]);
const info = reactive({
    isInfo:false,
    isAdd:true,
    isShow:false
})
const quota = reactive({
                isQuota: false
            })
const searchform = reactive({
    cloudId:route.query.cloudId,
    domainId:route.query.domainId,
    projectName:'',
    pageIndex:1,
    pageSize:10,
    enabled:1
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '名称', dataIndex: 'projectName', key: 'id',align:'left'},
    {title: '所属域', dataIndex: 'domainName', key: 'id',align:'center'},
    {title: '是否激活', dataIndex: 'enabled', slots: { customRender: 'enabled' }, key: 'id',align:'center'},
    {title: '修改人', dataIndex: 'updateUserName', key: 'id',align:'center'},
    {title: '修改时间', dataIndex: 'updateTime', key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:230 }
];
const getList = async () => {
    // searchform.cloudId = localStorage.getItem('cloudId');
    searchform.cloudId = route.query?.cloudId;
    searchform.domainId = route.query?.domainId;
    projectlist.value = await proxy.$getList(loading, getProjectList, searchform, pagination, getList )
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    projectName: "",
    enabled:1
  })
  getList();
}
const handleView = (record) => {
    info.isInfo = true;
    info.isShow = true;
    let record1 = {...record}
    if(record1.enabled == 1)
    record1.enabled = true;
    else
    record1.enabled = false;
    Object.assign(projectDialog.value.projectform, record1)
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
}
const handleEdit = (record) => {
    info.isAdd = false;
    info.isShow = true;
    let record1 = {...record}
    if(record1.enabled == 1)
    record1.enabled = true;
    else
    record1.enabled = false;
    Object.assign(projectDialog.value.projectform, record1)
}
const handleQuota = (record) => {
    quota.isQuota = true;
    // quotaDialog.value.setInfo(record.sysOpenstackProjectCompute, record.sysOpenstackProjectNetwork, record.sysOpenstackProjectVolume,record.id)
    quotaDialog.value.setInfo(record.id)
    // let record1 = {...record}
    // if(record1.enabled == 1)
    // record1.enabled = true;
    // else
    // record1.enabled = false;
    // Object.assign(projectDialog.value.projectform, record1)
}
onMounted(() => { 
    getList()
    // if(route.path == '/admin/devops/project')
    proxy.$mitt.on('getprojectlist',getList)
    })
</script>
<style lang='scss' scoped>
</style>