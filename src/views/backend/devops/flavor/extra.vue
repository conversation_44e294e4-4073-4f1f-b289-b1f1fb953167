<template>
    <div class=''>
        <a-modal title="设置附加属性" v-model:visible="info.isShow" 
        @cancel="cancel"
        @ok="save"
        ok-text="提交"
        cancel-text="取消"
        width="820px !important"
        :body-style="{height:'550px',overflowY:'auto'}"
        :maskClosable="false"
        centered
        :getContainer="modalBindNode">

            <a-transfer
                :data-source="mockData"
                :target-keys="targetKeys"
                :show-search="true"
                :filter-option="(inputValue, item) => {inputValue1 = inputValue;return item.title.indexOf(inputValue) !== -1}"
                :show-select-all="false"
                @change="onChange"
                @search="onSearch"
                :listStyle="{flex:1,height:'400px'}"
            >
                <template
                    #children="{
                    direction,
                    filteredItems,
                    selectedKeys,
                    onItemSelectAll,
                    onItemSelect,
                    }"
                >
                    <div v-if="direction === 'left'" style="height:300px;overflow-y:auto">
                        <div style="display:flex" align="baseline">
                            <label class="custom-label" style="width:70px">定制</label>
                            <a-input v-model:value="portform.ruleName6" :disabled="info.isInfo" @change="changeExtra" style="border-right:none" allow-clear placeholder="请输入" />
                            <a-button type="primary" :disabled="!portform.ruleName6 || isRepeat" @click="addUser"><PlusOutlined class="text_img" /></a-button>
                        </div>
                        <!-- <a-tree
                            blockNode
                            checkable
          checkStrictly
          defaultExpandAll
          :checkedKeys="[...selectedKeys, ...targetKeys]"
                            :treeData="treeData"
                            @check="
            (_, props) => {
              onChecked(_, props, [...selectedKeys, ...targetKeys], onItemSelect);
            }
          "
          @select="
            (_, props) => {
              onChecked(_, props, [...selectedKeys, ...targetKeys], onItemSelect);
            }
          "
                            /> -->
                        <a-table
                        :show-header="false"
                        :columns="leftColumns"
                        :data-source="treeData"
                        size="small"
                        :row-selection="{selectedRowKeys:SelectedRowKeys,columnWidth:0,type:'radio',onChange:onSelectChange}"
                        :customRow="customRow"
                        expandRowByClick
                        :pagination="false"
                        >
                        <template #expandIcon="{record,expanded,onExpand}">
                            <CaretDownOutlined @click="(e) => {
	                onExpand(record, e);
                    SelectedRowKeys = [record.key];
            description = `<b>${record.title}</b> ${record.label ? `(<i>`+record.label+`</i>)` : ``}<br><br>${record.description}`;
	              }" v-if="expanded && record.children && record.children.length > 0" />
                            <CaretRightOutlined @click="(e) => {
	                onExpand(record, e);
                    SelectedRowKeys = [record.key];
            description = `<b>${record.title}</b> ${record.label ? `(<i>`+record.label+`</i>)` : ``}<br><br>${record.description}`;
	              }" v-else-if="record.children && record.children.length > 0" />
                        </template>
                        <template #action="{record,index}">
                            <a @click="handleTrans(record)" v-if="!record.children || record.children.length <= 0"><PlusSquareFilled /></a>
                        </template>
                        </a-table>
                    </div>
                    <div v-if="direction === 'right'" style="height:300px;overflow-y:auto">
                        <a-form
                        :model="portform" 
                        ref="portForm" 
                        :rules="rules" 
                        :colon="false"
                        >
                            <a-radio-group v-model:value="description" button-style="solid" class="target-radios" @click.capture>
                                <a-radio-button v-for="(user, index) in portform.extra" :key="user.key" :value="`<b>${user.title}</b> ${user.label ? `(<i>`+user.label+`</i>)` : ``}<br><br>${user.description}`">
                                <a-space  
                                    style="display: flex;line-height:52px"
                                    align="top"
                                    @click="()=>{description = `<b>${user.title}</b> ${user.label ? `(<i>`+user.label+`</i>)` : ``}<br><br>${user.description}`
                                    SelectedRowKeys = [user.key]}"
                                >
                                    <a-form-item 
                                        :name="['extra', index, 'value']"
                                        :label="`${user.label}`"
                                        :rules="{
                                            required: true,
                                            message: '请输入',
                                        }"
                                    >
                                        <a-select :mode="user.multiple ? 'multiple' : null" v-model:value="user.value" v-if="user.options" style="width:120px" @click.prevent>
                                            <a-select-option v-for="(item,index) in user.options" :key="index" :value="item">{{item}}</a-select-option>
                                        </a-select>
                                        <a-checkbox v-model:checked="user.value" v-else-if="user.checkbox"></a-checkbox>
                                        <a-input-number v-model:value.trim="user.value" :min="user.min ? user.min : -Infinity" :max="user.max ? user.max : Infinity" :step="1" :precision="0" v-else-if="user.inputType && user.inputType == 'number'" placeholder="请输入" style="width:120px" allow-clear></a-input-number>
                                        <a-input v-model:value="user.value" v-else placeholder="请输入" style="width:120px" allow-clear />
                                    </a-form-item>
                                    <MinusCircleOutlined @click="removeUser(user)" />
                                </a-space>
                                </a-radio-button>
                            </a-radio-group>
                        </a-form>
                    </div>
                </template>
            </a-transfer>
            <a-typography-paragraph>
            <pre v-html="description"></pre>
            </a-typography-paragraph>

        
            
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { saveClusterNode, selectClusterProfileList, updateClusterNode } from '@/api/backend/cluster';
import { savePort, selectPortGroupList, updatePort } from '@/api/backend/baremetal';
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { setextraSpecs } from '@/api/backend/devops/flavor';
import { message } from 'ant-design-vue';
import emiter from '@/utils/Bus';
import mockdataRaw from "@/common/meta/mockdata.json";
// treeData监听的table数据，已搜索
let mockdata = reactive({value:JSON.parse(JSON.stringify(mockdataRaw))});
// 搜索过滤前的table数据，已穿梭未搜索
let beforeSearch = JSON.parse(JSON.stringify(mockdataRaw));
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info: {
        type:Object,
        default(){
            return {
                isShow: false,
            }
        }
    }
})
const leftColumns = [
  {
    dataIndex: 'title',
    key:'title'
  },
  {
    dataIndex: 'action',
    key:'action',
    slots:{customRender:'action'},
    align:'right'
  },
];
let count = 117;
const inputValue1 = ref('');
const mockData = ref([]);
const description = ref('点击每个条目来获取描述');
const SelectedRowKeys = ref([]);
const targetKeys = ref([]);
const isRepeat = ref(false);
const portForm = ref();
const profilelist = ref([])
const portgrouplist = ref([])
const defaultform = {
    "id":'',
    "ruleName6":'',
    "extra1":[],
    "extra":[],
    "extraSpecsaMap":{}
}
const portform = reactive({
    "id":'',
    "ruleName6":'',
    "extra1":[],
    "extra":[],
    "extraSpecsaMap":{},
})
const rules = {
    ruleName6:[{required:true,message:'请输入',trigger:'change'}],
}
// 数字，子节点也计算
function flatten(list = [],extra1) {
  list.forEach(item => {
    if(extra1)
        if(Object.keys(extra1).includes(item.label)){
            item.value = extra1[item.label];
            targetKeys.value.push(item.key);
            portform.extra1.push(item);
            portform.extra.push(item);
        }
    if(!item.children)
        mockData.value.push(item);
    flatten(item.children,extra1);
  });
}

// mockData的filterItems因计算数字而多出了展示出来的节点，不使用，使用自定义treeData
const handleTreeData = (data1, targetkeys = [], count, parent) => {
    data1.forEach((item,index) => {
        item.hide = (targetkeys.includes(item.key) || item.hideSearch);
        if (item.children) {
            item.count = 0;
            handleTreeData(item.children, targetkeys, item.count, item);
        }
        // 已调位置解决多级顶级的空显示
        if(item.hide){
            count++;
        }
        if(index >= data1.length - 1){
            if(count == data1.length ){
                parent.hide = true;
            }
        }
    });
    return data1;
}
// 受控展示的数据，已穿梭已搜索
const treeData = computed(() => {
    let res = handleTreeData(mockdata.value, targetKeys.value);
    return res;
});
const isChecked = (selectedKeys, eventKey) => {
  return selectedKeys.indexOf(eventKey) !== -1;
}
const onChecked = (_, e, checkedKeys, onItemSelect) => {
    const { eventKey } = e.node;
    onItemSelect(eventKey, !isChecked(checkedKeys, eventKey));
};
const handleSearch = (data1,value) => {
    data1.forEach((item,index) => {
        if (!item.children) {
            // console.log("serch",value,item.label,item.title,item.label?.indexOf(value),item.title?.indexOf(value),item.label?.indexOf(value) == -1 && item.title?.indexOf(value) == -1)
            item.hideSearch = (item.label?.indexOf(value) == -1 && item.title?.indexOf(value) == -1);
        } else {
            handleSearch(item.children,value);
        }
    });
}
const onSearch = (direction,value) => {
    if(direction === 'left'){
        handleSearch(mockdata.value,value)
    }
    if(direction === 'right')
    portform.extra = portform.extra1.filter(item=>item.label.indexOf(value) !== -1);
}
const onSelectChange = (selectedRowKey, selectedRows) => {
    SelectedRowKeys.value = [selectedRowKey];
}
const customRow = (record) => {
    return {
        style:record.hide ? {
            display:'none'
        }:{},
        onClick: (e) => {
            SelectedRowKeys.value = [record.key];
            description.value = `<b>${record.title}</b> ${record.label ? `(<i>`+record.label+`</i>)` : ``}<br><br>${record.description}`;
        }
    }
}
const handleTrans = (record) => {
    
    targetKeys.value.push(record.key);
    // handleTreeData(mockdata, targetKeys.value)
    portform.extra1.push(record);
    portform.extra = [...portform.extra1];
}
const changeExtra = (e) => {
    isRepeat.value = portform.extra1.some((item)=>{
        return item.label == e.target.value;
    })
}
const cancel = () => {
    props.info.isShow = false;
    portForm.value.resetFields();
    portform.id = '';
    targetKeys.value = [];
    portform.ruleName6 = '';
    portform.extra = [];
    portform.extra1 = [];
    portform.extraSpecsaMap = {};
    SelectedRowKeys.value = [];
    description.value = '点击每个条目来获取描述';
    mockData.value = [];
    // Object.assign(portform,defaultform)
}
const save = () => {
    let portform1 = {...portform};
    if(portform.extra1 && portform.extra1.length > 0){
        portform1.extraSpecsaMap = {};
        portform.extra1.forEach((item,index)=>{
            portform1.extraSpecsaMap[item.label] = item.value;
        })
        // portform1.extraSpecsaMap = JSON.stringify(portform1.extraSpecsaMap);
    }else{
        portform1.extraSpecsaMap = undefined;
    }
    portform1.extra1 = undefined;
    portform.ruleName6 = null;
    portForm.value.validate().then(async ()=>{
        let res = await setextraSpecs(portform1)
        if(res.code == 0 && res.data !== false){
            message.success("设置成功");
            emit('getlist');cancel()
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '设置失败' : res.msg);
        }
    })
    // proxy.$handleSave(portForm.value, setextraSpecs, setextraSpecs, false, portform1, ()=>{emit('getlist');cancel()},null)
}
const setInfo = (record) => {
    
    let record1 = {...record};
    // if(record1.extraSpecs){
        let extra1 = JSON.parse(record1.extraSpecs);
        flatten(JSON.parse(JSON.stringify(mockdata.value)),extra1);
        // extra1.forEach((item,index)=>{
            // mockdata.value.forEach((it,ii)=>{
            //     if(Object.keys(extra1).includes(it.label)){
            //         it.value = extra1[it.label];
            //         targetKeys.value.push(it.key);
            //         console.log("before",portform.extra1,portform.extra);
            //         portform.extra1.push(it);
            //         portform.extra.push(it);
            //         console.log("after",portform.extra1,portform.extra);
            //     }
            // })
            
        // })
        // portform.extra1 = record1.extra1;
    // }
    portform.id = record1.id;
    
}
const addUser = () => {
    if(!isRepeat.value){
        portform.extra1.push({
            key:String(++count),
            title:portform.ruleName6,
            label:portform.ruleName6,
            description:``,
        });
        portform.extra = [...portform.extra1];
    }
    isRepeat.value = portform.extra1.some((item)=>{
        return item.label == portform.ruleName6;
    })
};

const removeUser = item => {
    let index = portform.extra.indexOf(item);
    let index1 = portform.extra1.indexOf(item);
    if(index !== -1){
        portform.extra.splice(index, 1);
        if (index1 !== -1) {
            targetKeys.value.splice(index1, 1);
            portform.extra1.splice(index1, 1);
        }
    }
    
};
onMounted(() => {})
defineExpose({portform, setInfo})
</script>
<style lang='scss' scoped>
:deep(.ant-space-align-baseline){.ant-space-item:first-child{width:100%}}
:deep(.ant-table td){white-space: nowrap;}
:deep(.ant-table-selection-column){display: none;}
.custom-label{
    cursor: pointer;
    line-height: 30px;
    padding: 0 10px;
    border:1px solid #d9d9d9;
    background:#f5f5f5;
    display: inline-block;width:150px;overflow:hidden;text-overflow:ellipsis;
}
.target-radios{
    width: 100%;
    .ant-radio-button-wrapper{width: 100%;height: auto;border-left-width: 1px;border-left-style: solid;border-radius: 0;}
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled){color: initial;background-color: #e6f7ff;}
    .ant-form-item{
        margin: 10px 0;
        :deep(.ant-form-item-label > label){
            cursor: pointer;
            line-height: 32px;
            border:1px solid #d9d9d9;
            background:#f5f5f5;
            display: inline-block;width:150px;overflow:hidden;text-overflow:ellipsis;
        }
    }
}
</style>