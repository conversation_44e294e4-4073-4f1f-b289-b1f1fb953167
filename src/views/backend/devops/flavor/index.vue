<template>
<div class="cloudContent">
    <div class="cloudRight">
        <div class="buttonPadding">
            <a-form layout="inline" :model="searchform" class="searchform">
            <a-form-item label="虚机类型名称">
                <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.name" allowClear />
            </a-form-item>
            </a-form>
            <MoreSearch @search="handleSearch" @reset="handleAllReset" />
        </div>
        <div class='innerPadding'>
        <a-row class="buttonGroup">
            <a-button type="primary" class="btnMargin" @click="handleAdd"> 新增 </a-button>
            <a-button @click="$handleDel(selectRowIds,deleteFlavor,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                批量删除
            </a-button>
        </a-row>
        <a-table
         :row-selection="rowSelection" 
         :columns="columns"  
         row-key="id" 
         :data-source="flavorlist" 
         :pagination="pagination" 
         @change="changeTable"
         :scroll="{x:true}"
        >
            <template #index={record,index}>
                {{index+1+(pagination.pageSize * (pagination.current-1))}}
            </template>
            <template #totalDisk={record}>
                {{record.totalDisk >= 1024 ? (record.totalDisk/1024).toFixed(2) + ' TB' : record.totalDisk + ' GB'}}
            </template>
            <template #ram={record}>
                {{record.ram < 1024 ? record.ram + ' MB' : (record.ram >= 1024 * 1024 ? (record.ram / (1024 * 1024)).toFixed(2) + ' TB' : (record.ram/1024).toFixed(1) + ' GB')}}
            </template>
            <template #publicStatus={record}>
                {{record.publicStatus == 1 ? '是':'否'}}
            </template>
            <template #action={record}>
                <a-button class="button_V" @click="handleExtra(record)">设置附加参数</a-button>
                <a-button class="button_V" @click="handleView(record)">查看</a-button>
                <a-button class="button_D" @click="$handleDel([record.id],deleteFlavor,getList)">删除</a-button>
            </template>
        </a-table>
    </div>
    </div>
</div>
    <Info :info="info" ref="flavorDialog" @getlist="getList" />
    <Extra :info="extrainfo" ref="extraDialog" @getlist="getList" />
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue"
import Extra from "./extra.vue"
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { getFlavorList, deleteFlavor } from "@/api/backend/devops/flavor";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { useRoute } from "vue-router";
const { proxy,appContext } = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(['changes'])
const props = defineProps({
    selectedRowKeys: {
        type:Array,
        default(){
            return []
        }
    }
})
const flavorDialog = ref(null);
const extraDialog = ref(null);
const loading = ref(false);
const flavorlist = ref([]);
let selectRowIds: string[] = ref([]);
const info = reactive({
    isInfo:false,
    isAdd:true,
    isShow:false
})
const extrainfo = reactive({
    isShow:false
})
const searchform = reactive({
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    domainId:route.query.domainId,
    name:'',
    pageIndex:1,
    pageSize:10
})

const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '名称', dataIndex: 'name', key: 'id' ,align:'left'},
    {title: '硬盘大小', dataIndex: 'totalDisk', key: 'id', slots: { customRender: 'totalDisk' } ,align:'center'},
    {title: '核数', dataIndex: 'vcpus', key: 'id' ,align:'center'},
    {title: '内存', dataIndex: 'ram', slots: { customRender: 'ram' }, key: 'id' ,align:'center'},
    {title: '是否公开', dataIndex: 'publicStatus', slots: { customRender: 'publicStatus' }, key: 'id' ,align:'center'},
    {title: '创建人', dataIndex: 'createUserName', key: 'id' ,align:'center'},
    {title: '创建时间', dataIndex: 'createTime', key: 'id' ,align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:220 }
];
const getList = async () => {
    searchform.cloudId = route.query.cloudId;
    searchform.projectId = route.query.projectId;
    searchform.domainId = route.query.domainId;
    flavorlist.value = await proxy.$getList(loading, getFlavorList, searchform, pagination, getList )
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    name: ""
  })
//   getList();
}
const handleExtra = (record) => {
    extrainfo.isShow = true;
    proxy.$nextTick(()=>{
        extraDialog.value.setInfo(record)
    })
}
const handleView = (record) => {
    info.isInfo = info.isShow = true;
    let record1 = {...record};
    record1.ram = record1.ram;
    if(record1.publicStatus == 1)
    record1.publicStatus = true
    else
    record1.publicStatus = false
    Object.assign(flavorDialog.value.flavorform, record1)
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
}
const handleEdit = (record) => {
    info.isAdd = false;
    info.isShow = true;
    let record1 = {...record};
    record1.ram = record1.ram;
    if(record1.publicStatus == 1)
    record1.publicStatus = true
    else
    record1.publicStatus = false
    Object.assign(flavorDialog.value.flavorform, record1)
}
onMounted(() => {
    getList();
    // if(route.path == '/admin/devops/flavor')
    proxy.$mitt.on('getflavorlist',getList)
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
// .buttonPadding,.innerPadding{min-width: 1023px;}
</style>