<template>
    <div class=''>
        <a-modal 
        v-model:visible="info.isShow"
        @cancel="cancel"
        :maskClosable="info.isInfo"
        centered
        :getContainer="modalBindNode"
        >
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isInfo ? '查看' : (info.isAdd ? '新增':'修改')}}</span>
                    <a-popover trigger="click" placement="leftTop">
                        <template #content>
                            <span>{{content.flavor_add}}</span>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="flavorform" ref="flavorForm" :rules="rules" :labelCol="{span:6}">
                <a-form-item label="ID" name="thirdFlavorId" v-if="info.isInfo">
                    {{flavorform.thirdFlavorId}}
                </a-form-item>
                <a-form-item label="虚机类型名称" name="name">
                    <a-input v-model:value.trim="flavorform.name" placeholder="请输入" style="width:calc(100% - 25px)" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="CPU核数" name="vcpus">
                    <a-input-number v-model:value.trim="flavorform.vcpus" :min="1" :step="1" :precision="0" style="width:calc(100% - 25px)" placeholder="请输入" :disabled="info.isInfo"></a-input-number>
                </a-form-item>
                <a-form-item label="内存大小" name="ram">
                    <a-input-number v-model:value.trim="flavorform.ram" :min="1" placeholder="请输入" :disabled="info.isInfo" style="width:calc(100% - 25px)"></a-input-number> MB
                </a-form-item>
                <a-form-item label="磁盘大小" name="totalDisk">
                    <a-input-number v-model:value.trim="flavorform.totalDisk" :min="1" placeholder="请输入" :disabled="info.isInfo" style="width:calc(100% - 25px)"></a-input-number> GB
                </a-form-item>
                <a-form-item label="根磁盘大小" name="rootDist">
                    <a-input-number v-model:value.trim="flavorform.rootDist" :min="0" :step="1" :precision="0" placeholder="请输入" :disabled="info.isInfo" style="width:calc(100% - 25px)"></a-input-number> GB
                </a-form-item>
                <a-form-item label="临时磁盘大小" name="ephemeralDisk">
                    <a-input-number v-model:value.trim="flavorform.ephemeralDisk" :min="0" :step="1" :precision="0" placeholder="请输入" :disabled="info.isInfo" style="width:calc(100% - 25px)"></a-input-number> GB
                </a-form-item>
                <a-form-item label="是否公开" name="publicStatus">
                    <a-switch v-model:checked="flavorform.publicStatus" :disabled="info.isInfo" />
                </a-form-item>
                <a-form-item label="说明" name="info">
                    <a-textarea v-model:value="flavorform.info" placeholder="请输入说明内容" :disabled="info.isInfo" allow-clear></a-textarea>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { saveFlavor,updateFlavor } from "@/api/backend/devops/flavor"
import { selectOpenstList } from "@/api/backend/devops/domain"
import { selectDictList } from '@/api/backend/systems/dictionary';
import { userStore } from '@/store/user';
import {content} from "@/common/explain/modal"
import { useRoute } from 'vue-router';
import router from '@/router';
const route = useRoute()
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})
const modalBindNode = ref();
const flavorForm = ref();
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    projectId:router.currentRoute.value.query.projectId,
    thirdProjectId:route.query.thirdProjectId,
    // createUserId:userStore().userId,
    name:'',
    vcpus:'',
    ram:'',
    totalDisk:'',
    rootDist:undefined,
    ephemeralDisk:undefined,
    publicStatus:''
}
const flavorform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    projectId:router.currentRoute.value.query.projectId,
    thirdProjectId:route.query.thirdProjectId,
    // createUserId:userStore().userId,
    name:'',
    vcpus:'',
    ram:'',
    totalDisk:'',
    rootDist:undefined,
    ephemeralDisk:undefined,
    publicStatus:''
})
const rules = {
    name:[{required:true, message:'请输入',trigger:'change'}],
    vcpus:[{type:'integer',required:true, message:'请输入大于0正整数',trigger:'change'}],
    ram:[{type:'integer',required:true, message:'请输入大于0正整数',trigger:'change'}],
    totalDisk:[{type:'integer',required:true, message:'请输入大于0正整数',trigger:'change'}],
    rootDist:[{type:'integer',required:false, message:'请输入正整数',trigger:'change'}],
    ephemeralDisk:[{type:'integer',required:false, message:'请输入正整数',trigger:'change'}],
}
const options3 = ref([])
const selectopenstList = async () => {
    let res = await selectOpenstList({cloudId:localStorage.getItem('cloudId')})
    if(res.code == 0){
        options3.value = res.data
    }
}
const handleSave = () => {
    let flavorform1 = {...flavorform}
    proxy.$handleSave(flavorForm.value, saveFlavor, updateFlavor, props,flavorform1, ()=>{cancel();emit('getlist')},null,()=>{
        flavorform1.ram = flavorform1.ram;
        flavorform1.totalDisk = flavorform1.totalDisk;
        flavorform1.rootDist = flavorform1.rootDist;
        flavorform1.ephemeralDisk = flavorform1.ephemeralDisk;
        if(flavorform1.publicStatus == true)
        flavorform1.publicStatus = 1;
        else
        flavorform1.publicStatus = 0;
    })
}
const cancel = () => {
    props.info.isShow = false
    props.info.isInfo = false
    // isInfo.value = false
    flavorForm.value.resetFields()
    Object.assign(flavorform,defaultform)
}
onMounted(() => {modalBindNode.value = document.getElementsByClassName('full-modal')[0] ? document.getElementsByClassName('full-modal ant-modal-body')[0] : proxy.modalBindNode;})
defineExpose({flavorform})
</script>
<style lang='scss' scoped>
</style>