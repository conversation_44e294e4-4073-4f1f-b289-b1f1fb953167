<template>
    <div class=''>
        <a-modal centered
            v-model:visible="info.isShow"
            ok-text="提交"
            cancel-text="取消"
            @ok="save"
            @cancel="cancel"
            width="720.8px"
            :maskClosable="!info.isAdd && info.isBind"
            :getContainer="modalBindNode"
        >
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isAdd?'新增':(info.isBind ? '绑定':'解绑')}}</span>
                    <a-popover trigger="click" placement="leftTop">
                        <template #content>
                            <span>{{content.float_add}}</span>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <a-form :model="floatform" ref="floatformRef" :rules="rules" :labelCol="{span:3}">
                <a-form-item label="资源池" name="networkId">
                    <a-select
                        v-model:value="floatform.networkId"
                        placeholder="请选择"
                        allowClear>
                        <a-select-option v-for="(item,index) in netList" :key="index" :value="item.id">{{item.networkName}}</a-select-option>
                    </a-select>
                </a-form-item>
                    <a-form-item label="数量" name="number">
                   <a-input-number  v-model:value.trim="floatform.number" :min="1"  :step="1" :precision="0" style="width:100%" placeholder="请输入" :disabled="info.isInfo" allow-clear></a-input-number>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { saveFloatingip, updateFloatingip } from "@/api/backend/devops/floatingip";
import { selectNetworkList } from "@/api/backend/devops/network";
import {content} from "@/common/explain/modal"
import router from '@/router';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow:false,
                isBind:false
            }
        }
    }
})
const floatform = reactive({networkId:undefined,number:"",cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId})
const floatformRef = ref()
const netList = ref([])
const rules = {
    networkId:[{required:true,type:'number',message:'请选择网络',trigger:'change'}],
     number:[{required:true, type:'number', message:'仅支持输入0-100之间的整数',trigger:'change',transform(value){ 
                        if(value){
                            // 将输入的值转为数字
                            var val = Number(value)
                            // 正则表达式校验输入的数字是否在0-100之内并且属于整数
                            if(/^(?:[1-9]?\d|100)$/.test(val)) return val
                            // 返回false即为校验失败
                            return false
                        }
                    }}],
}
const getNetlist = async () => {
    let cloudId = router.currentRoute.value.query.cloudId;
    let projectId = router.currentRoute.value.query.projectId;
    let res = await selectNetworkList({cloudId,projectId,routerExternal:1})
    if(res.code == 0){
        netList.value = res.data;
    }
}
const cancel = () => {
    props.info.isShow = false;
    floatform.networkId = undefined;
    floatform.number = '';
    floatformRef.value.resetFields();
}
const save = () => {
    proxy.$handleSave(floatformRef.value, saveFloatingip, updateFloatingip, props, floatform,()=>{emit('getlist');cancel()})
}
onMounted(() => {})
defineExpose({getNetlist})
</script>
<style lang='scss' scoped>
</style>