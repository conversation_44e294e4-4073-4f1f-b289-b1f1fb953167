<template>
    <div class=''>
        <a-modal title="端口映射" v-model:visible="info.isShow" @cancel="cancel" :maskClosable="false" centered :getContainer="modalBindNode">
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" @click="save" 
                :disabled="disabledSubmit"
                >提交</a-button>
            </template>
            <a-form-item label="浮动IP地址">
                {{portform.floatingIpAddress}}
            </a-form-item>
            <a-table :columns="columns" :data-source="dataSource" size="small" :pagination="false" bordered>
                <template #externalPort="{ text, record }">
                    <a-input v-model:value="record.externalPort" size="small" :disabled="record.id" />
                </template>
                <!-- <template #port="{record}">
                    <a-select
                        v-model:value="record.port" 
                        show-search
                        placeholder="输入搜索"
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :not-found-content="null"
                        :options="exportList"
                        @change="handleChange"
                        size="small"
                    >
                    </a-select>
                </template> -->
                <template #internalIpAddress="{record,index}">
                    <a-select
                        v-model:value="record.internalIpAddress" 
                        show-search
                        :default-active-first-option="false"
                        @change="handleChange"
                        :show-arrow="false"
                        :not-found-content="null"
                        style="width:170px" size="small"
                        :disabled="record.id"
                    >
                        <template v-for="(item) in interportList" :key="item.id">
                            <a-select-option :value="item.value" :id="item.id" :index="index">{{item.value}}</a-select-option>
                        </template>
                    </a-select>
                </template>
                <template #internalPort="{ text, record }">
                    <a-input v-model:value="record.internalPort" size="small" :disabled="record.id" />
                </template>
                <template #protocol="{record}">
                    <a-select v-model:value="record.protocol" style="width:70px" size="small" :disabled="record.id" :getPopupContainer="triggerNode => triggerNode.parentNode">
                        <a-select-option value="tcp">TCP</a-select-option>
                        <a-select-option value="udp">UDP</a-select-option>
                    </a-select>
                </template>
                <template #operation="{ record,index }">
                    <div class="editable-row-operations">
                        <span style="display:flex">
                            <a-popconfirm title="确定删除?" @confirm="onDelete(record)" :disabled="index >= dataSource.length - 1">
                                <!-- <a>删除</a> -->
                                <a-button type="link" size="small" :disabled="index >= dataSource.length - 1">删除</a-button>
                            </a-popconfirm>
                            <!-- <a @click="handleCopy(record)" >克隆</a> -->
                            <a-button type="link" @click="handleCopy(record)" size="small">克隆</a-button>
                        </span>
                    </div>
                </template>
            </a-table>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { deleteFloatingipPort, getflaotipInfo, saveFloatingipPort } from '@/api/backend/devops/floatingip';
import { selectMapPortList, selectPortList } from '@/api/backend/devops/network';
import emiter from '@/utils/Bus';
import { message } from 'ant-design-vue';
import { computed, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false
            }
        }
    }
    
})
const columns = reactive([
    {
    title: '源端口号',
    dataIndex: 'externalPort',
    width: '16%',
    slots: { customRender: 'externalPort' },
  },
//   {
//     title: '目标地址',
//     dataIndex: 'port',
//     colSpan: 2,
//     // width: '1%',
//     slots: { customRender: 'port' },
//   },
  {
    title: '目标地址',
    dataIndex: 'internalIpAddress',
    // colSpan: 0,
    // width: '15%',
    // align:'center',
    slots: { customRender: 'internalIpAddress' },
  },
  {
    title: '目标端口号',
    dataIndex: 'internalPort',
    width: '16%',
    slots: { customRender: 'internalPort' },
  },
  {
    title: '协议',
    dataIndex: 'protocol',
    // width: '16%',
    slots: { customRender: 'protocol' },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '16%',
    align:'center',
    slots: { customRender: 'operation' },
  },
]);
const portform = reactive({
    floatingIpAddress:''
})
const editableData = reactive({});
const loading = ref(false);
const rawportlist = ref([]);
// const exportList = ref([]);
const interportList = ref([]);
const count = computed(() => dataSource.value.length + 1);
const dataSource = ref([
  {
    "key":1,
    "cloudId":0,
    "externalPort": "",
    "floatingIpId": 0,
    "internalIpAddress": "",
    "internalPort": "",
    "internalPortId":0,
    "protocol": undefined,
  }
]);
const disabledSubmit = computed(() => {
    return !dataSource.value.some((item,index)=>(!item.id && item.externalPort && item.internalIpAddress && item.internalPort && item.internalPortId && item.protocol))
}
)
// const data = [];

// for (let i = 0; i < 1; i++) {
//     data.push({
//         key: i.toString(),
//         name: `Edrward ${i}`,
//         age: 32,
//         address: `London Park no. ${i}`
//     });
// }
const handleCopy = (record) => {
    // console.log('record',record)
    const newData = {
        key: `${count.value}`,
        cloudId: record.cloudId,
        externalPort: record.externalPort,
        floatingIpId: record.floatingIpId,
        internalIpAddress: record.internalIpAddress,
        internalPort: record.internalPort,
        internalPortId: record.internalPortId,
        protocol: record.protocol
      };
      dataSource.value.splice(Number(record.key)+1,0,newData);
}
const onDelete = async (record) => {
    if(record.id){
        let res = await deleteFloatingipPort([record.id])
        if(res.code == 0 && res.data !== false){
            message.success('删除成功');
            dataSource.value = dataSource.value.filter(item => item.key !== record.key);
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '删除失败' : res.msg);
        }
    }else
        dataSource.value = dataSource.value.filter(item => item.key !== record.key);
        console.log("dataSource",dataSource.value,dataSource.value[0].externalPort, dataSource.value[0].internalIpAddress, dataSource.value[0].internalPort, dataSource.value[0].internalPortId, dataSource.value[0].protocol)
};
const handleChange = (value,option) => {
    // interportList.value = rawportlist.value[b.key].portIpEntityList;
    dataSource.value[option.index].internalPortId = option.id;
    console.log("option",option.id,dataSource.value)
}
// const changeTable = (a,b,c) => {
//     console.log('table',a,b,c)
// }
const cancel = () => {
    props.info.isShow = false;
    dataSource.value = [
  {
    "key":1,
    "cloudId":0,
    "externalPort": "",
    "floatingIpId": 0,
    "internalIpAddress": "",
    "internalPort": "",
    "internalPortId":0,
    "protocol": undefined,
  }
];
}
const save = () => {
    let dataSource1 = [];
    dataSource.value.forEach(async (item,index)=>{
        if(!item.id && item.externalPort && item.internalIpAddress && item.internalPort && item.internalPortId && item.protocol){
            dataSource1.push(item)
        }
        if(index >= dataSource.value.length - 1){
            if(dataSource1.length <= 0){
                message.error('请填写完整后提交');
                return;
            }
            let res = await saveFloatingipPort(dataSource1);
            if(res.code == 0 && res.data !== false){
                message.success('端口映射成功');
                // emit('getlist')
                cancel();
            }else if(res.code == 0){
                message.error((!res.msg || res.msg == 'success') ? '端口映射失败' : res.msg);
            }
        }

    })
    
}
const setInfo = (record) => {
    getMapList(record)
    
    // dataSource.value[0].internalPortId = record.id;
    // dataSource.value = data;
}
const getMapList = async (record) => {
    let res = await getflaotipInfo({id:record.id})
    if(res.code == 0){
        if(!res.data.floatingIpPortForwardingEntities)
            res.data.floatingIpPortForwardingEntities = [];
        dataSource.value = res.data.floatingIpPortForwardingEntities.concat(dataSource.value);
        getExportList();
        Object.assign(portform,record)
        dataSource.value[dataSource.value.length-1].cloudId = route.query.cloudId;
        dataSource.value[dataSource.value.length-1].floatingIpId = record.id;
        dataSource.value.forEach((item,index)=>{
            item.key = index;
        })
    }
}
const getExportList = async () => {
    loading.value = true;
    let res = await selectMapPortList({cloudId:route.query.cloudId,projectId:route.query.projectId})
    if(res.code == 0){
        let exportipList = [];
        let rawportList = [];
        let countPort = 0;
        res.data.forEach((item,index)=>{
            // if(item.portIpEntityList && item.portIpEntityList.length > 0){
                // exportipList.push({value:item.portIpEntityList[0].id,key:countPort++})
            exportipList.push({value:item.ipAddress,key:countPort++,id:item.portId})
            rawportList.push(item)
            // }
            if(index >= res.data.length - 1){
                interportList.value = exportipList;
                rawportlist.value = rawportList;
                
            }
        })
        // rawportlist.value = res.data;
        loading.value = false;
    }else{
        loading.value = false;
    }
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
.editable-row-operations a:first-child {
  margin-right: 8px;
}
</style>