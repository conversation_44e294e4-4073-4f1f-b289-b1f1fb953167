<template>
    <div class="cloudContent">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                <a-form-item label="浮动ip">
                    <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.ip" allowClear />
                </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd">新增 </a-button>
                    <a-button @click="$handleDel(selectRowIds,deleteFloatingip,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table :row-selection="rowSelection" :columns="columns" row-key="id" :scroll="{ x: true }" :data-source="floatingiplist" :pagination="pagination" @change="changeTable">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #serverName="{record}">
                        {{record.thirdServerId ? record.serverName : '-'}}
                    </template>
                    <!-- <template #thirdServerId={record}>
                        {{record.thirdServerId ? '已占用':'未占用'}}
                    </template> -->
                    <template #action={record}>
                        <a-button class="button_E" @click="handlePort(record)" v-if="record.status.toLowerCase() != 'active'">端口映射</a-button>
                        <a-button class="button_E" @click="handleUnbind(record)" v-if="record.status == 'active'">解除绑定</a-button>
                        <a-button class="button_E" @click="handleBind(record)" v-else>绑定虚机</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteFloatingip,getList)">删除</a-button>
                    </template>
                </a-table>
            </div>
        </div>
        <Info ref="floatingipDialog" :info="info" @getlist="getList" />
        <a-modal title="绑定虚机" v-model:visible="info.isBind" ok-text="提交" cancel-text="取消" @ok="bind" @cancel="cancelBind" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form :model="bindform" ref="bindForm" :rules="rules1">
                <a-form-item label="虚机" name="serverId">
                    <a-select v-model:value="bindform.serverId" @change="getIpderss" placeholder="请选择虚机" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in serverlist" :key="index" :value="item.id" :label="item.serverName">{{item.serverName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="待连接的接口" name="ipId" v-if="bindform.serverId">
                    <a-select
                        v-model:value="bindform.ipId"
                        placeholder="请选择"
                        allowClear>
                        <a-select-option v-for="(item,index) in addressList " :key="index" :value="item.id">{{item.addr}}</a-select-option>
                    </a-select>
                </a-form-item>
            </a-form>
        </a-modal>
        <Port ref="portMap" :info="portinfo" @getlist="getList" />
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Port from "./port.vue"
import Info from "./info.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue"
import { CloseOutlined, ExclamationCircleOutlined, PlusOutlined } from "@ant-design/icons-vue"
import { computed, createVNode, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { getFloatingipList, deleteFloatingip, bindFloatingip, unbindFloatingip } from "@/api/backend/devops/floatingip";
import { postaddip, postremoveip, selectServerList } from "@/api/backend/devops/server";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { useRoute } from "vue-router";
import { message, Modal } from "ant-design-vue";
import { getselectserveIP } from "@/api/backend/devops";
import router from "@/router";
const { proxy } = getCurrentInstance();
const route = useRoute()
const floatingipDialog = ref(null);
const portMap = ref(null);
const loading = ref(false);
const floatingiplist = ref([]);
let selectRowIds: string[] = ref([]);
const portinfo = reactive({
    isShow:false
})
const info = reactive({
    isAdd:true,
    isBind:false,
    isShow:false
})
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    ip:'',
    pageIndex:1,
    pageSize:10
})
const rules1 = {
    serverId:[{required:true,type:'number',message:'请选择虚机',trigger:'change'}],
    ipId:[{required:true,type:'number',message:'请选择接口',trigger:'change'}]
}
const bindform = reactive({floatIpId:'',ipId:undefined,serverId:undefined});
const bindForm = ref()
const serverlist = ref([])
const addressList = ref([])
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '浮动IP', dataIndex: 'floatingIpAddress', key: 'id',align:'center'},
    {title: '虚机名称', dataIndex: 'serverName', slots: { customRender: 'serverName' }, key: 'id',align:'left'},
    {title: 'IP池', dataIndex: 'pool', key: 'id',align:'center'},
    {title: '状态', dataIndex: 'statusText', key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:220 }
];
const getList = async () => {
    searchform.cloudId = localStorage.getItem('cloudId');
    floatingiplist.value = await proxy.$getList(loading, getFloatingipList, searchform, pagination, getList )
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    ip: ""
  })
//   getList();
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
    info.isBind = false;
    proxy.$nextTick(()=>{
        floatingipDialog.value.getNetlist()
    })
}
const handleEdit = (record) => {
    info.isBind = false;
    info.isAdd = false;
    info.isShow = true;
    Object.assign(floatingipDialog.value.floatingipform, record)
}
const handleBind = (record) => {
    info.isBind = true;
    selectServerlist();
    bindform.floatIpId = record.id
}
const relieveIP = (serverID,remove)=>{
    postremoveip({
        serverID,
        remove
    }).then((res)=>{
        if(res.code==0 && res.data !== false){
            message.success("解绑成功")
            getList()
        }
    })
}
const handleUnbind = (record) => {
    Modal.confirm({
        // title: 'Confirm',
        icon: createVNode(ExclamationCircleOutlined),
        content: `是否同时释放浮动ip`,
        okText: '是',
        cancelText: '否',
        maskClosable: true,
        closeIcon: createVNode(CloseOutlined),
        onOk(){
            relieveIP(record.serverId,true)
        },
        onCancel(){
            relieveIP(record.serverId,false)
        }
    });
}
const cancelBind = () => {
    info.isBind = false;
    bindform.serverId = undefined;
    bindform.ipId = undefined;
    bindform.floatIpId = '';
}
const bind = () => {
    bindForm.value.validate().then(async ()=>{
        let res = await postaddip(bindform)
        if(res.code == 0 && res.data !== false){
            message.success('虚机绑定浮动IP成功');
            getList()
            cancelBind()
        }else if(res.code == 0){
            message.error((!res.msg || res.msg == 'success') ? '虚机绑定失败' : res.msg);
        }
    })
    
}
const handlePort = (record) => {
    portinfo.isShow = true;
    portMap.value.setInfo(record)
}
const getIpderss = async (serverId) => {
    let res = await getselectserveIP({serverId})
    addressList.value=res.data
}
const selectServerlist = async () => {
    let res = await selectServerList({cloudId:localStorage.getItem('cloudId'),projectId:router.currentRoute.value.query.projectId})
    if(res.code == 0){
        serverlist.value = res.data;
    }
}
onMounted(() => {
    getList()
    if(route.path == '/admin/devops/floatingip')
        proxy.$mitt.on('getlist',getList)
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
// .buttonPadding,.innerPadding{min-width: 769px;}
</style>