<template>
    <div class="cloudContent">
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                    <a-form-item label="密钥对名称">
                        <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.name" allowClear />
                    </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class="innerPadding">
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="toAdd">新增</a-button>
                    <a-button @click="$handleDel(selectRowIds,deleteKeypair,()=>{selectRowIds = [];getKeypairlist()})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table
                 :row-selection="rowSelection" 
                 :columns="columns" 
                 row-key="id" 
                 :data-source="keypairlist" 
                 :pagination="pagination" 
                 @change="changeTable"
                 :scroll="{x:true}">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #action="{record}">
                        <a-button class="button_V" @click="handleView(record)">查看</a-button>
                        <!-- <a-button class="button_E" @click="handleEdit(record)">编辑</a-button> -->
                        <a-button class="button_D" @click="$handleDel([record.id],deleteKeypair,getKeypairlist)">删除</a-button>
                    </template>
                </a-table>
            </div>
            <Info :info="info" ref="keypairDialog" @getlist="getKeypairlist" />
        </div>
    </div>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { getKeypairList, deleteKeypair } from "@/api/backend/devops/keypair";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import router from "@/router";
const { proxy } = getCurrentInstance();
const keypairDialog = ref();
const keypairlist = ref([]);
const loading = ref(false);
let selectRowIds: string[] = ref([]);
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getKeypairlist();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title:'名称',dataIndex:'name'},
    {title:'指纹',dataIndex:'fingerPrint'},
    {title:'创建人',dataIndex:'createUserName',width:110},
    {title:'创建时间',dataIndex:'createTime', width:164},
    {title:'修改人',dataIndex:'updateUserName',width:110},
    {title:'修改时间',dataIndex:'updateTime', width:164},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
]
const searchform = reactive({
    name:'',
    pageIndex:1,
    pageSize:10,
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId
})
const info = reactive({
    isAdd:true,
    isInfo:false,
    isShow:false
})
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getKeypairlist();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    name: ""
  })
//   getKeypairlist();
}
const toAdd = () => {
    info.isAdd = info.isShow = true;
    info.isInfo = false;
}
const getKeypairlist = async () => {
    keypairlist.value = await proxy.$getList(loading, getKeypairList, searchform, pagination, getKeypairlist)
}
const handleView = (record) => {
    info.isInfo = info.isShow = true;
    keypairDialog.value.setInfo(record);
}
const handleEdit = (record) => {
    info.isInfo = info.isAdd = false;
    info.isShow = true;
    keypairDialog.value.setInfo(record);
}
onMounted(() => {getKeypairlist();
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th) { white-space: nowrap; }
// :deep(.ant-table td) { white-space: nowrap; }
// .buttonPadding,.innerPadding{min-width: 1219px;}
</style>