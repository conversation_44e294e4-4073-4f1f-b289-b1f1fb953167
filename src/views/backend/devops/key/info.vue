<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" @cancel="cancel" :maskClosable="info.isInfo" centered :getContainer="modalBindNode">
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isInfo ? '查看' : (info.isAdd ? '新增' : '修改')}}</span>
                    <a-popover trigger="click" placement="leftTop" v-if="info.isAdd || info.isInfo">
                        <template #content>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto;margin-bottom:0" v-html="content.key_add"></pre>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <template #footer>
                <a-button style="margin-left: 10px" @click="cancel" v-if="!info.isInfo" >{{ $t("m.cancel") }}</a-button>
                <a-button type="primary" @click="save" v-if="!info.isInfo">{{ $t("m.save") }}</a-button>
                <a-button type="primary" style="margin-left: 10px" @click="cancel" v-if="info.isInfo" >{{ $t("m.close") }}</a-button>
            </template>
            <a-form ref="keypairForm" :model="keypairform" :rules="rules" :label-col={span:4}>
                <a-form-item label="密钥名称" name="name">
                    <a-input v-model:value="keypairform.name" placeholder="请输入名称" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="指纹" name="fingerPrint" v-if="info.isInfo">
                    <a-input v-model:value="keypairform.fingerPrint" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="公钥" name="pubKey">
                    <a-textarea v-model:value="keypairform.pubKey" placeholder="请输入公钥" :disabled="info.isInfo" allow-clear></a-textarea>
                </a-form-item>
                <a-form-item label="说明" name="info">
                    <a-textarea v-model:value="keypairform.info" placeholder="请输入说明内容" :disabled="info.isInfo" allow-clear></a-textarea>
                </a-form-item>
                <a-form-item label="创建人" name="createUserName" v-if="info.isInfo">
                    <a-input v-model:value="keypairform.createUserName" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="创建时间" name="createTime" v-if="info.isInfo">
                    <a-input v-model:value="keypairform.createTime" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="修改人" name="updateUserName" v-if="info.isInfo">
                    <a-input v-model:value="keypairform.updateUserName" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="修改时间" name="updateTime" v-if="info.isInfo">
                    <a-input v-model:value="keypairform.updateTime" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <!-- <a-form-item label="HA" name="ha">
                    <a-input-number v-model:value="keypairform.ha" :min="0" :disabled="info.isInfo" style="width:100%"></a-input-number>
                </a-form-item>
                <a-form-item label="SNAT" name="snat">
                    <a-input-number v-model:value="keypairform.snat" :min="0" :disabled="info.isInfo" style="width:100%"></a-input-number>
                </a-form-item>
                <a-form-item label="状态" name="status">
                    <a-switch v-model:checked="keypairform.status" checked-children="正常" un-checked-children="禁用" :disabled="info.isInfo" />
                </a-form-item> -->
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getKeypairInfo, saveKeypair, updateKeypair } from '@/api/backend/devops/keypair';
import {content} from "@/common/explain/modal"
import router from '@/router';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow:false,
                isInfo:false
            }
        }
    }
})
const modalBindNode = ref();
const keypairForm = ref()
const defaultform = {
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    pubKey:'',
    createUserName:'',
    createTime:'',
    updateUserName:'',
    updateTime:'',
    fingerPrint:'',
    id:''
}
const keypairform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    projectId:router.currentRoute.value.query.projectId
})
const rules = reactive({
    name:[{required:true, message:'请输入名称', trigger:'change'}],
    pubKey:[{required:true, message:'请输入公钥', trigger:'change'}]
})
const setInfo = async (record) => {
    // let res = await getKeypairInfo({id})
    // if(res.code == 0){
        // let keypairform1 = {...res.data};
        // if(res.data.status == 0){
        //     keypairform1.status = false;
        // }else{
        //     keypairform1.status = true;
        // }
        Object.assign(keypairform, record);
    // }
}
const cancel = () => {
    props.info.isShow = false;
    keypairForm.value.resetFields();
    Object.assign(keypairform, defaultform);
}
const save = () => {
    proxy.$handleSave(keypairForm.value, saveKeypair, updateKeypair, props, keypairform, ()=>{emit('getlist');cancel();})
}
onMounted(() => {modalBindNode.value = document.getElementsByClassName('full-modal')[0] ? document.getElementsByClassName('full-modal ant-modal-body')[0] : proxy.modalBindNode;})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>