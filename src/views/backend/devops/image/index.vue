<template>
    <div class="cloudContent">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                <a-form-item label="镜像名称">
                    <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.name" allowClear />
                </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd">新增 </a-button>
                    <a-button @click="$handleDel(selectRowIds,deleteImage,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table
                 :row-selection="rowSelection" 
                 :columns="columns" 
                 row-key="id" 
                 :data-source="imagelist" 
                 :pagination="pagination" 
                 @change="changeTable"
                 :scroll="{x:true}"
                >
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #imageOSText="{record}">
                        {{record.imageOSText ? record.imageOSText : '其他'}}
                    </template>
                    <template #imageSize={record}>
                        {{(record.imageSize/(1024*1024*1024)).toFixed(2)}}
                    </template>
                    <template #protect={record}>
                        {{record.protect == 1 ? '是' : ((record.protect === 0 || record.protect === '0') ? '否' : '-')}}
                    </template>
                    <template #action={record}>
                        <a-button class="button_V" @click="handleView(record)">查看</a-button>
                        <a-button class="button_E" @click="handleDownload(record)">下载</a-button>
                        <a-button class="button_E" @click="handleEdit(record)" v-if="record.projectId == route.query.projectId">修改</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteImage,getList)" v-if="record.protect != 1 && record.projectId == route.query.projectId">删除</a-button>
                    </template>
                </a-table>
            </div>
        </div>
    </div>
    <Info ref="imageDialog" :info="info" @getlist="getList" />
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { computed, getCurrentInstance, nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import { getImageList, deleteImage } from "@/api/backend/devops/image";
import { useRoute } from "vue-router";
import { selectAttachmentList } from "@/api/backend/systems/user";
import { message } from "ant-design-vue";
import { getToken } from "@/utils/auth";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import emiter from "@/utils/Bus";
const { proxy } = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(['changes'])
const props = defineProps({
    selectedRowKeys: {
        type:Array,
        default(){
            return []
        }
    }
})
const imageDialog = ref(null);
const loading = ref(false);
const imagelist = ref([]);
let selectRowIds: string[] = ref([]);
const info = reactive({
    isInfo:false,
    isAdd:true,
    isShow:false
})
const searchform = reactive({
    cloudId:route.query.cloudId,
    projectId:route.query.projectId,
    domainId:route.query.domainId,
    name:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
  begin();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '项目名称', dataIndex: 'projectName', key: 'id',align:'left'},
    {title: '名称', dataIndex: 'name', key: 'id',align:'left',width:400,ellipsis:true},
    {title: '镜像类型', dataIndex: 'imageType', key: 'id',width:120,ellipsis:true,align:'center'},
    {title: 'OS类型', dataIndex: 'imageOSText', slots: { customRender: 'imageOSText' },　key: 'id',width:200,align:'center'},
    {title: '磁盘格式', dataIndex: 'diskFormat', key: 'id',align:'center'},
    {title: '大小(G)', dataIndex: 'imageSize', slots: { customRender: 'imageSize' },　key: 'id',align:'center'},
    {title: '可见性', dataIndex: 'visibility', key: 'id',align:'center'},
    {title: '受保护的', dataIndex: 'protect', slots: { customRender: 'protect' }, key: 'id',align:'center'},
    {title: '状态', dataIndex: 'statusText', key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
const getList = async (isUnLoading) => {
    // searchform.cloudId = e;
    // searchform.cloudId = localStorage.getItem('cloudId');
    searchform.cloudId = route.query.cloudId;
    searchform.projectId = route.query.projectId;
    searchform.domainId = route.query.domainId;
    if(!isUnLoading)
        emiter.emit("loading",true)
    imagelist.value = await proxy.$getList(loading, getImageList, searchform, pagination, getList )
    emiter.emit("loading",false)
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
  begin();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    name: ""
  })
//   getList();
}
const handleView = (record) => {
    info.isInfo = info.isShow = true;
    let record1 = {...record}
    record1.minDisk = record.minDisk;
    record1.minRam = record.minRam;
    record1.visibility = record.visibility == 'UNKNOWN' ? '' : record.visibility.toLowerCase();
    Object.assign(imageDialog.value.imageform, record1)
    nextTick(()=>{
        imageDialog.value.helper();
    })
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
    nextTick(()=>{
        imageDialog.value.getCode();
        imageDialog.value.helper();
    })
}
const handleEdit = (record) => {
    info.isAdd = false;
    info.isShow = true;
    let record1 = {...record};
    record1.attachmentCode = undefined;
    record1.diskFormat = undefined;
    record1.imageType = undefined;
    record1.minDisk = undefined;
    record1.minRam = undefined;
    record1.visibility = record.visibility == 'UNKNOWN' ? undefined : record.visibility.toLowerCase();

    // record1.id = record.id;
    // record1.name = record.name;
    // record1.description = record.description;
    record1.cloudId = localStorage.getItem('cloudId');
    Object.assign(imageDialog.value.imageform, record1)
    nextTick(()=>{
        imageDialog.value.helper();
    })
    // imageDialog.value.imageform.attachmentCode = undefined;
    // imageDialog.value.imageform.diskFormat = undefined;
    // imageDialog.value.imageform.imageOS = undefined;
    // imageDialog.value.imageform.imageType = undefined;
    // imageDialog.value.imageform.minDisk = undefined;
    // imageDialog.value.imageform.minRam = undefined;
    // imageDialog.value.getAttachlist()
}
const handleDownload = (record) => {
    const token = getToken();
    location.href = import.meta.env.VITE_BASE_API+'/sys/sysimage/download?id='+record.id+'&token='+token;
}
var timerImage = null;
const begin = () => {
    clearInterval(timerImage);
    if(!searchform.name){
        timerImage = setInterval(()=>{
            getList(true);
        },60000)
    }
}
onMounted(() => {
    getList();
    begin();
    nextTick(()=>{
        handleWidth()
    })
})
onUnmounted(()=>{
    clearInterval(timerImage);
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
// :deep(.ant-table th){ white-space: nowrap; }
// :deep(.ant-table td){ white-space: nowrap; }
// .buttonPadding{position: relative;height: 78px;padding-bottom: 0;overflow: hidden;
// .button-group{
//     position: absolute;
//     bottom: 20px;
//     right: 20px;
// }}
// .buttonPadding,.innerPadding{min-width: 1411px;}
// :deep(.ant-form-inline .ant-form-item){margin-bottom: 20px;}
</style>