<template>
    <div class=''>
        <a-modal 
        v-model:visible="info.isShow"
        @cancel="cancel"
        :maskClosable="info.isInfo"
        centered
        :getContainer="modalBindNode"
        >
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isInfo ? '查看':(info.isAdd ? '新增':'修改')}}</span>
                    <a-popover trigger="click" placement="leftTop" v-if="info.isAdd || info.isInfo">
                        <template #content>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-html="content.image_add"></pre>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="handleSave" :disabled="isUpload">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel" :disabled="isUpload">关闭</a-button>
            </template>
            <a-form :model="imageform" ref="imageForm" :rules="rules" :labelCol="{span:5}">
                <a-form-item label="ID" name="thirdImageId" v-if="info.isInfo">
                    {{imageform.thirdImageId}}
                </a-form-item>
                <a-form-item label="镜像名称" name="name">
                    <a-input v-model:value.trim="imageform.name" :disabled="info.isInfo" placeholder="请输入镜像名称" allow-clear></a-input>
                </a-form-item>
                <!-- <a-form-item label="类型" name="imageType">
                    <a-select v-model:value="imageform.imageType"> 
                        <a-select-option v-for="(item,index) in options" :key="index" :value="item.dictValue" >{{item.dictLabel}}</a-select-option>
                    </a-select>
                </a-form-item> -->
                <a-form-item label="镜像文件" v-if="info.isAdd && !info.isInfo">
                    <template #extra v-if="progressStatus == 'active'">
                        <div>
                            <a-progress :percent="progressValue" :status="progressStatus"></a-progress>
                        </div>
                    </template>
                    <a-upload
                    v-model:file-list="fileList"
                    :customRequest="customRequest"
                    :before-upload="beforeUpload"
                    :remove="(file)=>removeFilelist(file,true)"
                    @change="changeFile"
                    >
                        <a-button :disabled="info.isInfo" style="width:100%"><upload-outlined></upload-outlined>上传文件</a-button>
                    </a-upload>
                </a-form-item>
                
                <a-form-item label="镜像格式" name="diskFormat" v-if="info.isAdd || info.isInfo">
                    <a-select v-model:value="imageform.diskFormat" :disabled="info.isInfo || !info.isAdd" placeholder="请选择镜像格式" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear> 
                        <a-select-option v-for="(item,index) in options3" :key="index" :value="item.dictValue" >{{item.dictLabel}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="镜像OS类型" name="imageOS">
                    <a-select v-model:value="imageform.imageOS" :disabled="info.isInfo" placeholder="请选择镜像OS类型" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear> 
                        <a-select-option v-for="(item,index) in options4" :key="index" :value="item.dictValue" >{{item.dictLabel}}</a-select-option>
                    </a-select>
                </a-form-item>
                <!-- <a-form-item label="镜像架构" name="name">
                    <a-input v-model:value.trim="imageform.name"></a-input>
                </a-form-item> -->
                <a-form-item label="最小磁盘" name="minDisk" v-if="info.isAdd || info.isInfo">
                    <a-input-number v-model:value.trim="imageform.minDisk" style="width:calc(100% - 25px)" :min="0" :disabled="info.isInfo"></a-input-number> GB
                    <!-- <a-radio-group v-model:value="single" :disabled="info.isInfo">
                        <a-radio-button :value="1024*1024*1024">GB</a-radio-button>
                        <a-radio-button :value="1024*1024">MB</a-radio-button>
                        <a-radio-button :value="1024">KB</a-radio-button>
                        <a-radio-button :value="1">B</a-radio-button>
                    </a-radio-group> -->
                </a-form-item>
                <a-form-item label="最低内存" name="minRam" v-if="info.isAdd || info.isInfo">
                    <a-input-number v-model:value.trim="imageform.minRam" style="width:calc(100% - 25px)" :min="0" :disabled="info.isInfo"></a-input-number> MB
                    <!-- <a-radio-group v-model:value="single1" :disabled="info.isInfo">
                        <a-radio-button :value="1024*1024*1024">GB</a-radio-button>
                        <a-radio-button :value="1024*1024">MB</a-radio-button>
                        <a-radio-button :value="1024">KB</a-radio-button>
                        <a-radio-button :value="1">B</a-radio-button>
                    </a-radio-group> -->
                </a-form-item>
                <a-form-item label="镜像可见性" name="visibility">
                    <a-select v-model:value="imageform.visibility" :disabled="info.isInfo" placeholder="请选择镜像可见性" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear> 
                        <a-select-option v-for="(item,index) in options1" :key="index" :value="item.dictValue" >{{item.dictLabel}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="受保护的" name="protect">
                    <a-select v-model:value="imageform.protect" :disabled="info.isInfo" placeholder="请选择受保护类型" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear> 
                        <a-select-option v-for="(item,index) in options2" :key="index" :value="item.dictValue" >{{item.dictLabel}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="启动(BIOS)模式" v-if="info.isAdd || info.isInfo">
                    <a-select v-model:value="imageform.bootMode" :placeholder="info.isInfo ? '' : '请选择启动模式'" :disabled="info.isInfo" allow-clear>
                        <a-select-option value="bios">Legacy</a-select-option>
                        <a-select-option value="uefi">UEFI</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="镜像描述" name="description">
                    <a-textarea v-model:value.trim="imageform.description" :disabled="info.isInfo" allow-clear></a-textarea>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {saveImage,updateImage } from "@/api/backend/devops/image"
import { selectDictList } from '@/api/backend/systems/dictionary';
import { message, Modal } from 'ant-design-vue';
import { deleteAttach, getcode, selectAttachmentList, upload } from '@/api/backend/systems/user';
import {content} from "@/common/explain/modal"
import FileUploader from '@/utils/files/chunk';
import router from '@/router';
const {proxy} = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})
const isDeleted = ref(false);
const progressValue = ref(0)
const progressStatus = ref('')
const modalBindNode = ref();
const isUpload = ref(false);
const single = ref(1024 * 1024 * 1024)
const single1 = ref(1024 * 1024 * 1024)
const imageForm = ref();
const defaultform = {
    attachmentCode:'',
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    imageType:'',
    visibility:'private',
    protect:'0',
    diskFormat:undefined,
    minDisk:'',
    minRam:'',
    imageOS:undefined,
    bootMode:undefined
}
const imageform = reactive({
    attachmentCode:'',
    cloudId:router.currentRoute.value.query.cloudId,
    domainId:router.currentRoute.value.query.domainId,
    projectId:router.currentRoute.value.query.projectId,
    name:'',
    imageType:'',
    visibility:'private',
    protect:'0',
    diskFormat:undefined,
    minDisk:'',
    minRam:'',
    imageOS:undefined,
    bootMode:undefined
})
const options = ref([])
const options1 = ref([
    {id:1,dictLabel:'公开',dictValue:'public'},
    {id:2,dictLabel:'私有',dictValue:'private'}
])
const options2 = ref([
    {id:1,dictLabel:'是',dictValue:'1'},
    {id:2,dictLabel:'否',dictValue:'0'}
])
const options3 = ref([])
const options4 = ref([])
const rules = {
    name:[{required:true, message:'请输入',trigger:'change'}],
    imageType:[{required:true, message:'请输入',trigger:'change'}],
    visibility:[{required:true, message:'请输入',trigger:'change'}],
    protect:[{required:true, message:'请输入',trigger:'change'}],
    diskFormat:[{required:true, message:'请输入',trigger:'change'}],
    imageOS:[{required:true, message:'请输入',trigger:'change'}],
    minDisk:[{type:'number',required:true, message:'请输入',trigger:'change'}],
    minRam:[{type:'number',required:true, message:'请输入',trigger:'change'}],
}

const fileList = ref([]);
const fileList1 = ref([]);
const acceptTypeEquip = ref([])
const getCode = async () => {
    let res = await getcode()
    if(res.code){
        imageform.attachmentCode = res.code;
    }
}
const getAttachlist = async () => {
        let res = await selectAttachmentList({attachmentCode:imageform.attachmentCode})
        if(res) 
        fileList.value = res.data;
    }
// 文件上传前的格式验证
const beforeUpload = (file) => {
    isUpload.value = true;
    isDeleted.value = false;
    fileList.value = [...fileList.value, file];
    const uploadSize = file.size / 1024 / 1024 < 5;
    const uploadType = file.name.substring(file.name.lastIndexOf(".") + 1);
    let blob = new Blob([file])
    if(fileList.value.length > 1){
        message.error("文件数量超出限制,最多1个文件");
        return false;
    }
};
const changeFile = ({
  file,
  filelist,
  event,
}) => {
    if(fileList.value.length > 1)
        fileList.value.pop();
}
const fileUploader = new FileUploader();
const customRequest = (data) => {
    if(data.file.size <= 1024 * 1024 * 100){
        customRequest1(data);
        return;
    }
    progressStatus.value = 'active';
    // 自定义上传进度更新逻辑
    fileUploader.onProgressUpdate = (progress) => {
        if(!isDeleted.value){
            progressStatus.value = 'active';
            progressValue.value = progress.toFixed();
            console.log(`当前上传进度：${progress}%`);
        }
    };
    // 自定义上传完成逻辑
    fileUploader.onUploadComplete = () => {
        if(!isDeleted.value){
            fileList.value = [data.file];
            message.success('文件上传已完成！');
            progressStatus.value = '';
            isDeleted.value = false;
            isUpload.value = false;
        }
    };
    // 自定义上传完成逻辑
    fileUploader.onUploadIncomplete = () => {
        if(!isDeleted.value){
            progressStatus.value = 'exception';
            // removeFilelist(data.file);
            fileList.value[0].status = 'error';
            message.error('文件上传失败！');
            isUpload.value = false;
        }
    };
    // 上传对象
    fileUploader.upload(data.file,imageform.attachmentCode);
}
const customRequest1 = (data) => {
    const formData = new FormData();
    formData.append("file", data.file);
    formData.append("readType", 'localhost');
    formData.append("attachmentCode", imageform.attachmentCode);
    // formData.append("token", "aiufpaidfupipiu"); //随便写一个token示例
    upload(formData)
        .then(res => {
            isUpload.value = false;
        if (res.code == 0 && res.data !== false) {
            message.success("上传镜像成功");
            fileList.value = [data.file];
            fileList1.value = [res.data];
        } else {
            removeFilelist(data.file)
            Modal.error({
                title: () => '上传失败',
                content: () => '文件上传失败,无法创建镜像,请调整后再试。'
            });
        }
        })
        .catch(err => {
            removeFilelist(data.file)
            // message.error("调用接口失败，请联系管理员");
            Modal.error({
                title: () => '上传失败',
                content: () => '文件上传失败,无法创建镜像,请调整后再试。'
            });
        });
};
const removeFilelist = (file,handle) => {
    fileUploader.cancel();
    isDeleted.value = true;
    isUpload.value = false;
    progressStatus.value = '';
    progressValue.value = 0;
    fileList.value = fileList.value.filter(item => item.uid !== file.uid);
    if(handle)
        message.success("已删除");
}
// 删除文件
const removeFile = async (file) => {
    let res = await deleteAttach(fileList1.value[0].id)
    if(res.code == 0){
        removeFilelist(file);
        message.success('已删除')
    }else
    message.error('删除失败')
};
const handleSave = () => {
    if((!fileList.value || (fileList.value && fileList.value.length <= 0)) && props.info.isAdd){
        message.error('请上传镜像文件')
    }else{
        let imageform1 = {...imageform}
        proxy.$handleSave(imageForm.value,saveImage,updateImage,props,imageform1,()=>{cancel();emit('getlist')},null,()=>{
            // imageform1.minDisk = imageform1.minDisk * single.value;
            // imageform1.minRam = imageform1.minRam * single1.value;
        })
    }
    
    // imageForm.value.validate().then(async ()=>{
    //     let imageform1 = {...imageform}
    //     imageform1.minDisk = imageform1.minDisk * single.value;
    //     imageform1.minRam = imageform1.minRam * single1.value;
    //     let res;
    //     if(props.info.isAdd){
    //         res = await saveImage(imageform1)
    //     }else{
    //         res = await updateImage(imageform1)
    //     }
    //     if(res.code == 0){
    //         if(res.data){
    //             message.success('操作成功')
    //             cancel();emit('getlist')
    //         }else{
    //             message.error('操作失败');
    //         }
    //     }else{
    //         message.error('操作失败');
    //     }
        
    // })
    // proxy.$handleSave(imageForm.value, saveImage, updateImage, props,imageform, ()=>{cancel();emit('getlist')},null,()=>{imageform.minDisk = imageform.minDisk * single.value;imageform.minRam = imageform.minRam * single1.value})
}
const cancel = () => {
    removeFilelist(fileList.value[0])
    single.value = 1;
    props.info.isShow = false
    props.info.isInfo = false
    fileList.value = []
    progressValue.value = 0;
    // isInfo.value = false
    imageForm.value.resetFields()
    Object.assign(imageform,defaultform)
}
const selectType = async () => {
    let res = await selectDictList({dictType:'IMAGE_TYPE'})
    if(res.code == 0){
        options.value = res.data;
    }
}
const selectVisibility = async () => {
    let res = await selectDictList({dictType:'IMAGE_VISIBILITY'})
    if(res.code == 0){
        options1.value = res.data;
    }
}
const selectProtect = async () => {
    let res = await selectDictList({dictType:'IMAGE_PROTECT'})
    if(res.code == 0){
        options2.value = res.data;
    }
}
// 镜像格式
const selectFormat = async () => {
    let res = await selectDictList({dictType:'DISK_FORMAT'})
    if(res.code == 0){
        options3.value = res.data;
        options3.value.forEach((item)=>{
            acceptTypeEquip.value.push(item.dictValue)
        })
    }
}
// 镜像os
const selectOs = async () => {
    let res = await selectDictList({dictType:'IMAGE_OS'})
    if(res.code == 0){
        options4.value = res.data;
    }
}
const helper = () => {
    selectFormat();selectOs()
}
onMounted(() => {modalBindNode.value = document.getElementsByClassName('full-modal')[0] ? document.getElementsByClassName('full-modal ant-modal-body')[0] : proxy.modalBindNode;})
defineExpose({imageform,single,getCode,getAttachlist,helper})
</script>
<style lang='scss' scoped>
:deep(.ant-upload){width:100%}
:deep(.ant-upload-list-item-progress){display: none;}
</style>