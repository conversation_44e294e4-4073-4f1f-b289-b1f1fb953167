<template>
    <div class='back-page'>
        <a-page-header
          style="background-color:#fff"
          :title="balanceform.name"
          @back="()=>{props.info.isShow = props.info.isInfo = false;balanceactiveKey = '2'}"
      />
        <a-tabs class="back-content" :animated="false" v-model:activeKey="balanceactiveKey" @change="changeBalanceTab">
            <a-tab-pane tab=""></a-tab-pane>
            <a-tab-pane key="2" tab="概览">
                <a-descriptions :column="2" style="width:900px">
                    <a-descriptions-item label="ID">{{balanceform.thirdId}}</a-descriptions-item>
                    <a-descriptions-item label="名称">{{balanceform.name}}</a-descriptions-item>
                    <a-descriptions-item label="描述">{{balanceform.description?balanceform.description:'-'}}</a-descriptions-item>
                    <a-descriptions-item label="项目 ID">{{balanceform.thirdProjectId}}</a-descriptions-item>
                    <a-descriptions-item label="子网 ID">{{balanceform.thirdSubnetId}}</a-descriptions-item>
                    <a-descriptions-item label="端口 ID">{{balanceform.thirdPortId}}</a-descriptions-item>
                    <a-descriptions-item label="网络 ID">{{balanceform.thirdNetworkId}}</a-descriptions-item>
                    <a-descriptions-item label="提供者">{{balanceform.provider}}</a-descriptions-item>
                    <a-descriptions-item label="虚机类型 ID">{{balanceform.thirdId}}</a-descriptions-item>
                    <a-descriptions-item label="IP 地址">{{balanceform.vipAddress}}</a-descriptions-item>
                    <a-descriptions-item label="浮动 IP">{{balanceform.vipAddress}}</a-descriptions-item>
                    <a-descriptions-item label="可用域">{{balanceform.availabilityZone?balanceform.availabilityZone:'-'}}</a-descriptions-item>
                    <a-descriptions-item label="操作状态">{{balanceform.operatingStatusText ? balanceform.operatingStatusText : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="配置状态">{{balanceform.provisioningStatusText ? balanceform.provisioningStatusText : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="管理状态">{{balanceform.adminStateUp ? '开启' : '未开启'}}</a-descriptions-item>
                    <a-descriptions-item label="创建时间">{{balanceform.updateTime}}</a-descriptions-item>
                    <a-descriptions-item label="修改时间">{{balanceform.createTime}}</a-descriptions-item>
                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="3" tab="监控器">
                <Monitor ref="monitorRef" />
            </a-tab-pane>
            <a-tab-pane key="4" tab="资源池">
                <Pool ref="poolRef" parentKey="1" />
            </a-tab-pane>
        </a-tabs>
    </div>
</template>
<script lang='ts' setup>
import { getBalanceInfoAPI } from '@/api/backend/devops/balance';
import emiter from '@/utils/Bus';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import Monitor from "./monitor/index.vue"
import Pool from "./pool/index.vue"
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
                isInfo:true
            }
        }
    }
})
const {proxy} = getCurrentInstance()
const balanceactiveKey = ref('2')
const monitorRef = ref()
const poolRef = ref()
const balanceId = ref()
const balanceform = reactive({
    "adminStateUp": 0,
    "availabilityZone": "",
    "cloudId": 0,
    "description": "",
    "id": 0,
    "name": "",
    "operatingStatusText": "",
    "provider": "",
    "provisioningStatusText": "",
    "thirdId": "",
    "thirdNetworkId": "",
    "thirdPortId": "",
    "thirdProjectId": "",
    "thirdSubnetId": "",
    "vipAddress": ""
})
const setInfo = (id) => {
    balanceId.value = id;
    getBalanceInfoAPI({id}).then((res)=>{
        if(res.code == 0){
            Object.assign(balanceform,res.data)
        }
    })
}
const changeBalanceTab = (key) => {
    if(key == '3'){
        proxy.$nextTick(()=>{
            monitorRef.value.getMonitorList(balanceform)
        })
    }
    if(key == '4'){
        proxy.$nextTick(()=>{
            poolRef.value.getPoolList(balanceform)
        })
    }
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
@import url('../../../../styles/tabs.scss');
</style>