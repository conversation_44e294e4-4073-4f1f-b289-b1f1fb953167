<template>
    <a-modal v-model:visible="info.isShow" :body-style="{height:'497px',paddingRight:0,display:'flex'}" width="900px !important" ok-text="提交" @ok="handleSave" @cancel="cancel" :getContainer="modalBindNode">
        <template #title>
            <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                <span>{{(info.isAdd ? '新增' : '修改')+funcName[info.addKey]+'&nbsp;'}}</span>
                <a-popover trigger="click" placement="leftTop">
                    <template #content>
                        <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="activeKey == '1'"><span>{{content.load_balancer}}</span></pre>
                        <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="activeKey == '2'"><span>{{content.balancer_listener}}</span></pre>
                        <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="activeKey == '3'"><span>{{content.balancer_pool}}</span></pre>
                        <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="activeKey == '4'"><span>{{content.balancer_member}}</span></pre>
                        <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto" v-if="activeKey == '5'"><span>{{content.balancer_health}}</span></pre>
                    </template>
                    <InfoCircleOutlined  />
                </a-popover>
            </div>
        </template>
        <a-tabs tab-position="left" v-model:activeKey="activeKey" v-if="info.isAdd && !(info.addKey == '4' || info.addKey == '5')">
                <a-tab-pane key="1" v-if="info.addKey == '1'">
                    <template #tab>
                        <span style="color:#ff4d4f" v-if="!balanceform.loadBalancerForm.name">*</span>
                        <span>
                        负载均衡器详情
                        </span>
                    </template>
                </a-tab-pane>
                <a-tab-pane key="2" v-if="(info.isAdd && (info.addKey == '1' || info.addKey == '2')) || (!info.isAdd && info.addKey == '2')">
                    <template #tab>
                        <span style="color:#ff4d4f" v-if="balanceform.createListener && !(balanceform.listenerForm.protocol && balanceform.listenerForm.protocolPort && balanceform.listenerForm.connectionLimit)">*</span>
                        <span>
                        监控器详情
                        </span>
                    </template>
                    
                </a-tab-pane>
                <a-tab-pane key="3" v-if="(info.isAdd && (info.addKey != '4' && info.addKey != '5')) || (!info.isAdd && info.addKey == '3')">
                    <template #tab>
                        <span style="color:#ff4d4f" v-if="balanceform.createPool && !(balanceform.poolForm.poolName && balanceform.poolForm.protocol)">*</span>
                        <span>
                        资源池详情
                        </span>
                    </template>
                    
                </a-tab-pane>
                <a-tab-pane key="4" tab="资源池成员" v-if="(info.isAdd && info.addKey != '5') || (!info.isAdd && info.addKey == '4')">
                    
                </a-tab-pane>
                <a-tab-pane key="5" v-if="(info.isAdd && info.addKey != '4') || (!info.isAdd && info.addKey == '5')">
                    <template #tab>
                        <span style="color:#ff4d4f" v-if="balanceform.createHealthMonitor && !(balanceform.healthMonitorForm.healthMonitorType && balanceform.healthMonitorForm.maxRetriesDown
                        && balanceform.healthMonitorForm.delay && balanceform.healthMonitorForm.maxRetries && balanceform.healthMonitorForm.timeout)">*</span>
                        <span>
                        健康监控器详情
                        </span>
                    </template>
                    
                </a-tab-pane>
            </a-tabs>
        <a-form :label-col="{span:4}" :wrapper-col="{span:18}" :model="balanceform" ref="balanceForm" style="flex:5">
            
            <template v-if="activeKey == '1'">
                <div style="height:448px;overflow-y:auto">
                    <a-form-item label="名称" :name="['loadBalancerForm', 'name']">
                        <a-input v-model:value="balanceform.loadBalancerForm.name"></a-input>
                    </a-form-item>
                    <a-form-item label="IP地址" :name="['loadBalancerForm', 'vipAddress']">
                        <a-input v-model:value="balanceform.loadBalancerForm.vipAddress" :disabled="!info.isAdd"></a-input>
                    </a-form-item>
                    <a-form-item label="描述" :name="['loadBalancerForm', 'description']">
                        <a-input v-model:value="balanceform.loadBalancerForm.description"></a-input>
                    </a-form-item>
                    <a-form-item label="可用域" :name="['loadBalancerForm', 'availabilityZone']">
                        <a-select v-model:value="balanceform.loadBalancerForm.availabilityZone" :options="zonelist" :disabled="!info.isAdd" allow-clear></a-select>
                    </a-form-item>
                    <a-form-item label="类型" :name="['loadBalancerForm', 'thirdFlavorId']">
                        <a-select v-model:value="balanceform.loadBalancerForm.thirdFlavorId" :options="flavorlist" :disabled="!info.isAdd" allow-clear></a-select>
                    </a-form-item>
                    <a-form-item label="子网" :name="['loadBalancerForm', 'thirdSubnetId']" :rules="[{required:true,message:'请选择子网'}]">
                        <a-select v-model:value="balanceform.loadBalancerForm.thirdSubnetId" :options="subnetlist" :disabled="!info.isAdd"></a-select>
                    </a-form-item>
                    <a-form-item label="管理状态" :name="['loadBalancerForm', 'adminStateUp']">
                        <a-switch v-model:checked="balanceform.loadBalancerForm.adminStateUp" checked-children="启动" un-checked-children="关闭"/>
                    </a-form-item>
                </div>
            </template>
            <template v-if="activeKey == '2'">
                <div style="height:448px;overflow-y:auto">
                    <a-form-item label="创建监控器" :label-col="{span:5}" v-if="(info.isAdd && info.addKey == '1')">
                        <a-switch v-model:checked="balanceform.createListener" checked-children="是" un-checked-children="否" @change="(e)=>changeSwitch(e,'createListener')"/>
                    </a-form-item>
                    <div v-if="balanceform.createListener">
                        <a-form-item label="名称" :label-col="{span:5}" :name="['listenerForm', 'listenerName']">
                            <a-input v-model:value="balanceform.listenerForm.listenerName"></a-input>
                        </a-form-item>
                        <a-form-item label="描述" :label-col="{span:5}" :name="['listenerForm', 'description']">
                            <a-input v-model:value="balanceform.listenerForm.description"></a-input>
                        </a-form-item>
                        <a-form-item label="协议" :label-col="{span:5}" :name="['listenerForm', 'protocol']" :rules="[{required:true,message:'请选择协议'}]">
                            <a-select v-model:value="balanceform.listenerForm.protocol" :disabled="!info.isAdd">
                                <a-select-option value="HTTP">HTTP</a-select-option>
                                <a-select-option value="TCP">TCP</a-select-option>
                                <a-select-option value="HTTPS">HTTPS</a-select-option>
                                <a-select-option value="UDP">UDP</a-select-option>
                                <a-select-option value="SCTP">SCTP</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="端口" :label-col="{span:5}" :name="['listenerForm', 'protocolPort']" :rules="[{type:'number',required:true,message:'请输入端口'}]">
                            <a-input-number v-model:value="balanceform.listenerForm.protocolPort" :disabled="!info.isAdd" style="width:100%" :min="0" :precision="0"></a-input-number>
                        </a-form-item>
                        <div v-if="balanceform.listenerForm.protocol != 'UDP' && balanceform.listenerForm.protocol != 'SCTP'">
                            <a-form-item label="客户数据超时时限" :label-col="{span:5}" :name="['listenerForm', 'timeoutClientData']">
                                <a-input-number v-model:value="balanceform.listenerForm.timeoutClientData" style="width:100%" :min="0" :precision="0"></a-input-number>
                            </a-form-item>
                            <a-form-item label="TCP 监测超时时限" :label-col="{span:5}" :name="['listenerForm', 'timeoutTcpInspect']">
                                <a-input-number v-model:value="balanceform.listenerForm.timeoutTcpInspect" style="width:100%" :min="0" :precision="0"></a-input-number>
                            </a-form-item>
                            <a-form-item label="成员连接超时时限" :label-col="{span:5}" :name="['listenerForm', 'timeoutMemberConnect']">
                                <a-input-number v-model:value="balanceform.listenerForm.timeoutMemberConnect" style="width:100%" :min="0" :precision="0"></a-input-number>
                            </a-form-item>
                            <a-form-item label="成员数据超时时限" :label-col="{span:5}" :name="['listenerForm', 'timeoutMemberData']">
                                <a-input-number v-model:value="balanceform.listenerForm.timeoutMemberData" style="width:100%" :min="0" :precision="0"></a-input-number>
                            </a-form-item>
                        </div>
                        <a-form-item label="连接限制" :label-col="{span:5}" :name="['listenerForm', 'connectionLimit']" :rules="[{type:'number',required:true,message:'请输入连接限制'}]">
                            <a-input-number v-model:value="balanceform.listenerForm.connectionLimit" style="width:100%" :min="-1" :precision="0"></a-input-number>
                        </a-form-item>
                        <a-form-item label="Allowed Cidrs" :label-col="{span:5}" :name="['listenerForm', 'allowedCidrsList']">
                            <!-- <a-textarea v-model:value="balanceform.listenerForm.allowedCidrsList"></a-textarea> -->
                            <template v-for="(tag, index) in balanceform.listenerForm.allowedCidrsList" :key="tag">
                                <a-tooltip v-if="tag.length > 20" :title="tag">
                                <a-tag :closable="index !== 0" @close="handleClose(tag)">
                                    {{ `${tag.slice(0, 20)}...` }}
                                </a-tag>
                                </a-tooltip>
                                <a-tag v-else closable @close="handleClose(tag)">
                                {{ tag }}
                                </a-tag>
                            </template>
                            <a-input
                                v-if="inputVisible"
                                ref="inputRef"
                                type="text"
                                size="small"
                                :style="{ width: '78px' }"
                                v-model:value="inputValue"
                                @blur="handleInputConfirm"
                                @keyup.enter="handleInputConfirm"
                            />
                            <a-tag v-else @click="showInput" style="background: #fff; border-style: dashed">
                                <plus-outlined />
                            </a-tag>
                        </a-form-item>
                        <a-form-item label="插入报头" :label-col="{span:5}" :name="['listenerForm', 'insertHeaders']" v-if="balanceform.listenerForm.protocol == 'HTTP'">
                            <a-checkbox-group v-model:value="balanceform.listenerForm.insertHeaders" name="checkboxgroup" :options="plainOptions" />
                        </a-form-item>
                        <a-form-item label="管理状态" :label-col="{span:5}" :name="['listenerForm', 'adminStateUp']">
                            <a-switch v-model:checked="balanceform.listenerForm.adminStateUp" checked-children="启动" un-checked-children="关闭"/>
                        </a-form-item>
                    </div>
                </div>
            </template>
            <template v-if="activeKey == '3'">
                <div style="height:448px;overflow-y:auto">
                    <a-form-item label="创建资源池" v-if="(info.isAdd && (info.addKey == '1' || info.addKey == '2'))">
                        <a-switch v-model:checked="balanceform.createPool" checked-children="是" un-checked-children="否" @change="(e)=>changeSwitch(e,'createPool')"/>
                    </a-form-item>
                    <div v-if="balanceform.createPool">
                        <a-form-item label="名称" :name="['poolForm','poolName']">
                            <a-input v-model:value="balanceform.poolForm.poolName"></a-input>
                        </a-form-item>
                        <a-form-item label="描述" :name="['poolForm','description']">
                            <a-input v-model:value="balanceform.poolForm.description"></a-input>
                        </a-form-item>
                        <a-form-item label="算法" :name="['poolForm','lbMethod']" :rules="[{required:true,message:'请选择算法'}]">
                            <a-select v-model:value="balanceform.poolForm.lbMethod" placeholder="请选择">
                                <a-select-option value="LEAST_CONNECTIONS">LEAST_CONNECTIONS</a-select-option>
                                <a-select-option value="ROUND_ROBIN">ROUND_ROBIN</a-select-option>
                                <a-select-option value="SOURCE_IP">SOURCE_IP</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="协议" :name="['poolForm', 'protocol']" :rules="[{required:true,message:'请选择协议'}]" v-if="info.addKey == '3'">
                            <a-select v-model:value="balanceform.poolForm.protocol" placeholder="请选择" :disabled="!info.isAdd">
                                <a-select-option value="HTTP">HTTP</a-select-option>
                                <a-select-option value="HTTPS">HTTPS</a-select-option>
                                <a-select-option value="PROXY">PROXY</a-select-option>
                                <a-select-option value="PROXYV2">PROXYV2</a-select-option>
                                <a-select-option value="TCP">TCP</a-select-option>
                                <a-select-option value="UDP">UDP</a-select-option>
                                <a-select-option value="SCTP">SCTP</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="会话持久化" :name="['poolForm','sessionPersistenceType']">
                            <a-select v-model:value="balanceform.poolForm.sessionPersistenceType" placeholder="请选择" allow-clear>
                                <a-select-option value="SOURCE_IP">SOURCE_IP</a-select-option>
                                <a-select-option value="HTTP_COOKIE">HTTP_COOKIE</a-select-option>
                                <a-select-option value="APP_COOKIE">APP_COOKIE</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="Cookie 名称" :name="['poolForm','cookieName']" v-if="balanceform.poolForm.sessionPersistenceType == 'APP_COOKIE'">
                            <a-input v-model:value="balanceform.poolForm.cookieName"></a-input>
                        </a-form-item>
                        <a-form-item label="TLS Enabled" :name="['poolForm','tlsEnabled']">
                            <a-switch v-model:checked="balanceform.poolForm.tlsEnabled" checked-children="是" un-checked-children="否"/>
                        </a-form-item>
                        <a-form-item label="TLS Cipher String" :label-col="{span:5}" :name="['poolForm', 'tlsCiphers']" v-if="balanceform.poolForm.tlsEnabled">
                            <a-textarea v-model:value="balanceform.poolForm.tlsCiphers"></a-textarea>
                        </a-form-item>
                        <a-form-item label="管理状态" :name="['poolForm','adminStateUp']">
                            <a-switch v-model:checked="balanceform.poolForm.adminStateUp" checked-children="启动" un-checked-children="关闭"/>
                        </a-form-item>
                    </div>
                </div>
            </template>
            <template v-if="activeKey == '4'">
                <div v-if="balanceform.createPool" style="height:448px;overflow-y:auto;padding-right:24px">
                    <!-- <a-form-item label="分配成员" name="name"> -->
                        <p>分配成员：</p>
                        <a-table :columns="columns" :data-source="memberlist" :pagination="false" size="small" :expandedRowKeys="expandedRowKeys">
                            <template #expandIcon="{expanded,record}">
                                <RightOutlined v-show="!expanded" @click="()=>onExpand(expanded,record)" />
                                <DownOutlined v-show="expanded" @click="()=>onExpand(expanded,record)" />
                            </template>
                            <template v-for="col in ['address', 'subnetId', 'protocolPort', 'weight']" #[col]="{ text, record }" :key="col">
                                <div>
                                    <template v-if="editableData[record.key]">
                                        <span v-if="editableData[record.key][col] === undefined">{{ text }}</span>
                                        <a-input v-else-if="col == 'address'" v-model:value="editableData[record.key][col]" style="margin: -5px 0" />
                                        <a-select v-else-if="col == 'subnetId'" v-model:value="editableData[record.key][col]" :options="subnetlist" style="margin: -5px 0;width:144px" >
                                        </a-select>
                                        <a-input-number v-else-if="col == 'protocolPort'" v-model:value="editableData[record.key][col]" :min="0" :precision="0" style="margin: -5px 0;width:100%" />
                                        <a-input-number v-else-if="col == 'weight'" v-model:value="editableData[record.key][col]" :min="0" :precision="0" style="margin: -5px 0;width:100%" />
                                    </template>
                                </div>
                            </template>
                            <template #action="{record}">
                                <a-popconfirm title="确定删除?" @confirm="onDelete(record.key)">
                                    <a>移除</a>
                                </a-popconfirm>
                            </template>
                            <template #expandedRowRender="{ record }">
                                <a-form-item label="健康监控器地址" :label-col="{span:5}">
                                    <a-input v-model:value="editableData[record.key].monitorAddress"></a-input>
                                </a-form-item>
                                <a-form-item label="健康监控器端口" :label-col="{span:5}">
                                    <a-input-number v-model:value="editableData[record.key].monitorPort" style="width:100%" :min="0" :precision="0"></a-input-number>
                                </a-form-item>
                                <a-form-item label="管理状态：启动" :label-col="{span:5}">
                                    <a-switch v-model:checked="editableData[record.key].adminStateUp" checked-children="是" un-checked-children="否"/>
                                </a-form-item>
                                <a-form-item label="备份" :label-col="{span:5}">
                                    <a-switch v-model:checked="editableData[record.key].backup" checked-children="是" un-checked-children="否"/>
                                </a-form-item>
                                <a-form-item label="名称" :label-col="{span:5}">
                                    <a-input v-model:value="editableData[record.key].name"></a-input>
                                </a-form-item>
                            </template>
                            <template #footer>
                                <a-button type="link" @click="()=>handleAdd()">
                                    <PlusOutlined />
                                    添加外部成员
                                </a-button>
                            </template>
                        </a-table>
                    <!-- </a-form-item> -->
                    <!-- <a-form-item label="可用虚机" name="name"> -->
                        <br>
                        <!-- <p>可用虚机：</p> -->
                        <a-table :columns="columns1" :data-source="serverlist" rowKey="id" size="small" :pagination="false">
                            <template #filterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
                                <div style="padding: 8px">
                                    <a-input ref="searchInput" :placeholder="`搜索 ${column.dataIndex}`" :value="selectedKeys[0]" style="width: 188px; margin-bottom: 8px; display: block" @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])" @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)" />
                                    <a-button type="primary" size="small" style="width: 90px; margin-right: 8px" @click="handleSearch(selectedKeys, confirm, column.dataIndex)" >
                                    <template #icon><SearchOutlined /></template>
                                    搜索
                                    </a-button>
                                    <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
                                    重置
                                    </a-button>
                                </div>
                                </template>
                                <template #filterIcon="filtered">
                                <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
                                </template>
                                <template #customRender="{ text, column }">
                                <span v-if="searchText && searchedColumn === column.dataIndex">
                                    <template v-for="(fragment, i) in text[0].addr.toString().split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i'))">
                                    <mark v-if="fragment.toLowerCase() === searchText.toLowerCase()" class="highlight" :key="i">
                                        {{ fragment }}
                                    </mark>
                                    <template v-else>{{ fragment }}</template>
                                    </template>
                                </span>
                                <template v-else>
                                    {{ text[0].addr }}
                                </template>
                                </template>
                            <!-- <template #address="{record}">
                                {{record.serverAddressEntityList[0].addr}}
                            </template> -->
                            <template #action="{record}">
                                <a-button type="link" @click="addToMember(record)">添加</a-button>
                            </template>
                        </a-table>
                        <!-- <a-select :options="subnetlist"></a-select> -->
                        <!-- <a-dropdown v-model:visible="isOpenServer" :trigger="['click']" placement="bottomLeft">
                            <a class="ant-dropdown-link" @click.prevent>
                            选择虚机添加到成员
                            <DownOutlined />
                            </a>
                            <template #overlay>
                                <a-menu @click="addToMember">
                                    <a-menu-item :key="index" v-for="(item,index) in serverlist" :extra="item">
                                        {{item.serverName}}（IP：{{item.serverAddressEntityList[0].addr}}）
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown> -->
                    <!-- </a-form-item> -->
                </div>
            </template>
            <template v-if="activeKey == '5'">
                <div style="height:448px;overflow-y:auto">
                    <a-form-item label="创建健康监控器" :label-col="{span:5}" v-if="(info.isAdd && (info.addKey != '4' && info.addKey != '5'))">
                        <a-switch v-model:checked="balanceform.createHealthMonitor" checked-children="是" un-checked-children="否" @change="(e)=>changeSwitch(e,'createHealthMonitor')"/>
                    </a-form-item>
                    <div v-if="balanceform.createHealthMonitor">
                        <a-form-item label="名称" :label-col="{span:5}" :name="['healthMonitorForm', 'name']">
                            <a-input v-model:value="balanceform.healthMonitorForm.name"></a-input>
                        </a-form-item>
                        <a-form-item label="类型" :label-col="{span:5}" :name="['healthMonitorForm', 'healthMonitorType']" :rules="[{required:true,message:'请选择类型'}]">
                            <a-select v-model:value="balanceform.healthMonitorForm.healthMonitorType" :disabled="!info.isAdd">
                                <a-select-option value="HTTP">HTTP</a-select-option>
                                <a-select-option value="HTTPS">HTTPS</a-select-option>
                                <a-select-option value="PING">PING</a-select-option>
                                <a-select-option value="TCP">TCP</a-select-option>
                                <a-select-option value="TLS-HELLO">TLS-HELLO</a-select-option>
                                <a-select-option value="UDP-CONNECT">UDP-CONNECT</a-select-option>
                                <a-select-option value="SCTP">SCTP</a-select-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item label="最大失败尝试次数" :label-col="{span:5}" :name="['healthMonitorForm', 'maxRetriesDown']" :rules="[{type:'number',required:true,message:'请输入最大失败尝试次数'}]">
                            <a-input-number v-model:value="balanceform.healthMonitorForm.maxRetriesDown" style="width:100%" :min="0" :precision="0"></a-input-number>
                        </a-form-item>
                        <a-form-item label="延迟（秒）" :label-col="{span:5}" :name="['healthMonitorForm', 'delay']" :rules="[{type:'number',required:true,message:'请输入延迟'}]">
                            <a-input-number v-model:value="balanceform.healthMonitorForm.delay" style="width:100%" :min="0" :precision="0"></a-input-number>
                        </a-form-item>
                        <a-form-item label="最大尝试次数" :label-col="{span:5}" :name="['healthMonitorForm', 'maxRetries']" :rules="[{type:'number',required:true,message:'请输入最大尝试次数'}]">
                            <a-input-number v-model:value="balanceform.healthMonitorForm.maxRetries" style="width:100%" :min="0" :precision="0"></a-input-number>
                        </a-form-item>
                        <a-form-item label="超时时限（秒）" :label-col="{span:5}" :name="['healthMonitorForm', 'timeout']" :rules="[{type:'number',required:true,message:'请输入超时时限'}]">
                            <a-input-number v-model:value="balanceform.healthMonitorForm.timeout" style="width:100%" :min="0" :precision="0"></a-input-number>
                        </a-form-item>
                        <div v-if="balanceform.healthMonitorForm.healthMonitorType == 'HTTP' || balanceform.healthMonitorForm.healthMonitorType == 'HTTPS'">
                            <a-form-item label="HTTP 方法" :label-col="{span:5}" :name="['healthMonitorForm', 'httpMethod']">
                                <a-select v-model:value="balanceform.healthMonitorForm.httpMethod" allow-clear>
                                    <a-select-option label="GET" value="GET" selected="selected">GET</a-select-option>
                                    <a-select-option label="HEAD" value="HEAD">HEAD</a-select-option>
                                    <a-select-option label="POST" value="POST">POST</a-select-option>
                                    <a-select-option label="PUT" value="PUT">PUT</a-select-option>
                                    <a-select-option label="DELETE" value="DELETE">DELETE</a-select-option>
                                    <a-select-option label="TRACE" value="TRACE">TRACE</a-select-option>
                                    <a-select-option label="OPTIONS" value="OPTIONS">OPTIONS</a-select-option>
                                    <a-select-option label="PATCH" value="PATCH">PATCH</a-select-option>
                                    <a-select-option label="CONNECT" value="CONNECT">CONNECT</a-select-option>
                                </a-select>
                            </a-form-item>
                            <a-form-item label="预期的状态码" :label-col="{span:5}" :name="['healthMonitorForm', 'expectedCodes']">
                                <a-input v-model:value="balanceform.healthMonitorForm.expectedCodes"></a-input>
                            </a-form-item>
                            <a-form-item label="URL 路径" :label-col="{span:5}" :name="['healthMonitorForm', 'urlPath']">
                                <a-input v-model:value="balanceform.healthMonitorForm.urlPath"></a-input>
                            </a-form-item>
                        </div>
                        <a-form-item label="管理状态" :label-col="{span:5}" :name="['healthMonitorForm', 'adminStateUp']">
                            <a-switch v-model:checked="balanceform.healthMonitorForm.adminStateUp" checked-children="启动" un-checked-children="关闭"/>
                        </a-form-item>
                    </div>
                </div>
            </template>
        </a-form>
    </a-modal>
</template>
<script lang='ts' setup>
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import {getFlavorCombine, getMemberCombine, getServerCombine, getSubnetCombine, getZoneCombine} from "@/common/responselist";
import { useRoute } from 'vue-router';
import { getBalanceInfoAPI, getMonitorInfoAPI, saveBalanceAPI, updateBalanceAPI } from '@/api/backend/devops/balance';
import * as BalanceAPI from "@/api/backend/devops/balance";
import {content} from "@/common/explain/modal"
const emit = defineEmits(['getlist'])
const {proxy} = getCurrentInstance()
const route = useRoute()
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                addKey:'0',
                isShow:false
            }
        }
    }
})
const activeKey = ref('')
const isCreateHM = ref(true)
const isCreateLS = ref(true)
const isCreatePool = ref(true)
const balanceForm = ref()
const funcName = [undefined,"负载均衡器","监控器","资源池","资源池成员","健康监控器"]
const plainOptions = ['X-Forwarded-For', 'X-Forwarded-Port', 'X-Forwarded-Proto'];
const defaultform = {
    "createListener":true,
    "createPool":true,
    "createHealthMonitor":true,
    "loadBalancerForm": {
        "adminStateUp": true,
        "availabilityZone": undefined,
        "cloudId": 0,
        "description": "",
        "id": 0,
        "name": undefined,
        "operatingStatus": "",
        "provider": "",
        "provisioningStatus": "",
        "thirdId": "",
        "thirdFlavorId":undefined,
        "thirdNetworkId": "",
        "thirdPortId": "",
        "thirdProjectId": "",
        "thirdSubnetId": "",
        "vipAddress": ""
    },
    "listenerForm": {
        "adminStateUp": 0,
        "allowedCidrsList": [],
        "cloudId": 0,
        "connectionLimit": -1,
        "defaultPoolId": "",
        "defaultTlsContainerRef": "",
        "description": "",
        "id": 0,
        "insertHeaders": [],
        "listenerName": "",
        "protocol": "",
        "protocolPort": 0,
        "sniContainerRefs": "",
        "thirdId": "",
        "thirdProjectId": "",
        "timeoutClientData": 50000,
        "timeoutMemberConnect": 5000,
        "timeoutMemberData": 50000,
        "timeoutTcpInspect": 0
    },
    "poolForm": {
        "adminStateUp": true,
        "cloudId": 0,
        "healthMonitorId": "",
        "id": 0,
        "lbMethod": "",
        "sessionPersistenceType":undefined,
        "poolName": undefined,
        "thirdId": "",
        "thirdProjectId": "",
        "tlsCiphers": "",
        "tlsEnabled": false,
    },
    "healthMonitorForm": {
        "adminStateUp": true,
        "cloudId": 0,
        "delay": 3,
        "expectedCodes": "200",
        "healthMonitorType": undefined,
        "httpMethod": "GET",
        "id": 0,
        "maxRetries": 3,
        "maxRetriesDown": 3,
        "name": "",
        "thirdId": "",
        "thirdPoolId": "",
        "thirdProjectId": "",
        "timeout": 4,
        "urlPath": "/"
    },
}
const balanceform = reactive({
    "createListener":true,
    "createPool":true,
    "createHealthMonitor":true,
    "loadBalancerForm": {
        "adminStateUp": true,
        "availabilityZone": undefined,
        "cloudId": 0,
        "description": "",
        "id": 0,
        "name": undefined,
        "operatingStatus": "",
        "provider": "",
        "provisioningStatus": "",
        "thirdId": "",
        "thirdFlavorId":undefined,
        "thirdNetworkId": "",
        "thirdPortId": "",
        "thirdProjectId": "",
        "thirdSubnetId": "",
        "vipAddress": ""
    },
    "listenerForm": {
        "adminStateUp": 0,
        "allowedCidrsList": [],
        "cloudId": 0,
        "connectionLimit": -1,
        "defaultPoolId": "",
        "defaultTlsContainerRef": "",
        "description": "",
        "id": 0,
        "insertHeaders": [],
        "listenerName": "",
        "protocol": "",
        "protocolPort": 0,
        "sniContainerRefs": "",
        "thirdId": "",
        "thirdProjectId": "",
        "timeoutClientData": 50000,
        "timeoutMemberConnect": 5000,
        "timeoutMemberData": 50000,
        "timeoutTcpInspect": 0
    },
    "poolForm": {
        "adminStateUp": true,
        "cloudId": 0,
        "healthMonitorId": "",
        "id": 0,
        "lbMethod": "",
        "sessionPersistenceType":undefined,
        "poolName": undefined,
        "thirdId": "",
        "thirdProjectId": "",
        "tlsCiphers": "",
        "tlsEnabled": false,
    },
    "healthMonitorForm": {
        "adminStateUp": true,
        "cloudId": 0,
        "delay": 3,
        "expectedCodes": "200",
        "healthMonitorType": undefined,
        "httpMethod": "GET",
        "maxRetries": 3,
        "maxRetriesDown": 3,
        "name": "",
        "thirdId": "",
        "thirdPoolId": "",
        "thirdProjectId": "",
        "timeout": 4,
        "urlPath": "/"
    },
})
const columns = [
    { title: 'IP地址', dataIndex: 'address', slots: { customRender: 'address' }, width:220, key: 'address' },
    { title: '子网', dataIndex: 'subnetId', slots: { customRender: 'subnetId' }, width:160, key: 'subnetId' },
    { title: '端口', dataIndex: 'protocolPort', slots: { customRender: 'protocolPort' }, key: 'protocolPort' },
    { title: '权重', dataIndex: 'weight', slots: { customRender: 'weight' }, key: 'weight' },
    // { title: '健康监控器地址', dataIndex: 'a', slots: { customRender: 'address' }, key: 'address' },
    // { title: '健康监控器端口', dataIndex: 'b', slots: { customRender: 'address' }, key: 'address' },
    // { title: '管理状态：启动', dataIndex: 'c', slots: { customRender: 'address' }, key: 'address' },
    // { title: '备份', dataIndex: 'd', slots: { customRender: 'address' }, key: 'address' },
    // { title: '名称', dataIndex: 'e', slots: { customRender: 'address' }, key: 'address' },
    { title: '操作', dataIndex: 'action', slots: { customRender: 'action' }, width:60, key: 'x' },
];
const columns1 = [
    { title: '名称', dataIndex: 'serverName', slots: { customRender: 'name' }, key: 'name' },
    { title: 'IP地址', dataIndex: 'serverAddressEntityList', slots: {
          filterDropdown: 'filterDropdown',
          filterIcon: 'filterIcon',
          customRender: 'customRender',
        },
        onFilter: (value, record) => {console.log('val;ue',value); return record.serverAddressEntityList[0]?.addr.toString().toLowerCase().includes(value.toLowerCase())},
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                setTimeout(() => {
                searchInput.value.focus();
                }, 100);
            }
        }
    },
    { title: '操作', dataIndex: 'action', slots: { customRender: 'action' }, width:60, key: 'x' },
]
const expandedRowKeys = ref([])
const memberlist = ref([])
const serverlist = ref([])
const zonelist = ref([])
const subnetlist = ref([])
const flavorlist = ref([])
const isOpenServer = ref(true);
const editableData = reactive({});
const formname = ref('loadBalancerForm');
const updatename = ref('updateBalanceAPI');
const savename = ref('saveBalanceAPI');
const searchText = ref('');
const searchedColumn = ref('');
const searchInput = ref()
const Parentform = reactive({})
const inputVisible = ref(false)
const inputRef = ref()
const inputValue = ref('')
const handleSearch = (selectedKeys, confirm, dataIndex) => {
      confirm();
      searchText.value = selectedKeys[0];
      searchedColumn.value = dataIndex;
};
const handleReset = clearFilters => {
      clearFilters();
      searchText.value = '';
};
const handleClose = removedTag => {
    balanceform.listenerForm.allowedCidrsList.filter(tag => tag !== removedTag);
    // console.log(tags);
    // balanceform.listenerForm.allowedCidrsList = tags;
    // inputVisible.value = false;
    console.log("inputVisible",inputVisible.value,balanceform.listenerForm.allowedCidrsList)
};
const handleInputConfirm = () => {
    const inputValue1 = inputValue.value;
    let tags = balanceform.listenerForm.allowedCidrsList?balanceform.listenerForm.allowedCidrsList:[];
    console.log("tags",tags)
    if (inputValue1 && tags.indexOf(inputValue1) === -1) {
        tags = [...tags, inputValue1];
    }
    console.log(tags);
    balanceform.listenerForm.allowedCidrsList = tags;
    inputVisible.value = false;
    inputValue.value = '';
};
const showInput = () => {
      inputVisible.value = true;
      nextTick(() => {
        inputRef.value.focus();
      });
    };
const onExpand = (expanded, record) => {
    console.log("expanded, record",expanded, record)
    if(expanded)
        expandedRowKeys.value.splice(expandedRowKeys.value.findIndex(key => key === record.key), 1)
    else
        expandedRowKeys.value.push(record.key)
    console.log("expandedRowKeys.value",expandedRowKeys.value)
}
const computedRes = (valuename) => {
    if(valuename == 'createPool'){
        if(balanceform.createPool)
            balanceform.createListener = balanceform.createPool
        else{
            balanceform.createHealthMonitor = balanceform.createPool
            console.log("balanceform.createListener1",balanceform.createListener,balanceform.createPool)
        }
    }
    if(valuename == 'createHealthMonitor'){
        if(balanceform.createHealthMonitor)
            balanceform.createPool = balanceform.createHealthMonitor
        if(balanceform.createPool)
            balanceform.createListener = balanceform.createPool
    }
    if(valuename == 'createListener'){
        // console.log("balanceform.createListener",balanceform.createListener,balanceform.createPool)
        if(!balanceform.createListener){
            balanceform.createPool = balanceform.createListener
            console.log("balanceform.createListener",balanceform.createListener,balanceform.createPool)
        }
        if(!balanceform.createPool){

            balanceform.createHealthMonitor = balanceform.createPool
            console.log("balanceform.createListener1",balanceform.createListener,balanceform.createPool)
        }
    }
}
const changeSwitch = (e,valuename) => {
    console.log('e,b',e,valuename,balanceform[valuename])
    computedRes(valuename)
    // if(valuename == 'createHealthMonitor'){
    //     if(e){
    //         balanceform.createPool = e;

    //     }
    // }
}
const handleAdd = (row) => {
    const rowData = {};
    const newData = {};
    console.log("row",row)
    if(!row){
        Object.assign(rowData,{
            key: `${memberlist.value.length}`,
            address: '',
            subnetId: '',
            protocolPort: '',
            weight:1,
            b:'',
            c:'',
            adminStateUp:true,
            backup:false,
            name: ''
        });
        Object.assign(newData,{
            key: `${memberlist.value.length}`,
            address: '',
            subnetId: '',
            protocolPort: '',
            weight:1,
            b:'',
            c:'',
            adminStateUp:true,
            backup:false,
            name: ''
        });
    }else{
        Object.assign(newData,{
            key: `${memberlist.value.length}`,
            protocolPort: '',
            weight:1,
            b:'',
            c:'',
            adminStateUp:true,
            backup:false,
            name:row?.serverName
        });
        Object.assign(rowData,{
            key: `${memberlist.value.length}`,
            address: row?.address ? row.address : undefined,
            subnetId: row?.subnetId ? row.subnetId : undefined,
            protocolPort: '',
            weight:1,
            b:'',
            c:'',
            adminStateUp:true,
            backup:false,
            name:row?.serverName ? row.serverName : undefined
        })
    }
    editableData[memberlist.value.length] = newData;
    memberlist.value.push(rowData);
};
const onDelete = (key) => {
    delete editableData[key];
    memberlist.value = memberlist.value.filter(item => item.key !== key);
}
const addToMember = (record) => {
    handleAdd({address:record.serverAddressEntityList[0].addr,subnetId:record.serverAddressEntityList[0].subnetId,serverName:record.serverName})
}
const setInitInfo = async (record,parentform) => {
    // console.log("parentform",parentform)
    if(parentform)
    Object.assign(Parentform,parentform)
    activeKey.value = props.info.addKey;
    if(activeKey.value == '1'){
        zonelist.value = await getZoneCombine({cloudId:route.query.cloudId,projectId:route.query.projectId,module:'nova'});
        subnetlist.value = await getSubnetCombine({cloudId:route.query.cloudId,projectId:route.query.projectId});
        flavorlist.value = await getFlavorCombine({cloudId:route.query.cloudId,projectId:route.query.projectId});
    }
    if(activeKey.value == '4' && props.info.isAdd){
        serverlist.value = await getServerCombine({cloudId:route.query.cloudId,projectId:route.query.projectId});
        memberlist.value = await getMemberCombine({cloudId:route.query.cloudId,projectId:route.query.projectId,poolId:parentform.id});
    }
    let apiname = 'getBalanceInfoAPI';
    if(props.info.addKey == '1'){
        // balanceId.value = id;
        apiname = 'getBalanceInfoAPI'
        formname.value = 'loadBalancerForm'
        updatename.value = 'updateBalanceAPI'
        savename.value = 'saveBalanceAPI'
        // getBalanceInfoAPI({id}).then((res)=>{
        //     if(res.code == 0){
        //         Object.assign(balanceform,res.data)
        //     }
        // })
    }else if(props.info.addKey == '2'){
        apiname = 'getMonitorInfoAPI'
        formname.value = 'listenerForm'
        updatename.value = 'updateMonitorAPI'
        savename.value = 'saveMonitorAPI'
        // getMonitorInfoAPI({id}).then((res)=>{
        //     if(res.code == 0){
        //         Object.assign(balanceform,res.data)
        //     }
        // })
    }else if(props.info.addKey == '3'){
        apiname = 'getPoolInfoAPI'
        formname.value = 'poolForm'
        updatename.value = 'updatePoolAPI'
        savename.value = 'savePoolAPI'
        
    }else if(props.info.addKey == '5'){
        apiname = 'getHealthInfoAPI'
        formname.value = 'healthMonitorForm'
        updatename.value = 'updateHealthAPI'
        savename.value = 'saveHealthAPI'
    }
    if(!props.info.isAdd){
        BalanceAPI[apiname]({id:record.id}).then((res)=>{
            if(res.code == 0){
                Object.assign(balanceform[formname.value],res.data)
                balanceform[formname.value].adminStateUp = Boolean(res.data.adminStateUp)
            }
        })
        console.log("zonelist.value",zonelist.value)
    }
    if(props.info.addKey == '5')
        balanceform[formname.value].thirdPoolId = parentform.thirdId;
    else if(props.info.addKey == '2'){
        balanceform[formname.value].insertHeaders = balanceform[formname.value].insertHeadersEntityList ? balanceform[formname.value].insertHeadersEntityList : []
    }
    else if(props.info.addKey == '3'){
        balanceform[formname.value].tlsEnabled = Boolean(balanceform[formname.value].tlsEnabled)
    }
    nextTick(()=>{
        console.log("formname.value",formname.value,balanceform[formname.value],balanceform[formname.value].adminStateUp)
    })
    //     balanceform[formname.value].lbId = parentform.thirdId;
    if(parentform)
    balanceform[formname.value].thirdProjectId = parentform.thirdProjectId;
}
const cancel = () => {
    props.info.isShow = false;
    setTimeout(()=>{
        balanceForm.value.resetFields();
        Object.assign(balanceform,defaultform);
        activeKey.value = '1'
    })
}
const handleSave = () => {
    // let balanceform1 = {...balanceform};
    let balanceform1 = JSON.parse(JSON.stringify(balanceform));
    if(!balanceform.createListener){
        balanceform1.listenerForm = undefined;
    }else{
        if(balanceform.listenerForm.protocol == 'UDP' || balanceform.listenerForm.protocol == 'SCTP'){
            balanceform1.listenerForm.timeoutClientData = balanceform1.listenerForm.timeoutMemberConnect = balanceform1.listenerForm.timeoutMemberData = balanceform1.listenerForm.timeoutTcpInspect = undefined;
        }
        if(balanceform.listenerForm.protocol != 'HTTP'){
            balanceform1.listenerForm.listenerName = undefined;
        }
    }
    if(!balanceform.createPool)
        balanceform1.poolForm = undefined;
    else{
        // if(balanceform.poolForm.poolName != 'APP_COOKIE')
        //     balanceform1.poolForm.poolName = undefined;
        if(!balanceform.poolForm.tlsEnabled)
            balanceform1.poolForm.tlsCiphers = undefined;
    }
    if(!balanceform.createHealthMonitor)
        balanceform1.healthMonitorForm = undefined;
    else{
        if(balanceform.healthMonitorForm.healthMonitorType != 'HTTP' && balanceform.healthMonitorForm.healthMonitorType != 'HTTPS'){
            balanceform1.healthMonitorForm.httpMethod = balanceform1.healthMonitorForm.expectedCodes = balanceform1.healthMonitorForm.urlPath = undefined;
        }
    }
    if(props.info.isAdd){
        if(props.info.addKey == '2'){
            balanceform1.createListener = undefined;
            balanceform1.loadBalancerForm = undefined;
        }
        if(props.info.addKey == '3'){
            balanceform1.createPool = undefined;
            balanceform1.listenerForm = undefined;
        }
        if(props.info.addKey == '5'){
            balanceform1.createHealthMonitor = undefined;
            balanceform1.poolForm = undefined;
        }
    }
    if(props.info.isAdd === false){
        if(props.info.addKey == '1')
            balanceform1.listenerForm = balanceform1.poolForm = balanceform1.healthMonitorForm = undefined;
        if(props.info.addKey == '2')
            balanceform1.loadBalancerForm = balanceform1.poolForm = balanceform1.healthMonitorForm = undefined;
        if(props.info.addKey == '3')
            balanceform1.loadBalancerForm = balanceform1.listenerForm = balanceform1.healthMonitorForm = undefined;
        if(props.info.addKey == '5')
            balanceform1.loadBalancerForm = balanceform1.listenerForm = balanceform1.poolForm = undefined;
    }
    if(props.info.addKey == '2'){
        let temp = {};
        // Object.assign(balanceform1.listenerForm.insertHeaders,{...{}});
        // console.log("lis",balanceform.listenerForm.insertHeaders,balanceform1.listenerForm.insertHeaders)
        // {"X-Forwarded-For":false,"X-Forwarded-Port":false,"X-Forwarded-Proto":false}
        plainOptions.forEach((item,index)=>{
            if(balanceform.listenerForm.insertHeaders.includes(item)){
                temp[item] = true;
            }else{
                temp[item] = false;
            }
        })
        balanceform1.listenerForm.insertHeaders = {...temp};
        // Object.assign(balanceform1.listenerForm.insertHeaders,{...temp})
    }
    if(props.info.addKey == '3')
        balanceform1.poolForm.tlsEnabled = Number(balanceform.poolForm.tlsEnabled)
    balanceform1[formname.value].cloudId = Number(route.query.cloudId);
    balanceform1[formname.value].domainId = Number(route.query.domainId);
    balanceform1[formname.value].projectId = Number(route.query.projectId);
    balanceform1[formname.value].adminStateUp = Number(balanceform1[formname.value].adminStateUp)
    // , ()=>{emit('getlist');cancel()},null
    if(props.info.addKey == '1' && props.info.isAdd){
        // if(props.info.isAdd)
            proxy.$handleSave(balanceForm.value, saveBalanceAPI, updateBalanceAPI, props, balanceform1,()=>{cancel();emit('getlist',Parentform)},null)
        // else

    }
    else{
        proxy.$handleSave(balanceForm.value, BalanceAPI[savename.value], BalanceAPI[updatename.value], props, balanceform1[formname.value],()=>{cancel();emit('getlist',Parentform)},null)
    }
}
onMounted(() => {})
defineExpose({setInitInfo})
</script>
<style lang='scss' scoped>
// .ant-form{height: 100%;}
// .ant-tabs{height: 100%;}
// :deep(.ant-tabs-content){height: 100%;}
// :deep(.ant-tabs-tabpane){height: 100%;overflow-y: scroll;}
:deep(.ant-table-footer){text-align: center;}
:deep(.ant-tabs .ant-tabs-left-bar .ant-tabs-tab){width: 156px;}
:deep(.ant-modal-close-x){width:36px;text-align: left;}
</style>