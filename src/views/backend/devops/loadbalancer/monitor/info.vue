<template>
    <div class='back-page'>
        <a-page-header
          style="background-color:#fff"
          :title="monitorform.listenerName"
          @back="()=>{props.info.viewKey = '1'}"
      />
        <a-tabs class="back-content" :animated="false" @change="changeMonitorTab">
            <a-tab-pane tab=""></a-tab-pane>
            <a-tab-pane key="2" tab="概览">
                <a-descriptions :column="2" style="width:900px">
                    <a-descriptions-item label="ID">{{monitorform.thirdId}}</a-descriptions-item>
                    <a-descriptions-item label="名称">{{monitorform.listenerName}}</a-descriptions-item>
                    <a-descriptions-item label="描述">{{monitorform.description ? monitorform.description : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="项目 ID">{{monitorform.thirdProjectId}}</a-descriptions-item>
                    <a-descriptions-item label="协议">{{monitorform.protocol}}</a-descriptions-item>
                    <a-descriptions-item label="端口">{{monitorform.protocolPort}}</a-descriptions-item>
                    <a-descriptions-item label="连接限制">{{monitorform.connectionLimit}}</a-descriptions-item>
                    <a-descriptions-item label="插入报头">{{monitorform.createTime}}</a-descriptions-item>
                    <a-descriptions-item label="默认资源池 ID">{{monitorform.defaultPoolId}}</a-descriptions-item>
                    <a-descriptions-item label="客户数据超时时限">{{monitorform.timeoutClientData ? monitorform.timeoutClientData : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="成员连接超时时限">{{monitorform.timeoutMemberConnect ? monitorform.timeoutMemberConnect : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="成员数据超时时限">{{monitorform.timeoutMemberData ? monitorform.timeoutMemberData : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="TCP 监测超时时限">{{monitorform.timeoutTcpInspect ? monitorform.timeoutTcpInspect : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="Allowed Cidrs">{{monitorform.allowedCidrsList ? monitorform.allowedCidrsList : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="TLS Cipher String">{{monitorform.defaultTlsContainerRef ? monitorform.defaultTlsContainerRef : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="操作状态">{{monitorform.operatingStatusText ? monitorform.operatingStatusText : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="配置状态">{{monitorform.provisioningStatusText ? monitorform.provisioningStatusText : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="管理状态">{{monitorform.adminStateUp ? '开启' : '未开启'}}</a-descriptions-item>
                    <a-descriptions-item label="创建时间">{{monitorform.createTime}}</a-descriptions-item>
                    <a-descriptions-item label="修改时间">{{monitorform.updateTime}}</a-descriptions-item>
                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="3" tab="资源池">
                <Pool ref="poolRef" parentKey="2" />
            </a-tab-pane>
            <a-tab-pane key="4" tab="七层策略">
                <Seven ref="sevenRef" />
            </a-tab-pane>
        </a-tabs>
    </div>
</template>
<script lang='ts' setup>
import { getMonitorInfoAPI } from '@/api/backend/devops/balance';
import emiter from '@/utils/Bus';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import Pool from "../pool/index.vue"
import Seven from "./seven/index.vue"
const {proxy} = getCurrentInstance()
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
                isInfo:true
            }
        }
    }
})
const poolRef = ref()
const sevenRef = ref()
const monitorId = ref()
const monitorform = reactive({
  "adminStateUp": 0,
  "cloudId": 0,
  "connectionLimit": 0,
  "defaultPoolId": "",
  "defaultTlsContainerRef": "",
  "description": "",
  "id": 0,
  "listenerName": "",
  "protocol": "",
  "protocolPort": 0,
  "sniContainerRefs": "",
  "thirdId": "",
  "thirdProjectId": "",
  "timeoutClientData": 0,
  "timeoutMemberConnect": 0,
  "timeoutMemberData": 0,
  "timeoutTcpInspect": 0
})
const changeMonitorTab = (key) => {
    if(key == '3'){
        proxy.$nextTick(()=>{
            poolRef.value.getPoolList(undefined,monitorId.value)
        })
    }
    if(key == '4'){
        proxy.$nextTick(()=>{
            sevenRef.value.getSevenList(monitorform)
        })
    }
}
const setInfo = (id) => {
    monitorId.value = id;
    getMonitorInfoAPI({id}).then((res)=>{
        if(res.code == 0){
            Object.assign(monitorform,res.data)
        }
    })
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
@import url('@/styles/tabs.scss');
</style>