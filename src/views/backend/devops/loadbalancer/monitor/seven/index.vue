<template>
    <div class='cloudContent' v-if="!editinfo.isInfo">
        <div class="cloudRight">
            <div class="innerPadding">
                <div class="buttonGroup">
                    <a-button type="primary" @click="handleEdit(true)">新增</a-button>
                    <a-button style="margin-left:10px" @click="$handleDel(selectRowIds,deleteSevenAPI,()=>{selectRowIds = [];getPoolList(Parentform)})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </div>
                <a-table :row-selection="rowSelection" :columns="columns" :dataSource="healthlist" rowKey="id" :loading="loading" :pagination="false">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #name={record}>
                        <a href="#" @click="handleView(record)">{{record.name ? record.name : '-'}}</a>
                        <!-- <span>{{record.name ? record.name : '-'}}</span> -->
                    </template>
                    <template #operatingStatusText={record}>
                        {{record.operatingStatusText ? record.operatingStatusText : '-'}}
                    </template>
                    <template #provisioningStatusText={record}>
                        {{record.provisioningStatusText ? record.provisioningStatusText : '-'}}
                    </template>
                    <template #adminStateUp={record}>
                        {{record.adminStateUp ? '开启' : '未开启'}}
                    </template>
                    <template #action1="{record}">
                        <a-button class="button_E" @click="handleEdit(false,record)">编辑</a-button>
                        <!-- <a-button class="button_E" @click="handleFloatip">关联浮动IP</a-button> -->
                        <a-button class="button_D" @click="$handleDel([record.id],deleteSevenAPI,()=>getPoolList(Parentform))">删除</a-button>
                    </template>
                </a-table>
            </div>
        </div>
        <Edit ref="editRef" :info="editinfo" @getlist="getSevenList" />
        <!-- <Bindfloat ref="bindRef" :info="floatinfo" /> -->
    </div>
    <Info ref="viewRef" :info="editinfo" v-else/>
</template>
<script lang='ts' setup>
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
import Edit from "./edit.vue";
// import Bindfloat from "./bindfloat.vue";
import Info from "./info.vue";
import { useRoute } from 'vue-router';
import { selectSevenListAPI,deleteSevenAPI } from '@/api/backend/devops/balance';
const {proxy} = getCurrentInstance()
const props = defineProps({
    parentKey:String
})
const route = useRoute()
const Parentform = reactive({})
const columns = [
    {title:'名称',dataIndex:'name',slots:{customRender:'name'}},
    {title:'位置',dataIndex:'position'},
    {title:'行为',dataIndex:'action'},
    {title:'操作状态',dataIndex:'operatingStatusText',slots:{customRender:'operatingStatusText'}},
    {title:'配置状态',dataIndex:'provisioningStatusText',slots:{customRender:'provisioningStatusText'}},
    {title:'管理状态',dataIndex:'adminStateUp',slots:{customRender:'adminStateUp'}},
    {title:'操作',dataIndex:'action1',slots:{customRender:'action1'}},
]
let selectRowIds: string[] = ref([]);
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const searchform = reactive({
    cloudId:route.query.cloudId,projectId:route.query.projectId,name:undefined
})
const floatinfo = reactive({
    isShow:false
})
const bindRef = ref()
const editRef = ref()
const viewRef = ref()
const editinfo = reactive({isAdd:true,isInfo:false,isShow:false,addKey:undefined})
const loading = ref(false);
const healthlist = ref([
    // {name:'senlin-lb-my_cluster',IP:'***********',zone:'-',state:'衰退',status:'运行中',active:'是'}
])
const handleSearch = () => {}
const handleEdit = (isAdd,record) => {
    editinfo.isAdd = isAdd;
    editinfo.isShow = true;
    // if(!isAdd)
        editRef.value.setInitInfo(record,Parentform);
}
const handleView = (record) => {
    // editinfo.isInfo = true;
    // viewRef.value.setInfo(record);
    proxy.$mitt.emit("setEditinfo",{key:'6',record})
    // proxy.$mitt.emit("setPoolparentKey",'2')
}
const handleFloatip = () => {
    floatinfo.isShow = true;
}
const getSevenList = async (parentform) => {
    loading.value = true;
    Object.assign(Parentform,parentform)
    let res = await selectSevenListAPI({listenerId:parentform.id})
    loading.value = false;
    if(res.code == 0 && res.data){
        healthlist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({getSevenList})
</script>
<style lang='scss' scoped>
</style>