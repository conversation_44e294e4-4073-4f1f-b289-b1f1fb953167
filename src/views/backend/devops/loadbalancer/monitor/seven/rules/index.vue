<template>
    <div class='cloudContent' v-if="!editinfo.isInfo">
        <div class="cloudRight">
            <div class="innerPadding">
                <div class="buttonGroup">
                    <a-button type="primary" @click="handleEdit(true)">新增</a-button>
                    <a-button style="margin-left:10px" @click="$handleDel(selectRowIds,deleteRulesAPI,()=>{selectRowIds = [];getRulesList(Parentform)})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </div>
                <a-table :row-selection="rowSelection" :columns="columns" :dataSource="healthlist" rowKey="id" :loading="loading" :pagination="false">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #ruleKey={record}>
                        {{record.ruleKey ? record.ruleKey : '-'}}
                    </template>
                    <template #invert={record}>
                        {{record.invert ? '是' : '否'}}
                    </template>
                    <template #operatingStatusText={record}>
                        {{record.operatingStatusText ? record.operatingStatusText : '-'}}
                    </template>
                    <template #provisioningStatusText={record}>
                        {{record.provisioningStatusText ? record.provisioningStatusText : '-'}}
                    </template>
                    <template #adminStateUp={record}>
                        {{record.adminStateUp ? '开启' : '未开启'}}
                    </template>
                    <template #action="{record}">
                        <a-button class="button_E" @click="handleEdit(false,record)">编辑</a-button>
                        <!-- <a-button class="button_E" @click="handleFloatip">关联浮动IP</a-button> -->
                        <a-button class="button_D" @click="$handleDel([record.id],deleteRulesAPI,()=>getRulesList(Prentform))">删除</a-button>
                    </template>
                </a-table>
            </div>
        </div>
        <Edit ref="editRef" :info="editinfo" @getlist="getRulesList" />
        <!-- <Bindfloat ref="bindRef" :info="floatinfo" /> -->
    </div>
    <!-- <Info ref="viewRef" :info="editinfo" v-else/> -->
</template>
<script lang='ts' setup>
import { computed, onMounted, reactive, ref } from 'vue';
import Edit from "./edit.vue";
// import Bindfloat from "./bindfloat.vue";
// import Info from "./info.vue";
import { useRoute } from 'vue-router';
import { selectRulesListAPI, selectSevenListAPI,deleteRulesAPI } from '@/api/backend/devops/balance';
const props = defineProps({
    parentKey:String
})
const route = useRoute()
const PolicieId = ref()
const columns = [
    {title:'类型',dataIndex:'type'},
    {title:'比较类型',dataIndex:'compareType'},
    {title:'键',dataIndex:'ruleKey',slots:{customRender:'ruleKey'}},
    {title:'值',dataIndex:'value'},
    {title:'反转',dataIndex:'invert',slots:{customRender:'invert'}},
    {title:'操作状态',dataIndex:'operatingStatusText',slots:{customRender:'operatingStatusText'}},
    {title:'配置状态',dataIndex:'provisioningStatusText',slots:{customRender:'provisioningStatusText'}},
    {title:'管理状态',dataIndex:'adminStateUp',slots:{customRender:'adminStateUp'}},
    {title:'操作',dataIndex:'action',slots:{customRender:'action'}},
]
const searchform = reactive({
    cloudId:route.query.cloudId,projectId:route.query.projectId,name:undefined
})
const floatinfo = reactive({
    isShow:false
})
const bindRef = ref()
const editRef = ref()
const viewRef = ref()
const Prentform = reactive({})
const editinfo = reactive({isAdd:true,isInfo:false,isShow:false,addKey:undefined})
const loading = ref(false);
const healthlist = ref([
    // {name:'senlin-lb-my_cluster',IP:'***********',zone:'-',state:'衰退',status:'运行中',active:'是'}
])
let selectRowIds: string[] = ref([]);
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const handleSearch = () => {}
const handleEdit = (isAdd,record) => {
    editinfo.isAdd = isAdd;
    editinfo.isShow = true;
    // if(!isAdd)
        editRef.value.setInitInfo(record,Prentform);
}
const handleView = (record) => {
    editinfo.isInfo = true;
    viewRef.value.setInfo(record);
}
const handleFloatip = () => {
    floatinfo.isShow = true;
}
const getRulesList = async (parentform) => {
    loading.value = true;
    // PolicieId.value = parentform.id;
    Object.assign(Prentform,parentform)
    let res = await selectRulesListAPI({policieId:parentform.id})
    loading.value = false;
    if(res.code == 0 && res.data){
        healthlist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({getRulesList})
</script>
<style lang='scss' scoped>
</style>