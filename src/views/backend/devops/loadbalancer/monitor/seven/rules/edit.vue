<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" ok-text="提交" @ok="save" @cancel="cancel" :getContainer="modalBindNode">
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                <span>{{(info.isAdd ? '新增七层规则' : '修改七层规则')}}</span>
                <a-popover trigger="click" placement="leftTop">
                    <template #content>
                        <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto">{{content.balancer_seven_rule}}</pre>
                    </template>
                    <InfoCircleOutlined  />
                </a-popover>
                </div>
            </template>
            <a-form :model="sevenform" ref="sevenForm" :label-col="{span:4}">
                <a-form-item label="反转" name="invert">
                    <a-switch v-model:checked="sevenform.invert" checked-children="是" un-checked-children="否"/>
                </a-form-item>
                <a-form-item label="类型" name="type" :rules="[{required:true,message:'请选择类型'}]">
                    <a-select v-model:value="sevenform.type" @change="changeAction">
                        <a-select-option value="HOST_NAME">HOST_NAME</a-select-option>
                        <a-select-option value="PATH">PATH</a-select-option>
                        <a-select-option value="FILE_TYPE">FILE_TYPE</a-select-option>
                        <a-select-option value="HEADER">HEADER</a-select-option>
                        <a-select-option value="COOKIE">COOKIE</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="键" name="key" v-if="sevenform.type == 'HEADER' || sevenform.type == 'COOKIE'">
                    <a-input v-model:value="sevenform.key"></a-input>
                </a-form-item>
                <a-form-item label="比较类型" name="compareType" :rules="[{required:true,message:'请选择比较类型'}]">
                    <a-select v-model:value="sevenform.compareType" @change="changeAction">
                        <a-select-option value="REGEX">REGEX</a-select-option>
                        <a-select-option value="EQUAL_TO">EQUAL_TO</a-select-option>
                        <a-select-option value="STARTS_WITH">STARTS_WITH</a-select-option>
                        <a-select-option value="ENDS_WITH">ENDS_WITH</a-select-option>
                        <a-select-option value="CONTAINS">CONTAINS</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="值" name="value">
                    <a-input v-model:value="sevenform.value"></a-input>
                </a-form-item>
                <a-form-item label="管理状态" name="adminStateUp">
                    <a-switch v-model:checked="sevenform.adminStateUp" checked-children="启动" un-checked-children="关闭"/>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { saveRulesAPI, saveSevenAPI, updateRulesAPI, updateSevenAPI } from '@/api/backend/devops/balance';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {content} from "@/common/explain/modal"
import { useRoute } from 'vue-router';
const {proxy} = getCurrentInstance()
const route = useRoute()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})
const sevenForm = ref()
const defaultform = {
  "adminStateUp": 0,
  "cloudId": route.query.cloudId,
  "compareType": "",
  "invert": 0,
  "key": "",
  "policieId": "",
  "projectId": route.query.projectId,
  "type": "",
  "value": ""
}
const sevenform = reactive({
  "adminStateUp": 0,
  "cloudId": route.query.cloudId,
  "compareType": "",
  "invert": 0,
  "key": "",
  "policieId": "",
  "projectId": route.query.projectId,
  "type": "",
  "value": ""
})
const poollist = ref([])
const Prentform = reactive({})
const changeAction = (value) => {
    // if(value == 'REDIRECT_TO_POOL'){

    // }
}
const setInitInfo = (record,parentform) => {
    Object.assign(Prentform,parentform)
    if(record){
        let record1 = {...record};
        // record1.thirdId = record1.policieId;
        record1.adminStateUp = Boolean(record.adminStateUp);
        Object.assign(sevenform,record1)
    }
    sevenform.policieId = parentform.thirdId;
    sevenform.projectId = parentform.projectId;
    // else if(parentform){
    // }
    
}
const cancel=()=>{
    props.info.isShow = false;
    sevenForm.value.resetFields()
    Object.assign(sevenform,defaultform)
}
const save = () => {
    let sevenform1 = {...sevenform}
    if(!(sevenform.type == 'HEADER' || sevenform.type == 'COOKIE'))
    sevenform1.key = undefined
    sevenform1.adminStateUp = Number(sevenform.adminStateUp)
        proxy.$handleSave(sevenForm.value, saveRulesAPI, updateRulesAPI, props, sevenform1,()=>{emit('getlist',Prentform);cancel()},null)
}
onMounted(() => {})
defineExpose({setInitInfo})
</script>
<style lang='scss' scoped>
:deep(.ant-modal-close-x){width:36px;text-align: left;}
</style>