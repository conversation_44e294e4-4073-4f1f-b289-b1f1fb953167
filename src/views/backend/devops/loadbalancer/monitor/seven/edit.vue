<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" ok-text="提交" @ok="save" @cancel="cancel" :getContainer="modalBindNode">
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                <span>{{(info.isAdd ? '新增七层策略' : '修改七层策略')}}</span>
                <a-popover trigger="click" placement="leftTop">
                    <template #content>
                        <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto">{{content.balancer_seven}}</pre>
                    </template>
                    <InfoCircleOutlined  />
                </a-popover>
                </div>
            </template>
            <a-form :model="sevenform" ref="sevenForm" :label-col="{span:4}">
                <a-form-item label="名称" name="name">
                    <a-input v-model:value="sevenform.name"></a-input>
                </a-form-item>
                <a-form-item label="描述" name="description">
                    <a-input v-model:value="sevenform.description"></a-input>
                </a-form-item>
                <a-form-item label="行为" name="action" :rules="[{required:true,message:'请选择行为'}]">
                    <a-select v-model:value="sevenform.action" @change="changeAction">
                        <a-select-option value="REJECT">REJECT</a-select-option>
                        <a-select-option value="REDIRECT_TO_URL">REDIRECT_TO_URL</a-select-option>
                        <a-select-option value="REDIRECT_TO_POOL">REDIRECT_TO_POOL</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="重定向 URL" name="redirectUrl" :rules="[{required:true,message:'请输入重定向 URL'}]" v-if="sevenform.action == 'REDIRECT_TO_URL'">
                    <a-input v-model:value="sevenform.redirectUrl" placeholder="https://www.example.com"></a-input>
                </a-form-item>
                <a-form-item label="重定向资源池 ID" name="redirectPoolId" :rules="[{required:true,message:'请选择重定向资源池 ID'}]" v-if="sevenform.action == 'REDIRECT_TO_POOL'">
                    <a-select v-model:value="sevenform.redirectPoolId" :options="poollist"></a-select>
                </a-form-item>
                <a-form-item label="位置" name="position">
                    <a-input-number v-model:value="sevenform.position" style="width:100%" :min="0" :precision="0"></a-input-number>
                </a-form-item>
                <a-form-item label="管理状态" name="adminStateUp">
                    <a-switch v-model:checked="sevenform.adminStateUp" checked-children="开启" un-checked-children="关闭"/>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { saveSevenAPI, updateSevenAPI } from '@/api/backend/devops/balance';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {content} from "@/common/explain/modal"
import { useRoute } from 'vue-router';
const {proxy} = getCurrentInstance()
const route = useRoute()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})
const sevenForm = ref()
const defaultform = {
  "action": "",
  "adminStateUp": true,
  "cloudId": route.query.cloudId,
  "description": "",
  "id": 0,
  "listenerId": "",
  "name": "",
  "position": 0,
  "projectId": route.query.projectId,
  "redirectPoolId": "",
  "redirectUrl": "",
}
const sevenform = reactive({
  "action": "",
  "adminStateUp": true,
  "cloudId": route.query.cloudId,
  "description": "",
  "id": 0,
  "listenerId": "",
  "name": "",
  "position": 0,
  "projectId": route.query.projectId,
  "redirectPoolId": "",
  "redirectUrl": "",
})
const poollist = ref([])
const Parentform = reactive({})
const changeAction = (value) => {
    // if(value == 'REDIRECT_TO_POOL'){

    // }
}
const setInitInfo = (record,parentform) => {
    Object.assign(Parentform,parentform)
    if(record){
        let record1 = {...record};
        record1.adminStateUp = Boolean(record.adminStateUp);
        Object.assign(sevenform,record1)
    }
    sevenform.listenerId = parentform.thirdId;
    sevenform.projectId = parentform.thirdProjectId;
}
const cancel=()=>{
    props.info.isShow = false;
    sevenForm.value.resetFields()
    Object.assign(sevenform,defaultform)
}
const save = () => {
    let sevenform1 = {...sevenform}
    if(sevenform.action != 'REDIRECT_TO_URL')
        sevenform1.redirectUrl = undefined;
    else if(sevenform.action != 'REDIRECT_TO_POOL')
        sevenform1.redirectPoolId = undefined;
    sevenform1.adminStateUp = Number(sevenform.adminStateUp)
        proxy.$handleSave(sevenForm.value, saveSevenAPI, updateSevenAPI, props, sevenform1,()=>{emit('getlist',Parentform);cancel()},null)
}
onMounted(() => {})
defineExpose({setInitInfo})
</script>
<style lang='scss' scoped>
:deep(.ant-modal-close-x){width:36px;text-align: left;}
</style>