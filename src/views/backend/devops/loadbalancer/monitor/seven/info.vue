<template>
    <div class='back-page'>
        <a-page-header
          style="background-color:#fff"
          :title="poolform.name"
          @back="()=>{props.info.viewKey = '2'}"
      />
        <a-tabs class="back-content" :animated="false" @change="changePoolTab">
            <a-tab-pane tab=""></a-tab-pane>
            <a-tab-pane key="2" tab="概览">
                <a-descriptions :column="2" style="width:900px">
                    <a-descriptions-item label="ID">{{poolform.thirdId}}</a-descriptions-item>
                    <a-descriptions-item label="名称">{{poolform.name}}</a-descriptions-item>
                    <a-descriptions-item label="描述">{{poolform.description ? poolform.description : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="项目 ID">{{poolform.projectId}}</a-descriptions-item>
                    <a-descriptions-item label="行为">{{poolform.action}}</a-descriptions-item>
                    <a-descriptions-item label="位置">{{poolform.position}}</a-descriptions-item>
                    <a-descriptions-item label="重定向资源池 ID">{{poolform.redirectPoolId ? poolform.redirectPoolId : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="重定向 URL">{{poolform.redirectUrl ? poolform.redirectUrl : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="监控器ID">{{poolform.listenerId}}</a-descriptions-item>
                    <a-descriptions-item label="操作状态">{{poolform.operatingStatusText}}</a-descriptions-item>
                    <a-descriptions-item label="配置状态">{{poolform.provisioningStatusText}}</a-descriptions-item>
                    <a-descriptions-item label="管理状态">{{poolform.adminStateUp ? '开启' : '未开启'}}</a-descriptions-item>
                    <a-descriptions-item label="创建时间">{{poolform.createTime}}</a-descriptions-item>
                    <a-descriptions-item label="修改时间">{{poolform.updateTime}}</a-descriptions-item>
                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="3" tab="七层规则">
                <Rules ref="rulesRef" />
            </a-tab-pane>
        </a-tabs>
    </div>
</template>
<script lang='ts' setup>
import { getPoolInfoAPI } from '@/api/backend/devops/balance';
import emiter from '@/utils/Bus';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import Rules from "./rules/index.vue"
const {proxy} = getCurrentInstance()
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
                isInfo:true
            }
        }
    },
    parentKey:String
})
const rulesRef = ref()
const poolId = ref()
const poolform = reactive({
    id:'',
    action: "REJECT",
    adminStateUp: 1,
    cloudId: 70,
    createTime: "2023-10-17 17:48:11",
    description: "",
    listenerId: "c8a9837e-5ac5-469f-859f-2665cbb62bf9",
    name: "test123",
    operatingStatusText: "",
    position: 1,
    projectId: "e7cae84a81e6474aaae1eb50acec7729",
    provisioningStatusText: "ACTIVE",
    redirectHttpCode: null,
    redirectPoolId: null,
    redirectUrl: null,
    thirdId: "3be79540-59ae-40be-8a81-cd3283449ff5",
    updateTime: "2023-10-17 17:48:11"
})
const changePoolTab = (key) => {
    if(key == '3'){
        proxy.$nextTick(()=>{
            rulesRef.value.getRulesList(poolform)
        })
    }
}
const setInfo = (record) => {
    let record1 = {...record}
    // poolId.value = id;
    // getPoolInfoAPI({id}).then((res)=>{
    //     if(res.code == 0){
        proxy.$nextTick(()=>{
            Object.assign(poolform,record1)
            console.log("seform",poolform,record)
        })
    //     }
    // })
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
@import url('@/styles/tabs.scss');
</style>