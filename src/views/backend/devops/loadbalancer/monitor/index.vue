<template>
    <div class='cloudContent' v-if="!editinfo.isInfo">
        <div class="cloudRight">
            <!-- <div class="buttonPadding">
                <a-form layout="inline" :model="searchform">
                    <a-form-item label="名称" name="name">
                        <a-input v-model:value="searchform.name"></a-input>
                    </a-form-item>
                    <a-form-item>
                        <a-button type="primary" @click="handleSearch">查询</a-button>
                    </a-form-item>
                </a-form>
            </div> -->
            <div class="innerPadding">
                <div class="buttonGroup">
                    <a-button type="primary" @click="handleEdit(true)">新增</a-button>
                    <a-button style="margin-left:10px" @click="$handleDel(selectRowIds,deleteMonitorAPI,()=>{selectRowIds = [];getMonitorList(Parentform)})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </div>
                <a-table :row-selection="rowSelection" :columns="columns" :dataSource="monitorlist" row-key="id" :loading="loading" :pagination="false">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #listenerName={record}>
                        <a href="#" @click="handleView(record)">{{record.listenerName ? record.listenerName : '-'}}</a>
                    </template>
                    <template #operatingStatusText={record}>
                        {{record.operatingStatusText ? record.operatingStatusText : '-'}}
                    </template>
                    <template #provisioningStatusText={record}>
                        {{record.provisioningStatusText ? record.provisioningStatusText : '-'}}
                    </template>
                    <template #adminStateUp={record}>
                        {{record.adminStateUp ? '开启' : '未开启'}}
                    </template>
                    <template #action="{record}">
                        <a-button class="button_E" @click="handleEdit(false,record)">编辑</a-button>
                        <!-- <a-button class="button_E" @click="handleFloatip">关联浮动IP</a-button> -->
                        <a-button class="button_D" @click="$handleDel([record.id],deleteMonitorAPI,()=>getMonitorList(Parentform))">删除</a-button>
                    </template>
                </a-table>
            </div>
        </div>
        <Edit ref="editRef" :info="editinfo" @getlist="getMonitorList" />
        <!-- <Bindfloat ref="bindRef" :info="floatinfo" /> -->
    </div>
    <!-- <Info ref="viewRef" :info="editinfo" v-else/> -->
</template>
<script lang='ts' setup>
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
import Edit from "../edit.vue";
// import Bindfloat from "./bindfloat.vue";
// import Info from "./info.vue";
import { useRoute } from 'vue-router';
import { deleteMonitorAPI, selectMonitorListAPI } from '@/api/backend/devops/balance';
const {proxy} = getCurrentInstance()
const route = useRoute()
const columns = [
    {title:'名称',dataIndex:'listenerName',slots:{customRender:'listenerName'}},
    {title:'协议',dataIndex:'protocol'},
    {title:'端口',dataIndex:'protocolPort'},
    {title:'操作状态',dataIndex:'operatingStatusText',slots:{customRender:'operatingStatusText'}},
    {title:'配置状态',dataIndex:'provisioningStatusText',slots:{customRender:'provisioningStatusText'}},
    {title:'管理状态',dataIndex:'adminStateUp',slots:{customRender:'adminStateUp'}},
    {title:'操作',dataIndex:'action',slots:{customRender:'action'}},
]
let selectRowIds: string[] = ref([]);
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const searchform = reactive({
    cloudId:route.query.cloudId,projectId:route.query.projectId,name:undefined
})
const floatinfo = reactive({
    isShow:false
})
const bindRef = ref()
const editRef = ref()
const viewRef = ref()
const editinfo = reactive({isAdd:true,isInfo:false,isShow:false,addKey:undefined})
const loading = ref(false);
const monitorlist = ref([
    // {id:1,name:'senlin-lb-my_cluster',IP:'***********',zone:'-',state:'衰退',status:'运行中',active:'是'}
])
const Parentform = reactive({})
const handleSearch = () => {}
const handleEdit = (isAdd,record) => {
    editinfo.isAdd = isAdd;
    editinfo.addKey = '2';
    editinfo.isShow = true;
    console.log("reco",record)
    editRef.value.setInitInfo(record,Parentform);
}
const handleView = (record) => {
    // editinfo.isInfo = true;
    proxy.$mitt.emit("setEditinfo",{key:'2',record})
    proxy.$mitt.emit("setPoolparentKey",'2')
}
const handleFloatip = () => {
    floatinfo.isShow = true;
}
const getMonitorList = async (parentform) => {
    loading.value = true;
    Object.assign(Parentform,parentform)
    let res = await selectMonitorListAPI({lbId:parentform.id})
    loading.value = false;
    if(res.code == 0 && res.data){
        monitorlist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({getMonitorList})
</script>
<style lang='scss' scoped>
</style>