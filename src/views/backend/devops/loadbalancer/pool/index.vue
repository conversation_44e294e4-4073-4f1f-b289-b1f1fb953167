<template>
    <div class='cloudContent' v-if="!editinfo.isInfo">
        <div class="cloudRight">
            <!-- <div class="buttonPadding">
                <a-form layout="inline" :model="searchform">
                    <a-form-item label="名称" name="name">
                        <a-input v-model:value="searchform.name"></a-input>
                    </a-form-item>
                    <a-form-item>
                        <a-button type="primary" @click="handleSearch">查询</a-button>
                    </a-form-item>
                </a-form>
            </div> -->
            <div class="innerPadding">
                <div class="buttonGroup">
                    <a-button type="primary" @click="handleEdit(true)">新增</a-button>
                    <a-button style="margin-left:10px" @click="$handleDel(selectRowIds,deletePoolAPI,()=>{selectRowIds = [];getPoolList(Parentform)})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </div>
                <a-table :row-selection="rowSelection" :columns="columns" :dataSource="poollist" row-key="id" :loading="loading" :pagination="false">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #poolName={record}>
                        <a href="#" @click="handleView(record)">{{record.poolName ? record.poolName : '-'}}</a>
                    </template>
                    <template #protocol={record}>
                        {{record.protocol ? record.protocol : '-'}}
                    </template>
                    <template #lbMethod={record}>
                        {{record.lbMethod ? record.lbMethod : '-'}}
                    </template>
                    <template #operatingStatusText={record}>
                        {{record.operatingStatusText ? record.operatingStatusText : '-'}}
                    </template>
                    <template #provisioningStatusText={record}>
                        {{record.provisioningStatusText ? record.provisioningStatusText : '-'}}
                    </template>
                    <template #adminStateUp={record}>
                        {{record.adminStateUp ? '启动' : '未启动'}}
                    </template>
                    <template #action="{record}">
                        <a-button class="button_E" @click="handleEdit(false,record)">编辑</a-button>
                        <!-- <a-button class="button_E" @click="handleFloatip">关联浮动IP</a-button> -->
                        <a-button class="button_D" @click="$handleDel([record.id],deletePoolAPI,()=>getPoolList(Parentform))">删除</a-button>
                    </template>
                </a-table>
            </div>
        </div>
        <Edit ref="editRef" :info="editinfo" @getlist="getPoolList" />
        <!-- <Bindfloat ref="bindRef" :info="floatinfo" /> -->
    </div>
    <!-- <Info ref="viewRef" :info="editinfo" v-else/> -->
</template>
<script lang='ts' setup>
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
import Edit from "../edit.vue";
// import Bindfloat from "./bindfloat.vue";
// import Info from "./info.vue";
import { useRoute } from 'vue-router';
import { deletePoolAPI, selectPoolListAPI } from '@/api/backend/devops/balance';
const {proxy} = getCurrentInstance()
const route = useRoute()
const props = defineProps({
    parentKey:String
})
const columns = [
    {title:'名称',dataIndex:'poolName',slots:{customRender:'poolName'}},
    {title:'协议',dataIndex:'protocol',slots:{customRender:'protocol'}},
    {title:'算法',dataIndex:'lbMethod',slots:{customRender:'lbMethod'}},
    {title:'操作状态',dataIndex:'operatingStatusText',slots:{customRender:'operatingStatusText'}},
    {title:'配置状态',dataIndex:'provisioningStatusText',slots:{customRender:'provisioningStatusText'}},
    {title:'管理状态',dataIndex:'adminStateUp',slots:{customRender:'adminStateUp'}},
    {title:'操作',dataIndex:'action',slots:{customRender:'action'}},
]
let selectRowIds: string[] = ref([]);
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const searchform = reactive({
    cloudId:route.query.cloudId,projectId:route.query.projectId
})
const floatinfo = reactive({
    isShow:false
})
const bindRef = ref()
const editRef = ref()
const viewRef = ref()
const Parentform = reactive({})
const editinfo = reactive({isAdd:true,isInfo:false,isShow:false,addKey:undefined})
const loading = ref(false);
const poollist = ref([
    // {id:1,name:'senlin-lb-my_cluster',IP:'***********',zone:'-',state:'衰退',status:'运行中',active:'是'}
])
const handleSearch = () => {}
const handleEdit = (isAdd,record) => {
    editinfo.isAdd = isAdd;
    editinfo.addKey = '3';
    editinfo.isShow = true;
    editRef.value.setInitInfo(record,Parentform);
}
const handleView = (record) => {
    // editinfo.isInfo = true;
    proxy.$mitt.emit("setEditinfo",{key:'3',record})
    proxy.$mitt.emit("setPoolparentKey",props.parentKey)
}
const handleFloatip = () => {
    floatinfo.isShow = true;
}
const getPoolList = async (parentform) => {
    loading.value = true;
    Object.assign(Parentform,parentform)
    let res = await selectPoolListAPI({lbId:parentform.id})
    loading.value = false;
    if(res.code == 0 && res.data){
        poollist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({getPoolList})
</script>
<style lang='scss' scoped>
</style>