<template>
    <div class='back-page'>
        <a-page-header
          style="background-color:#fff"
          :title="poolform.poolName"
          @back="()=>{info.viewKey = parentKey}"
      />
        <a-tabs class="back-content" :animated="false" @change="changePoolTab">
            <a-tab-pane tab=""></a-tab-pane>
            <a-tab-pane key="2" tab="概览">
                <a-descriptions :column="2" style="width:900px">
                    <a-descriptions-item label="ID">{{poolform.thirdId}}</a-descriptions-item>
                    <a-descriptions-item label="名称">{{poolform.poolName}}</a-descriptions-item>
                    <a-descriptions-item label="描述">{{poolform.description ? poolform.description : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="项目 ID">{{poolform.thirdProjectId}}</a-descriptions-item>
                    <a-descriptions-item label="协议">{{poolform.protocol ? poolform.protocol : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="算法">{{poolform.lbMethod ? poolform.lbMethod : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="会话持久化">{{poolform.sessionPersistenceType ? poolform.sessionPersistenceType : '无'}}</a-descriptions-item>
                    <a-descriptions-item label="健康监控器ID">{{poolform.healthMonitorId}}</a-descriptions-item>
                    <a-descriptions-item label="TLS Enabled">{{poolform.tlsEnabled ? poolform.tlsEnabled : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="TLS Cipher String">{{poolform.tlsCiphers ? poolform.tlsCiphers : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="操作状态">{{poolform.operatingStatusText ? poolform.operatingStatusText : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="配置状态">{{poolform.provisioningStatusText ? poolform.provisioningStatusText : '-'}}</a-descriptions-item>
                    <a-descriptions-item label="管理状态">{{poolform.adminStateUp ? '启动' : '未启动'}}</a-descriptions-item>
                    <a-descriptions-item label="创建时间">{{poolform.createTime}}</a-descriptions-item>
                    <a-descriptions-item label="修改时间">{{poolform.updateTime}}</a-descriptions-item>
                </a-descriptions>
            </a-tab-pane>
            <a-tab-pane key="3" tab="健康监控">
                <Health ref="healthRef" :parentKey="parentKey" />
            </a-tab-pane>
            <a-tab-pane key="4" tab="成员">
                <Member ref="memberRef" :parentKey="parentKey" />
            </a-tab-pane>
        </a-tabs>
    </div>
</template>
<script lang='ts' setup>
import { getPoolInfoAPI } from '@/api/backend/devops/balance';
import emiter from '@/utils/Bus';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import Health from "./health/index.vue"
import Member from "./member/index.vue"
const {proxy} = getCurrentInstance()
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:true,
                isAdd:false,
                isInfo:true
            }
        }
    },
    parentKey:String
})
const healthRef = ref()
const memberRef = ref()
const poolId = ref()
const poolform = reactive({
  "adminStateUp": 0,
  "cloudId": 0,
  "healthMonitorId": "string",
  "id": 0,
  "lbMethod": "string",
  "poolName": "string",
  "thirdId": "string",
  "thirdProjectId": "string",
  "tlsCiphers": "string",
  "tlsEnabled": 0
})
const changePoolTab = (key) => {
    if(key == '3'){
        proxy.$nextTick(()=>{
            healthRef.value.getHealthList(poolform)
        })
    }
    if(key == '4'){
        proxy.$nextTick(()=>{
            memberRef.value.getMemberList(poolform)
        })
    }
}
const setInfo = (id) => {
    poolId.value = id;
    getPoolInfoAPI({id}).then((res)=>{
        if(res.code == 0){
            Object.assign(poolform,res.data)
        }
    })
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
@import url('@/styles/tabs.scss');
</style>