<template>
    <div class=''>
        <a-modal v-model:visible="info.isShow" ok-text="提交" @ok="save" @cancel="cancel" :getContainer="modalBindNode">
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                <span>{{(info.isAdd ? '新增' : '修改')}}</span>
                <a-popover trigger="click" placement="leftTop">
                    <template #content>
                        <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto">{{content.balancer_seven}}</pre>
                    </template>
                    <InfoCircleOutlined  />
                </a-popover>
                </div>
            </template>
            <a-form :model="sevenform" ref="sevenForm" :label-col="{span:5}">
                <a-form-item label="名称" name="name">
                    <a-input v-model:value="sevenform.name"></a-input>
                </a-form-item>
                <a-form-item label="IP地址" name="address" :rules="[{required:true,message:'请输入IP地址'}]">
                    <a-input v-model:value="sevenform.address" :disabled="!info.isAdd"></a-input>
                </a-form-item>
                <a-form-item label="协议端口" name="protocolPort" :rules="[{required:true,message:'请输入协议端口'}]">
                    <a-input-number style="width:100%" v-model:value="sevenform.protocolPort" :min="0" :precision="0"></a-input-number>
                </a-form-item>
                <a-form-item label="权重" name="weight" :rules="[{required:true,message:'请输入权重'}]">
                    <a-input-number style="width:100%" v-model:value="sevenform.weight" :min="0" :precision="0"></a-input-number>
                </a-form-item>
                <a-form-item label="健康监控器地址" name="monitorAddress">
                    <a-input v-model:value="sevenform.monitorAddress"></a-input>
                </a-form-item>
                <a-form-item label="健康监控器端口" name="monitorPort">
                    <a-input-number style="width:100%" v-model:value="sevenform.monitorPort" :min="0" :precision="0"></a-input-number>
                </a-form-item>
                <a-form-item label="管理状态" name="adminStateUp">
                    <a-switch v-model:checked="sevenform.adminStateUp" checked-children="启动" un-checked-children="关闭"/>
                </a-form-item>
                <a-form-item label="备份" name="backup">
                    <a-switch v-model:checked="sevenform.backup" checked-children="是" un-checked-children="否"/>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { saveMemberAPI, saveSevenAPI, updateMemberAPI, updateSevenAPI } from '@/api/backend/devops/balance';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {content} from "@/common/explain/modal"
import { useRoute } from 'vue-router';
const {proxy} = getCurrentInstance()
const route = useRoute()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isShow:false,
                isAdd:true
            }
        }
    }
})
const sevenForm = ref()
const defaultform = {
  "address": "",
  "adminStateUp": true,
  "backup": 0,
  "cloudId": route.query.cloudId,
  "projectId": route.query.projectId,
  "name": "",
  "protocolPort": "",
  "monitorAddress":"",
  "monitorPort":"",
  "subnetId": "",
  "thirdId": "",
  "thirdPoolId": "",
  "thirdProjectId": "",
  "weight": 0
}
const sevenform = reactive({
  "address": "",
  "adminStateUp": true,
  "backup": 0,
  "cloudId": route.query.cloudId,
  "projectId": route.query.projectId,
  "name": "",
  "protocolPort": "",
  "monitorAddress":"",
  "monitorPort":"",
  "subnetId": "",
  "thirdId": "",
  "thirdPoolId": "",
  "thirdProjectId": "",
  "weight": 0
})
const poollist = ref([])
const Parentform = reactive({})
const changeAction = (value) => {
    // if(value == 'REDIRECT_TO_POOL'){

    // }
}
const setInitInfo = (record,parentform) => {
    Object.assign(Parentform,parentform)
    if(record){
        let record1 = {...record};
        record1.adminStateUp = Boolean(record.adminStateUp);
        record1.backup = Boolean(record.backup);
        Object.assign(sevenform,record1)
    }
    sevenform.thirdPoolId = parentform.thirdId;
    sevenform.thirdProjectId = parentform.thirdProjectId;
}
const cancel=()=>{
    props.info.isShow = false;
    sevenForm.value.resetFields()
    Object.assign(sevenform,defaultform)
}
const save = () => {
    let sevenform1 = {...sevenform}
    sevenform1.adminStateUp = Number(sevenform.adminStateUp)
    sevenform1.backup = Number(sevenform.backup)
        proxy.$handleSave(sevenForm.value, saveMemberAPI, updateMemberAPI, props, sevenform1,()=>{emit('getlist',Parentform);cancel()},null)
}
onMounted(() => {})
defineExpose({setInitInfo})
</script>
<style lang='scss' scoped>
:deep(.ant-modal-close-x){width:36px;text-align: left;}
</style>