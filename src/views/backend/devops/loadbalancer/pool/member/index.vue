<template>
    <div class='cloudContent' v-if="!editinfo.isInfo">
        <div class="cloudRight">
            <!-- <div class="buttonPadding">
                <a-form layout="inline" :model="searchform">
                    <a-form-item label="名称" name="name">
                        <a-input v-model:value="searchform.name"></a-input>
                    </a-form-item>
                    <a-form-item>
                        <a-button type="primary" @click="handleSearch">查询</a-button>
                    </a-form-item>
                </a-form>
            </div> -->
            <div class="innerPadding">
                <div class="buttonGroup">
                    <a-button type="primary" @click="handleEdit(true)">新增</a-button>
                    <a-button style="margin-left:10px" @click="$handleDel(selectRowIds,deleteMemberAPI,()=>{selectRowIds = [];getMemberList(Parentform)})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </div>
                <a-table :row-selection="rowSelection" :columns="columns" rowKey="id" :dataSource="memberlist" :loading="loading" :pagination="false">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #name={record}>
                        <!-- <a href="#" v-if="parentKey == '1'">{{record.name ? record.name : '-'}}</a> -->
                        <span>{{record.name ? record.name : '-'}}</span>
                    </template>
                    <template #backup={record}>
                        {{record.backup ? '是' : '否'}}
                    </template>
                    <template #operatingStatusText={record}>
                        {{record.operatingStatusText ? record.operatingStatusText : '-'}}
                    </template>
                    <template #provisioningStatusText={record}>
                        {{record.provisioningStatusText ? record.provisioningStatusText : '-'}}
                    </template>
                    <template #adminStateUp={record}>
                        {{record.adminStateUp ? '启动' : '未启动'}}
                    </template>
                    <template #action="{record}">
                        <a-button class="button_E" @click="handleEdit(false,record)">编辑</a-button>
                        <!-- <a-button class="button_E" @click="handleFloatip">关联浮动IP</a-button> -->
                        <a-button class="button_D" @click="$handleDel([record.id],deleteMemberAPI,()=>getMemberList(Parentform))">删除</a-button>
                    </template>
                </a-table>
            </div>
        </div>
        <Edit ref="editRef" :info="editinfo" @getlist="getMemberList" />
        <MemberEdit ref="membereditRef" :info="editinfo1" @getlist="getMemberList" />
        <!-- <Bindfloat ref="bindRef" :info="floatinfo" /> -->
    </div>
    <!-- <Info ref="viewRef" :info="editinfo" v-else/> -->
</template>
<script lang='ts' setup>
import { computed, onMounted, reactive, ref } from 'vue';
import Edit from "../../edit.vue";
import MemberEdit from "./edit.vue";
// import Bindfloat from "./bindfloat.vue";
// import Info from "./info.vue";
import { useRoute } from 'vue-router';
import { selectMemberListAPI,deleteMemberAPI } from '@/api/backend/devops/balance';
const props = defineProps({
    parentKey:String
})
const route = useRoute()
const columns = [
    {title:'名称',dataIndex:'name',slots:{customRender:'name'}},
    {title:'IP地址',dataIndex:'address'},
    {title:'端口',dataIndex:'protocolPort'},
    {title:'权重',dataIndex:'weight'},
    {title:'备份',dataIndex:'backup',slots:{customRender:'backup'}},
    {title:'操作状态',dataIndex:'operatingStatusText',slots:{customRender:'operatingStatusText'}},
    {title:'配置状态',dataIndex:'provisioningStatusText',slots:{customRender:'provisioningStatusText'}},
    {title:'管理状态',dataIndex:'adminStateUp',slots:{customRender:'adminStateUp'}},
    {title:'操作',dataIndex:'action',slots:{customRender:'action'}},
]
const searchform = reactive({
    cloudId:route.query.cloudId,projectId:route.query.projectId,name:undefined
})
const floatinfo = reactive({
    isShow:false
})
const bindRef = ref()
const editRef = ref()
const viewRef = ref()
const membereditRef = ref()
const editinfo = reactive({isAdd:true,isInfo:false,isShow:false,addKey:undefined})
const editinfo1 = reactive({isAdd:true,isInfo:false,isShow:false,addKey:undefined})
const loading = ref(false);
const memberlist = ref([
    // {name:'senlin-lb-my_cluster',IP:'***********',zone:'-',state:'衰退',status:'运行中',active:'是'}
])
const Parentform = reactive({})
let selectRowIds: string[] = ref([]);
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const handleSearch = () => {}
const handleEdit = (isAdd,record) => {
    editinfo.isAdd = isAdd;
    editinfo1.isAdd = isAdd;
    if(isAdd){
        editinfo.addKey = '4';
        editinfo.isShow = true;
        editRef.value.setInitInfo();
    }else{
        editinfo1.isShow = true;
        membereditRef.value.setInitInfo(record,Parentform);
    }
}
const handleView = () => {
    editinfo.isInfo = true;
}
const handleFloatip = () => {
    floatinfo.isShow = true;
}
const getMemberList = async (parentform) => {
    loading.value = true;
    Object.assign(Parentform,parentform)
    let res = await selectMemberListAPI({poolId:parentform.id})
    loading.value = false;
    if(res.code == 0 && res.data){
        memberlist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({getMemberList})
</script>
<style lang='scss' scoped>
</style>