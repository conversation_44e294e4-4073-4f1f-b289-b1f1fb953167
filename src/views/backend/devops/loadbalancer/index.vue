<template>
    <div class='cloudContent' v-if="!editinfo.isInfo">
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                    <a-form-item label="均衡器名称" name="name">
                        <a-input v-model:value="searchform.name"></a-input>
                    </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class="innerPadding">
                <div class="buttonGroup">
                    <a-button type="primary" @click="handleEdit(true)">新增</a-button>
                    <a-button style="margin-left:10px" @click="$handleDel(selectRowIds,deleteBalanceAPI,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </div>
                <a-table :row-selection="rowSelection" :columns="columns" :dataSource="loadlist" row-key="id" :pagination="pagination" @change="changeTable" :scroll="{x:true}">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #name={record}>
                        <a href="#" @click="handleView('1',record)">{{record.name ? record.name : '-'}}</a>
                    </template>
                    <template #availabilityZone={record}>
                        {{record.availabilityZone ? record.availabilityZone : '-'}}
                    </template>
                    <template #operatingStatusText={record}>
                        {{record.operatingStatusText ? record.operatingStatusText : '-'}}
                    </template>
                    <template #provisioningStatusText={record}>
                        {{record.provisioningStatusText ? record.provisioningStatusText : '-'}}
                    </template>
                    <template #adminStateUp={record}>
                        {{record.adminStateUp ? '开启' : '未开启'}}
                    </template>
                    <template #action="{record}">
                        <a-button class="button_E" @click="handleEdit(false,record)">编辑</a-button>
                        <a-button class="button_E" @click="handleFloatip">关联浮动IP</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteBalanceAPI,getList)">删除</a-button>
                    </template>
                </a-table>
            </div>
        </div>
        <Edit ref="editRef" :info="editinfo" @getlist="getList" />
        <Bindfloat ref="bindRef" :info="floatinfo" />
    </div>
    <Info ref="viewRef" :info="editinfo" v-show="editinfo.isInfo && editinfo.viewKey == '1'"/>
    <PoolInfo ref="poolviewRef" :info="editinfo" :parentKey="editinfo.poolparentKey" v-if="editinfo.isInfo && editinfo.viewKey == '3'"/>
    <MonitorInfo ref="monitorviewRef" :info="editinfo" v-show="editinfo.isInfo && editinfo.viewKey == '2'"/>
    <SevenInfo ref="sevenviewRef" :info="editinfo" v-if="editinfo.isInfo && editinfo.viewKey == '6'"/>
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import Edit from "./edit.vue";
import Bindfloat from "./bindfloat.vue";
import Info from "./info.vue";
import PoolInfo from "./pool/info.vue";
import MonitorInfo from "./monitor/info.vue";
import SevenInfo from "./monitor/seven/info.vue";
import { useRoute } from 'vue-router';
import {getBalanceListAPI,deleteBalanceAPI} from "@/api/backend/devops/balance"
import { buttonExpand, handleWidth } from "@/utils/moreform";
const route = useRoute()
const {proxy} = getCurrentInstance()
const columns = [
    {title:'序号',dataIndex:'index',slots:{customRender:'index'}},
    {title:'名称',dataIndex:'name',slots:{customRender:'name'}},
    {title:'IP地址',dataIndex:'vipAddress'},
    {title:'可用域',dataIndex:'availabilityZone',slots:{customRender:'availabilityZone'}},
    {title:'操作状态',dataIndex:'operatingStatusText',slots:{customRender:'operatingStatusText'}},
    {title:'配置状态',dataIndex:'provisioningStatusText',slots:{customRender:'provisioningStatusText'}},
    {title:'管理状态',dataIndex:'adminStateUp',slots:{customRender:'adminStateUp'}},
    {title:'操作',dataIndex:'action',slots:{customRender:'action'}},
]
const searchform = reactive({
    pageIndex:1,pageSize:10,cloudId:route.query.cloudId,projectId:route.query.projectId,name:undefined
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
let selectRowIds: string[] = ref([]);
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const floatinfo = reactive({
    isShow:false
})
const bindRef = ref()
const editRef = ref()
const viewRef = ref()
const poolviewRef = ref()
const monitorviewRef = ref()
const sevenviewRef = ref()
const editinfo = reactive({isAdd:true,isInfo:false,isShow:false,addKey:undefined,viewKey:undefined,poolparentKey:undefined})
const loading = ref()
const loadlist = ref([
    // {id:1,name:'senlin-lb-my_cluster',IP:'***********',zone:'-',state:'衰退',status:'运行中',active:'是'}
])
const handleSearch = () => {
    searchform.pageIndex = 1;
    getList();
}
const handleAllReset = () => {
    searchform.name = undefined;
    searchform.pageIndex = 1;
    // getList();
}
const handleEdit = (isAdd,record) => {
    editinfo.isAdd = isAdd;
    editinfo.addKey = '1';
    editinfo.isShow = true;
    editRef.value.setInitInfo(record);
}
const handleView = (key,record) => {
    editinfo.isInfo = true;
    editinfo.viewKey = key;
    if(key == '1')
    viewRef.value.setInfo(record.id);
    if(key == '2')
    proxy.$nextTick(()=>{
        monitorviewRef.value.setInfo(record.id)
    })
    if(key == '3')
    proxy.$nextTick(()=>{
        poolviewRef.value.setInfo(record.id)
    })
    if(key == '6'){
        console.log("key,record",key,record)
        proxy.$nextTick(()=>{
            sevenviewRef.value.setInfo(record)
        })
    }
}
const handleFloatip = () => {
    floatinfo.isShow = true;
    nextTick(()=>{
        bindRef.value.setInfo()
    })
}
const getList = async () => {
    console.log("search")
    loadlist.value = await proxy.$getList(loading, getBalanceListAPI, searchform, pagination, getList)
}
onMounted(() => {
    getList();
    proxy.$mitt.on("setEditinfo",({key,record})=>handleView(key,record))
    proxy.$mitt.on("setPoolparentKey",(key)=>editinfo.poolparentKey = key)
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
</style>