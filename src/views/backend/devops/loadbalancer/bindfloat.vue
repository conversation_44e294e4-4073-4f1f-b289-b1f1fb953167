<template>
    <div class=''>
        <a-modal title="关联浮动 IP 地址" v-model:visible="info.isShow" ok-text="提交" :getContainer="modalBindNode">
            <a-form>
                <a-form-item label="浮动IP地址">
                    <a-select :options="floatlist">
                    </a-select>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getFloatipCombine, getNetworkCombine } from '@/common/responselist';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute()
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {isShow}
        }
    }
})
const floatlist = ref([])
const setInfo = async () => {
    floatlist.value = await getFloatipCombine({cloudId:route.query.cloudId,projectId:route.query.projectId,active:1})
    // floatlist.value = await getNetworkCombine({cloudId:route.query.cloudId,projectId:route.query.projectId,networkType:'FLAT'})
    console.log("floatlist.value",floatlist.value)
}
onMounted(() => {})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
</style>