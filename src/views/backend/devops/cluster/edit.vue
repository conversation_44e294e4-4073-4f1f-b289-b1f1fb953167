<template>
    <div class=''>
        <a-modal :title="info.isAdd ? '添加集群' : '修改集群'" v-model:visible="info.isShow" ok-text="提交" cancel-text="取消" @cancel="cancel" @ok="save" :maskClosable="false" centered :getContainer="modalBindNode">
            <a-form :model="clusterform" ref="clusterForm" :rules="rules" :label-col="{span:5}">
                <a-form-item label="集群名称" name="clusterName">
                    <a-input v-model:value="clusterform.clusterName" placeholder="请输入集群名称" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="集群配置" name="profileId">
                    <a-select v-model:value="clusterform.profileId" placeholder="请选择集群配置" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option v-for="(item,index) in profilelist" :key="index" :value="item.id">{{item.profileName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="最小主机数" name="minSize">
                    <a-input-number v-model:value="clusterform.minSize" :min="0" :step="1" :precision="0" style="width:100%" placeholder="请输入最小主机数"></a-input-number>
                </a-form-item>
                <a-form-item name="maxSize">
                    <template #label>
                        <span>最大主机数&nbsp;</span>
                        <a-tooltip>
                            <template #title>-1时无上限</template>
                            <QuestionCircleFilled style="font-size:12px" />
                        </a-tooltip>
                    </template>
                    <a-input-number v-model:value="clusterform.maxSize" :min="-1" :step="1" :precision="0" style="width:100%" placeholder="请输入最大主机数"></a-input-number>
                </a-form-item>
                <a-form-item label="所需主机数" name="desiredCapacity">
                    <a-input-number v-model:value="clusterform.desiredCapacity" :min="0" :step="1" :precision="0" style="width:100%" placeholder="请输入所需主机数"></a-input-number>
                </a-form-item>
                <a-form-item label="超时" name="timeout">
                    <a-input-number v-model:value="clusterform.timeout" :min="0" :step="1" :precision="0" style="width:100%" placeholder="请输入超时时长"></a-input-number>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { saveCluster, selectClusterProfileList, updateCluster,  } from '@/api/backend/cluster';
import router from '@/router';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info: {
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow: false
            }
        }
    }
})
const clusterForm = ref();
const profilelist = ref([])
const defaultform = {
    "cloudId": router.currentRoute.value.query.cloudId,
    "clusterName": "",
    "desiredCapacity": 0,
    "maxSize": -1,
    "minSize": 0,
    "profileId": undefined,
    "projectId": router.currentRoute.value.query.projectId,
    "timeout": 0,
}
const clusterform = reactive({
    "cloudId": router.currentRoute.value.query.cloudId,
    "clusterName": "",
    "desiredCapacity": 0,
    "maxSize": -1,
    "minSize": 0,
    "profileId": undefined,
    "projectId": router.currentRoute.value.query.projectId,
    "timeout": 0,
})
const rules = {
    clusterName:[{required:true,message:'请输入集群名称',trigger:'change'}],
    desiredCapacity:[{required:true,type:'number',message:'请输入0、正整数',trigger:'change'}],
    minSize:[{required:true,type:'number',message:'请输入0、正整数',trigger:'change'}],
    maxSize:[{required:true,type:'number',message:'请输入-1、0、正整数',trigger:'change'}],
    timeout:[{required:true,type:'number',message:'请输入0、正整数',trigger:'change'}],
    profileId:[{required:true,type:'number',message:'请选择集群配置',trigger:'change'}],
}
const setInfo = (record) => {
    selectClusterProfilelist()
    Object.assign(clusterform,record);
}
const cancel = () => {
    props.info.isShow = false;
    setTimeout(()=>{
        Object.assign(clusterform,defaultform)
    })
}
const save = () => {
    let clusterform1 = {...clusterform};
    proxy.$handleSave(clusterForm.value, saveCluster, updateCluster, props, clusterform1, ()=>{emit('getlist');cancel()},null)
}
const selectClusterProfilelist = async () => {
    let res = await selectClusterProfileList({cloudId:router.currentRoute.value.query.cloudId,projectId:router.currentRoute.value.query.projectId});
    if(res.code == 0){
        profilelist.value = res.data;
    }
}
onMounted(() => {})
defineExpose({setInfo,selectClusterProfilelist})
</script>
<style lang='scss' scoped>
</style>