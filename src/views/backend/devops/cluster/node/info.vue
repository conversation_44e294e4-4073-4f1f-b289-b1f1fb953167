<template>
    <div class=''>
        <a-modal :title="info.isInfo ? '查看集群节点' : (info.isAdd ? '添加集群节点' : '修改集群节点')" v-model:visible="info.isShow" @cancel="cancel" :maskClosable="info.isInfo" centered :getContainer="modalBindNode">
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="save">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="nodeform" ref="nodeForm" :rules="rules" :label-col="{span:5}">
                <a-form-item label="集群配置" name="profileId">
                    {{nodeform.profileName}}
                </a-form-item>
                <a-form-item label="集群名称" name="clusterId">
                    {{nodeform.clusterName}}
                </a-form-item>
                <a-form-item label="集群节点名称" name="name">
                    <a-input v-model:value="nodeform.name" placeholder="请输入集群节点名称" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { saveClusterNode, selectClusterProfileList, updateClusterNode } from '@/api/backend/cluster';
import router from '@/router';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info: {
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow: false,
                isInfo: false
            }
        }
    }
})
const nodeForm = ref();
const profilelist = ref([])
const defaultform = {
    "cloudId": router.currentRoute.value.query.cloudId,
    "clusterId": "",
    "clusterName": "",
    "name": "",
    "profileId": "",
    "profileName": "",
    "projectId": router.currentRoute.value.query.projectId,
}
const nodeform = reactive({
    "cloudId": router.currentRoute.value.query.cloudId,
    "clusterId": "",
    "clusterName": "",
    "name": "",
    "profileId": "",
    "profileName": "",
    "projectId": router.currentRoute.value.query.projectId,
})
const rules = {
    name:[{required:true,message:'请输入集群节点名称',trigger:'change'}],
}
const cancel = () => {
    props.info.isShow = false;
    Object.assign(nodeform,defaultform)
}
const save = () => {
    let nodeform1 = {...nodeform};
    proxy.$handleSave(nodeForm.value, saveClusterNode, updateClusterNode, props, nodeform1, ()=>{emit('getlist',nodeform.clusterId);cancel()},null)
}
const setInfo = (record) => {
    Object.assign(nodeform, record)
}

onMounted(() => {})
defineExpose({nodeform, setInfo})
</script>
<style lang='scss' scoped>
</style>