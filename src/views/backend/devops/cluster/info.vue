<template>
    <div class='back-page'>
        <a-page-header
          style="background-color:#fff"
          :title="clusterInfo?.clusterName"
          @back="()=>{info.isInfo = info.isShow = false}"
      />
        <a-tabs class="back-content" :animated="false" @change="changeTab">
            <a-tab-pane tab=""></a-tab-pane>
            <a-tab-pane key="2" tab="概览">
                <!-- <div class="paint">
                    <div class="paint-top">
                        <a-card>
                            <a-card-meta :title="clusterInfo.clusterConfig" description="期望虚机数(台)">
                            </a-card-meta>
                            <template class="ant-card-actions" #actions>
                                <span>最小主机数 {{clusterInfo.minSize}}</span>
                                <span>最大主机数 {{clusterInfo.maxSize}}</span>
                            </template>
                        </a-card>
                    </div>
                    <div class="paint-down"><DownOutlined /></div>
                    <div class="paint-bottom">
                        <div class="bottom-one">
                            <a-card>
                                <a-card-meta title="0" description="正在加入">
                                </a-card-meta>
                            </a-card>
                        </div>
                        <div class="bottom-two"><RightOutlined /></div>
                        <div class="bottom-three">
                            <a-card>
                                <a-card-meta title="2" description="当前虚机数(台)">
                                </a-card-meta>
                            </a-card>
                        </div>
                        <div class="bottom-four"><RightOutlined /></div>
                        <div class="bottom-five">
                            <a-card>
                                <a-card-meta title="0" description="正在移除">
                                </a-card-meta>
                            </a-card>
                        </div>
                    </div>
                </div>
                <a-divider></a-divider> -->
                <div class="cluster-info">
                    <a-descriptions :column="1" :label-style="{justifyContent: 'end',width: '120px'}">
                            <a-descriptions-item label="集群名称">{{clusterInfo?.clusterName}}</a-descriptions-item>
                            <a-descriptions-item label="所需主机数">{{clusterInfo.desiredCapacity}}</a-descriptions-item>
                            <a-descriptions-item label="最小主机数">{{clusterInfo.minSize}}</a-descriptions-item>
                            <a-descriptions-item label="最大主机数">{{clusterInfo.maxSize }}</a-descriptions-item>
                            <a-descriptions-item label="超时时长">{{clusterInfo.timeout}}</a-descriptions-item>
                            <a-descriptions-item label="集群配置">{{clusterInfo.profileName}}</a-descriptions-item>
                            <a-descriptions-item label="状态">{{clusterInfo.status}}</a-descriptions-item>
                            <a-descriptions-item label="状态说明">{{clusterInfo.statusReason}}</a-descriptions-item>
                            <a-descriptions-item label="创建时间">{{clusterInfo.createTime}}</a-descriptions-item>
                            <!-- <a-descriptions-item label="创建人">{{clusterInfo.createUserName}}</a-descriptions-item> -->
                            <a-descriptions-item label="修改时间">{{clusterInfo.updateTime}}</a-descriptions-item>
                            <!-- <a-descriptions-item label="修改人">{{clusterInfo.updateUserName}}</a-descriptions-item> -->
                        </a-descriptions>
                </div>
            </a-tab-pane>
            <a-tab-pane key="3" tab="集群节点">
                <a-button type="primary" class="btnMargin" @click="handleNodeAdd">新增节点</a-button>
                <a-button @click="$handleDel(selectRowIds,deleteClusterNode,()=>{selectRowIds = [];selectClusteNodelist(clusterInfo.id)})" :disabled="selectRowIds.length <= 0">
                    批量删除
                </a-button>
                <br/><br/>
                <a-table :row-selection="rowSelection" :columns="columns1" :data-source="clusnodelist" rowKey="id" :loading="nodeloading" :scroll="{ x: true }">
                    <template #index={record,index}>
                        {{index+1}}
                    </template>
                    <template #serverName={record}>
                        <a @click="toServer(record.thirdServerId)">{{record.serverName}}</a>
                        <!-- <router-link :to="record.thirdServerId ? '/admin/server/info?thirdServerId='+record.thirdServerId : '#'">record.serverName</router-link> -->
                    </template>
                    <template #action={record}>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteClusterNode,()=>{selectClusteNodelist(clusterInfo.id)})">删除</a-button>
                    </template>
                </a-table>
            </a-tab-pane>
            <a-tab-pane key="4" tab="伸缩规则">
                <a-button type="primary" class="btnMargin" @click="handleReceiverAdd">新增规则 </a-button>
                <a-button @click="$handleDel(selectRowIds1,deleteClusterReceiver,()=>{selectRowIds1 = [];selectClusterReceiverlist(clusterInfo.id)})" :disabled="selectRowIds1.length <= 0">
                    批量删除
                </a-button>
                <br/><br/>
                <a-table :row-selection="rowSelection1" :columns="columns2" :data-source="clusrulelist" rowKey="id" :loading="ruleloading" :scroll="{ x: true }">
                    <template #index={record,index}>
                        {{index+1}}
                    </template>
                    <template #action1={record}>
                        <a-button class="button_V" @click="handleReceiverInfo(record)">查看</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteClusterReceiver,()=>{selectClusterReceiverlist(clusterInfo.id)})">删除</a-button>
                    </template>
                </a-table>
            </a-tab-pane>
            <!-- <a-tab-pane key="5" tab="活动历史">
                <a-timeline mode="alternate" :reverse="true">
                    <template #pending>
                        <a-descriptions title="正在进行的活动" :column="1" :label-style="{justifyContent: 'end',width: '82px'}" size="small">
                            <a-descriptions-item label="活动类型">Zhou Maomao</a-descriptions-item>
                            <a-descriptions-item label="描述">-</a-descriptions-item>
                            <a-descriptions-item label="输出">活动正在进行中</a-descriptions-item>
                            <a-descriptions-item label="开始时间">2022-12-13</a-descriptions-item>
                        </a-descriptions>
                    </template>
                    <a-timeline-item>
                    <template #dot><ClockCircleOutlined style="font-size: 16px" /></template>
                    <a-descriptions title="上一次的活动" :column="1" :label-style="{justifyContent: 'end',width: '82px'}" size="small">
                        <a-descriptions-item label="触发类型">集群扩容</a-descriptions-item>
                        <a-descriptions-item label="描述">增加1台虚机</a-descriptions-item>
                        <a-descriptions-item label="状态详情">行为已完成</a-descriptions-item>
                        <a-descriptions-item label="开始时间">2022-12-12</a-descriptions-item>
                    </a-descriptions>
                    </a-timeline-item>
                </a-timeline>
            </a-tab-pane> -->
        </a-tabs>
        <Node ref="nodeDialog" :info="nodeinfo" @getlist="selectClusteNodelist" v-if="nodeinfo.isShow" />
        <Receiver ref="receiverDialog" :info="receiverinfo" @getlist="selectClusterReceiverlist" v-if="receiverinfo.isShow" />
    </div>
</template>
<script lang='ts' setup>
import Node from "./node/info.vue";
import Receiver from "./receiver/info.vue";
import { selectClusteNodeList, selectClusterReceiverList, deleteClusterNode, deleteClusterReceiver } from '@/api/backend/cluster';
import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { message } from "ant-design-vue";
import router from "@/router";
import emiter from "@/utils/Bus";
const { proxy } = getCurrentInstance();
const props = defineProps({
    info: {
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow: false,
                isInfo:true
            }
        }
    }
})
let selectRowIds: string[] = ref([]);
let selectRowIds1: string[] = ref([]);
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const rowSelection1 = computed(() => {
  return {
    selectedRowKeys: selectRowIds1.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds1.value = [...selectedRowKeys];
    }
  };
});
const nodeinfo = reactive({
    isAdd: true,
    isShow: false,
    isInfo:false
})

const receiverinfo = reactive({
    isAdd: true,
    isShow: false,
    isInfo:false
})
const nodeDialog = ref();
const receiverDialog = ref();
const nodeloading = ref(false);
const ruleloading = ref(false);
const clusnodelist = ref([]);
const clusrulelist = ref([]);
const clusterInfo = reactive({
    clusterName:'',clusterConfig:'',minSize:'',maxSize:''
});

const columns1 = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '节点名称', dataIndex: 'name', key: 'id', align:'center'},
    {title: '虚机', dataIndex: 'serverName', slots: { customRender: 'serverName' }, key: 'id', align:'center'},
    {title: '集群配置', dataIndex: 'profileName', key: 'id', align:'center'},
    {title: '状态', dataIndex: 'status', key: 'id', align:'center'},
    {title: '状态说明', dataIndex: 'statusReason', key: 'id', align:'center',ellipsis:true},
    {title: '创建时间', dataIndex: 'createTime', key: 'id', align:'center'},
    {title: '修改时间', dataIndex: 'updateTime', key: 'id', align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' }, align:'center', width:200},
];
const columns2 = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '规则名称', dataIndex: 'name', key: 'id', align:'center'},
    {title: '伸缩类型', dataIndex: 'receiverType', key: 'id', align:'center'},
    {title: '伸缩操作', dataIndex: 'action', key: 'id', align:'center'},
    {title: 'alarmUrl', dataIndex: 'alarmUrl', key: 'id'},
    {title: '创建时间', dataIndex: 'createTime', key: 'id', align:'center'},
    {title: '修改时间', dataIndex: 'updateTime', key: 'id', align:'center'},
    {title: '操作', dataIndex: 'action1', key: 'id', slots: { customRender: 'action1' }, align:'center', width:200},
];
const toServer = (thirdServerId) => {
    if(!thirdServerId){
        message.warning('不存在虚机，无法跳转');
    }else{
        router.push({path:'/admin/server/info',query:{thirdServerId}})
    }
}
const changeTab = (e) => {
    if(e == 3)
    selectClusteNodelist(clusterInfo.id);
    if(e == 4)
    selectClusterReceiverlist(clusterInfo.id);
}
const setInfo = (record) => {
    Object.assign(clusterInfo,record);
    
}
const handleNodeAdd = () => {
    nodeinfo.isAdd = nodeinfo.isShow = true;
    nodeinfo.isInfo = false;
    proxy.$nextTick(()=>{
        nodeDialog.value.nodeform.clusterName = clusterInfo.clusterName;
        nodeDialog.value.nodeform.clusterId = clusterInfo.id;
        nodeDialog.value.nodeform.profileId = clusterInfo.profileId;
        nodeDialog.value.nodeform.profileName = clusterInfo.profileName;
    })
}
const handleNodeInfo = (record) => {
    nodeinfo.isInfo = nodeinfo.isShow = true;
    proxy.$nextTick(()=>{
        nodeDialog.value.setInfo(record)
    })
}

const handleReceiverAdd = () => {
    receiverinfo.isAdd = receiverinfo.isShow = true;
    receiverinfo.isInfo = false;
    proxy.$nextTick(()=>{
        receiverDialog.value.receiverform.clusterName = clusterInfo.clusterName;
        receiverDialog.value.receiverform.clusterId = clusterInfo.id;
    })
}
const handleReceiverInfo = (record) => {
    receiverinfo.isInfo = receiverinfo.isShow = true;
    proxy.$nextTick(()=>{
        receiverDialog.value.setInfo(record)
    })
}
const selectClusteNodelist = async (clusterId) => {
    nodeloading.value = true;
    let res = await selectClusteNodeList({clusterId});
    nodeloading.value = false;
    if(res.code == 0){
        clusnodelist.value = res.data;
    }
}
const selectClusterReceiverlist = async (clusterId) => {
    ruleloading.value = true;
    let res = await selectClusterReceiverList({clusterId});
    ruleloading.value = false;
    if(res.code == 0){
        clusrulelist.value = res.data;
    }
}
onMounted(() => {});
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
.paint{min-width: 1120px;padding: 20px;background-color: #f0f0f0;}
.ant-card{width: 350px;}
:deep(.ant-card-body){text-align: center;}
.paint-top{.ant-card{margin: 0 auto;}}
.paint-bottom{display: flex;justify-content: space-between;align-items: center;}
.paint-down{padding: 10px 0;.anticon{width: 100%;margin: 0 auto;}}
.ant-timeline{margin-top: 20px;.ant-descriptions{background-color: #f0f0f0;padding: 10px 25px;}}

.cluster-info{display: flex;justify-content: space-between;}
.back-content{padding: 0;height: 100%;overflow-y: initial;}
:deep(.ant-tabs .ant-tabs-top-content){
      height: calc(100vh - 230px);
    overflow-y: scroll;
    padding: 0 16px 16px;
}
.ant-descriptions{padding: 20px;margin: 0 16px 16px;border: 1px solid #f0f2f5;transition:all .3s;}
.ant-descriptions:hover {
    border-color: #00000017;
    box-shadow: 0 2px 8px #00000017;
}
:deep(.ant-table th) { white-space: nowrap; }
:deep(.ant-table td) { white-space: nowrap; }
</style>