<template>
    <div class='cloudContent' v-if="!info.isInfo">
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                    <a-form-item label="集群名称">
                        <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.clusterName" allowClear />
                    </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class="innerPadding">
                <a-button type="primary" class="btnMargin" @click="handleAdd">新增</a-button>
                <a-button @click="$handleDel(selectRowIds,deleteCluster,()=>{selectRowIds = [];getList()})" :disabled="selectRowIds.length <= 0">
                    批量删除
                </a-button>
            <br/><br/>
                <a-table :row-selection="rowSelection" :columns="columns3" :data-source="cluslavorlist" row-key="id" :pagination="pagination" @change="changeTable">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #clusterName={record}>
                        <a href="" @click.prevent="handleInfo(record)">{{record.clusterName}}</a>
                    </template>
                    <template #action={record}>
                        <a-button class="button_V" @click="handleInfo(record)">查看</a-button>
                        <!-- <a-button class="button_E" @click="handleEdit(record)">编辑</a-button> -->
                        <a-button class="button_D" @click="$handleDel([record.id],deleteCluster,getList)">删除</a-button>
                    </template>
                </a-table>
            </div>
        </div>
        <Edit ref="clusterEditRef" :info="info" @getlist="getList" />
        
    </div>
    <Info ref="clusterInfoRef" :info="info" v-else />
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Edit from "./edit.vue";
import Info from "./info.vue";
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { deleteCluster, getClusterList } from "@/api/backend/cluster";
import router from "@/router";
import { useRoute } from "vue-router";
import { handleWidth } from "@/utils/moreform";
const route = useRoute();
const { proxy } = getCurrentInstance();
const clusterEditRef = ref();
const clusterInfoRef = ref();
const cluslavorlist = ref([]);
const loading = ref(false);
let selectRowIds: string[] = ref([]);
const info = reactive({
    isAdd: true,
    isShow: false,
    isInfo: false
})
const searchform = reactive({
    clusterName:'',
    pageIndex:1,
    pageSize:10,
    "cloudId": router.currentRoute.value.query.cloudId,
    "projectId": router.currentRoute.value.query.projectId,
})
const columns3 = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '名称', dataIndex: 'clusterName', slots: { customRender: 'clusterName' }, key: 'id', align:'center'},
    {title: '集群配置', dataIndex: 'profileName', key: 'id', align:'center'},
    {title: '最小主机数', dataIndex: 'minSize', key: 'id', align:'center'},
    {title: '最大主机数', dataIndex: 'maxSize', key: 'id', align:'center'},
    {title: '状态', dataIndex: 'statusText', key: 'id', align:'center'},
    {title: '状态解释', dataIndex: 'statusReason', key: 'id', align:'center', ellipsis:true},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' }, width:130},
];
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、排序、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const handleSearch = () => {
    searchform.pageIndex = 1;
    getList();
}
const handleAllReset = () => {
    searchform.clusterName = '';
    searchform.pageIndex = 1;
    // getList();
}
const handleAdd = () => {
    info.isShow = info.isAdd = true;
    info.isInfo = false;
    proxy.$nextTick(()=>{
        clusterEditRef.value.selectClusterProfilelist()
    })
}

const handleEdit = (record) => {
    info.isShow = true;
    info.isAdd = false;
    info.isInfo = false;
    proxy.$nextTick(()=>{
        clusterEditRef.value.setInfo(record)
    })
}
const handleInfo = (record) => {
    info.isInfo = true;
    proxy.$nextTick(()=>{
        clusterInfoRef.value.setInfo(record)
    })
}

// const handleSearch = (selectedKeys, confirm, dataIndex) => {
//     confirm();
//     searchText.value = selectedKeys[0];
//     searchedColumn.value = dataIndex;
// };

// const handleReset = clearFilters => {
//     clearFilters();
//     searchText.value = '';
// };
const getList = async () => {
    cluslavorlist.value = await proxy.$getList(loading, getClusterList, searchform, pagination, getList)
}
onMounted(() => {getList();
    nextTick(()=>{
        handleWidth()
    })})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
:deep(.ant-table th) { white-space: nowrap; }
:deep(.ant-table td) { white-space: nowrap; }
.buttonPadding,.innerPadding{min-width: 1250px;}
</style>