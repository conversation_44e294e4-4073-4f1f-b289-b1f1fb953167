<template>
    <div class=''>
        <a-modal :title="info.isInfo ? '查看伸缩规则' : (info.isAdd ? '添加伸缩规则' : '修改伸缩规则')" v-model:visible="info.isShow" @cancel="cancel" :maskClosable="info.isInfo" centered :getContainer="modalBindNode">
            <template #footer>
                <a-button @click="cancel" v-if="!info.isInfo">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="save">提交</a-button>
                <a-button type="primary" v-if="info.isInfo" @click="cancel">关闭</a-button>
            </template>
            <a-form :model="receiverform" ref="receiverForm" :rules="rules" :label-col="{span:6}">
                <a-form-item label="伸缩规则名称" name="name">
                    <a-input v-model:value="receiverform.name" placeholder="请输入伸缩规则名称" :disabled="info.isInfo" allow-clear></a-input>
                </a-form-item>
                <a-form-item label="类型" name="receiverType">
                    <a-select v-model:value="receiverform.receiverType" placeholder="请选择类型" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option value="webhook">webhook</a-select-option>
                    </a-select>
                </a-form-item>
                
                <a-form-item label="alarmUrl" name="alarmUrl" v-if="info.isInfo">
                    <a-textarea v-model:value="receiverform.alarmUrl" :disabled="info.isInfo" allow-clear></a-textarea>
                </a-form-item>
                <a-form-item label="操作" name="action">
                    <a-select v-model:value="receiverform.action" placeholder="请选择操作" :disabled="info.isInfo" :getPopupContainer="triggerNode => triggerNode.parentNode" allow-clear>
                        <a-select-option value="CLUSTER_SCALE_IN">CLUSTER_SCALE_IN</a-select-option>
                        <a-select-option value="CLUSTER_SCALE_OUT">CLUSTER_SCALE_OUT</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item label="最小CPU使用率" name="minCpu" v-if="receiverform.action == 'CLUSTER_SCALE_IN'">
                    <a-input-number v-model:value="receiverform.minCpu" :min="0" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入伸缩数量" :disabled="info.isInfo"></a-input-number>
                </a-form-item>
                <a-form-item label="最小内存使用率" name="minMemory" v-if="receiverform.action == 'CLUSTER_SCALE_IN'">
                    <a-input-number v-model:value="receiverform.minMemory" :min="0" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入伸缩数量" :disabled="info.isInfo"></a-input-number>
                </a-form-item>
                <a-form-item label="最大CPU使用率" name="maxCpu" v-if="receiverform.action == 'CLUSTER_SCALE_OUT'">
                    <a-input-number v-model:value="receiverform.maxCpu" :min="0" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入伸缩数量" :disabled="info.isInfo"></a-input-number>
                </a-form-item>
                <a-form-item label="最大内存使用率" name="maxMemory" v-if="receiverform.action == 'CLUSTER_SCALE_OUT'">
                    <a-input-number v-model:value="receiverform.maxMemory" :min="0" :max="100" :step="1" :precision="0" style="width:100%" placeholder="请输入伸缩数量" :disabled="info.isInfo"></a-input-number>
                </a-form-item>
                <a-form-item label="数量" name="count">
                    <a-input-number v-model:value="receiverform.count" :min="1" :step="1" :precision="0" style="width:100%" placeholder="请输入伸缩数量" :disabled="info.isInfo"></a-input-number>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { saveClusterReceiver, selectClusterProfileList, updateClusterReceiver } from '@/api/backend/cluster';
import router from '@/router';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['getlist'])
const props = defineProps({
    info: {
        type:Object,
        default(){
            return {
                isAdd: true,
                isShow: false
            }
        }
    }
})
const receiverForm = ref();
const profilelist = ref([])
const defaultform = {
    "cloudId": router.currentRoute.value.query.cloudId,
    "name": "",
    "receiverType": "webhook",
    "clusterId": "",
    "clusterName": "",
    "action": undefined,
    "maxCpu":'',
    "maxMemory":'',
    "count": 0,
    "projectId": router.currentRoute.value.query.projectId,
}
const receiverform = reactive({
    "cloudId": router.currentRoute.value.query.cloudId,
    "name": "",
    "receiverType": "webhook",
    "clusterId": "",
    "clusterName": "",
    "action": undefined,
    "maxCpu":'',
    "maxMemory":'',
    "count": 0,
    "projectId": router.currentRoute.value.query.projectId,
})
const rules = {
    name:[{required:true,message:'请输入伸缩规则名称',trigger:'change'}],
    receiverType:[{required:true,message:'请输入伸缩规则类型',trigger:'change'}],
    minCpu:[{required:true,type:'number',message:'请输入最小CPU利用率',trigger:'change'}],
    minMemory:[{required:true,type:'number',message:'请输入最小内存利用率',trigger:'change'}],
    maxCpu:[{required:true,type:'number',message:'请输入最大CPU利用率',trigger:'change'}],
    maxMemory:[{required:true,type:'number',message:'请输入最大内存利用率',trigger:'change'}],
    action:[{required:true,message:'请选择操作',trigger:'change'}],
}
const cancel = () => {
    props.info.isShow = false;
    Object.assign(receiverform,defaultform)
}
const save = () => {
    let receiverform1 = {...receiverform};
    proxy.$handleSave(receiverForm.value, saveClusterReceiver, updateClusterReceiver, props, receiverform1, ()=>{emit('getlist',receiverform.clusterId);cancel()},null)
}
const setInfo = (record) => {
    Object.assign(receiverform, record)
}

onMounted(() => {})
defineExpose({receiverform, setInfo})
</script>
<style lang='scss' scoped>
</style>