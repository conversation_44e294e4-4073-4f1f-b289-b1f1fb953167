<template>
    <div class='contentPadding'>
        <div style="min-width:1001px">
            <OperateBtn @view="viewDomain" @add="addDomain" @edit="editDomain" @delete="deleteDomain" isAddHide isViewHide isEditHide isDeleteHide >
            <!-- <OperateBtn isViewHide isAddHide isEditHide isDeleteHide> -->
                <template v-slot:domain>
                        <a-spin :spinning="projectLoading">
                    <div style="width:100%;height:155px">
                        <a-divider style="margin:0"></a-divider>
                        <a-empty v-if="countEmpty === true" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                        <a-row v-else-if="countEmpty === false" class="content">
                                <div class="domain">
                                <swiper class="domain-swiper" :slides-per-view="slideNum" navigation>
                                    <swiper-slide v-for="(item,index) in projectlist" :key="index">
                                        <a-card :title="'项目：'+item.projectName" size="small">
                                            <template #actions v-if="userlimit">
                                                <a-tooltip title="修改项目"><FormOutlined style="color:#007aff" @click="editProject(item)" /></a-tooltip>
                                                <a-tooltip title="删除项目"><MinusSquareTwoTone twoToneColor="#D53620" @click="delProject(item)" /></a-tooltip>
                                                <a-tooltip title="项目配额"><ControlOutlined style="color:#007aff" @click="quotaProject(item)" /></a-tooltip>
                                            </template>
                                            <div class="text">
                                                <DesktopOutlined style="font-size:35px;font-weight:bold;color:#1296DB;margin-right: 10px;" />
                                                <span class="count" style="text-align:center">
                                                    <div style="color:#00000073">虚机总数量(个)</div>
                                                    <span style="font-size:16px;font-weight:bold">{{item.serverNum}}</span>
                                                </span>
                                            </div>
                                        </a-card>
                                    </swiper-slide>
                                </swiper>
                            </div>
                        </a-row>
                    </div>
                        </a-spin>
                </template>
            </OperateBtn>
            <a-divider style="margin-top:0"></a-divider>
            <a-page-header title="系统监控">
                <Poly ref="polyRef" :isDomain="true" />
            </a-page-header>
        </div>
        <Info ref="domainDialog" :info="domaininfo" />
        <ProjectInfo ref="projectDialog" :info="projectinfo" />
        <Quota ref="quotaDialog" :info="quota" />
    </div>
</template>
<script lang='ts' setup>
import { Empty } from 'ant-design-vue';
import Info from "@/views/backend/devops/openstack/info.vue"
import ProjectInfo from "@/views/backend/devops/project/info.vue"
import Quota from "@/views/backend/devops/project/quota.vue";
import Poly from "@/views/backend/devops/poly/index.vue"
import SwiperCore, { Navigation, Pagination } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/scss';
import 'swiper/scss/navigation';
import 'swiper/scss/pagination';
import OperateBtn from "@/components/operatebtn/operatebtn.vue";
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { deleteOpenst, getDomainInfo } from "@/api/backend/devops/domain";
import { deleteProject, getProjectCount, getProjectInfo, selectProjectList } from "@/api/backend/devops/project";
import { menuStore } from "@/store/menu";
import { storeToRefs } from "pinia";
import { getVisualList } from "@/api/backend/devops/poly";
import emiter from "@/utils/Bus";
import { userStore } from "@/store/user";
import router from "@/router";
SwiperCore.use([Navigation, Pagination])
const {proxy} = getCurrentInstance()
const menu_store = menuStore()
const userlimit = computed(()=>userStore().userInfo.roleIdList.includes(1))
const {cloudIndex,domainIndex,projectIndex} = storeToRefs(menu_store);
const route = useRoute()
const physicsActiveKey = ref('2')
const vmlist = ref([])
const projectLoading = ref(false)
const countEmpty = ref(undefined);
const projectlist = ref([])
const projectCount = ref(0)
const domainDialog = ref()
const projectDialog = ref()
const quotaDialog = ref()
const polyRef = ref()
const domaininfo = reactive({
    isAdd:true,
    isShow:false
})
const projectinfo = reactive({
    isInfo:false,
    isAdd:true,
    isShow:false
})
const quota = reactive({
    isQuota: false
})
const columns1 = [
    {title: '主机名称', dataIndex: 'hypervisor_hostname', key: 'id' ,align:'center'},
    {title: '主机类型', dataIndex: 'hypervisor_type', key: 'id' ,align:'center'},
    {title: 'VCPUs(used)', dataIndex: 'vcpus_used', key: 'id' ,align:'center'},
    {title: 'VCPUs(total)', dataIndex: 'vcpus', key: 'id' ,align:'center'},
    {title: 'RAM(used)', dataIndex: 'memory_mb_used', key: 'id' ,align:'center'},
    {title: 'RAM(total)', dataIndex: 'memory_mb', key: 'id' ,align:'center'},
    {title: 'Local Storage(used)', dataIndex: 'local_gb_used', key: 'id' ,align:'center'},
    {title: 'Local Storage(total)', dataIndex: 'local_gb', key: 'id' ,align:'center'},
    {title: 'Instances', dataIndex: 'running_vms', key: 'id' ,align:'center'},
]
const columns2 = [
    {title: '主机名称', dataIndex: 'hypervisor_hostname', key: 'id' ,align:'center'},
    // {title: '可用域', dataIndex: 'zone', key: 'id' ,align:'center'},
    {title: '服务状态(Status)', dataIndex: 'status', slots: { customRender: 'status' }, key: 'id' ,align:'center'},
    {title: '主机状态(State)', dataIndex: 'state', slots: { customRender: 'state' }, key: 'id' ,align:'center'},
    // {title: '更新时间', dataIndex: 'updated_at', key: 'id' ,align:'center'},
]
const slideNum = ref(5)
const setSlideNum = () => {
    if(window.innerWidth <= 1200){
        slideNum.value = 4;
    }else
        slideNum.value = 5;
    window.addEventListener('resize',(e)=>{
        // console.log('e',e)
        if(e.currentTarget.innerWidth <= 1200){
            slideNum.value = 4;
        }else
            slideNum.value = 5;
    })
}
const getDomaininfo = async (callback) => {
    let res = await getDomainInfo({id:route.query.domainId})
    if(res.code == 0){
        Object.assign(domainDialog.value.domainform, res.data)
        callback()
    }
}
const viewDomain = () => {
}
const addDomain = () =>{
    domaininfo.isAdd = domaininfo.isShow = true;
    nextTick(()=>{
        domainDialog.value.selectCloudlist()
    })
}
const addProject = () => {
    projectinfo.isAdd = projectinfo.isShow = true;
}
const editDomain = () => {
    getDomaininfo(()=>{
        domaininfo.isAdd = false;
        domaininfo.isShow = true;
        nextTick(()=>{
            domainDialog.value.selectCloudlist()
        })
    })
}
const editProject = async (item) => {
    
    let res = await getProjectInfo({id:item.id})
        if(res.code == 0){
            let record1 = res.data;
        if(record1.enabled == 1)
            record1.enabled = true;
        else
            record1.enabled = false;
            proxy.$nextTick(()=>{
                Object.assign(projectDialog.value.projectform, record1)
                projectinfo.isAdd = false;
                projectinfo.isShow = true;
            })
    }
    
}
const deleteDomain = () => {
    proxy.$handleDel([route.query.domainId],deleteOpenst,()=>{
        proxy.$mitt.emit('SelectDomainList',{cloudId:route.query.cloudId,index:cloudIndex.value,isDel:true})
    })
}
const delProject = (item) => {
    proxy.$handleDel([item.id],deleteProject,()=>{
        getProjectList({cloudId:route.query.cloudId,domainId:route.query.domainId});
        emiter.emit("ProjectListAPI",{key:'domain_'+route.query.domainId,cloudKey:'cloud_'+route.query.cloudId,type:'project',action:'del'})
        // proxy.$mitt.emit('SelectProjectList',{cloudId:route.query.cloudId,domainId:route.query.domainId,index:domainIndex.value,preIndex:cloudIndex.value,isDel:true})
    })
}
const quotaProject = (item) => {
    quota.isQuota = true;
    quotaDialog.value.setInfo(item.id)
}
const getvmList = async () => {
    let res = await getVisualList({cloudId:localStorage.getItem('cloudId')})
    if(res.code == 0){
        vmlist.value = res.data;
    }else{
        vmlist.value = []
    }
}
const getProjectList = async ({cloudId,domainId}) => {
    // emiter.emit('allLoading',false)
    projectLoading.value = true;
    countEmpty.value = undefined;
    // selectProjectList({cloudId,domainId,enabled:1}).then(async(res)=>{
    //     if(res.code == 0){
            let res1 = await getProjectCount({domainId})
            if(res1.code == 0){
                countEmpty.value = res1.data ? false : true;
                // res.data.forEach(item => {
                //     item.count = res1.data[item.projectName];
                // });
                if(res1.data.project_num)
                projectCount.value = res1.data.project_num;
                if(res1.data.project_list)
                projectlist.value = res1.data.project_list;
            }else{
                projectCount.value = 0;
                projectlist.value = [];
                countEmpty.value = true;
            }
            projectLoading.value = false;
        // }
    // })
    
}
const setDomain = () => {
    getProjectList({cloudId:route.query.cloudId,domainId:route.query.domainId});
    setSlideNum()
}
watch(()=>router.currentRoute.value,(to,from)=>{
  // console.log('ok')
  if(router.currentRoute.value.path == '/admin/devops/menu/domain'){
    setDomain()
  }
},{immediate:true,deep:true})
onMounted(() => {
    localStorage.setItem("domainName",route.query.title)
    console.log("new")
    // getvmList()
    proxy.$mitt.on('getProjectCount',(e)=>{getProjectList(e);})
    proxy.$mitt.on('getPolylist',(e)=>{
        polyRef.value.polyexpose()
    })
})
</script>
<style lang='scss' scoped>
.contentPadding{margin: 0 16px;padding: 8px 0;}
.ant-page-header{padding: 0 24px;}
:deep(.ant-page-header-heading){
    background-color: #fafafa;
    .ant-page-header-heading-left{margin: 0;}
}
.ant-page-header:not(.header-title){
  :deep(.ant-page-header-heading){
    ::before{
      content: "";
      display: inline-block;
      width: 4px;
      height: 100%;
      margin-right: 4px;
      vertical-align: text-bottom;
      background-color: #1890ff;
    }
  }
}
.ant-row{flex-flow: nowrap;align-items: center;}
.doamin-operate{flex: 1;min-width: 146px;}
.domain{width: 100%;min-width: 914px;}
.domain-swiper{
    padding: 0 10px;
    .ant-card{margin: 0 10px;}
    
    .ant-statistic{flex: 1;}
    .project-btn{flex: 3;}
    :deep(.swiper-button-prev){left: -8px;};
    :deep(.swiper-button-next){right: -8px;};
    :deep(.swiper-button-prev:after),:deep(.swiper-button-next:after){
        font-size: 20px;
        font-weight: 600;
    }
    .swiper-wrapper{overflow-y: hidden;cursor: grabbing;}
}
.content{
    height: 154px;
    padding: 10px 0;
    :deep(.ant-card-head){border-bottom: 0;}
    :deep(.ant-card-actions){background: #fff;border-top: 0;& > li{margin: 0;}}
    :deep(.ant-card-actions > li:not(:last-child)){border-right: 0;}
    .text{
    display: flex;align-items: center;justify-content: center;
    img{margin-right: 10px;}
  }
}

:deep(.ant-table-thead > tr > th){white-space: nowrap;}
a{color: #000000D9;}
.domain-card{:deep(.ant-card-body){display: flex;justify-content: space-evenly;align-items: center;}}
</style>