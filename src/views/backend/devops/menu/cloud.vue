<template>
    <div class='contentPadding'>
            <OperateBtn @view="viewCloud" @add="addCloud" @edit="editCloud" @sync="syncCloud" @sync_history="syncHistory" @delete="delCloud" isSyncShow isSyncHistoryShow :Initializing="Initializing" :createUserName="createUserName">
                <template v-slot:cloud>
                    <a-spin :spinning="domainLoading" wrapperClassName="full-width">
                    <div style="width:100%;height:131px">
                        <a-divider style="margin:0"></a-divider>
                        <a-empty v-if="countEmpty === true" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                        <a-row v-else-if="countEmpty === false" class="content">
                          <div class="domain">
                              <swiper class="domain-swiper" :style="{padding: (domainlist.length <= 4 ? '0' : '0 10px')}" slides-per-view="4" navigation>
                                <swiper-slide v-for="(item,index) in domainlist" :key="index">
                                    <a-card size="small" :title="item.domainName" :class="domainlist.length <= 4 ? 'no-margin':(index == 3 ? '' : 'has-margin')">
                                          <div class="text">
                                            <img src="@/assets/count/project.png" width="35" height="35" alt="">
                                            <span class="count" style="text-align:center;">
                                              <div style="color:#00000073">项目总数量(个)</div>
                                              <span style="font-size:16px;font-weight:bold">{{item.projectNum}}</span>
                                            </span>
                                          </div>
                                          <div class="btn"></div>
                                    </a-card>
                                </swiper-slide>
                              </swiper>
                          </div>
                    </a-row>
                    </div>
                        </a-spin>
                </template>
            </OperateBtn>
            <a-divider style="margin-top:0"></a-divider>
            <a-page-header title="物理资源" ghost>
                <a-empty v-if="physicalEmpty" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                <a-spin :spinning="sourceLoading" v-else>
                    <div style="height:135px">
                        <div class="physics" v-if="!sourceLoading">
                            <a-card size="small" class="card-count">
                            <div class="counts">
                                <div class="text">
                                <svg class="iconfont" aria-hidden="true" style="height:2em;width:4em">
                                <use xlink:href="#icon-wulitu_yingpan" />
                                </svg>
                                <span class="count" style="text-align:center;min-width:66px">
                                    <div style="color:#00000073">总数量(台)</div>
                                    <span style="font-size:20px;font-weight:bold">{{physicalsource.node.sum? physicalsource.node.sum : 0}}</span>
                                </span>
                                </div>
                                <a-list>
                                <a-list-item style="justify-content: space-around;"> <a-badge dot :number-style="{ backgroundColor: '#52c41a' }" :offset="[-65,7]">正常节点</a-badge><span style="font-weight:bold">{{physicalsource.node.up_node}}</span></a-list-item>
                                <a-list-item style="justify-content: space-around;"> <a-badge :offset="[-65,7]" dot>异常节点</a-badge><span style="font-weight:bold">{{physicalsource.node.down_node}}</span></a-list-item>
                                <!-- <a-list-item> <a-badge :offset="[-65,7]" dot :number-style="{ backgroundColor: '#666' }">其他节点</a-badge><span style="font-weight:bold">{{physicalsource.node.other_node}}</span></a-list-item> -->
                                </a-list>
                            </div>
                            </a-card>
                            <div class="charts">
                            <a-card title="主存储" size="small">
                                <a-row justify="center">
                                <a-progress type="circle" 
                                    :format="()=>(physicalsource.statistic.local_gb == -1 ? physicalsource.statistic.local_gb : (physicalsource.statistic.local_gb ? ((physicalsource.statistic.local_gb_used / physicalsource.statistic.local_gb) * 100).toFixed() + '%' : 0))"
                                    :percent="physicalsource.statistic.local_gb ? Number(((physicalsource.statistic.local_gb_used/physicalsource.statistic.local_gb)*100).toFixed(2)) : 0" :width="60" :stroke-color="color1" />
                                <div style="margin-left:10px">
                                    <div>已用：<span style="font-weight:bold">{{physicalsource.statistic.local_gb_used1 ? physicalsource.statistic.local_gb_used1:0}}</span></div>
                                    <div>总量：<span style="font-weight:bold">{{physicalsource.statistic.local_gb == -1 ? '无限制' : (physicalsource.statistic.local_gb1?physicalsource.statistic.local_gb1:0)}}</span></div>
                                </div>
                                </a-row>
                            </a-card>
                            <a-card title="CPU总数量" size="small">
                                <a-row justify="center">
                                <a-progress type="circle" 
                                    :format="()=>(physicalsource.statistic.vcpus == -1 ? physicalsource.statistic.vcpus : (physicalsource.statistic.vcpus ? ((physicalsource.statistic.vcpus_used / physicalsource.statistic.vcpus) * 100).toFixed() + '%' : 0))"
                                    :percent="physicalsource.statistic.vcpus ? Number(((physicalsource.statistic.vcpus_used/physicalsource.statistic.vcpus)*100).toFixed(2)) : 0" :width="60" :stroke-color="color2" />
                                <div style="margin-left:10px">
                                    <div>已用：<span style="font-weight:bold">{{physicalsource.statistic.vcpus_used ? physicalsource.statistic.vcpus_used:0}} 个</span></div>
                                    <div>总量：<span style="font-weight:bold">{{physicalsource.statistic.vcpus == -1 ? '无限制' : (physicalsource.statistic.vcpus?physicalsource.statistic.vcpus:0)+'个'}} </span></div>
                                </div>
                                </a-row>
                            </a-card>
                            <a-card title="内存总数" size="small">
                                <a-row justify="center">
                                <a-progress type="circle" 
                                    :format="()=>(physicalsource.statistic.memory_mb == -1 ? physicalsource.statistic.memory_mb : (physicalsource.statistic.memory_mb ? ((physicalsource.statistic.memory_mb_used / physicalsource.statistic.memory_mb) * 100).toFixed() + '%' : 0))"
                                    :percent="physicalsource.statistic.memory_mb ? Number(((physicalsource.statistic.memory_mb_used/physicalsource.statistic.memory_mb)*100).toFixed(2)) : 0" :width="60" :stroke-color="color3" />
                                <div style="margin-left:10px">
                                    <div>已用：<span style="font-weight:bold">{{physicalsource.statistic.memory_mb_used1?physicalsource.statistic.memory_mb_used1:0}}</span></div>
                                    <div>总量：<span style="font-weight:bold">{{physicalsource.statistic.memory_mb == -1 ? '无限制' : (physicalsource.statistic.memory_mb1?physicalsource.statistic.memory_mb1:0)}}</span></div>
                                </div>
                                </a-row>
                            </a-card>
                            </div>
                        </div>
                    </div>
                </a-spin>
            </a-page-header>
            <a-divider />
            <a-page-header title="资源信息">
                <div class="monitor">
                  <div class="echart-left">
                    <a-spin :spinning="chartLoding[1]" wrapperClassName="width-49">
                    <a-card v-if="chartEmpaty[1]" title="物理机CPU使用率(%)" size="small" class="echarts progress">
                      <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                    </a-card>
                      <div v-show="!chartEmpaty[1]" class="echarts" id="chart1" ref="chart1"></div>
                    </a-spin>
                    <a-spin :spinning="chartLoding[2]" wrapperClassName="width-49">
                    <a-card v-if="chartEmpaty[2]" title="物理机内存负载率(%)" size="small" class="echarts progress">
                      <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                    </a-card>
                      <div v-show="!chartEmpaty[2]" class="echarts" id="chart2" ref="chart2"></div>
                    </a-spin>
                    
                    <a-spin :spinning="chartLoding[3]" wrapperClassName="width-49">
                    <a-card v-if="chartEmpaty[3]" title="物理机网络发送量(kb/s)" size="small" class="echarts progress">
                      <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                    </a-card>
                      <div v-show="!chartEmpaty[3]" class="echarts" id="chart3" ref="chart3"></div>
                    </a-spin>
                    <a-spin :spinning="chartLoding[4]" wrapperClassName="width-49">
                    <a-card v-if="chartEmpaty[4]" title="物理机网络接收量(kb/s)" size="small" class="echarts progress">
                      <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                    </a-card>
                      <div v-show="!chartEmpaty[4]" class="echarts" id="chart4" ref="chart4"></div>
                    </a-spin>
                    
                    <a-spin :spinning="chartLoding[5]" wrapperClassName="width-49">
                    <a-card v-if="chartEmpaty[5]" title="物理机磁盘读取量O(kb/s)" size="small" class="echarts progress">
                      <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                    </a-card>
                      <div v-show="!chartEmpaty[5]" class="echarts" id="chart5" ref="chart5"></div>
                    </a-spin>
                    <a-spin :spinning="chartLoding[6]" wrapperClassName="width-49">
                    <a-card v-if="chartEmpaty[6]" title="物理机磁盘写入量I(kb/s)" size="small" class="echarts progress">
                      <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
                    </a-card>
                      <div v-show="!chartEmpaty[6]" class="echarts" id="chart6" ref="chart6"></div>
                    </a-spin>
                  </div>
                  <div class="process-right">
                          <a-spin :spinning="top5Loading1" wrapperClassName="echarts progress">
                      <a-card :title="'Top '+topfivelist1.length+'：物理机CPU使用率(%)'" size="small">
                        <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" v-if="!top5Loading1 && (!topfivelist1 || topfivelist1.length <= 0)" />
                          <a-row v-for="(item,index) in topfivelist1" :key="index">{{Object.keys(item)[0]}}&nbsp;<a-progress :percent="Number((Object.values(item)[0]).toFixed(2))" :strokeWidth="15" :stroke-color="item.color"></a-progress></a-row>
                      </a-card>
                      </a-spin>
                    
                        <a-spin :spinning="top5Loading2" wrapperClassName="echarts progress">
                      <a-card :title="'Top '+topfivelist2.length+'：物理机内存使用率(%)'" size="small">
                        <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" v-if="!top5Loading2 && (!topfivelist2 || topfivelist2.length <= 0)" />
                          <a-row v-for="(item,index) in topfivelist2" :key="index">{{Object.keys(item)[0]}}&nbsp;<a-progress :format="(percent)=>percent + '%'" :percent="Number((Object.values(item)[0]).toFixed(2))" :strokeWidth="15" :stroke-color="item.color"></a-progress></a-row>
                      </a-card>
                        </a-spin>
                  </div>
                </div>
            </a-page-header>
          <a-divider v-show="isOpenCeph == 1" />
            <a-page-header title="存储管理" v-show="isOpenCeph==1">
              <!-- <a-spin :spinning="cephLoading"> -->
                 <Ceph ref="cephRef" @setOpenCeph="(e)=>{isOpenCeph = e}" />
              <!-- </a-spin> -->
            </a-page-header>
        </div>
        <Add ref="cloudAddDialog" :info="cloudinfo" :options="options" :userlist="userlist" />
        <Info ref="cloudEditDialog" :info="cloudinfo1" :options="options" :userlist="userlist" @callback="(e)=>{isOpenCeph = e}" />
        <Sync ref="syncDialog" :info="cloudinfo1" @sync="syncing" />
        <SyncHistory ref="syncHistoryDialog" :info="cloudinfo1" />
        <DomainInfo ref="domainDialog" :info="domaininfo" />
</template>
<script lang='ts' setup>
import "@/assets/iconfont/iconfont.css"
import "@/assets/iconfont/iconfont.js"
import Add from "@/views/backend/cloud/add.vue";
import Info from "@/views/backend/cloud/info.vue";
import Sync from "@/views/backend/cloud/reset.vue";
import SyncHistory from "@/views/backend/cloud/history.vue";
import DomainInfo from "@/views/backend/devops/openstack/info.vue";
import Ceph from "@/views/backend/sms/page/index.vue"
import SwiperCore, { Navigation, Pagination } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/scss';
import 'swiper/scss/navigation';
import 'swiper/scss/pagination';
import OperateBtn from "@/components/operatebtn/operatebtn.vue";
import * as echarts from "echarts";
import { computed, getCurrentInstance, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import { queryWorker } from "@/api/backend/systems/user";
import { selectDictList } from "@/api/backend/systems/dictionary";
import { deleteCloud, getCloudInfo, initCloud, initCloudState, queryPhysicalSource } from "@/api/backend/cloud";
import { onBeforeRouteLeave, useRoute } from "vue-router";
import { message, Modal, Empty } from "ant-design-vue";
import { deleteOpenst, getDomainCount, getDomainInfo, selectOpenstList } from "@/api/backend/devops/domain";
import { storeToRefs } from "pinia";
import { menuStore } from "@/store/menu";
import emiter from "@/utils/Bus";
import router from "@/router";
import { setCephToken } from '@/utils/auth';
import { getNowResourceCloud, getTopFiveCpu, getTopFiveMemory } from "@/api/backend/devops/agent";
import { setNotopt } from "@/utils/tool";
import { userStore } from "@/store/user";
import { Options } from "@/common/chartoption";
import { randomColor } from "@/utils/randomcolor";
SwiperCore.use([Navigation, Pagination])
const {proxy} = getCurrentInstance()
const route = useRoute()
const menu_store = menuStore()
const {cloudIndex} = storeToRefs(menu_store)
const cephRef = ref(null);
// const cephLoading = ref(false);
const chartEmpaty = ref([false,false,false,false,false,false,false]);
const physicalEmpty = ref(false);
const countEmpty = ref(undefined);
const top5Loading1 = ref(false);
const top5Loading2 = ref(false);
const cloudAddDialog = ref(null);
const cloudEditDialog = ref(null);
const syncDialog = ref(null);
const syncHistoryDialog = ref(null);
const domainDialog = ref()
const userlist = ref([])
const topfivelist1 = ref([])
const topfivelist2 = ref([])
const domainLoading = ref(false)
const sourceLoading = ref(false)
const chartLoding = ref([false,false,false,false,false,false,false])
const domainlist = ref([])
const domainCount = ref([])
const options = ref([])
const physicalsource = reactive({node:{sum:'-'},statistic:{}})
const color1 = ref()
const color2 = ref()
const color3 = ref()
const isOpenCeph = ref(0)
const Initializing = ref(false)
const createUserName = ref('')
const colorObj = {};
const openPrometheus = ref(Number(localStorage.getItem('openPrometheus')));
const cloudinfo = reactive({
    isShow:false
})
const domaininfo = reactive({
    isAdd:true,
    isShow:false
})
const cloudinfo1 = reactive({
    isShow:false,
    isInfo:false,
    isSync:false,
    isHistory:false
})
const viewCloud = () => {
    getCloudinfo(()=>{
        cloudinfo1.isShow = true;
        cloudinfo1.isInfo = true;
        queryworker()
    },true)
    
}
const editCloud = () => {
    getCloudinfo(()=>{
        cloudinfo1.isShow = true;
        cloudinfo1.isInfo = false;
        queryworker()
    },true)
    
}
const addCloud = () => {
    cloudinfo.isShow = true;
    queryworker()
}
const delCloud = () => {
    proxy.$handleDel([route.query.cloudId],deleteCloud,()=>{
      proxy.$mitt.emit('backToHome')
    })
}
const addDomain = () =>{
    domaininfo.isAdd = domaininfo.isShow = true;
    nextTick(()=>{
        domainDialog.value.selectCloudlist()
    })
}
const editDomain = (item) => {
    getDomaininfo(item.id,()=>{
        domaininfo.isAdd = false;
        domaininfo.isShow = true;
        nextTick(()=>{
          domainDialog.value.selectCloudlist()
      })
    })    
}
const deleteDomain = (item) => {
    proxy.$handleDel([item.id],deleteOpenst,()=>{
      getDomainCount(route.query.cloudId)
        proxy.$mitt.emit('SelectDomainList',{cloudId:route.query.cloudId,index:cloudIndex.value,isDel:true})
    })
}
const syncCloud = async () => {
  cloudinfo1.isSync = true;
  nextTick(()=>{
    syncDialog.value.setInfo();
  })
}
const syncing = async (params) => {
  cloudinfo1.isSync = false;
  Initializing.value = true;
  createUserName.value = userStore().loginName;
  let res = await initCloud(params);
  if(res.code == 0){
    if(res.data){
      if(res.data.initResult == 'success'){
      if(res.data.uuid){
        getProcess(res.data.uuid)
      }
    }else if(res.data.initResult == 'failed'){
      Modal.error({
        content: () => '初始化失败'
      });
      Initializing.value = false;
      // emiter.emit('allLoading',false)
    }else if(res.data.initResult == 'running'){
      if(res.data.uuid){
        getProcess(res.data.uuid)
      }
    }else{
      Modal.error({
        content: () => '初始化失败'
      });
      Initializing.value = false;
      // emiter.emit('allLoading',false)
    }
    }
  }else if(res.msg){
    Modal.error({
      content: () => res.msg
    });
    Initializing.value = false;
    // emiter.emit('allLoading',false)
  }else{
    Modal.error({
      content: () => '初始化失败'
    });
    Initializing.value = false;
    // emiter.emit('allLoading',false)
  }
}
const getProcess = async (uuid,isMounted,cloudId) => {
  if(isMounted)
    Initializing.value = false;
  let res = await initCloudState({uuid,cloudId:cloudId ? cloudId : route.query.cloudId});
  if(res.code == 0){
    if(res.data){
      if(res.data.initResult == "success"){
      if(!isMounted){
        Initializing.value = false;
        // emiter.emit('allLoading',false)
        Modal.destroyAll();
        Modal.success({
          content: () => '初始化成功'
        });
        getCloudinfo(()=>{
          cloudLoop(router.currentRoute.value.query.cloudId)
          begin()
        },false)
      }
    }else if(res.data.initResult == 'running'){
      if(isMounted){
        Initializing.value = true;
        createUserName.value = res.data.createUserName;
      }else{
        setTimeout(()=>{
          getProcess(uuid);
        },5000)
      }
    }else{
      if(!isMounted){
        Modal.error({
          title:'初始化失败',
          content: () => res.data.resultInfo
        });
        Initializing.value = false;
      }
      // emiter.emit('allLoading',false)
    }
    }
    
  }else if(res.msg){
    if(!isMounted){
      Modal.error({
        title: () => route.query.title,
        content: () => res.msg
      });
      Initializing.value = false;
    }
    // emiter.emit('allLoading',false)
  }
  else{
    if(!isMounted){
      Modal.error({
        content: () => '初始化失败'
      });
      Initializing.value = false;
    }
    // emiter.emit('allLoading',false)
  }
}
const syncHistory = () => {
  cloudinfo1.isHistory = true;
  nextTick(()=>{
    syncHistoryDialog.value.getList();
  })
}
const getCloudinfo = async (callback,isDialog) => {
    let res = await getCloudInfo({id:route.query.cloudId})
    if(res.code == 0){
      let record1 = {};
      Object.assign(record1,res.data)
      if(isDialog){
        let manageUserId1 = record1.manageUserId?.split(',')
        manageUserId1.splice(0,1)
        manageUserId1.splice(manageUserId1.length-1,1)
        record1.manageUserId = manageUserId1;
        record1.openBaremetal = (record1.openBaremetal == 0) ? false : true;
        record1.openCluster = (record1.openCluster == 0) ? false : true;
        record1.openPrometheus = (record1.openPrometheus == 0) ? false : true;
        record1.openCeph = (record1.openCeph == 0) ? false : true;
        Object.assign(cloudEditDialog.value.cloudform, record1)
      }else{
        localStorage.setItem('openPrometheus',record1.openPrometheus)
        localStorage.setItem('openCeph',record1.openCeph)
        openPrometheus.value = record1.openPrometheus;
        emiter.emit("setCloud")
        emiter.emit("setMenu")
        emiter.emit("DomainListAPI","cloud_"+router.currentRoute.value.query.cloudId);
      }
      callback();
    }
}
const getDomaininfo = async (id,callback) => {
    let res = await getDomainInfo({id})
    if(res.code == 0){
        Object.assign(domainDialog.value.domainform, res.data)
        callback()
    }
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
}
const getDictList = async () => {
    let res = await selectDictList({dictType:'CLOUD_TYPE'})
    if(res){
        if(res.code == 0){
            options.value = res.data;
            // 避免字典中的cloutype被随意添加致radioOptions变混乱
            cloudAddDialog.value.radioOptions = cloudAddDialog.value.radioOptions.filter((item,index)=>{
              return options.value.some((t,i)=>{
               return t.dictValue == item.value
              })
            })
        }
    }
}
const gettopFive1 = async (cloudId,isLoop) => {
  if(!isLoop)
  top5Loading1.value = true;
  let res = await getTopFiveCpu({cloudId:cloudId ? cloudId : route.query.cloudId})
  if(res.code == 0){
    if(!isLoop)
    top5Loading1.value = false;
    if(res.data && res.data.length > 0){
      res.data.forEach((item,index)=>{
        item.color = topFiveColor(Object.values(item)[0])
      })
      if(res.data.length > 5){
        topfivelist1.value = res.data.slice(0,5)
      }else{
        topfivelist1.value = res.data;
      }
    }
  }else{
    if(!isLoop)
    top5Loading1.value = false;
    topfivelist1.value = []
  }
}
const gettopFive2 = async (cloudId,isLoop) => {
  if(!isLoop)
  top5Loading2.value = true;
  let res = await getTopFiveMemory({cloudId:cloudId ? cloudId : route.query.cloudId})
  if(res.code == 0){
    if(!isLoop)
    top5Loading2.value = false;
    if(res.data && res.data.length > 0){
      res.data.forEach((item,index)=>{
        item.color = topFiveColor(Object.values(item)[0])
      })
      if(res.data.length > 5){
        topfivelist2.value = res.data.slice(0,5)
      }else
        topfivelist2.value = res.data;
    }
  }else{
    if(!isLoop)
    top5Loading2.value = false;
    topfivelist2.value = [];
  }
}
const topFiveColor = (item) => {
    return item < 60 ? '#60B515' : (item < 80 ? '#FAC858' : '#EE6666')
}
const myChart = {
  myChart1:'',
  myChart2:'',
  myChart3:'',
  myChart4:'',
  myChart5:'',
  myChart6:'',
}
const chart1 = ref()
const chart2 = ref()
const chart3 = ref()
const chart4 = ref()
const chart5 = ref()
const chart6 = ref()
const option = {
  option1:Options.cloud_1,
  option2:Options.cloud_2,
  option3:Options.cloud_3,
  option4:Options.cloud_4,
  option5:Options.cloud_5,
  option6:Options.cloud_6
}
const initChart = (cloudId) => {
  let n = 6;
  while(n > 0){
    (function(n){
      chartEmpaty.value[n] = false;
      let chart = [chart1.value,chart2.value,chart3.value,chart4.value,chart5.value,chart6.value];
      if(!chart[n-1])
        return
      chartLoding.value[n] = true;
      echarts.dispose(chart[n-1]);
      option['option'+n].xAxis.data = [];
      // proxy.$nextTick(()=>{
      setTimeout(()=>{
        myChart['myChart'+n] = echarts.init(chart[n-1]);
        if(!chartLoding.value[n])
          myChart['myChart'+n].setOption(option['option'+n])
        chartEmpaty.value[n] = !openPrometheus.value;
        myChart['myChart'+n].resize()
        window.addEventListener('resize',function(){
          myChart['myChart'+n].resize()
        })
      })
    })(n)
    if(n == 1 && openPrometheus.value == 1){
      proxy.$nextTick(()=>{
        getChartLoop(cloudId)
      })
    }
    n--;
  }
  
} 
const getChartLoop = (cloudId) => {
  let chartname = ['cpu','memery','networkTransmit','networkReceive','diskReaddisk','diskWritedisk'];
  chartname.forEach((item,index)=>{
    GetNowResourceCloud(cloudId,item,index+1)
  })
}
const GetNowResourceCloud = async (cloudId,NAME,INDEX) => {
  option['option'+INDEX].series = [];
  option['option'+INDEX].xAxis.data = []
  let res = await getNowResourceCloud({cloudId: cloudId ? cloudId : route.query.cloudId,type:NAME.toUpperCase()})
  chartLoding.value[INDEX] = false;
  if(res.code == 0){
    if(res.data[NAME+'Map']){
      if((!res.data[NAME+'Map'][NAME+'Time'] || res.data[NAME+'Map'][NAME+'Time'].length <= 0) && (!Object.keys(res.data[NAME+'Map'][NAME]) || Object.keys(res.data[NAME+'Map'][NAME]).length <= 0))
        chartEmpaty.value[INDEX] = true;
      option['option'+INDEX].xAxis.data = res.data[NAME+'Map'][NAME+'Time'] ? res.data[NAME+'Map'][NAME+'Time'] : [];
      for(let i = 0; i < Object.keys(res.data[NAME+'Map'][NAME]).length;i++){
        if(!option['option'+INDEX].series[i] || option['option'+INDEX].series[i].length <= 0){
          let color = colorObj[i] ? colorObj[i] : randomColor();
          colorObj[i] = color;
          option['option'+INDEX].series[i] = {
            type: 'line',
            data: [],
            name:'-',
            color,
            symbolSize: 0,
            areaStyle:{color:new echarts.graphic.LinearGradient(0,0,0,1,[
              {offset:0,color},
              {offset:1,color:'#fff'},
            ])},
            lineStyle:{width:1}
          };
        }
        option['option'+INDEX].series[i].name = Object.keys(res.data[NAME+'Map'][NAME])[i];
        option['option'+INDEX].series[i].data = Object.values(res.data[NAME+'Map'][NAME])[i];
      }
    }
    if(!chartLoding.value[INDEX])
      myChart['myChart'+INDEX].setOption(option['option'+INDEX])
  }else{
    chartEmpaty.value[INDEX] = true;
    proxy.$nextTick(()=>{
    if(!chartLoding.value[INDEX])
      myChart['myChart'+INDEX].setOption(option['option'+INDEX])
    })
  }
}
const getDomainList = (cloudId,isRefresh) => {
  // domainlist.value = [];
  if(!isRefresh){
    domainLoading.value = true;
    countEmpty.value = undefined;
  }
  getDomainCount({cloudId}).then((res)=>{
    if(res.code == 0 && res.data){
      
      countEmpty.value = false;
      // if(res.data){
        domainlist.value = res.data;
        domainCount.value = res.data?.length;
      // }else{
      //   domainlist.value = [];
      //   domainCount.value = 0;
      // }
        
    }else{
      countEmpty.value = true;
      domainlist.value = [];
      domainCount.value = 0;
    }
    domainLoading.value = false;
  }).catch(()=>{
    domainLoading.value = false;
  })
}
const formattUnit = (physicalsource1,props,props1) => {
  if(physicalsource1.statistic[props] >= 1024*1024*1024*1024){
    physicalsource1.statistic[props+'1'] = (physicalsource1.statistic[props] / (1024*1024*1024*1024)).toFixed(2)+' TB';
    physicalsource1.statistic[props1+'1'] = (physicalsource1.statistic[props1] / (1024*1024*1024*1024)).toFixed(2)+' TB';
  }else if(physicalsource1.statistic[props] >= 1024*1024*1024){
    physicalsource1.statistic[props+'1'] = (physicalsource1.statistic[props] / (1024*1024*1024)).toFixed(1)+' GB';
    physicalsource1.statistic[props1+'1'] = (physicalsource1.statistic[props1] / (1024*1024*1024)).toFixed(2)+' GB';
  }else if(physicalsource1.statistic[props] >= 1024*1024 && physicalsource1.statistic[props] < 1024*1024*1024){
    physicalsource1.statistic[props+'1'] = (physicalsource1.statistic[props] / (1024*1024)).toFixed(2)+' MB';
    physicalsource1.statistic[props1+'1'] = (physicalsource1.statistic[props1] / (1024*1024)).toFixed(2)+' MB';
  }else if(physicalsource1.statistic[props] >= 1024 && physicalsource1.statistic[props] < 1024*1024){
    physicalsource1.statistic[props+'1'] = (physicalsource1.statistic[props] / 1024).toFixed(2)+' KB';
    physicalsource1.statistic[props1+'1'] = (physicalsource1.statistic[props1] / 1024).toFixed(2)+' KB';
  }
  Object.assign(physicalsource,physicalsource1)
}
const formattColor = (physicalsource1,props,props1) => {
    return (physicalsource1.statistic[props1] / physicalsource1.statistic[props] < 0.5) ? '#60B515' : ((physicalsource1.statistic[props1] / physicalsource1.statistic[props] < 0.8) ? '#FAC858' : '#EE6666')
}
const getPhysicalSource = async (data,cloudId,isRefresh) => {
  if(!isRefresh){
    Object.assign(physicalsource,{node:{sum:'-'},statistic:{}})
    sourceLoading.value = true;
  }
  let res = await queryPhysicalSource({cloudId});
  sourceLoading.value = false;
  if(res.code == 0){
    physicalEmpty.value = false;
    setBaseInfo(data,cloudId,res.data,isRefresh)
  }else{
    physicalEmpty.value = true;
  }
}
const setBaseInfo = async (cloudinfo,cloudId,data,isRefresh) => {
  if(cloudinfo.openCeph == 1){
    data.statistic.local_gb = Number(Number(data.statistic.local_gb * 0.95) / Number(data.node.sum * cloudinfo.storageCopyNum)) * 1024 * 1024 * 1024;
  }else
    data.statistic.local_gb = Number(data.statistic.local_gb) * 1024 * 1024 * 1024;
  data.statistic.local_gb_used = Number(data.statistic.local_gb_used) * 1024 * 1024 * 1024;
  data.statistic.memory_mb = Number(data.statistic.memory_mb) * 1024 * 1024;
  data.statistic.memory_mb_used = Number(data.statistic.memory_mb_used) * 1024 * 1024;
  formattUnit(data,'local_gb','local_gb_used')
  formattUnit(data,'memory_mb','memory_mb_used')
  color1.value = formattColor(data,'local_gb','local_gb_used')
  color2.value = formattColor(data,'vcpus','vcpus_used')
  color3.value = formattColor(data,'memory_mb','memory_mb_used')
}
var timer3 = null;
const begin = () => {
  clearInterval(timer3)
  timer3 = setInterval(()=>{
    cloudLoop(router.currentRoute.value.query.cloudId,true)
  },60000)
}
const getStorageCeph = (data,cloudId,isRefresh) => {
  localStorage.setItem("openPrometheus",data.openPrometheus)
  if(data.openCeph == 1){
    setTimeout(()=>{
      cephRef.value.getDashboard(isRefresh);
      // cephRef.value.begin()
    })
  }else{
    isOpenCeph.value = data.openCeph;
    localStorage.setItem("openCeph",data.openCeph)
  }
  // if(res.data.openPrometheus == 1){
  //   gettopFive1()
  //   gettopFive2();
  //   setTimeout(()=>{
  //     initChart()
  //   })
  // }
}
const cloudLoop = async (cloudId,isRefresh) => {
  getDomainList(cloudId,isRefresh);
  let res = {data:{}};
  res = await getCloudInfo({id:cloudId})
  localStorage.setItem("openPrometheus",res.data.openPrometheus)
  openPrometheus.value = res.data.openPrometheus;
  if(res.data.openPrometheus){
    getPhysicalSource(res.data,cloudId,isRefresh);
    if(isRefresh)
      getChartLoop(cloudId);
    else
      initChart(cloudId)
    gettopFive1(cloudId,isRefresh)
    gettopFive2(cloudId,isRefresh)
  }else{
    physicalEmpty.value = true;
    sourceLoading.value = false;
    top5Loading1.value = false;
    top5Loading2.value = false;
    topfivelist1.value = [];
    topfivelist2.value = [];
    let n = 6;
    while(n > 0){
      chartLoding.value[n] = false;
      chartEmpaty.value[n] = true;
      n--;
    }
  }
  if(res.data.openCeph){
    getStorageCeph(res.data,cloudId,isRefresh);
  }
}
const setCloud = () => {
  // openPrometheus.value = Number(localStorage.getItem('openPrometheus'));
  isOpenCeph.value = 0;
  physicalEmpty.value = false;
  sourceLoading.value = true;
  topfivelist1.value = [];
  topfivelist2.value = [];
  top5Loading1.value = true;
  top5Loading2.value = true;
  let n = 6;
  while(n > 0){
      chartEmpaty.value[n] = false;
      let chart = [chart1.value,chart2.value,chart3.value,chart4.value,chart5.value,chart6.value];
      chartLoding.value[n] = true;
      if(chart[n-1])
        echarts.dispose(chart[n-1]);
      option['option'+n].xAxis.data = [];
      n--;
  }
  if(router.currentRoute.value.query.cloudId){
    emiter.emit('allLoading',false)
    cloudLoop(router.currentRoute.value.query.cloudId)
    getDictList();
    getProcess(undefined,true)
    begin()
  }
}
watch(()=>router.currentRoute.value,(to,from)=>{
  if(router.currentRoute.value.path == '/admin/devops/menu/cloud'){
    setCloud()
  }else{
    clearInterval(timer3)
  }
},{immediate:true,deep:true})
onMounted(() => {
  emiter.on("setCloud",setCloud)
  proxy.$mitt.on('getDomainCount',getDomainList)
})
onBeforeRouteLeave(()=>{
  clearInterval(timer3)
})
onBeforeUnmount(()=>{
  clearInterval(timer3)
})
</script>
<style lang='scss' scoped>
:deep(.ant-card-head){border-bottom: none;}
.contentPadding{margin: 0 16px;padding: 8px 0;min-width: auto;}
.cloud-left{width: 74%;padding: 8px 0 24px;background: #fff;}
.cloud-right{width: 25%;padding: 24px 0;background: #fff;}
.ant-page-header{padding: 0 24px;}
.ant-row{flex-flow: nowrap;align-items: center;}
:deep(.ant-page-header-heading){
  background-color: #fafafa;
  .ant-page-header-heading-left{margin: 0;}
}
.ant-page-header:not(.header-title){
  :deep(.ant-page-header-heading){
    ::before{
      content: "";
      display: inline-block;
      width: 4px;
      height: 100%;
      margin-right: 4px;
      vertical-align: text-bottom;
      background-color: #1890ff;
    }
  }
}
.ant-table-wrapper{padding: 0 24px;}
.ant-divider{margin-top: 12px;}
.domain-btn{display: flex;justify-content: space-between;}
.project-btn{
    height: 64px;display: flex;flex-direction: column;justify-content: space-between;align-items: end;
}
.ant-spin-container{margin: 0 16px;}
.doamin-operate{width: 300px;display: flex;flex-direction: column;justify-content: space-around;}
.domain{width: 100%;padding: 10px 0;}
.domain-swiper{
    
    .ant-card{
      &.no-margin{
        margin: 0 20px 0 0;
        min-width: 195px;
      }
      &.has-margin{
        margin: 0 10px;
      }
    }
    :deep(.ant-card-actions > li){margin: 2px 0;}
    .ant-statistic{flex: 1;}
    .project-btn{flex: 3;}
    :deep(.swiper-button-prev){left: -8px;};
    :deep(.swiper-button-next){right: -8px;};
    :deep(.swiper-button-prev:after),:deep(.swiper-button-next:after){
        font-size: 20px;
        font-weight: 600;
    }
    .swiper-wrapper{overflow-y: hidden;cursor: grabbing;}
}
.content,.physics{
  .title{white-space:'nowrap';display:'block';overflow:'hidden';text-overflow:'ellipsis'}
  .text{
    display: flex;align-items: center;justify-content: center;
    img{margin-right: 10px;}
  }
  .btn{display: flex;justify-content: flex-end;align-items: center;font-size: 20px;
    span{cursor: pointer;margin: 0 10px;}
  }
  }
a{color: #000000D9;}
.physics{
    display: flex;
    width: 100%;
    .card-count{flex: 5;
    .counts{display: flex;justify-content: space-around;.ant-list{width: 170px;}}
    }
    .nodes{flex: 1;padding: 0 64px;
    // :deep(.ant-list-item){justify-content: space-around;}
    }
    .charts{flex: 16;display: flex;align-items: center;justify-content: flex-end;
    .ant-card{width:32%;height: 100%;margin-left: 20px;}
    }
}
:deep(.ant-statistic-content){text-align: center;}
:deep(.ant-card-small > .ant-card-head > .ant-card-head-wrapper > .ant-card-head-title){font-weight: bold;}
.monitor{
    width: 100%;display: flex;flex-wrap: wrap;
    justify-content: space-between;
    .echart-left{
      display: flex;flex-wrap: wrap;
      justify-content: space-between;
      width:66.67%;
      min-width: 618px;
      .width-49{width: 49%;}
      .echarts{
        // width:49%;
        height:230px;
        min-width: 303px;
      }
    }
    .process-right{
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-end;
      width:33.33%;
      min-width: 309px;
      .echarts{
        width:96%;
        height:355px;
        min-width: 280px;
        overflow: hidden;
        .ant-spin-container{height: 100%;}
        .ant-card-bordered{height: 100%;border: none;}
      }
    }
    .echarts{
        
        padding: 10px 8px;
        border: 1px solid #f0f2f5;
        margin-bottom: 20px;
        :deep(.ant-card-head){
          font-weight: 600;font-size: 14px;padding-left: 6px;
          .ant-card-head-title{padding: 0;}
        }
    }
    .progress{
      :deep(.ant-card-body){
        // display: flex;
        // flex-direction: column;
        // justify-content: space-between;
        height: 300px;
        overflow-y: auto;
        .ant-row{margin-top: 26.66px;}
      }
      :deep(.ant-progress-outer){
        width: 96%;
      }
    }
}
:deep(.ant-progress-text){color: #000000D9;}
:deep(.ant-progress-circle.ant-progress-status-success .ant-progress-text){color: #000000D9;}
// :deep(.ant-card-small > .ant-card-body){min-width: 195px;}
:deep(.ant-page-header-content){min-width: 973px;}
.full-width{width: 100%;}
</style>