<template>
    <div class='contentPadding'>
        <OperateBtn :isViewHide="true" :isQuotaShow="true" isRCShow @add="addProject" @edit="editProject" @delete="delProject" @quota="quotaProject" @rc="downloadRC" />
        <a-divider></a-divider>
        <a-spin :spinning="Spinning">
            <a-page-header title="计算" ghost>
                <div class="content-wrapper">
                    <template v-for="(item,index) in calclist" :key="index" v-if="isHas[0] === true">
                        <div class="item">
                            <a-progress type="dashboard" :width="80"
                            :format="()=>(item.total == -1 ? item.used : (item.total ? ((item.used / item.total) * 100).toFixed() + '%' : '0%'))"
                            :status="statusFilter(item)"
                            :percent="Number(((item.used/item.total)*100).toFixed())" />
                            <p></p><span>{{item.title}}</span><p>已使用 {{index == 2 ? filterMemory(item.used)+' of '+(item.total == -1 ? '无限制' : filterMemory(item.total)) : item.used+' of '+(item.total == -1 ? '无限制' : item.total)}}</p>
                        </div> 
                    </template>
                    <a-empty v-else-if="isHas[0] === false"></a-empty>
                </div>
            </a-page-header>
            <a-divider />
            <a-page-header title="卷">
                <div class="content-wrapper">
                    <template v-for="(item,index) in vollist" :key="index" v-if="isHas[1] === true">
                        <div class="item">
                            <a-progress type="dashboard" :width="80" 
                            :format="()=>(item.total == -1 ? item.used : (item.total ? ((item.used / item.total) * 100).toFixed() + '%' : '0%'))"
                            :status="statusFilter(item)"
                            :percent="Number(((item.used/item.total)*100).toFixed())"/>
                            <p></p><span>{{item.title}}</span><p>已使用 {{index == 2 ? filterGB(item.used)+' of '+(item.total == -1 ? '无限制' : filterGB(item.total)) : item.used+' of '+(item.total == -1 ? '无限制' : item.total)}}</p>
                        </div> 
                    </template>
                    <a-empty v-else-if="isHas[1] === false"></a-empty>
                </div>
            </a-page-header>
            <a-divider />
            <a-page-header title="网络">
                <div class="content-wrapper">
                    <template v-for="(item,index) in netlist" :key="index" v-if="isHas[2] === true">
                        <div class="item">
                            <a-progress type="dashboard" :width="80" 
                            :format="()=>(item.total == -1 ? item.used : (item.total ? ((item.used / item.total) * 100).toFixed() + '%' : '0%'))"
                            :status="statusFilter(item)"
                            :percent="Number(((item.used/item.total)*100).toFixed())"/>
                            <p></p><span>{{item.title}}</span><p>已使用 {{item.used+' of '+(item.total == -1 ? '无限制' : item.total)}}</p>
                        </div> 
                    </template>
                    <a-empty v-else-if="isHas[2] === false"></a-empty>
                </div>
            </a-page-header>
        </a-spin>
    </div>
    <Info ref="projectDialog" :info="info" />
    <Quota ref="quotaDialog" :info="quota" />
    <!-- <Index1 ref="index1Ref" :vminfo="vminfo" v-else-if="cloudType == 'vm'" /> -->
</template>
<script lang='ts' setup>
import Info from "@/views/backend/devops/project/info.vue";
import Quota from "@/views/backend/devops/project/quota.vue";
import OperateBtn from "@/components/operatebtn/operatebtn.vue";
import Index1 from "./index1.vue";
import { projectResource, queryResource } from '@/api/backend/devops/index';
import { computed, getCurrentInstance, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { selectTotalresource } from "@/api/backend/vmware";
import { useRoute } from "vue-router";
import { deleteProject, getProjectInfo } from "@/api/backend/devops/project";
import { menuStore } from "@/store/menu";
import { storeToRefs } from "pinia";
import router from "@/router";
import emiter from "@/utils/Bus";
const route = useRoute()
const {proxy} = getCurrentInstance();
const menu_store = menuStore()
const {cloudIndex,domainIndex} = storeToRefs(menu_store)
const projectDialog = ref()
const quotaDialog = ref()
const index1Ref = ref()
const isHas = ref([])
const Spinning = ref(false);
const projectform = reactive({projectName:''})
const info = reactive({
    isInfo:false,
    isAdd:true,
    isShow:false
})
const quota = reactive({
    isQuota: false
})
const vminfo = reactive({
    freeCpu: 0,
    freeDisk: 0,
    freeMemory: 0,
    poweredOffHost: 0,
    poweredOffVirtual: 0,
    poweredOnHost: 0,
    poweredOnVirtual: 0,
    standByHost: 0,
    standByVirtual: 0,
    sumCpu: 0,
    sumDisk: 0,
    sumMemory: 0,
    usedCpu: 0,
    usedDisk: 0,
    usedMemory: 0
})
const cloudType = ref(localStorage.getItem('cloudType'));
const calclist = ref([
    {name:'server',title:'虚机',used:0,total:0},
    {name:'vcpu',title:'CPU数量',used:0,total:0},
    {name:'ram',title:'内存',used:0,total:0},
])
const vollist = ref([
    {name:'cinderNum',title:'卷',used:0,total:0},
    {name:'backups',title:'卷备份',used:0,total:0},
    {name:'cinderSnapshot',title:'卷快照',used:0,total:0},
    {name:'cinderSize',title:'卷存储',used:0,total:0},
])
const netlist = ref([
    {name:'floatipNum',title:'浮动IP',used:0,total:0},
    {name:'safeGroup',title:'安全组',used:0,total:0},
    {name:'safeRules',title:'安全组规则',used:0,total:0},
    {name:'networkNum',title:'网络',used:0,total:0},
    {name:'portNum',title:'端口',used:0,total:0},
    {name:'routeNum',title:'路由',used:0,total:0},
])
const columns = [
    {title:'项目名称',dataIndex:'projectName'},
    {title:'CPUs',dataIndex:'vcpus'},
    {title:'磁盘',dataIndex:'diskSize'},
    {title:'内存',dataIndex:'ram'},
    {dataIndex:'vcpuHours', slots: { title: 'vcpuHours'} },
    {dataIndex:'diskHours', slots: { title: 'diskHours' }},
    {dataIndex:'memoryHours', slots: { title: 'memoryHours' }},
]
const infolist = ref([
    {id:1,projectName:'项目1',vcpus:8,diskSize:100,ram:8,vcpuHours:11,diskHours:11,memoryHours:11}
])
const statusFilter = computed(()=>(item)=>{
    return ((item.used/item.total)*100) <= 30 ? 'success':(((item.used/item.total)*100) >= 80 ? 'exception' : 'normal')
})
const filterMemory = (data) => {
    if(!data && data !== 0){
        return '-'
    }else{
        if(data < 1024)
        return data + 'MB';
    if(data >= 1024 && data < 1024 * 1024)
        return (data / 1024).toFixed(1) + 'GB';
    if(data >= 1024 * 1024)
        return (data / (1024 * 1024)).toFixed(2) + 'TB';
    }
}
const filterGB = (data) => {
    if(!data && data !== 0){
        return '-'
    }else{
        if(data < 1024)
            return data + 'GB';
        if(data >= 1024)
            return (data / 1024).toFixed(2) + 'TB';
    }
}
const queryresource = async (paramsId) => {
    Spinning.value = true;
    isHas.value = [undefined,undefined,undefined];
    let projectId = paramsId ? paramsId : (route.query.projectId ? route.query.projectId : menu_store.SubmenuFirst[0].children[0].children[0].id);
    let res;
    try{
        res = await queryResource({projectId})
    }catch{
        Spinning.value = false;
    }finally{
        Spinning.value = false;
        if(res.code == 0 && res.data && res.data.length > 0){
            isHas.value[0] = res.data[0]?true:false;
            isHas.value[1] = res.data[1]?true:false;
            isHas.value[2] = res.data[2]?true:false;
            calclist.value.forEach((t,i)=>{
                Object.keys(res.data[0]).forEach((item,index)=>{
                    if(item == t.name){
                        t.used = res.data[0][item][0][0];
                        t.total = res.data[0][item][0][1];
                    }
                })
            })
            vollist.value.forEach((t,i)=>{
                Object.keys(res.data[1]).forEach((item,index)=>{
                    if(item == t.name){
                        t.used = res.data[1][item][0][0] ? res.data[1][item][0][0] : 0;
                        t.total = res.data[1][item][0][1];
                    }
                })
            })
            netlist.value.forEach((t,i)=>{
                Object.keys(res.data[2]).forEach((item,index)=>{
                    if(item == t.name){
                        t.used = res.data[2][item][0];
                        t.total = res.data[2][item][1];
                    }
                })
            })
        }else{
            isHas.value = [false,false,false];
        }
    }
    
}
const projectresource = async () => {
    let cloudId = localStorage.getItem('cloudId')
    let res = await projectResource({cloudId})
    if(res.code == 0){
        infolist.value = res.data;
    }
}
const selectTotalResource = async () => {
    let cloudId = localStorage.getItem('cloudId')
    let res = await selectTotalresource({cloudId});
    if(res.code == 0){
        Object.assign(vminfo,res.data)
    }
}
const getprojectInfo = async () => {
    let res = await getProjectInfo({id:route.query.projectId})
    if(res.code == 0){
        Object.assign(projectform,res.data)
        // projectform res.data
    }
}
const addProject = () => {
    info.isAdd = info.isShow = true;
}
const editProject = async () => {
    info.isAdd = false;
    info.isShow = true;
    let res = await getProjectInfo({id:route.query.projectId})
        if(res.code == 0){
            let record1 = res.data;
        if(record1.enabled == 1)
            record1.enabled = true;
        else
            record1.enabled = false;
            proxy.$nextTick(()=>{

                Object.assign(projectDialog.value.projectform, record1)
            })
    }
    
}
const delProject = () => {
    proxy.$handleDel([route.query.projectId],deleteProject,()=>{
        emiter.emit("ProjectListAPI",{key:'domain_'+route.query.domainId,cloudKey:'cloud_'+route.query.cloudId,type:'project',action:'del'})
        router.push({path:'/admin/devops/menu/domain',query:{domainId:route.query.domainId,cloudId:route.query.cloudId,title:localStorage.getItem("domainName")}})
    })
}
const quotaProject = () => {
    quota.isQuota = true;
    quotaDialog.value.setInfo(route.query.projectId)
}
const downloadRC = () => {
    console.log("rc")
}
const setProject = () => {
    queryresource();
}
watch(()=>router.currentRoute.value,(to,from)=>{
  // console.log('ok')
  if(router.currentRoute.value.path == '/admin/devops/menu/project'){
    setProject()
  }
},{immediate:true,deep:true})
onMounted(() => {
    console.log("neew")
    // if(cloudType.value == 'OPENSTACK'){
    // }
    emiter.on("setProject",setProject)
    if(cloudType.value == 'vm'){
        selectTotalResource();
    }
    // getprojectInfo()
    proxy.$mitt.on('getCloudType',(e)=>{cloudType.value = e;})
    proxy.$nextTick(()=>{

        proxy.$mitt.on('refreshcloudIndex',queryresource)
    })
    proxy.$mitt.on('refreshvmIndex',selectTotalResource)
})
onUnmounted(()=>{
    console.log('un')
})
</script>
<style lang='scss' scoped>
.contentPadding{margin: 0 16px;padding: 8px 0;}
.item{margin: 10px 50px;text-align: center;p{white-space: nowrap;}}
.ant-page-header{padding: 0 24px;}
:deep(.ant-page-header-heading){
    background-color: #fafafa;
    .ant-page-header-heading-left{margin: 0;}
}
.ant-page-header:not(.header-title){
  :deep(.ant-page-header-heading){
    ::before{
      content: "";
      display: inline-block;
      width: 4px;
      height: 100%;
      margin-right: 4px;
      vertical-align: text-bottom;
      background-color: #1890ff;
    }
  }
}
.content-wrapper{display: flex;height: 184px;}
.ant-table-wrapper{padding: 0 24px;}
.ant-divider{margin-top: 8px;}
:deep(.ant-progress-circle.ant-progress-status-exception .ant-progress-text){color: #000000D9;}
:deep(.ant-progress-circle.ant-progress-status-success .ant-progress-text){color: #000000D9;}
</style>