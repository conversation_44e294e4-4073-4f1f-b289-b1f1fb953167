<template>
    <div class="cloudContent">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform">
                    <a-form-item label="域名称">
                        <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.domainName" allowClear />
                    </a-form-item>
                    <a-form-item>
                        <a-button type="primary" @click="handleSearch"> {{ $t("m.search") }} </a-button>
                    </a-form-item>
                    <a-form-item>
                        <a-button @click="handleAllReset"> {{ $t("m.reset") }} </a-button>
                    </a-form-item>
                </a-form>
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd">新增 </a-button>
                </a-row>
                <a-table :columns="columns" row-key="id" :data-source="imagelist" :pagination="pagination" @change="changeTable">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #imageSize={record}>
                        {{(record.imageSize/(1024*1024*1024)).toFixed(2)}}
                    </template>
                    <template #action={record}>
                        <a-button type="link" class="button_E"   @click="handleEdit(record)">修改</a-button>
                        <a-button type="link" class="button_D" @click="$handleDel([record.id],deleteOpenst,getList)">删除</a-button>
                    </template>
                </a-table>
                <Info ref="imageDialog" :info="info" @getlist="getList" />
            </div>
        </div>
    </div>
</template>
<script lang='ts' setup>
import Info from "./info.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { getOpenstList, deleteOpenst } from "@/api/backend/devops/domain";
import { useRoute } from "vue-router";
const { proxy } = getCurrentInstance();
const route = useRoute()
const imageDialog = ref(null);
const loading = ref(false);
const imagelist = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false
})
const searchform = reactive({
    cloudId:route.query.cloudId,
    domainName:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '云平台域', dataIndex: 'domainName', key: 'id',align:'left'},
    {title: '描述', dataIndex: 'description', key: 'id',align:'center'},
    {title: '修改人', dataIndex: 'updateUserName', key: 'id',align:'center'},
    {title: '修改时间', dataIndex: 'updateTime', key: 'id',align:'center'},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
const getList = async () => {
    // searchform.cloudId = localStorage.getItem('cloudId');
    searchform.cloudId = route.query?.cloudId;
    imagelist.value = await proxy.$getList(loading, getOpenstList, searchform, pagination, getList )
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    domainName: ""
  })
  getList();
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
}

const handleEdit = (record) => {
    info.isAdd = false;
    info.isShow = true;
    // console.log('编辑',imageDialog.value.cloudform)
    Object.assign(imageDialog.value.cloudform, record)
}

onMounted(() => {
    getList()
    // if(route.path == '/admin/devops/openstack')
    proxy.$mitt.on('getdomainlist',getList)
    })
</script>
<style lang='scss' scoped>
</style>