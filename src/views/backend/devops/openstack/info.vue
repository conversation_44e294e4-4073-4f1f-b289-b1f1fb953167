<template>
    <div class=''>
        <a-modal centered
            v-model:visible="info.isShow"
            :title="isInfo ? '查看域' :　(info.isAdd ? '新增域' : '修改域')"
            ok-text="提交"
            cancel-text="取消"
            @ok="save"
            @cancel="cancel"
            width="608.8px"
            :maskClosable="isInfo"
            :getContainer="modalBindNode"
            >
                <a-form :model="domainform" ref="cloudformRef" :rules="rules" :label-col="{span:4}">

                    <!-- <a-form-item label="云平台" name="cloudId">
                        <a-select
                            v-model:value="domainform.cloudId"
                            placeholder="请选择"
                            allowClear>
                            <a-select-option v-for="(item,index) in options" :key="index" :value="item.id">{{item.cloudName}}</a-select-option>
                        </a-select>
                    </a-form-item> -->

                    <a-form-item label="云平台域" name="domainName">
                        <a-input v-model:value.trim="domainform.domainName" placeholder="请输入" :disabled="isInfo" allow-clear />
                    </a-form-item>
                   
                    <a-form-item label="描述" name="description">
                        <!-- <a-input v-model:value.trim="domainform.description"></a-input> -->
                         <a-textarea v-model:value.trim="domainform.description" placeholder="请输入" allow-clear />
                    </a-form-item>
                   
                    
                </a-form>
            </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {  selectCloudList } from "@/api/backend/cloud";
import { saveOpenst, updateOpenst } from "@/api/backend/devops/domain";
import { useRoute } from 'vue-router';
import { menuStore } from '@/store/menu';
import { storeToRefs } from 'pinia';
import router from '@/router';
const menu_store = menuStore()
const {cloudIndex,domainIndex,projectIndex} = storeToRefs(menu_store);
const { proxy } = getCurrentInstance()
const emit = defineEmits(['getlist'])
const route = useRoute()
const cloudformRef = ref(null);
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isAdd: true,
        isShow: false
      };
    }
  },
})
const options = ref([])
const isInfo = ref(false)
const defaultform = {
    "domainName":"",
    "description":"",
    "cloudId": router.currentRoute.value.query.cloudId,
    "projectId": router.currentRoute.value.query.projectId,
   
}
const domainform = reactive({
    "domainName":"",
    "description":"",
    "cloudId": router.currentRoute.value.query.cloudId,
    "projectId": router.currentRoute.value.query.projectId,
})

const validateId = (rule, value) => {
  if (!value) {
    return Promise.reject("请输入上级菜单");
  } else {
    return Promise.resolve();
  }
}

const rules = reactive({
    domainName:[{ required:true,message:'请输入',trigger:'change'}],
    // description:[{required:true, message:'请输入',trigger:'change'}],
    cloudId:[{required:true, validator:validateId,trigger:'change'}],
})

const save = () => {
  domainform.cloudId = route.query.cloudId;
    proxy.$handleSave(cloudformRef.value, saveOpenst, updateOpenst, props, domainform, ()=>{
      cancel();
      if(route.path == '/admin/devops/menu/cloud')
        proxy.$mitt.emit('getDomainCount',route.query.cloudId)
      proxy.$mitt.emit('SelectDomainList',{cloudId:route.query.cloudId,index:cloudIndex.value,isAdd:props.info.isAdd})
      
    })
}
const cancel = () => {
    isInfo.value = false
    cloudformRef.value.resetFields();
    Object.assign(domainform,defaultform)
}
const selectCloudlist = () => {
  console.log("new")
  selectCloudList().then((res)=>{
    options.value = res.data;
  })
}
onMounted(() => {})
defineExpose({domainform,isInfo,selectCloudlist})
</script>
<style lang='scss' scoped>
</style>