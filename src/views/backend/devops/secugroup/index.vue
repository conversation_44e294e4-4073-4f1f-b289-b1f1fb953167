<template>
    <div class="cloudContent" v-if="!info.isRule">
        <!-- <Menu @refreshList="(e)=>getList(e)" /> -->
        <div class="cloudRight">
            <div class="buttonPadding">
                <a-form layout="inline" :model="searchform" class="searchform">
                <a-form-item label="安全组名称">
                    <a-input placeholder="请输入" @pressEnter="handleSearch" v-model:value="searchform.groupName" allowClear />
                </a-form-item>
                </a-form>
                <MoreSearch @search="handleSearch" @reset="handleAllReset" />
            </div>
            <div class='innerPadding'>
                <a-row class="buttonGroup">
                    <a-button type="primary" class="btnMargin" @click="handleAdd">新增 </a-button>
                    <a-button @click="$handleDel(selectRowIds,deleteSecugroup,()=>{selectRowIds = [];getList()},'将同时删除该安全组下的全部规则，是否继续？')" :disabled="selectRowIds.length <= 0">
                        批量删除
                    </a-button>
                </a-row>
                <a-table :row-selection="rowSelection" :columns="columns" row-key="id" :data-source="secugrouplist" :pagination="pagination" @change="changeTable" :scroll="{x:true}">
                    <template #index={record,index}>
                        {{index+1+(pagination.pageSize * (pagination.current-1))}}
                    </template>
                    <template #groupName={record}> 
                        <a @click="handleRule(record)">{{record.groupName}}</a>
                        <!-- <router-link :to="'/admin/devops/rule?id='+record.id">{{record.groupName}}</router-link> -->
                    </template>
                    <template #secugroupSize={record}>
                        {{(record.secugroupSize/(1024*1024*1024)).toFixed(2)}}
                    </template>
                    <template #action={record}>
                        <a-button class="button_E" @click="handleEdit(record)">修改</a-button>
                        <a-button class="button_D" @click="$handleDel([record.id],deleteSecugroup,getList,'将同时删除该安全组下的全部规则，是否继续？')">删除</a-button>
                    </template>
                </a-table>
                <Info ref="secugroupDialog" :info="info" @getlist="getList" />
            </div>
        </div>
    </div>
    <Rule :info="info" ref="ruleRef" v-else />
</template>
<script lang='ts' setup>
import MoreSearch from "@/components/moresearch/moresearch.vue";
import Info from "./info.vue";
import Rule from "../rule/index.vue";
import Menu from "@/components/cloudmenu/cloudmenu.vue";
import { PlusOutlined } from "@ant-design/icons-vue"
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref } from 'vue';
import { getSecugroupList, deleteSecugroup } from "@/api/backend/devops/security";
import { buttonExpand, handleWidth } from "@/utils/moreform";
import { useRoute } from "vue-router";
import router from "@/router";
const { proxy } = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(['changes'])
const props = defineProps({
    selectedRowKeys: {
        type:Array,
        default(){
            return []
        }
    }
})
const secugroupDialog = ref(null);
const ruleRef = ref(null);
const loading = ref(false);
const secugrouplist = ref([]);
let selectRowIds: string[] = ref([]);
const info = reactive({
    isAdd:true,
    isShow:false,
    isRule:false
})
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    projectId:router.currentRoute.value.query.projectId,
    groupName:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectRowIds.value,
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      selectRowIds.value = [...selectedRowKeys];
    }
  };
});
const columns = [
    {title: '序号', dataIndex: 'index', slots: { customRender: 'index' }, key: 'id' ,width:60,align:'center'},
    {title: '名称', dataIndex: 'groupName', key: 'id',slots: { customRender: 'groupName' },align:'left'},
    {title: '描述', dataIndex: 'description', key: 'id',align:'center'},
    {title: '修改时间', dataIndex: 'updateTime', key: 'id',align:'center',ellipsis:true},
    {title: '操作', dataIndex: 'action', key: 'id', slots: { customRender: 'action' },width:200 }
];
const getList = async () => {
    // searchform.cloudId = e;
    searchform.cloudId = localStorage.getItem('cloudId');
    secugrouplist.value = await proxy.$getList(loading, getSecugroupList, searchform, pagination, getList )
}
// 查询
const handleSearch: any = () => {
  searchform.pageIndex = "1";
  searchform.pageSize = pagination.pageSize;
  getList();
};
const handleAllReset: any = () => {
    Object.assign(searchform,{
    pageSize: 10,
    pageIndex: 1,
    groupName: ""
  })
//   getList();
}
const handleRule = (record) => {
    info.isRule = true;
    proxy.$nextTick(()=>{

        ruleRef.value.getList(record.id)
        ruleRef.value.groupId = record.id
        ruleRef.value.groupName = record.groupName
    })
}
const handleAdd = () => {
    info.isAdd = info.isShow = true;
}
const handleEdit = (record) => {
    info.isAdd = false;
    info.isShow = true;
    Object.assign(secugroupDialog.value.groupform, record)
}
onMounted(() => {
    getList()
    if(route.path == '/admin/devops/secugroup')
        proxy.$mitt.on('getlist',getList)
    nextTick(()=>{
        handleWidth()
    })
})
</script>
<style lang='scss' scoped>
.btnMargin {
  margin-right: 10px;
}
</style>