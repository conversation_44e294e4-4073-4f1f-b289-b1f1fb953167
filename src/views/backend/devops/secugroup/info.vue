<template>
    <div class=''>
        <a-modal 
        v-model:visible="info.isShow"
        @ok="handleSave"
        @cancel="cancel"
        ok-text="提交"
        cancel-text="取消"
        :maskClosable="false"
        centered
        :getContainer="modalBindNode"
        >
            <template #title>
                <div style="display:flex;justify-content:space-between;margin-right:36px;align-items:center">
                    <span>{{info.isAdd ? '新增':'修改'}}</span>
                    <a-popover trigger="click" placement="leftTop">
                        <template #content>
                            <pre style="width:500px;white-space:pre-wrap;max-height:530px;overflow-y:auto;margin-bottom:0">{{info.isAdd ? content.securitygroup_add : content.securitygroup_update}}</pre>
                        </template>
                        <InfoCircleOutlined  />
                    </a-popover>
                </div>
            </template>
            <a-form :model="groupform" ref="groupForm" :rules="rules" :labelCol="{span:3}">
                <a-form-item label="名称" name="groupName">
                    <a-input v-model:value.trim="groupform.groupName" placeholder="请输入" allowClear></a-input>
                </a-form-item>
                <a-form-item label="描述" name="description">
                    <a-textarea v-model:value.trim="groupform.description" placeholder="请输入" allowClear></a-textarea>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import {saveSecugroup,updateSecugroup } from "@/api/backend/devops/security"
import { selectDictList } from '@/api/backend/systems/dictionary';
import {content} from "@/common/explain/modal";
import { useRoute } from 'vue-router';
const {proxy} = getCurrentInstance();
const route = useRoute()
const emit = defineEmits(['getlist'])
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isAdd:true,
                isShow:false
            }
        }
    }
})
const modalBindNode = ref();
const groupForm = ref();
const defaultform = {
    cloudId:localStorage.getItem('cloudId'),
    projectId:route.query.projectId,
    groupName:'',
    description:''
}
const groupform = reactive({
    cloudId:localStorage.getItem('cloudId'),
    projectId:route.query.projectId,
    groupName:'',
    description:''
})
const rules = {
    groupName:[{required:true, message:'请输入',trigger:'change'}],
}
const handleSave = () => {
    proxy.$handleSave(groupForm.value, saveSecugroup, updateSecugroup, props,groupform, ()=>{cancel();emit('getlist')})
}
const cancel = () => {
    props.info.isShow = false
    // isInfo.value = false
    groupForm.value.resetFields()
    Object.assign(groupform,defaultform)
}
onMounted(() => {modalBindNode.value = document.getElementsByClassName('full-modal')[0] ? document.getElementsByClassName('full-modal ant-modal-body')[0] : proxy.modalBindNode;})
defineExpose({groupform})
</script>
<style lang='scss' scoped>
</style>