<template>
<div>
  <a-modal title="初始化记录" centered v-model:visible="info.isHistory" width="100% !important" wrap-class-name="full-modal" :footer="null" :getContainer="modalBindNode">
    <a-table :data-source="historylist" :columns="columns" row-key="id" @change="changeTable" :pagination="pagination" :loading="loading">
        <template #index={record,index}>
            {{index+1+(pagination.pageSize * (pagination.current-1))}}
        </template>
    </a-table>
  </a-modal>
</div>
</template>
<script setup lang="ts">
import { queryInitHistory } from "@/api/backend/cloud";
import router from "@/router";
import { getCurrentInstance, computed, onMounted, ref, reactive } from "vue";
const { proxy }: any = getCurrentInstance();
const props = defineProps({
  info: {
    type: Object,
    default() {
      return {
        isHistory: false
      };
    }
  }
});
const columns = [
    {title:'序号',dataIndex:'index',slots:{customRender:'index'},width:120,align:'center'},
    {title:'UUID',dataIndex:'uuid',width:300},
    {title:'结果',dataIndex:'initResult',width:150,align:'center'},
    {title:'备注',dataIndex:'resultInfo',ellipsis:true},
    {title:'执行人',dataIndex:'createUserName',width:200},
    {title:'开始时间',dataIndex:'createTime',width:200},
    {title:'完成时间',dataIndex:'updateTime',width:200},

]
const searchform = reactive({
    cloudId:router.currentRoute.value.query.cloudId,
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
    position:'top',
    // 分页配置器
    pageSize: 10, // 一页的数据限制
    current: 1, // 当前页
    total: 0, // 总数
    hideOnSinglePage: false, // 只有一页时是否隐藏分页器
    showQuickJumper: true, // 是否可以快速跳转至某页
    showSizeChanger: true, // 是否可以改变 pageSize
    pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
    showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
    searchform.pageIndex = pagination.current;
    searchform.pageSize = pagination.pageSize;
    getList();
};
const historylist = ref([]);
const loading = ref(false);
const getList = async () => {
    historylist.value = await proxy.$getList(loading, queryInitHistory, searchform, pagination, getList )
}
onMounted(()=>{
})
defineExpose({getList})
</script>
<style lang="scss" scoped>
.full-modal {
  .ant-modal-body {
    flex: 1;
  }
}
</style>
