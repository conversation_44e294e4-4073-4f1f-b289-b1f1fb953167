<template>
    <div class=''>
        <a-modal centered
            v-model:visible="info.isShow"
            title="新增云平台"
            width="608.8px"
            @cancel="cancel"
            :bodyStyle="{height:'500px',overflowY:'auto',position: 'relative'}"
            :maskClosable="false"
            :getContainer="modalBindNode"
            >
            <template #footer>
                <a-button @click="()=>{current = 1}" v-if="current==2">上一步</a-button>
                <a-button @click="cancel">取消</a-button>
                <!-- <a-button @click="checkConn" v-if="current==2">测试连接</a-button> -->
                <a-button type="primary" v-if="current==2" @click="save">提交</a-button>
                <a-button type="primary" :disabled="!cloudform.cloudType" @click="()=>{current = 2}" v-if="current == 1">下一步</a-button>
            </template>
                <a-form :model="cloudform" ref="cloudformRef" :rules="rules" :layout="current == 1 ? 'vertical' : 'horizontal'" :labelCol="{ span: 7}">
                    <a-form-item label="请选择云平台类型" name="cloudType" v-if="current==1" :labelCol="{ span: 6}">
                        <a-radio-group v-model:value="cloudform.cloudType" @change="changeRadio">
                            <a-radio-button v-for="(item,index) in radioOptions" :key="index" :value="item.value" :class="item.className" :style="{backgroundImage:item.className!='OPENSTACK' ? 'url('+item.url+')':''}"></a-radio-button>
                        </a-radio-group>
                    </a-form-item>
                    <i v-if="current==1" style="position:absolute;bottom:0">说明：UStack支持对OpenStack的Pike、Queens、Rocky、Stein、Train、Ussuri、Victoria、Wallaby等多个版本的纳管。</i>
                    <a-form-item label="名称" name="cloudName" v-if="current==2">
                        <a-input v-model:value.trim="cloudform.cloudName" :disabled="isInfo" placeholder="请输入" allow-clear />
                    </a-form-item>
                    <a-form-item label="云平台地址" name="cloudAddress" v-if="current==2">
                        <a-input v-model:value.trim="cloudform.cloudAddress" :disabled="isInfo" :placeholder="'格式：'+placeholder" allow-clear></a-input>
                    </a-form-item>
                    <a-form-item label="域" name="cloudDomain" v-if="current==2 && cloudform.cloudType != '13'">
                        <a-input v-model:value.trim="cloudform.cloudDomain" :disabled="isInfo" placeholder="请输入" allow-clear />
                    </a-form-item>
                    <a-form-item label="项目" name="cloudProject" v-if="current==2 && cloudform.cloudType != '13'">
                        <a-input v-model:value.trim="cloudform.cloudProject" :disabled="isInfo" placeholder="请输入" allow-clear></a-input>
                    </a-form-item>
                    <a-form-item label="账号" name="cloudUser" v-if="current==2">
                        <a-input v-model:value.trim="cloudform.cloudUser" :disabled="isInfo" placeholder="请输入" allow-clear></a-input>
                    </a-form-item>
                    <a-form-item label="密码" name="cloudPassword" v-if="current==2">
                        <a-input-password v-model:value.trim="cloudform.cloudPassword" :disabled="isInfo" placeholder="请输入" allow-clear></a-input-password>
                    </a-form-item>
                    <a-form-item label="版本" name="cloudVersion" v-if="current==2 && cloudform.cloudType != '13'">
                        <!-- <a-input v-model:value.trim="cloudform.cloudVersion"></a-input> -->
                        <a-radio-group v-model:value="cloudform.cloudVersion" :disabled="isInfo">
                            <a-radio value="V3"> V3 </a-radio>
                            <a-radio value="V2"> V2 </a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item label="负责人" name="manageUserId" v-if="current==2">
                        <a-select v-model:value="cloudform.manageUserId" 
                        :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}" 
                        placeholder="请选择" 
                        show-search 
                        allow-clear 
                        mode="multiple"
                        :getPopupContainer="triggerNode => triggerNode.parentNode"
                        >
                            <a-select-option v-for="(item,index) in userlist" :key="index" :value="item.userId">{{item.userName}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <template v-if="cloudform.cloudType != 13">
                        <a-form-item label="是否开启监控服务" name="openPrometheus" v-if="current==2">
                            <a-switch v-model:checked="cloudform.openPrometheus" @change="(checked)=>changePrometheus(checked,'openPrometheus')" checked-children="是" un-checked-children="否" />
                        </a-form-item>
                        <a-form-item label="Prometheus地址" name="prometheusUrl" v-if="current==2 && cloudform.openPrometheus">
                            <a-input v-model:value.trim="cloudform.prometheusUrl" :disabled="isInfo" placeholder="请输入" allow-clear></a-input>
                        </a-form-item>
                        <!-- <a-form-item label="是否开启裸金属服务" name="openBaremetal" v-if="current==2">
                            <a-switch v-model:checked="cloudform.openBaremetal" checked-children="是" un-checked-children="否" />
                        </a-form-item> -->
                        <!-- <a-form-item label="裸金属服务API地址" name="baremetalUrl" v-if="current==2 && cloudform.openBaremetal">
                            <a-input v-model:value.trim="cloudform.baremetalUrl" :disabled="isInfo" placeholder="请输入" allow-clear></a-input>
                        </a-form-item> -->
                        <a-form-item label="是否开启集群管理" name="openCluster" v-if="current==2">
                            <a-switch v-model:checked="cloudform.openCluster" @change="(checked)=>changePrometheus(checked,'openCluster')" checked-children="是" un-checked-children="否" />
                        </a-form-item>
                        <a-form-item label="是否开启存储管理" name="openCeph" v-if="current==2">
                            <a-switch v-model:checked="cloudform.openCeph" checked-children="是" un-checked-children="否" />
                        </a-form-item>
                        <a-form-item label="存储副本数量" name="storageCopyNum" v-if="current==2 && cloudform.openCeph">
                            <a-input-number v-model:value.trim="cloudform.storageCopyNum" :min="1" :step="1" :precision="0" :disabled="isInfo" placeholder="请输入" style="width:100%" allow-clear></a-input-number>
                        </a-form-item>
                        <a-form-item label="CEPH地址" name="cephUrl" v-if="current==2 && cloudform.openCeph">
                            <a-input v-model:value.trim="cloudform.cephUrl" :disabled="isInfo" placeholder="https://127.0.0.1:8443/api" allow-clear></a-input>
                        </a-form-item>
                        <a-form-item label="CEPH账号" name="cephUser" v-if="current==2 && cloudform.openCeph">
                            <a-input v-model:value.trim="cloudform.cephUser" :disabled="isInfo" placeholder="请输入" allow-clear></a-input>
                        </a-form-item>
                        <a-form-item label="CEPH密码" name="cephPassword" v-if="current==2 && cloudform.openCeph">
                            <a-input-password v-model:value.trim="cloudform.cephPassword" :disabled="isInfo" placeholder="请输入" allow-clear></a-input-password>
                        </a-form-item>
                    </template>
                    <!-- <a-form-item label="集群管理API地址" name="clusterUrl" v-if="current==2 && cloudform.openCluster">
                        <a-input v-model:value.trim="cloudform.clusterUrl" :disabled="isInfo" placeholder="请输入" allow-clear></a-input>
                    </a-form-item> -->
                    <a-form-item label="描述" name="cloudInfo" v-if="current==2">
                        <a-textarea v-model:value.trim="cloudform.cloudInfo" :disabled="isInfo" placeholder="请输入" allow-clear />
                    </a-form-item>
                </a-form>
            </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { saveCloud, testConnect, updateCloud } from "@/api/backend/cloud";
import { queryWorker } from '@/api/backend/systems/user';
import vm from "@/assets/logo/vm.png";
import ali from "@/assets/logo/ali.png";
import { message } from 'ant-design-vue';
import { setCephToken } from '@/utils/auth';
import { useRoute } from 'vue-router';
import emiter from '@/utils/Bus';
const { proxy } = getCurrentInstance()
const emit = defineEmits(['callback','getlist'])
const cloudformRef = ref(null);
const route = useRoute()
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isShow: false
      };
    }
  },
  options:{
    type:Array,
    default(){
        return []
    }
  },
  userlist:{
    type:Array,
    default(){
        return []
    }
  }
})
// const userlist = ref([])
const placeholder = ref('请输入');
const isInfo = ref(false)
const current = ref(1)
const defaultform = {
    "cloudDomain": "",
    "cloudAddress": "",
    "cloudName": "",
    "cloudPassword": "",
    "cloudProject": "",
    "cloudType": '',
    "cloudUser": "",
    "cloudVersion": "V3",
    "manageUserId":undefined,
    "cloudInfo":'',
    "openPrometheus":false,
    "openBaremetal":false,
    "openCluster":false,
    "prometheusUrl":'',
    "baremetalUrl":'',
    "clusterUrl":'',
    "openCeph":false,
    "storageCopyNum":"",
    "cephUrl":"",
    "cephUser":"",
    "cephPassword":""
}
const cloudform = reactive({
    "cloudDomain": "",
    "cloudAddress": "",
    "cloudName": "",
    "cloudPassword": "",
    "cloudProject": "",
    "cloudType": '',
    "cloudUser": "",
    "cloudVersion": "V3",
    "manageUserId":undefined,
    "cloudInfo":'',
    "openPrometheus":false,
    "openBaremetal":false,
    "openCluster":false,
    "prometheusUrl":'',
    "baremetalUrl":'',
    "clusterUrl":'',
    "openCeph":false,
    "storageCopyNum":"",
    "cephUrl":"",
    "cephUser":"",
    "cephPassword":""
})

const radioOptions = ref([
    {value:'1',className:'OPENSTACK',placeholder:'http://***********:5000/v3'},
    {value:'13',className:'vm',url:vm,placeholder:'https://************/sdk'},
    // {value:'2',className:'ali'},
    // {value:'3',className:'tencent'},
    // {value:'4',className:'huawei'},
    // {value:'5',className:'amason'},
    // {value:'6',className:'micro'},
    // {value:'7',className:'jd'},
    // {value:'8',className:'uc'},
    // {value:'9',className:'qc'},
    // {value:'10',className:'baidu'},
    // {value:'11',className:'google'},
    // {value:'12',className:'gc'},
])

const validateType = (rule, value) => {
  if (!value) {
    return Promise.reject("请选择指定云平台");
  } else {
    return Promise.resolve();
  }
}

const rules = reactive({
    cloudName:[{required:true, message:'请输入',trigger:'change'}],
    cloudDomain:[{required:cloudform.cloudType != '13', message:'请输入',trigger:'change'}],
    cloudAddress:[{required:true, message:'请输入',trigger:'change'}],
    cloudPassword:[{required:true, message:'请输入',trigger:'change'}],
    cloudProject:[{required:cloudform.cloudType != '13', message:'请输入',trigger:'change'}],
    cloudType:[{required:cloudform.cloudType != '13', validator:validateType, trigger:'change'}],
    cloudUser:[{required:true, message:'请输入',trigger:'change'}],
    cloudVersion:[{required:cloudform.cloudType != '13', message:'请输入',trigger:'change'}],
    manageUserId:[{type:'array',required:true, message:'请输入',trigger:'change'}],
    prometheusUrl:[{required:true, message:'请输入prometheusUrl',trigger:'change'}],
    storageCopyNum:[{required:true,type:'number', message:'请输入副本数量',trigger:'change'}],
    cephUrl:[{required:true, message:'请输入CEPH地址',trigger:'change'}],
    cephUser:[{required:true, message:'请输入CEPH账号',trigger:'change'}],
    cephPassword:[{required:true, message:'请输入CEPH密码',trigger:'change'}]
})
const changeRadio = (e,b) => {
    let defaultform1 = {...defaultform};
    defaultform1.cloudType = cloudform.cloudType;
    Object.assign(cloudform,defaultform1)
    radioOptions.value.forEach(item => {
        if(item.value+'' == e.target.value){
            placeholder.value = item.placeholder;
        }
    });
}
const checkConn = () => {
    cloudformRef.value.validate().then(async ()=>{
        let cloudform1 = {...cloudform}
        cloudform1.manageUserId = '-100,'+cloudform1.manageUserId.join(',')+',-100';
        if(!cloudform1.openPrometheus){
            cloudform1.prometheusUrl = null;
            cloudform1.openPrometheus = 0;
        }else
            cloudform1.openPrometheus = 1;
        if(!cloudform1.openBaremetal){
            cloudform1.baremetalUrl = null;
            cloudform1.openBaremetal = 0;
        }else
            cloudform1.openBaremetal = 1;
        if(!cloudform1.openCluster){
            cloudform1.clusterUrl = null;
            cloudform1.openCluster = 0;
        }else
            cloudform1.openCluster = 1;
        if(!cloudform1.openCeph){
            cloudform1.openCeph = 0;
            cloudform1.storageCopyNum = null;
            cloudform1.cephUrl = null;
            cloudform1.cephUser = null;
            cloudform1.cephPassword = null;
        }else
            cloudform1.openCeph = 1;
        let res = await testConnect(cloudform1)
        if(res.code == 0){
            proxy.$handleSave(cloudformRef.value, saveCloud, updateCloud, false, cloudform1, ()=>{
                // emit('callback',cloudform1.openCeph);
                if(route.path == '/admin/index')
                    emit('getlist');
                else{
                    emiter.emit("setMenu", {type:'cloud', action:'add'})
                    emiter.emit("DomainListAPI","cloud_"+router.currentRoute.value.query.cloudId);
                }
                cancel();
                },()=>{
                    cancel();
                })
        }
    }).catch((err)=>{
        if(err.errorFields){
            message.error("请补全必填信息")
        }
    })
}
const save = () => {
    
    checkConn()
    
}
const cancel = () => {
    current.value = 1;
    props.info.isShow = false
    isInfo.value = false
    cloudformRef.value.resetFields();
    Object.assign(cloudform,defaultform)
}
const changePrometheus = (checked,key)=>{
    if(!checked && key == 'openPrometheus'){
        cloudform.openCluster = checked;
    }
    if(checked && key == 'openCluster'){cloudform.openPrometheus = checked}
}
onMounted(() => {})
defineExpose({cloudform,isInfo,radioOptions})
</script>
<style lang='scss' scoped>
.ant-radio-button-wrapper{
    width: 162px;
    height: 76px;
    margin: 10px;
    background-position:50%;
    background-repeat:no-repeat;
    
    background-size:132px;
    background-color:#fff;
    border: 1px solid #d9d9d9;
    &.OPENSTACK{
        background-image:url(@/assets/logo/OPENSTACK.jpeg);
    }
    &.amason{
        background-size: 66px;
    }
    &.ant-radio-button-wrapper-checked{
        background-color: rgb(24 144 255 / 8%);
        background-blend-mode: multiply;
        box-shadow: none;
        border-color: rgb(24 144 255);
    }
    &::before{width: 0;}
}
</style>