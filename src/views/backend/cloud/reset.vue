<template>
  <a-modal 
  v-model:visible="info.isSync" 
  title="初始化" 
  :getContainer="modalBindNode" 
  class="cloud-reset"
  :body-style="{
    height: '500px',
    overflowY: 'auto'
    }"
    @ok="handleSave"
    ok-text="提交"
  >
  <a-tree
    checkable
    :tree-data="treeData"
    :selectable="false"
    v-model:checkedKeys="checkedKeys"
    defaultExpandAll
  >
  </a-tree>
  </a-modal>
</template>
<script lang='ts' setup>
import { onMounted, ref } from 'vue';
import treeData from "@/common/menus/cloud.json";
import { initCloud } from '@/api/backend/cloud';
import { message } from 'ant-design-vue';
import router from '@/router';
const emit = defineEmits(['sync']);
const props = defineProps({
  info: {
    type: Object,
    default() {
      return {
        isSync: false
      };
    }
  }
});
const checkedKeys = ref([]);
const parentKeys = ["compute", "net", "storage", "other"];
const handleSave = () => {
  if(checkedKeys.value.length <= 0){
    message.warning("请至少选择一项进行初始化操作");
    return;
  }
  let saveKeys = checkedKeys.value.filter((item,index)=>!parentKeys.includes(item))
  // initCloud({id:router.currentRoute.value.query.cloudId,moduleList:saveKeys.join(',')}).then((res)=>{
    emit("sync",{id:router.currentRoute.value.query.cloudId,moduleList:saveKeys.join(',')});
  // })
}
const setInfo = () => {
  let tempkeys = [];
  treeData.map((item,index)=>{
    item.children.map((it,ii)=>{
      tempkeys.push(it.key);
    })
  })
  checkedKeys.value = tempkeys;
}
onMounted(() => {
})
defineExpose({setInfo})
</script>
<style lang='scss' scoped>
:deep(ul.ant-tree-child-tree-open){
  padding: 6px 0 8px 18px;
  margin-top: 10px;
  border-radius: 5px;
  background-color: rgb(247, 247, 247);
  &:before,&:after{
    display: table;
    content: '';
  }
  &:after {
    clear: both;
  }
  li{
    float: left;
    width: 154px;
    &:first-child{padding-top: 4px;}
  }
}
</style>