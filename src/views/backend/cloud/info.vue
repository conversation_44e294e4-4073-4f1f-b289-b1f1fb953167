<template>
    <div class=''>
        <a-modal centered
            v-model:visible="info.isShow"
            :title="info.isInfo ? '查看云平台' :'修改云平台'"
            ok-text="提交"
            cancel-text="取消"
            @ok="()=>{info.isInfo ? cancel() : save()}"
            @cancel="cancel"
            width="608.8px"
            :body-style="{height:'456px',overflowY:'auto'}"
            :maskClosable="info.isInfo"
            :getContainer="modalBindNode"
            >
            <template #footer>
                <a-button v-if="!info.isInfo" @click="cancel">取消</a-button>
                <a-button type="primary" v-if="!info.isInfo" @click="save">提交</a-button>
                <a-button type="primary" v-else @click="cancel">关闭</a-button>
            </template>
                <a-form :model="cloudform" ref="cloudformRef" :rules="rules" :label-col="{span:7}">
                    <a-form-item label="名称" name="cloudName">
                        <a-input v-model:value.trim="cloudform.cloudName" :disabled="info.isInfo" allow-clear/>
                    </a-form-item>
                    <a-form-item label="所属域" name="cloudDomain">
                        <a-input v-model:value.trim="cloudform.cloudDomain" :disabled="info.isInfo" allow-clear/>
                    </a-form-item>
                    <a-form-item label="云地址" name="cloudAddress">
                        <a-input v-model:value.trim="cloudform.cloudAddress" :disabled="info.isInfo" allow-clear></a-input>
                    </a-form-item>
                    <a-form-item label="账号" name="cloudUser">
                        <a-input v-model:value.trim="cloudform.cloudUser" :disabled="info.isInfo" allow-clear></a-input>
                    </a-form-item>
                    <a-form-item label="密码" name="cloudPassword">
                        <a-input-password v-model:value.trim="cloudform.cloudPassword" :disabled="info.isInfo" allow-clear></a-input-password>
                    </a-form-item>
                    <a-form-item label="项目" name="cloudProject">
                        <a-input v-model:value.trim="cloudform.cloudProject" :disabled="info.isInfo" allow-clear></a-input>
                    </a-form-item>
                    <a-form-item label="类型" name="cloudType">
                        <a-select
                            v-model:value="cloudform.cloudType"
                            placeholder="请选择"
                            allowClear
                            disabled>
                            <a-select-option v-for="(item,index) in options" :key="index" :value="Number(item.dictValue)">{{item.dictLabel}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <a-form-item label="版本" name="cloudVersion">
                        <!-- <a-input v-model:value.trim="cloudform.cloudVersion"></a-input> -->
                        <a-radio-group v-model:value="cloudform.cloudVersion" :disabled="info.isInfo">
                            <a-radio value="V3"> V3 </a-radio>
                            <a-radio value="V2"> V2 </a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item label="负责人" name="manageUserId">
                        <a-select v-model:value="cloudform.manageUserId" 
                        :filter-option="(input, option) => { return option.children[0].children.indexOf(input) !== -1}" 
                        show-search 
                        allow-clear 
                        mode="multiple" 
                        :disabled="info.isInfo"
                        :getPopupContainer="triggerNode => triggerNode.parentNode"
                        >
                            <a-select-option v-for="(item,index) in userlist" :key="index" :value="item.userId+''" :label="item.userName">{{item.userName}}</a-select-option>
                        </a-select>
                    </a-form-item>
                    <template v-if="cloudform.cloudType != 13">
                        <a-form-item label="是否开启监控服务" name="openPrometheus">
                            <a-switch v-model:checked="cloudform.openPrometheus" checked-children="是" un-checked-children="否" @change="(checked)=>changePrometheus(checked,'openPrometheus')" :disabled="info.isInfo" />
                        </a-form-item>
                        <a-form-item label="Prometheus地址" name="prometheusUrl" v-if="cloudform.openPrometheus">
                            <a-input v-model:value.trim="cloudform.prometheusUrl" :disabled="info.isInfo" placeholder="请输入" allow-clear></a-input>
                        </a-form-item>
                        <!-- <a-form-item label="是否开启裸金属服务" name="openBaremetal">
                            <a-switch v-model:checked="cloudform.openBaremetal" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                        </a-form-item>
                        <a-form-item label="裸金属服务API地址" name="baremetalUrl" v-if="cloudform.openBaremetal">
                            <a-input v-model:value.trim="cloudform.baremetalUrl" :disabled="info.isInfo" placeholder="请输入" allow-clear></a-input>
                        </a-form-item> -->
                        <a-form-item label="是否开启集群管理" name="openCluster">
                            <a-switch v-model:checked="cloudform.openCluster" checked-children="是" un-checked-children="否" @change="(checked)=>changePrometheus(checked,'openCluster')" :disabled="info.isInfo" />
                        </a-form-item>
                        <a-form-item label="是否开启存储管理" name="openCeph">
                            <a-switch v-model:checked="cloudform.openCeph" checked-children="是" un-checked-children="否" :disabled="info.isInfo" />
                        </a-form-item>
                        <a-form-item label="存储副本数量" name="storageCopyNum" v-if="cloudform.openCeph">
                            <a-input-number v-model:value.trim="cloudform.storageCopyNum" :min="1" :step="1" :precision="0" :disabled="info.isInfo" placeholder="请输入" style="width:100%" allow-clear></a-input-number>
                        </a-form-item>
                        <a-form-item label="CEPH地址" name="cephUrl" v-if="cloudform.openCeph">
                            <a-input v-model:value.trim="cloudform.cephUrl" :disabled="info.isInfo" :placeholder="info.isInfo ? '' : 'https://127.0.0.1:8443/api'" allow-clear></a-input>
                        </a-form-item>
                        <a-form-item label="CEPH账号" name="cephUser" v-if="cloudform.openCeph">
                            <a-input v-model:value.trim="cloudform.cephUser" :disabled="info.isInfo" :placeholder="info.isInfo ? '' : '请输入'" allow-clear></a-input>
                        </a-form-item>
                        <a-form-item label="CEPH密码" name="cephPassword" v-if="cloudform.openCeph">
                            <a-input-password v-model:value.trim="cloudform.cephPassword" :disabled="info.isInfo" :placeholder="info.isInfo ? '' : '请输入'" allow-clear></a-input-password>
                        </a-form-item>
                    </template>
                    <!-- <a-form-item label="集群管理API地址" name="clusterUrl" v-if="cloudform.openCluster">
                        <a-input v-model:value.trim="cloudform.clusterUrl" :disabled="isInfo" placeholder="请输入" allow-clear></a-input>
                    </a-form-item> -->
                    <!-- <a-form-item label="Prometheus Url" name="prometheusUrl">
                        <a-input v-model:value.trim="cloudform.prometheusUrl" :disabled="info.isInfo" placeholder="请输入" allow-clear></a-input>
                    </a-form-item> -->
                    <a-form-item label="描述" name="cloudInfo">
                        <a-textarea v-model:value.trim="cloudform.cloudInfo" :disabled="info.isInfo" :placeholder="info.isInfo ? '' : '请输入'" allow-clear />
                    </a-form-item>
                </a-form>
            </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { saveCloud, testConnect, updateCloud } from "@/api/backend/cloud";
import { message } from 'ant-design-vue';
import { setCephToken } from '@/utils/auth';
import emiter from '@/utils/Bus';
import router from '@/router';
const { proxy } = getCurrentInstance()
const emit = defineEmits(['callback'])
const cloudformRef = ref(null);
const props = defineProps({
    info: {
    type: Object,
    default() {
      return {
        isInfo: true,
        isShow: false
      };
    }
  },
  options:{
    type:Array,
    default(){
        return []
    }
  },
  userlist:{
    type:Array,
    default(){
        return []
    }
  }
})
// const isInfo = ref(false)
const defaultform = {
    "cloudDomain": "",
    "cloudAddress": "",
    "cloudName": "",
    "cloudPassword": "",
    "cloudProject": "",
    "cloudType": 1,
    "cloudUser": "",
    "cloudVersion": "V3",
    "manageUserId":undefined,
    "prometheusUrl":'',
    "openCeph":false,
    "storageCopyNum":"",
    "cephUrl":"",
    "cephUser":"",
    "cephPassword":""
}
const cloudform = reactive({
    "cloudDomain": "",
    "cloudAddress": "",
    "cloudName": "",
    "cloudPassword": "",
    "cloudProject": "",
    "cloudType": 1,
    "cloudUser": "",
    "cloudVersion": "V3",
    "manageUserId":undefined,
    "prometheusUrl":'',
    "openCeph":false,
    "storageCopyNum":"",
    "cephUrl":"",
    "cephUser":"",
    "cephPassword":""
})

const validateType = (rule, value) => {
  if (!value) {
    return Promise.reject("请输入云类型");
  } else {
    return Promise.resolve();
  }
}

const rules = reactive({
    cloudName:[{required:true, message:'请输入',trigger:'change'}],
    cloudDomain:[{required:true, message:'请输入',trigger:'change'}],
    cloudAddress:[{required:true, message:'请输入',trigger:'change'}],
    cloudPassword:[{required:true, message:'请输入',trigger:'change'}],
    cloudProject:[{required:true, message:'请输入',trigger:'change'}],
    cloudType:[{required:true, validator:validateType, trigger:'change'}],
    cloudUser:[{required:true, message:'请输入',trigger:'change'}],
    cloudVersion:[{required:true, message:'请输入',trigger:'change'}],
    manageUserId:[{type:'array',required:true, message:'请输入',trigger:'change'}],
    prometheusUrl:[{required:true, message:'请输入prometheusUrl',trigger:'change'}],
    storageCopyNum:[{required:true,type:'number', message:'请输入副本数量',trigger:'change'}],
    cephUrl:[{required:true, message:'请输入CEPH地址',trigger:'change'}],
    cephUser:[{required:true, message:'请输入CEPH账号',trigger:'change'}],
    cephPassword:[{required:true, message:'请输入CEPH密码',trigger:'change'}]
})

const save = () => {
    cloudformRef.value.validate().then(async ()=>{
        let cloudform1 = {...cloudform};
        if(!cloudform1.openPrometheus){
            cloudform1.prometheusUrl = null;
            cloudform1.openPrometheus = 0;
        }else
            cloudform1.openPrometheus = 1;
        if(!cloudform1.openBaremetal){
            cloudform1.baremetalUrl = null;
            cloudform1.openBaremetal = 0;
        }else
            cloudform1.openBaremetal = 1;
        if(!cloudform1.openCluster){
            cloudform1.clusterUrl = null;
            cloudform1.openCluster = 0;
        }else
            cloudform1.openCluster = 1;
        if(!cloudform1.openCeph){
            cloudform1.openCeph = 0;
            cloudform1.storageCopyNum = null;
            cloudform1.cephUrl = null;
            cloudform1.cephUser = null;
            cloudform1.cephPassword = null;
        }else
            cloudform1.openCeph = 1;
        cloudform1.manageUserId = '-100,'+cloudform1.manageUserId.join(',')+',-100';
        let res = await testConnect(cloudform1)
        if(res.code == 0){
            proxy.$handleSave(cloudformRef.value, saveCloud, updateCloud, props, cloudform1, ()=>{
                cancel();
                localStorage.setItem('openCeph',cloudform1.openCeph)
                localStorage.setItem('openPrometheus',cloudform1.openPrometheus)
                emiter.emit("setCloud")
                emiter.emit("setMenu")
                emiter.emit("DomainListAPI","cloud_"+router.currentRoute.value.query.cloudId);
                // isInit:true重载左侧
            },null)
        }
    })
}
const cancel = () => {
    props.info.isShow = false
    // props.info.isInfo = false
    cloudformRef.value.resetFields();
    Object.assign(cloudform,defaultform)
}
const changePrometheus = (checked,key)=>{
    if(!checked && key == 'openPrometheus'){
        cloudform.openCluster = checked;
    }
    if(checked && key == 'openCluster'){cloudform.openPrometheus = checked}
}
onMounted(() => {})
defineExpose({cloudform})
</script>
<style lang='scss' scoped>
</style>