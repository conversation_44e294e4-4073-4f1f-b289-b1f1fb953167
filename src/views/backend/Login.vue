<template>
  <div class="login">
    <div class="login-content">
      <div class="login-top" v-if="isInit">
        <img src="../../assets/logo1.png" alt="" style="height:50px;" />
        <span>&nbsp;UStack</span>
      </div>
      <div class="login-top" v-else>
        <img :src="href" alt="" style="height:50px;" />
      </div>
      <div class="login-bottom">
        <div class="login-left"></div>
        <div class="login-right">
          <div class="login-form">
            <a-form :model="formInline">
              <!-- <p class="title" v-if="isInitTitle">{{loginTitle?loginTitle:''}}</p> -->
              <p class="title">{{isInitTitle ? '统信云基础设施管理平台' : (loginTitle ? loginTitle : ' ')}}</p>
              <!-- <a-alert v-if="visible" message="账号或密码输入错误，请重试" type="error" :after-close="handleClose" closable show-icon /> -->
              <a-form-item>
                <a-input v-model:value="formInline.loginName" placeholder="账户" size="large" style="width: 400px">
                  <template #prefix><UserOutlined style="color: rgba(0, 0, 0, 0.25)"/></template>
                </a-input>
              </a-form-item>
              <a-form-item>
                <a-input-password v-model:value="formInline.pass" placeholder="密码" size="large" style="width: 400px">
                  <template #prefix><LockOutlined style="color: rgba(0, 0, 0, 0.25)"/></template>
                </a-input-password>
              </a-form-item>
              <a-form-item class="vertify" name="captchaCode" :wrapper-col="{ span: 9, offset: 0 }" v-if="openCaptcha">
                <a-input id="vertify" ref="vertify" v-model:value="formInline.captcha" size="large" placeholder="请输入验证码" />
                <div class="vertify-img">
                  <img :src="captcha" alt="" @click="getCaptcha()" style="height: 39px" />
                  <a href="#" @click="getCaptcha()">看不清楚</a>
                </div>
                <!-- <a-alert v-if="visibleCaptcha" message="验证码输入有误，请重新输入" type="error" :after-close="handleClose" closable show-icon /> -->
              </a-form-item>
              <!-- <div class="login-memory">
                <div class="login-remember">
                  <a-checkbox v-model:checked="checked" @change="onChange">
                    自动登录
                  </a-checkbox>
                </div>
                <div class="login-forget">
                  <a href="#">忘记密码？</a>
                </div>
              </div> -->
              <a-button
                size="large"
                type="primary"
                html-type="submit"
                :disabled="formInline.loginName === '' || formInline.pass === ''"
                @click="onSubmit"
                :loading="loading"
              >
                登录
              </a-button>
            </a-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { UserOutlined, LockOutlined } from "@ant-design/icons-vue";
import { mapActions } from "pinia";
import { userStore } from "@/store/user"
const user_store = userStore()
import * as Sha256 from "js-sha256"; 
import { publicStore } from "@/store/public"
import { checkLicence, selectSysconfig } from '@/api/backend/public';

import { message, Modal } from 'ant-design-vue';
import { h } from 'vue';
import { indexStore } from '@/store/index';
const public_store = publicStore()

const defaultformInline = {
  loginName: "",
  pass:"",
  password: "",
  captcha: "",
  uuid: ""
};
export default {
  name: "",
  props: {},
  data() {
    return {
      openCaptcha:false,
      loading:false,
      openLDAPState:false,
      isInit:true,
      href:'',
      isInitTitle:false,
      loginTitle:' ',
      // visible: false,
      // visibleCaptcha: false,
      checked: false,
      captcha: "",
      formInline: {
        loginName: "",
        pass: "",
        password: "",
        captcha: "",
        uuid: ""
      }
    };
  },
  computed: {
  },
  watch: {},
  methods: {
    ...mapActions(user_store,{
      Login: "Login"
    }),
    // handleClose() {
    //   this.visible = false;
    // },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onChange(e) {},
    getCaptcha() {
      // const { ctx } = getCurrentInstance();
      // const that = ctx;
      public_store.GetUUID()
        .then((res: any) => {
          // console.log('res0',res)
          this.formInline.uuid = res.data;
          public_store.Captcha({ uuid: this.formInline.uuid })
            .then((res1:any) => {
              // console.log(';res1',res1)
              if(res1.code == 0)
              this.captcha = JSON.parse(JSON.stringify(res1.data));
            })
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            .catch(err1 => {});
        })
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        .catch(err => {});
    },
    onSubmit() {
      if(this.openCaptcha){
        if(!this.formInline.captcha){
          this.$refs.vertify.focus()
          message.error('请输入验证码');
          return;
        }
      }
      this.loading = true;
      let sha256 = Sha256.sha256
      let defaultps = {...this.formInline}
      // let defaultps = this.formInline.pass
      
      if(this.openLDAPState)
        defaultps.password = defaultps.pass;
      else
        defaultps.password = sha256(defaultps.pass);
      defaultps.pass = ''
      user_store.Login(defaultps)
        .then((res) => {
          if (res.code != 0){
            this.loading = false;
            // this.formInline.pass = defaultps.password
            if(this.openCaptcha)
              this.getCaptcha();
            this.formInline.captcha = "";
          }
        })
        .catch(() => {
          this.loading = false;
          if(this.openCaptcha)
            this.getCaptcha();
          this.formInline.captcha = "";
        });
    },
    setLoginLogo(){
      let data = indexStore().configs;
      this.openLDAPState = data?.openLDAPState;
      if(data){
        if(data.openCaptcha){
          this.getCaptcha();
          this.openCaptcha = true;
        }else
          this.openCaptcha = false;
        if(data.loginTitle){
          this.isInitTitle = false;
          this.loginTitle = data.loginTitle;
        }else{
          this.isInitTitle = true;
        }
        if(data.loginLogoAttachmentId){
          let res1 = import.meta.env.VITE_BASE_API+'/sys/public/selectSystemConfigAttachmentById?attachmentId='+data.loginLogoAttachmentId+'&type=logo';
          this.isInit = false;
          this.href = res1;
        }
      }
    }
  },
  created() {
    // this.getCaptcha();
  },
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  mounted() {
    this.setLoginLogo()
  },
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  beforeUnmount() {},
  components: {
    UserOutlined,
    LockOutlined
  },
  filters: {}
};
</script>
<style lang="scss" scoped>
.login-top{
  span{
    vertical-align: bottom;
    font-size: 26px;
    font-weight: 1000;
    padding-left: 4px;
    background-image: radial-gradient(at left top,#20CCFE,#2789FC,#063EE1);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.login {
  // height: 100%;
  height: 100vh;
  overflow-y: auto;
  width: 100%;
  background-image: url("../../assets/login-bg-logo.svg");
  background-repeat: no-repeat;
  background-position-x: 100%;
  background-position-y: 0%;
  background-size: 700px;
  padding-top: 40px;
  background-color: #f8f9fc;
}
.login-content {
  margin: auto;
  // height: 890px;
  width: 1200px;
  // background: #fff;
}
.login-content .login-bottom {
  display: flex;
  margin-top: 40px;
  // height: 756px;
  width: 1200px;
  background: #fff;
  border-radius: 8px;
  // box-shadow: 0 2px 10px 0 rgba(9, 24, 67, 0.05);
  box-shadow: 0px 20px 80px 0px rgba(9, 24, 67, 0.05);
}
.login-content .login-left {
  // height: 756px;
  height: 680px;
  width: 484px;
  background-image: url("../../assets/login-left-bg.svg");
  background-repeat: no-repeat;
  background-position: 0% 100%;
}
.login-content .login-right {
  display: flex;
  justify-content: center;
  width: 715px;
}
.login-content .login-right .login-form {
  width: 400px;
  .title {
    text-align: center;
    font-size: 20px;
    font-family: SourceHanSansSC-Medium, SourceHanSansSC;
    color: #333;
    font-weight: 800;
    line-height: 28px;
    margin-bottom: 40px;
    margin-top: 200px;
  }
  .login-memory {
    display: flex;
    justify-content: space-between;
  }
  .ant-btn {
    width: 100%;
    margin-top: 20px;
    border-radius: 4px;
  }
}
.vertify {
  position: relative;
}
.vertify-img {
  display: flex;
  position: absolute;
  align-items: center;
  top: 0;
  left: 160px;
  width: 220px;
}
</style>
