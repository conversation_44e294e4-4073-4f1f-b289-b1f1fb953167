<template>
    <div class="contentPadding">
      <OperateBtn @view="viewCloud" @add="addCloud" @edit="editCloud" @delete="delCloud" @sync="syncCloud" isSyncShow :Initializing="Initializing" :createUserName="createUserName">
        <template v-slot:cloud v-if="!info.isView && !isEmpty">
            <div class="info">
                <div class="left">
                    <div class="img"><img src="@/assets/vm/obj-host.svg" alt=""></div>
                    <a-descriptions :column="1" :label-style="{width:'98px'}">
                        <a-descriptions-item label="主机名称">{{hostinfo.hostSystemName}}</a-descriptions-item>
                        <!-- <a-descriptions-item label="型号">{{hostinfo.hostSystemName}}</a-descriptions-item> -->
                        <!-- <a-descriptions-item label="处理器类型">q2fddew</a-descriptions-item> -->
                        <a-descriptions-item label="CPU描述">{{hostinfo.cpuInfo}}</a-descriptions-item>
                        <a-descriptions-item label="逻辑处理器">{{hostinfo.cpuNum}}</a-descriptions-item>
                        <!-- <a-descriptions-item label="网卡">{{hostinfo.hostSystemName}}</a-descriptions-item> -->
                        <a-descriptions-item label="虚拟机">{{pagination.total}}</a-descriptions-item>
                        <a-descriptions-item label="状况">{{hostinfo.powerState == 'poweredOn' ? '已连接': (hostinfo.powerState == 'poweredOff' ? '已关闭' : '已暂停')}}</a-descriptions-item>
                        <!-- <a-descriptions-item label="正常运行时间">{{hostinfo.hostSystemName}}</a-descriptions-item> -->
                    </a-descriptions>
                </div>
                <a-card :body-style="{width:'300px',height:'100%'}">
                    <div class="host-use">
                        <span><span>CPU</span><span>可用：22 GHz</span></span>
                        <a-progress :stroke-color="{ '0%': '#60B515', '832.5%':'#FAC858', '1111%': '#EE6666', }" :strokeWidth="12" strokeLinecap="square" :percent="9" status="active"/>
                        <p><span>已用：1.89 GHz</span><span>容量：{{hostinfo.cpuHz}} GHz</span></p> 
                    </div>
                    <div class="host-use">
                        <span><span>存储</span><span>可用：{{hostinfo.dist}} GB</span></span>
                        <a-progress :stroke-color="{ '0%': '#60B515', '832.5%':'#FAC858', '1111%': '#EE6666', }" :strokeWidth="12" strokeLinecap="square" :percent="9" status="active"/>
                        <p><span>已用：1.89 GB</span><span>容量：23.23 GB</span></p>
                    </div>
                    <div class="host-use">
                        <span><span>内存</span><span>可用：22 GB</span></span>
                        <a-progress :stroke-color="{ '0%': '#60B515', '832.5%':'#FAC858', '1111%': '#EE6666', }" :strokeWidth="12" strokeLinecap="square" :percent="9" status="active"/>
                        <p><span>已用：1.89 GB</span><span>容量：{{hostinfo.memory}} GB</span></p>
                    </div>
                </a-card>
            </div>
            <div class="list">
                <a-table :title="()=>'虚拟机列表'" :data-source="vmlist" row-key="id" :columns="columns" :pagination="pagination" @change="changeTable">
                    <template #virtualMachineName="{record}">
                        <a @click="handleView(record.id)">{{record.virtualMachineName}}</a>
                    </template>
                    <template #powerState="{record}">
                        <a-descriptions-item label="状况">{{record.powerState == 'poweredOn' ? '已连接': (record.powerState == 'poweredOff' ? '已关闭' : '已暂停')}}</a-descriptions-item>
                    </template>
                </a-table>
            </div>
        </template>
        <template v-slot:cloud v-else-if="isEmpty">
            <a-divider style="margin:0"></a-divider>
            <a-empty></a-empty>
        </template>
    </OperateBtn>
    <Info ref="virtualRef" :info="info" v-show="info.isView && !isEmpty" />
    <ViewInfo ref="cloudEditDialog" :info="cloudinfo1" :options="dictlist" :userlist="userlist"/>
    <Add ref="cloudAddDialog" :info="cloudinfo" :options="dictlist" :userlist="userlist" />
    </div>
    
    
    <!-- <div class='emptyPadding' v-if="isEmpty">
        <a-empty></a-empty>
    </div> -->
</template>
<script lang='ts' setup>
import OperateBtn from "@/components/operatebtn/operatebtn.vue";
import Info from "./info.vue";
import ViewInfo from "@/views/backend/cloud/info.vue";
import Add from "@/views/backend/cloud/add.vue";
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getvmlist, hostInfo } from '@/api/backend/vmware';
import router from '@/router';
import { deleteCloud, getCloudInfo, initCloud, initCloudState } from "@/api/backend/cloud";
import { queryWorker } from "@/api/backend/systems/user";
import { selectDictList } from "@/api/backend/systems/dictionary";
import { userStore } from "@/store/user";
import { Modal } from "ant-design-vue";
const {proxy} = getCurrentInstance()
const route = useRoute()
const virtualRef = ref()
const hostinfo = reactive({})
const vmlist = ref([])
const isEmpty = ref(false)
const loading = ref(false)
const Initializing = ref(false)
const createUserName = ref('')
const dictlist = ref([])
const userlist = ref([])
const cloudEditDialog = ref(null);
const cloudAddDialog = ref(null);
const cloudinfo1 = reactive({
    isShow:false,
    isInfo:false
})
const cloudinfo = reactive({
    isShow:false
})
// const isView = ref(false)
const info = reactive({
    isView:false
})
const queryform = reactive({
    hostId:'',
    pageIndex:1,
    pageSize:10
})
const pagination = reactive({
  // 分页配置器
  pageSize: 10, // 一页的数据限制
  current: 1, // 当前页
  total: 0, // 总数
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  showQuickJumper: true, // 是否可以快速跳转至某页
  showSizeChanger: true, // 是否可以改变 pageSize
  pageSizeOptions: ["10", "20", "30"], // 指定每页可以显示多少条
  showTotal: (total: any) => `共有 ${total} 条数据` //分页中显示总的数据
});
const columns = [
    {title:'虚拟机名称',dataIndex:'virtualMachineName',slots: { customRender: 'virtualMachineName' }},
    {title:'电源',dataIndex:'powerState',slots: { customRender: 'powerState' }},
    {title:'状态',dataIndex:'status'},
    {title:'存储(G)',dataIndex:'dist'},
    {title:'内存(G)',dataIndex:'memory'},
    {title:'cpu数量',dataIndex:'cpuNum'},
]
// 分页、序号、筛选变化时触发
const changeTable: any = (pagination: any, filters: any, sorter: any) => {
  searchform.pageIndex = pagination.current;
  searchform.pageSize = pagination.pageSize;
  getList();
};
const gethostInfo = async (e) => {
    if(e){
        isEmpty.value = false;
    }else{
        isEmpty.value = true;
    }
    let res = await hostInfo({id: (e ? e :route.query.id)})
    if(res.code == 0){
        Object.assign(hostinfo,res.data)
    }
}
const getVmlist = async (e) => {
    if(e){queryform.hostId = e}else{queryform.hostId = route.query.id}
    vmlist.value = await proxy.$getList(loading, getvmlist, queryform, pagination,getVmlist)
}
const handleView = (id) => {
    info.isView = true;
    proxy.$nextTick(()=>{
        virtualRef.value.getvirtualInfo(id)
    })
}
const getDictList = async () => {
    let res = await selectDictList({dictType:'CLOUD_TYPE'})
    if(res){
        if(res.code == 0){
            dictlist.value = res.data;
            // 避免字典中的cloutype被随意添加致radioOptions变混乱
            cloudAddDialog.value.radioOptions = cloudAddDialog.value.radioOptions.filter((item,index)=>{
              return dictlist.value.some((t,i)=>{
               return t.dictValue == item.value
              })
            })
        }
    }
}
const getCloudinfo = async (callback,isDialog) => {
    let res = await getCloudInfo({id:route.query.cloudId})
    if(res.code == 0){
        let record1 = {};
        Object.assign(record1,res.data)
        if(isDialog){
            let manageUserId1 = record1.manageUserId?.split(',')
            manageUserId1.splice(0,1)
            manageUserId1.splice(manageUserId1.length-1,1)
            record1.manageUserId = manageUserId1;
            Object.assign(cloudEditDialog.value.cloudform, record1)
        }else{
            proxy.$mitt.emit('SelectCloudList',{isAdd:undefined,isInit:true})
        }
        callback();
    }
}
const queryworker = async () => {
    let res = await queryWorker({pageIndex:1,pageSize:10000})
    if(res.code == 0)
    userlist.value = res.data.list;
}
const viewCloud = () => {
    getCloudinfo(()=>{
        cloudinfo1.isShow = true;
        cloudinfo1.isInfo = true;
        queryworker()
    },true)
}
const addCloud = () => {
    cloudinfo.isShow = true;
    queryworker()
}
const editCloud = () => {
    getCloudinfo(()=>{
        cloudinfo1.isShow = true;
        cloudinfo1.isInfo = false;
        queryworker()
    },true)
    
}
const delCloud = () => {
    proxy.$handleDel([route.query.cloudId],deleteCloud,()=>{
        proxy.$mitt.emit('backToHome')
    })
}
const syncCloud = async () => {
  // emiter.emit('allLoading',true)
  Initializing.value = true;
  createUserName.value = userStore().loginName;
  let res = await initCloud({id:route.query.cloudId});
  if(res.code == 0){
    if(res.data){
      if(res.data.initResult == 'success'){
      if(res.data.uuid){
        getProcess(res.data.uuid)
      }
    }else if(res.data.initResult == 'failed'){
      Modal.error({
        content: () => '初始化失败'
      });
      Initializing.value = false;
      // emiter.emit('allLoading',false)
    }else if(res.data.initResult == 'running'){
      if(res.data.uuid){
        getProcess(res.data.uuid)
      }
    }else{
      Modal.error({
        content: () => '初始化失败'
      });
      Initializing.value = false;
      // emiter.emit('allLoading',false)
    }
    }
  }else if(res.msg){
    Modal.error({
      content: () => res.msg
    });
    Initializing.value = false;
    // emiter.emit('allLoading',false)
  }else{
    Modal.error({
      content: () => '初始化失败'
    });
    Initializing.value = false;
    // emiter.emit('allLoading',false)
  }
}
const getProcess = async (uuid,isMounted) => {
  let res = await initCloudState({uuid,cloudId:route.query.cloudId});
  if(res.code == 0){
    if(res.data){
      if(res.data.initResult == "success"){
      if(!isMounted){
        Initializing.value = false;
        // emiter.emit('allLoading',false)
        Modal.destroyAll();
        Modal.success({
          content: () => '初始化成功'
        });
        getCloudinfo(()=>{
          console.log("callback")
        },false)
      }
    }else if(res.data.initResult == 'running'){
      if(isMounted){
        Initializing.value = true;
        createUserName.value = res.data.createUserName;
      }else{
        setTimeout(()=>{
          getProcess(uuid);
        },5000)
      }
    }else{
      if(!isMounted){
        Modal.error({
          content: () => '初始化失败'
        });
        Initializing.value = false;
      }
      // emiter.emit('allLoading',false)
    }
    }
    
  }else if(res.msg){
    if(!isMounted){
      Modal.error({
        title: () => route.query.title,
        content: () => res.msg
      });
      Initializing.value = false;
    }
    // emiter.emit('allLoading',false)
  }
  else{
    if(!isMounted){
      Modal.error({
        content: () => '初始化失败'
      });
      Initializing.value = false;
    }
    // emiter.emit('allLoading',false)
  }
}
onMounted(() => {
    // console.log('url',location.search,location.origin,location,useRoute(),useRouter())
    getDictList()
    gethostInfo()
    getVmlist()
    proxy.$mitt.on('gethostInfo',(e)=>{gethostInfo(e);getVmlist(e)})
})
</script>
<style lang='scss' scoped>
.contentPadding{padding: 0;background-color: transparent;}
.emptyPadding{display: flex;justify-content: center;align-items: center;height: calc(100vh - 96px);margin: 0 16px;background-color: #fff;}
.info{padding: 24px;display: flex;justify-content: space-between;background-color: #fff;.left{display: flex;.img{padding: 20px 44px 0 20px;}}}
.list{padding: 24px;margin-top: 16px;background-color: #fff;}
.host-use{span,p{display: flex;justify-content: space-between;}}
.ant-card-bordered{&:hover {
    border-color: #00000017;
    box-shadow: 0 2px 8px #00000017;
}
transition:all .3s;}
.ant-descriptions{width: 400px;margin-right: 24px;}
:deep(.ant-descriptions-item-content){justify-content: end;}
:deep(.ant-table-title){font-weight: bold;}
// :deep(.ant-descriptions-item-container .ant-descriptions-item-label){
//     white-space: nowrap;
// }
// :deep(.ant-descriptions-item-container .ant-descriptions-item-content){
//     white-space: nowrap;
// }
.ant-empty{background-color: #fff;height: calc(100vh - 134px);margin: 0;flex-direction: column;align-items: center;}
// .ant-page-header{padding: 0 16px;}
.ant-page-header-ghost{background-color: #fff;}
</style>