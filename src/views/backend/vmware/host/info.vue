<template>
    <div class='contentPadding'>
        <div style="padding:20px;background-color:#fff;display:flex;justify-content:space-between;border-bottom:2px solid #f0f2f5;min-width:896px">
            <a-button @click="()=>{info.isView = false}">返回</a-button>
            <!-- 空节点必须 -->
            <div></div>
        </div>
        
        <div class="virtual-content">
            <a-card title="客户机操作系统" class="virtual-left virtual">
            <div class="terminal">
                <img src="#" :width="200" :height="100" alt="screenShot">
                <a-button>启动 Remote Console</a-button>
                <a-button type="primary">启动 Web 控制台</a-button>
            </div>
            <a-descriptions :column="1"  :content-style="{justifyContent:'right'}">
                <a-descriptions-item label="虚拟机名称">{{virtualinfo.virtualMachineName}}</a-descriptions-item>
                <a-descriptions-item label="电源状态">{{virtualinfo.powerState == 'poweredOn' ? '已连接': (virtualinfo.powerState == 'poweredOff' ? '已关闭' : '已暂停')}}</a-descriptions-item>
                <a-descriptions-item label="客户机操作系统">VMware Photon OS (64-bit)</a-descriptions-item>
                <a-descriptions-item label="VMware Tools">未运行，未安装</a-descriptions-item>
                <a-descriptions-item label="DNS 名称">-</a-descriptions-item>
                <a-descriptions-item label="IP 地址">{{virtualinfo.hostName}}</a-descriptions-item>
                <a-descriptions-item label="加密">未加密</a-descriptions-item>
            </a-descriptions>
        </a-card>
        <!-- <div class="virtual-right virtual"></div> -->
        <a-card title="容量和使用情况" class="virtual-right virtual" :body-style="{minWidth:'300px'}">
                <div class="virtual-use">
                    <span class="title">CPU</span>
                    <p><span>已用：0 GHz</span><span>已分配：{{virtualinfo.cpuNum}} CPU</span></p> 
                </div>
                <div class="virtual-use">
                    <p></p>
                    <span class="title">存储</span>
                    <p><span>已用：0 G</span><span>已分配：0 G</span></p>
                </div>
                <div class="virtual-use">
                    <p></p>
                    <span class="title">内存</span>
                   <p><span>已用：0 G</span><span>已分配：{{virtualinfo.memory}} G</span></p>
                </div>
            </a-card>
        </div>
    </div>
</template>
<script lang='ts' setup>
import { onMounted, reactive } from 'vue';
import { virtualInfo } from "@/api/backend/vmware";
const props = defineProps({
    info:{
        type:Object,
        default(){
            return {
                isView:false
            }
        }
    }
})
const virtualinfo =  reactive({})
const getvirtualInfo = async (id) => {
    let res = await virtualInfo({id})
    if(res.code == 0){
        console.log('res',res.data)
        Object.assign(virtualinfo, res.data)
    }
}
onMounted(() => {})
defineExpose({getvirtualInfo})
</script>
<style lang='scss' scoped>
.virtual-content{display: flex;margin-top: 16px;}
.virtual{background-color: #fff;min-height: 335px;}
.virtual-left{flex: 5;:deep(.ant-card-body){display: flex;.terminal{text-align: center;margin-right: 20px;.ant-btn{margin-top: 20px;min-width: 168px;}}}}
.virtual-right{flex: 3;margin-left: 16px;}
.ant-descriptions{min-width: 340px;}
.virtual-use{border-bottom: 1px solid#f0f0f0;.title{font-weight: bold};p{display: flex;justify-content: space-between;}}
.ant-btn-text{font-weight: bold;}
</style>