<template>
    <div class='not-white'>
        <div class="banner"></div>
        <div class="alert">{{loginLimit.msg}}</div>
    </div>
</template>
<script lang='ts' setup>
import { nextTick, onMounted, ref } from 'vue';
const loginLimit = ref({state:1,msg:''});
onMounted(() => {
    loginLimit.value = JSON.parse(localStorage.getItem("loginLimit"));
})
</script>
<style lang='scss' scoped>
.not-white{
    height: 100vh;
    width: 100%;
    background-color: #353945;
    overflow: auto;
    .banner{
        height: 500px;
        width: 100%;
        margin: 50px auto;
        background-image: url('@/assets/whitelist.png');
        background-repeat: no-repeat;
        background-size: auto 100%;
        background-position: center;
    }
    .alert{
        text-align: center;
        font-size: 20px;
        color: #fff;
    }
}
</style>