import Cookies from "js-cookie";

const TokenKey = "ustack_token";
const CephTokenKey = "token";
const usernameKey = "userName";
const roleListKey = "roleList";
const userinfoKey = "userinfo";
const timeCookieKey = "timeCookie";
const userIdKey = "userId";

export function getTimeCookie(): any {
  return Cookies.get(timeCookieKey);
}

export function setTimeCookie(timeCookie: string) {
  if (import.meta.env.VITE_CAS === "cas") {
    return Cookies.set(timeCookieKey, timeCookie, {
      domain: "uniontech.com",
      expires: new Date(new Date().getTime() + 7200 * 1000)
    });
  } else {
    return Cookies.set(timeCookieKey, timeCookie);
  }
}

export function removeTimeCookie() {
  if (import.meta.env.VITE_CAS === "cas") {
    return Cookies.remove(timeCookieKey, {
      domain: "uniontech.com",
      expires: 0
    });
  } else {
    return Cookies.remove(timeCookieKey);
  }
}

export function getToken() {
  return Cookies.get(TokenKey) ? Cookies.get(TokenKey) : (new URLSearchParams(window.location.search)).get('token');
}

export function getCephToken() {
  return Cookies.get(CephTokenKey);
}

export function setToken(token: string) {
  if (import.meta.env.VITE_CAS === "cas") {
    return Cookies.set(TokenKey, token, {
      domain: "uniontech.com",
      expires: new Date(Number(getTimeCookie()) + 7200 * 1000)
    });
  } else {
    return Cookies.set(TokenKey, token);
  }
}

export function setCephToken(token: string) {
  if (import.meta.env.VITE_CAS === "cas") {
    return Cookies.set(CephTokenKey, token, {
      domain: "uniontech.com",
      expires: new Date(Number(getTimeCookie()) + 7200 * 1000)
    });
  } else {
    return Cookies.set(CephTokenKey, token);
  }
}

export function removeToken() {
  if (import.meta.env.VITE_CAS === "cas") {
    return Cookies.remove(TokenKey, {
      domain: "uniontech.com",
      expires: 0
    });
  } else {
    return Cookies.remove(TokenKey);
  }
}

export function getUsername() {
  return Cookies.get(usernameKey);
}

export function setUsername(username: string) {
  return Cookies.set(usernameKey, username);
}

export function removeUsername() {
  return Cookies.remove(usernameKey);
}

export function getRoleList() {
  return Cookies.get(roleListKey);
}
export function setRoleList(list: string) {
  return Cookies.set(roleListKey, list);
}

export function removeRoleList() {
  return Cookies.remove(roleListKey);
}

export function getUserInfo() {
  return Cookies.get(userinfoKey);
}
export function getUserId() {
  return Cookies.get(userIdKey);
}
export function setUserInfo(userinfo: string) {
  return Cookies.set(userinfoKey, userinfo);
}

export function removeUserInfo() {
  return Cookies.remove(userinfoKey);
}
export function removeUserId() {
  return Cookies.remove(userIdKey);
}

export function setUserId(userId: string) {
  return Cookies.set(userIdKey, userId);
}
