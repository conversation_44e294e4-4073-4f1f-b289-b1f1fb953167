import axios from "axios";
import { getToken } from "@/utils/auth";
import { ComponentInternalInstance, getCurrentInstance } from "vue";
import emiter from "./Bus";
import {handleErr} from "@/utils/tool"
import router from "@/router";
const whitelist = [
  '/sys/logout',
  '/sys/index/selectPieByOS',
  '/sys/index/selectPieByCloud',
  '/sys/index/selectBarbyResource',
  '/sys/index/selectLineByTicket',
  '/sys/openstack/index/queryStatisticByCloudId',
  '/sys/sysnoticemessage/selectNumber',
  '/sys/sysnoticemessage/selectList',
  // 'sys/safe/selectExecuteScanStatus',
  'sys/safe/executeScan',
  'sys/safe/queryScanResultList',
  'sys/safe/statistics',
  '/sys/safe/progress',
  '/sys/safe/importCVE',
  '/sys/safe/selectImportCVEState',
  '/agent/selectNowResourceSateByCloudId',
  '/sys/syshostcpumonitor/rankByCloudId',
  '/sys/syshostmemerymonitor/rankByCloudId',
  // '/health/minimal',
  // '/auth',
  '/sys/syscloud/initCloud',
  '/sys/syscloud/selectInitCloudState',
  // '/sys/sysport/selectList',
  // '/sys/menu/selectNav',
  '/sys/public/selectSystemConfig',
  '/sys/user/info',
  '/sys/menu/selectNavByCloud',
  '/sys/syscloud/selectList',
  '/sys/openstack/index/countDomain',
  '/sys/attachment/upload',
  '/sys/safe/createHealthCheck',
  '/sys/safe/checkHealth',
  '/sys/safe/selectHealthCheckResultByHistoryIdAndCategoryId',
  '/sys/safe/selectHealthCheckResult',
  '/sys/safe/updateHealthCheckState',
  '/sys/safe/statisticsHealthResult',
  '/agent/selectNowResourceSateByServerId',
  '/sys/ceph/health/checkCephAuth',
  '/sys/ceph/health/minimal',
  '/sys/public/checkLicence',
  '/sys/statistics/statisticsComputeByProject',
  '/sys/sysdomain/countProject',
  '/sys/openstack/index/queryHypervisorStatisticsByCloudId',
  '/sys/openstack/index/queryHypervisorByCloudId',
  '/sys/openstack/index/queryComputeServiceByCloudId',
  '/sys/openstack/index/queryBlockServiceByCloudId',
  '/sys/openstack/index/queryHostAggregateByCloudId',
  '/sys/openstack/index/queryNetworkAgentByCloudId',
  '/sys/index/selectCloudList',
  '/sys/index/rankingByServer',
  '/sys/index/statisticsTicket',
  '/sys/index/statisticsWarning',
  '/agent/selectResourceSateByServerIdAndTime',
  '/sys/statistics/statisticsProjectByCloudId',
  // '/sys/syscloud/info',
  '/sys/sysdomain/selectList',
  '/sys/attachment/uploadChunk',
  '/sys/attachment/mergeChunks',
  '/sys/openstack/index/queryResourceByCloudId',
  '/sys/sysclusternode/selectList',
  '/sys/sysclusterreceiver/selectList',
  '/sys/sysopenstackloadbalancerlistener/selectList',
  '/sys/sysopenstackloadbalancerpool/selectList',
  '/sys/sysopenstackloadbalancerlistenerl7policie/selectList',
  '/sys/sysopenstackloadbalancerlistenerl7policierule/selectList',
  '/sys/sysopenstackloadbalancerhealthmonitor/selectList',
  '/sys/sysopenstackloadbalancerpoolmember/selectList',
  '/sys/sysrouteinterface/queryPage',
  '/sys/sysserver/getConsoleOutput',
  '/sys/sysusbinfo/selectList',
  '/sys/syssubnet/queryPage',
  '/sys/sysconfigrule/getConfigByServerId',
  '/sys/syssafeserverinspectionsettings/selectList',
  '/sys/syscloud/queryInitHistory',
  '/sys/sysopenstackmasakarisegment/queryPage',
  '/sys/sysopenstackmasakarihost/queryPage',
  '/sys/sysopenstackmasakarihost/selectList'
];
const closelist = {
  "/admin/devops/server":[
    "/sys/sysflavor/queryPage",
    "/sys/sysvolumetype/selectList",
    "/sys/sysimage/queryPage",
    "/sys/sysvolume/queryPage",
    "/sys/sysvolumesnapshot/queryPage",
    "/sys/syszone/selectList",
    "/sys/sysdictdata/selectList",
    "/sys/sysnetwork/queryPage",
    "/sys/syssubnet/selectList",
    "/sys/sysport/queryPage",
    "/sys/syssecuritygroup/queryPage",
    "/sys/syskeypair/queryPage",
    "/sys/sysserver/getVNCConsoleURL",
    "/sys/sysserver/save",
    "/sys/sysserver/queryPage",
    // "/sys/user/selectList"
  ],
  "/admin/devops/image":[
    "/sys/sysimage/queryPage"
  ],
  "/admin/devops/menu/cloud":[
    "/sys/sysdictdata/selectList",
    "/sys/sysproject/selectList",
    "/sys/sysopenstackmasakarisegment/supportsMasakari",
    "/sys/syscloud/info",
    "/sys/ceph/selectHostList",
    "/sys/ceph/selectOSDList",
    "/sys/ceph/selectPoolList"
  ],
  "/admin/devops/menu/domain":[
    "/sys/sysproject/selectList",
    "/sys/sysopenstackmasakarisegment/supportsMasakari"
  ],
  "/admin/devops/menu/project":[
    "/sys/sysproject/selectList",
  ],
  "/admin/devops/network":[
    "/sys/sysport/queryPage",
    "/sys/sysnetwork/queryPage"
  ],
  "/admin/devops/route":[
    "/sys/sysroute/queryPage"
  ],
  "/admin/storage/volumeBackup":[
    "/sys/sysvolumebackup/queryPage"
  ],
  "/admin/storage/volume":[
    "/sys/sysvolume/queryPage"
  ],
  "/admin/storage/volumeSnapshot":[
    "/sys/sysvolumesnapshot/queryPage"
  ],
  "/admin/systems/flow":[
    "/sys/flowable/save",
    "/sys/flowable/update"
  ]
}
const self_error_msg = [
  "/sys/ceph/health/minimal",
  "/sys/syscloud/initCloud"
]
let loadingCount = 0;
/**
 * @description 函数返回唯一的请求key **/
 function getRequestKey(config) {
  let {
      method,
      url,
      params,
      data
  } = config;
  // axios中取消请求及阻止重复请求的方法
  // 参数相同时阻止重复请求：
  // return [method, url, JSON.stringify(params), JSON.stringify(data)].join("&");
  // 请求方法相同，参数不同时阻止重复请求
  return [method, url].join("&");
}

/**
* @description 添加请求信息 **/
let pendingRequest = new Map();

function addPendingRequest(config) {
  // console.log(config.url)
  let requestKey = getRequestKey(config);
  config.cancelToken = config.cancelToken || new axios.CancelToken((cancel) => {
      if (!pendingRequest.has(requestKey)) {
          pendingRequest.set(requestKey, cancel);
      }
  });
}

/**
* @description 取消重复请求 **/
function removePendingRequest(config) {
  let requestKey = getRequestKey(config);
  if (pendingRequest.has(requestKey)) {
      // 如果是重复的请求，则执行对应的cancel函数
      let cancel = pendingRequest.get(requestKey);
      cancel(requestKey);
      // 将前一次重复的请求移除
      pendingRequest.delete(requestKey);
  }
}

// const {proxy}:any = getCurrentInstance() as ComponentInternalInstance
// create an axios instance
const service = axios.create({
  baseURL: import.meta.env.VITE_BASE_API, // url = base url + request url
  // timeout: 900000 // request timeout
});

// request interceptor
service.interceptors.request.use(
  config => {
    // // 检查是否存在重复请求，若存在则取消已发的请求
    // removePendingRequest(config);
    // // 把当前请求信息添加到pendingRequest对象中
    // addPendingRequest(config);
    const tokenStr = getToken() ? getToken() : (new URLSearchParams(window.location.search)).get('token');
    // console.log('config.url',config.url,!whitelist.includes(config.url))
    if(!whitelist.includes(config.url.split('?')[0])){
      if(!closelist[router.currentRoute.value.path] || !(closelist[router.currentRoute.value.path].includes(config.url.split('?')[0]))){
        loadingCount++;
        console.log("当前页面取消loading则添加到closelist",router.currentRoute.value.path,config.url.split('?')[0],getToken(),window.location.search)
        // emiter.emit('allLoading',true);
        emiter.emit("loading",true)
      }
    }
    // if(closelist.includes(config.url.split('?')[0]))
    
    if (tokenStr !== "" || tokenStr !== null || tokenStr !== undefined) {
      //获取token
      config.headers["token"] = tokenStr;
    }
    return config;
  },
  error => {
    // do something with request error
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  response => {
    // console.log("response",response)
    // removePendingRequest(response.config);
    if(!self_error_msg.includes(response.config.url))
    handleErr(response)
    if(!whitelist.includes(response.config.url.split('?')[0])){
      if(!closelist[router.currentRoute.value.path] || !(closelist[router.currentRoute.value.path].includes(response.config.url.split('?')[0]))){
        loadingCount--;
        if(loadingCount <= 0)
          // emiter.emit('allLoading',false)
          emiter.emit("loading",false)
        }
    }
    // if(closelist.includes(response.config.url))
    return (!response.headers["Content-disposition"] && response.config.url != '/sys/attachment/uploadChunk') ? response.data : response;
  },
  error => {
    // // 从pendingRequest对象中移除请求
    // removePendingRequest(error.config || {});
    // if (axios.isCancel(error)) {
    //     console.log("被取消的重复请求：" + error.message);
    // }
    if(!self_error_msg.includes(error.response.config.url))
    handleErr(error)
    console.log("StopLoad....",error)
    loadingCount--;
    console.log("loadingCount-",loadingCount)
    if(loadingCount <= 0)
      // emiter.emit('allLoading',false)
      emiter.emit("loading",false)
    return Promise.reject(error);
  }
);

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const installer = {
  vm: {},
  install(Vue: any, router = {}) {
    Vue.use(router, service);
  }
};

export {
  //   installer as VueAxios,
  service as axios
};
