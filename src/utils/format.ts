export const filterMemory = (data) => {
    if(!data && data !== 0){
        return '-'
    }else{
        if(data < 1024)
        return data + 'MB';
    if(data >= 1024 && data < 1024 * 1024)
        return (data / 1024).toFixed(1) + 'GB';
    if(data >= 1024 * 1024)
        return (data / (1024 * 1024)).toFixed(2) + 'TB';
    }
}
export const filterGB = (data) => {
    if(!data && data !== 0){
        return '-'
    }else{
        if(data < 1024)
            return data + 'GB';
        if(data >= 1024)
            return (data / 1024).toFixed(2) + 'TB';
    }
}
export const filterNumber = (data) => {
    if(!data && data !== 0){
        return '-'
    }else{
        if(data < 1000)
            return data.toFixed(1);
        else if(data >= 1000 && data < 1000000)
            return (data / 1000).toFixed(1) + ' K';
        else if(data >= 1000000 && data < 100000000)
            return (data / 1000000).toFixed(1) + ' M';
        else if(data >= 100000000)
            return (data / 100000000).toFixed(1) + ' B';
    }
}