export default function registerStart (lf) {
  lf.register('bpmn:startEvent', ({ CircleNode, CircleNodeModel, h }) => {
    class StartNode extends CircleNode {
      getLabelShape () {
        const {model} = this.props
        const {
          x,
          y
        } = model
        return h(
          'text',
          {
            fill: '#000000',
            fontSize: 12,
            x: x - 17,
            y: y + 5,
            width: 50,
            height: 25
          },
          '申请人'
        )
      }
      getIconShape () {
        const {model} = this.props
        const {
          x,
          y
        } = model
        const {stroke} = model.getNodeStyle()
        return h(
          'svg',
          {
            x: x + 30,
            y: y - 5,
            width: 13,
            height: 13,
            viewBox: '0 0 1024 1024'
          },
          h(
            'path',
            {
              fill: stroke,
              d: 'M512 96C282.624 96 96 282.624 96 512s186.624 416 416 416 416-186.624 416-416S741.376 96 512 96z m0 768C317.92 864 160 706.08 160 512S317.92 160 512 160s352 157.92 352 352-157.92 352-352 352zM466.816 324.96a32 32 0 0 0-50.816 25.888v339.776a32 32 0 0 0 50.816 25.856l233.6-169.888a32 32 0 0 0 0-51.776l-233.6-169.856z'
            }
          )
        )
      }
      getShape () {
        const {model} = this.props
        const {
          x,
          y,
          r,
        } = model
        const {
          fill,
          stroke,
          strokeWidth} = model.getNodeStyle()
        return h(
          'g',
          {
          },
          [
            h(
              'circle',
              {
                cx: x,
                cy: y,
                r,
                fill,
                stroke,
                strokeWidth
              }
            ),
            
            this.getIconShape(),
            this.getLabelShape(),
          ]
        )
      }
    }
    class StartModel extends CircleNodeModel {
      // 自定义节点形状属性
      initNodeData(data) {
        data.text = {
          value: (data.text && data.text.value) || '',
          x: data.x,
          y: data.y + 35,
          dragable: false,
          editable: true
        }
        super.initNodeData(data)
        this.r = 50
      }
      // 自定义节点样式属性
      getNodeStyle() {
        const style = super.getNodeStyle()
        return style
      }
      // 自定义锚点样式
      getAnchorStyle() {
        const style = super.getAnchorStyle();
        style.hover.r = 8;
        style.hover.fill = "rgb(24, 125, 255)";
        style.hover.stroke = "rgb(24, 125, 255)";
        return style;
      }
      // 自定义节点outline
      getOutlineStyle() {
        const style = super.getOutlineStyle();
        style.stroke = '#88f'
        return style
      }
      getConnectedTargetRules () {
        const rules = super.getConnectedTargetRules()
        const notAsTarget = {
          message: '起始节点不能作为连线的终点',
          validate: () => false
        }
        rules.push(notAsTarget)
        return rules
      }
    }
    return {
      view: StartNode,
      model: StartModel
    }
  })
}
