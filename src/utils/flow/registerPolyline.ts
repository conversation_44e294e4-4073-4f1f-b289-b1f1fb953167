export default function registerPolyline (lf) {
    lf.register('bpmn:sequenceFlow', ({ PolylineEdge, PolylineEdgeModel }) => {
      class ConnnectionModel extends PolylineEdgeModel {
        constructor (data, graphModel) {
          super(data, graphModel)
        }
        setHovered (isHovered) {
          super.setHovered(isHovered);
          this.isAnimation = isHovered;
        }
        getEdgeAnimationStyle() {
          // const {model} = this.props;
          // const {stroke} = model.getNodeStyle()
          const style = super.getEdgeAnimationStyle();
          style.animationName = "lf_animate_dash"
          style.animationName = "lf_animate_dash"
          return style;
        }
      }
      return {
        view: PolylineEdge,
        model: ConnnectionModel
      }
    })
  }
  