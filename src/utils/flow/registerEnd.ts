export default function registerEnd (lf) {
    lf.register('bpmn:endEvent', ({ CircleNode, CircleNodeModel, h }) => {
      class EndNode extends CircleNode {
        getLabelShape () {
          const {model} = this.props
          const {
            x,
            y
          } = model
          return h(
            'text',
            {
              fill: '#000000',
              fontSize: 12,
              x: x - 17,
              y: y + 5,
              width: 50,
              height: 25
            },
            '归档'
          )
        }
        getIconShape () {
          const {model} = this.props
          const {
            x,
            y
          } = model
          const {stroke} = model.getNodeStyle()
          return h(
            'svg',
            {
              x: x + 30,
              y: y - 5,
              width: 12,
              height: 12,
              viewBox: '0 0 1024 1024'
            },
            h(
              'path',
              {
                fill: stroke,
                d: 'M678.528 642.304c0 17.6-14.4 32-32 32h-268.992a32 32 0 0 1-32-32V381.696a32 32 0 0 1 32-32h268.992c17.6 0 32 14.4 32 32v260.608z'
              }
            ),
            h(
                'path',
                {
                  fill: stroke,
                  d: 'M1015.552 512.128a502.656 502.656 0 0 0-503.68-503.68 502.208 502.208 0 0 0-356.096 147.264 502.016 502.016 0 0 0-147.328 356.416 500.288 500.288 0 0 0 146.816 356.736 499.584 499.584 0 0 0 356.544 146.688c277.312-2.816 503.744-226.24 503.744-503.424z m-947.968 0a444.288 444.288 0 0 1 444.288-444.544c246.976 0 447.296 200.128 447.296 444.544 0 244.032-200.32 444.416-447.296 444.416a442.304 442.304 0 0 1-444.288-444.416z'
                }
            )
          )
        }
        getShape () {
          const {model} = this.props
          const {
            x,
            y,
            r,
          } = model
          const {
            fill,
            stroke,
            strokeWidth} = model.getNodeStyle()
          return h(
            'g',
            {
            },
            [
              h(
                'circle',
                {
                  cx: x,
                  cy: y,
                  r,
                  fill,
                  stroke,
                  strokeWidth
                }
              ),
              
              this.getIconShape(),
              this.getLabelShape(),
            ]
          )
        }
      }
      class EndModel extends CircleNodeModel {
        initNodeData(data) {
          data.text = {
            value: (data.text && data.text.value) || '',
            x: data.x,
            y: data.y + 35
          }
          super.initNodeData(data)
          this.r = 50
        }
        // 自定义锚点样式
        getAnchorStyle() {
          const style = super.getAnchorStyle();
          style.hover.r = 8;
          style.hover.fill = "rgb(24, 125, 255)";
          style.hover.stroke = "rgb(24, 125, 255)";
          return style;
        }
        // 自定义节点outline
        getOutlineStyle() {
          const style = super.getOutlineStyle();
          style.stroke = '#88f'
          return style
        }
        getConnectedSourceRules () {
          const rules = super.getConnectedSourceRules()
          const notAsTarget = {
            message: '终止节点不能作为连线的起点',
            validate: () => false
          }
          rules.push(notAsTarget)
          return rules
        }
      }
      return {
        view: EndNode,
        model: EndModel
      }
    })
  }
  