import $ from "jquery";
export const resize = () => {
    let width = 0;
    $(".buttonPadding .ant-form-inline").children().each(function(){
        width += $(this).width();
    });
    if((width + $('.buttonPadding .button-group').width() + 64) >= $(".buttonPadding").width()){
        // $('.expand-button').show();
        $('.expand-button').css('visibility','visible');
        init();
        // buttonExpand(false)
    }else{
        $('.expand-button').css('visibility','hidden');
    }
}
const init = () => {
    $('.searchform').css('width',`calc(100% - ${$('.button-group').width()}px)`)
}
export const buttonExpand = (isexpand) => {
    if(isexpand){
        $('.button-group a.down').hide()
        $('.button-group a.up').show()
        $('.buttonPadding').css('height',`${$('.buttonPadding').height() + 78}px`)
        $('.searchform').css('width','100%')
    }else{
        $('.button-group a.down').show()
        $('.button-group a.up').hide()
        $('.buttonPadding').css('height','78px')
        // init();
    }
}
export const handleWidth = () => {
    // init();
    resize();
    window.addEventListener('resize',function(){
        resize();
    })
}