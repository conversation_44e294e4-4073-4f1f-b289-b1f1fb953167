import router from "@/router"
import { setToken, setUsername } from "../auth"
import {createP<PERSON>} from "pinia";
import { menuStore } from "@/store/menu";
import { userStore } from "@/store/user";
import { filterRouter } from "../route";
import { layoutStore } from "@/store/layout";
import $ from "jquery";
const layout_store = layoutStore(createPinia());
const routerPath = (page) => {
  switch (page) {
    case 'report':
      if(router.currentRoute.value.path != '/admin/report')
        router.push({path:"/admin/report",query:{token:(new URLSearchParams(window.location.search)).get('token')}})
      break;
    case 'health':
      if(router.currentRoute.value.path != '/admin/safe/health')
        router.push({path:"/admin/safe/health",query:{token:(new URLSearchParams(window.location.search)).get('token')}})
      break;
    default:
      break;
  }
}
const setMenuAndUser = (isFirst, page) => {
  isFirst = false;
    return menuStore().SelectNav()
  .then(async (res:any) => {
    console.log("setMenuAndUser",res.code)
    isFirst = false;
    if (res.code === 0) {
      const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};
      let res1:any = await userStore().UserInfo();
      if(res1.code == 0){
        user.userInfo = res1.data;
        setUsername(res1.data.userName);
      }
      const menuList = res.data;
      const permissions = res.permissions;
      user.menulist = menuList;
      user.permissions = permissions;
      localStorage.setItem('user',JSON.stringify(user))
      // if(to.path != '/admin/devops/menu/project'){
        const ansyRouter: any = filterRouter(menuList.concat(menuStore().cloudmenu));
        // const specialRoutes = ["云平台报表", "安全中心"];
        const specialRoutes = ["云平台报表", "安全中心", "工单管理"];
        ansyRouter.forEach((item: any) => {
          if (specialRoutes.includes(item.name)) {
            const newName = 'dynamic_iframe_' + item.name;
            if (!router.hasRoute(newName)) {
                item.name = newName;
                router.addRoute('layout1', item);
            }
          }
        });
        routerPath(page)
      // }
    }
  })
}
export function Receive(isFirst){
  if(isFirst){
    window.addEventListener('message',e=>{
      // if(e.origin.includes('8088')){
        switch (e.data.page) {
          case 'report':
            setToken(e.data.token)
            // setTimeout(()=>{
              setMenuAndUser(isFirst, e.data.page);
            // })
            break;
          case 'health':
            layout_store.setPaneLWidth('0%');
            $('.splitpanes__splitter').css('display', 'none');
            setToken(e.data.token)
            setMenuAndUser(isFirst, e.data.page);
            break;
          default:
            break;
        }
      // }
    },false)
  }
}
