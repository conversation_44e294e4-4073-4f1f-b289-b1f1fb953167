// 这步主要是为了让小圆点的颜色和饼状图的块对应，如果圆点的颜色是统一的，只需要把label写在series里面
export const setLabel = (data:any,color:any) => {
    let opts = [];
    for (let i = 0; i < data.length; i++) {
        let item:any = {};
        item.name = data[i].name;
        item.value = data[i].value;
        item.label = {
            normal: {
                //控制引导线上文字颜色和位置,此处a是显示文字区域，b做一个小圆圈在引导线尾部显示
                show: true,
                //a和b来识别不同的文字区域
                formatter: [
                    '{b|}' ,
                    '{a|{b}:{d}% }',
                ].join('\n'), //用\n来换行
                rich: {
                    a: {
                        // left: 20,
                        padding: [-20, 0, -10, 10],
                        fontSize:18
                    },
                    b: {
                        height: 10,
                        width: 10,
                        lineHeight: 10,
                        marginBottom: 20,
                        padding: [0, -10],
                        borderRadius: 10,
                        backgroundColor: color[i], // 圆点颜色和饼图块状颜色一致
                    }
                },

            }
        }

        opts.push(item)
    }
    return opts;
}