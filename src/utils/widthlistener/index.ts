export const widthListener = (element,inner,callback) => {
    // 确保浏览器支持 ResizeObserver
    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          const width = entry.contentRect.width;
          const height = entry.contentRect.height;
          if(inner)
          inner(width,height);
        }
        if(callback)
        callback()
      });
    
      // 获取需要监听的元素
    
      // 开始监听元素的尺寸变化
      resizeObserver.observe(element);
      // console.log("resizeObserver",resizeObserver)
    } else {
      console.log('ResizeObserver is not supported in this browser.');
      // 可以使用setTimeout和window.innerWidth来轮询宽度变化
    }
  }