const upAdapter = (e) => {
    if (e.path) { return e.path; }
    // 不存在则遍历target节点
    let target = e.target;
    let path = [];
    while (target.parentNode !== null) {
        path.push(target)
        target = target.parentNode
    }
    // 最后补上document和window
    path.push(document, window);
    return path;
}
window.onload = function(){
    window.addEventListener('click', function(e:any){
        if(upAdapter(e)[1].className == 'ant-modal-confirm-btns' ||  (upAdapter(e)[2] && upAdapter(e)[2].className == 'ant-modal-confirm-btns')){
            console.log("全局清除")
            let all = document.querySelectorAll(".ant-modal-root");
            all[all.length - 1].parentNode.removeChild(all[all.length - 1])
        }
    });
};