const INCLUDE_UPPERCASE = 1, INCLUDE_LOWERCASE = 2, INCLUDE_DIGIT = 4; export const randomString = (len, include) => { len = len || 32; include = include || 7; var $uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"; var $lowercase = $uppercase.toLowerCase(); var $digit = "0123456789"; var $chars = ""; if (include & INCLUDE_UPPERCASE) $chars += $uppercase; if (include & INCLUDE_LOWERCASE) $chars += $lowercase; if (include & INCLUDE_DIGIT) $chars += $digit; var maxPos = $chars.length; var str = ""; for (var i = 0; i < len; i++) { str += $chars.charAt(Math.floor(Math.random() * maxPos)); } return str+'0#'; }