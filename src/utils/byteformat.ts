export const ByteFormat = (value,i,NumOrStr) => {
    let temp;
    if(value >= 1024 * 1024 * 1024 * 1024){
        temp = {num:Number((value / (1024 * 1024 * 1024 * 1024)).toFixed(2)),str:(value / (1024 * 1024 * 1024 * 1024)).toFixed(2)+'T'+i+'B',unit:'T'+i+'B'}
    }else if(value >= 1024 * 1024 * 1024 && value < 1024 * 1024 * 1024 * 1024){
        temp = {num:Number((value / (1024 * 1024 * 1024)).toFixed(1)),str:(value / (1024 * 1024 * 1024)).toFixed(1)+'G'+i+'B',unit:'G'+i+'B'}
    }else if(value < 1024 * 1024 * 1024 && value >= 1024 * 1024){
        temp = {num:Number((value / (1024 * 1024)).toFixed(1)),str:(value / (1024 * 1024 )).toFixed(1)+'M'+i+'B',unit:'M'+i+'B'}
    }else if(value < 1024 * 1024 && value >= 1024){
        temp = {num:Number((value / 1024).toFixed()),str:(value / 1024).toFixed()+'K'+i+'B',unit:'K'+i+'B'}
    }else
        temp = {num:value,str:value+'B',unit:'B'}
    return temp[NumOrStr]; 
}