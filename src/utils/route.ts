import Layout from "@/layout/backend/index.vue";
const modules = import.meta.glob('../views/backend/**/index.vue')

interface Irouter {
    path?: string;
    name?: string;
    list?: any;
    redirect?: string;
    meta?: any;
    hidden?: boolean;
    url?: string;
    parentId?: number;
    component?: any;
    perms?: string;
    type?: number;
  }
// 将存入localhost中的菜单转换为router的格式
export const filterRouter = (routerStr: any) => {
    const res: any[] = [];
    routerStr.forEach((item: Irouter) => {
      if(item.type <= 1){
        const newItem: any = {};
        if (!item.url) {
          if(item.name == '卷管理' && !item.url){
            newItem.path = "/admin/storage";
            newItem.name ='卷管理 ';
          }else if(item.name == '集群管理' && !item.url){
            newItem.path = "/admin/devops";
            newItem.name ='集群管理 ';
          }else{
            newItem.path = "/admin";
            newItem.name = item.name;
          }
        } else {
          if(item.type == 0){
            newItem.path = "/admin";
            newItem.name = item.name;
          }else{
            newItem.name = item.name;
            newItem.path = "/admin/"+item.url;
          }
        }
    
        newItem.meta = { title: item.name };
        // console.log('newItem',newItem)
        // if(item.name != '存储管理'){
          if (item.parentId === 0) {
          } else {
            newItem.component = modules[`../views/backend/${item.url}/index.vue`];
            // newItem.component = () => import(`@/views/backend/${item.url}/index.vue`);
          }
        // }else{
        //   newItem.component = () => import(`@/views/backend/database/index.vue`);
        // }
        if (item.list && item.list.length) {
          newItem.children = filterRouter(item.list);
        }
        res.push(newItem);
      }
    });
    return res;
  };