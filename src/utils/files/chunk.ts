import { mergeChunks, uploadChunk } from "@/api/backend/systems/user";
import axios from "axios";
import * as Sha256 from "js-sha256";
// 此处使用了 spark-md5 来获取文件的md5值 不需要的可以删掉
// 请求接口获取文件标识:fragmentFlagAPI
// 请求接口上传每片文件:uploadAPI
// 请求接口组合文件：mergeAPI
// 全程上传状态
const stopStatus = {};
const CancelToken = axios.CancelToken;
let source = CancelToken.source();
export default class FileUploader {
  chunkSize;
  totalChunks;
  currentChunks;
  progress;
  progressT;
  fileSize;
  uploadedSize;
  percent;
  ramdon;
  pendingCount;
  constructor() {
    this.chunkSize = 0; // 每片文件大小，默认为 2MB
    this.totalChunks = 0; // 文件总片数
    this.currentChunks = 0;// 当前上传片数
    this.progress = 0; // 文件上传进度
    this.progressT = null; // 更新文件上传进度定时器
    this.fileSize = 0;
    this.uploadedSize = 0;
    this.percent = 0;
    this.ramdon = Math.random();
    this.pendingCount = 0;
  };
  onProgressUpdate(param:any){};
  onUploadComplete(param:any){};
  onUploadIncomplete(param:any){};
  queueReadNextChunk(){};
  // 上传文件
  async upload(file,attachmentCode) {
    try {

      // 文件分片 totalChunks片数
      const {chunkArr,totalChunks, arrPromise}:any = await this.sliceVideoFile(file,attachmentCode)
      this.totalChunks = totalChunks;
      // 开始上传
      // const arrPromise = await this.upChunkFile(chunkArr,attachmentCode,file.uid)
      
      console.log("try正常")
      // 所有内容上传成功
      Promise.all(arrPromise).then((result) => {
        // this.updateProgress();// 更新上传进度
        this.merge(chunkArr,attachmentCode,file.uid);
      }).catch((error) => {
        console.log("then异常")
        this.fail(error)
      });
    } catch (error) {
      this.fail(error)
    }
  }
  merge(chunkArr,attachmentCode,uid){
    this.mergeChunkFile(chunkArr,attachmentCode,uid).then((result) => {
      // this.updateProgress();// 更新上传进度
      this.succeed('上传完成');// 上传完成。根据业务逻辑返回相应内容
    }).catch((error) => {
      this.fail(error)
    });
  }

  // 单片
  upChunk(item,attachmentCode,uid,isQueue){
    // formdata参数根据后端接口调整
    item.append("attachmentCode",attachmentCode);
    return new Promise((resolve,reject)=>{
      // 请求接口上传每片文件
      console.log("uppppppppp",source,source.token,item,attachmentCode)
      uploadChunk(item,source.token).then((res:any)=>{
        this.pendingCount--;
        let func = this.queueReadNextChunk;
        if(isQueue && this.pendingCount < 10 && !stopStatus[uid]){
          if(typeof func === 'function' && func.toString().trim() !== 'function() {}'){
            console.log("2222222222",this.pendingCount);
            this.queueReadNextChunk();
            this.queueReadNextChunk = function() {};
          }
        }
        console.log("res=",res)
        if(res.data.code == 0){
          if(!stopStatus[uid]){
            this.updateProgress(res);// 更新上传进度
            resolve(1)// 成功=> 具体返回参数根据业务逻辑来
          }
        }else{
          stopStatus[uid] = true;
          source.cancel();
          reject(res.data.msg)
        }
      }).catch(err=>{
        console.log("一个失败",err)
        this.pendingCount--;
        stopStatus[uid] = true;
        source.cancel();
        // this.fail(err)
        reject(err)
      })
    })
  }

  /**
   * 上传每片文件
   * @param {Array} chunkArr 存放文件数组 FormData[]
   * @param {*} uploadId 上传所需参数
   * @param {*} fileKey  上传所需参数
   * @returns Promise 返回promise
   */
  upChunkFile(chunkArr,attachmentCode,uid){
    // 循环请求每一片( 并且成功更新上传进度) 
    let chunkArr1 = [...chunkArr];
    
    // 最后返回promise数组
    return chunkArr1.map((item,index)=>{
			this.upChunk(item,attachmentCode,uid,false);
    })

  }
  mergeChunkFile(chunkArr,attachmentCode,uid){
    // 循环请求每一片( 并且成功更新上传进度) 
    let chunkArr1 = [...chunkArr];
    // 最后返回promise数组
    // return chunkArr1.map((item,index)=>{
      let data = {
        attachmentCode:attachmentCode,
        totalChunks:this.totalChunks,
        identifier:chunkArr1[0].get('identifier'),
        fileName:chunkArr1[0].get('fileName')
      }
        return new Promise((resolve,reject)=>{
          // 请求接口上传每片文件
          mergeChunks(data).then((res:any)=>{
            if(res.code == 0){
              if(!stopStatus[uid]){
                this.updateProgress();
                resolve(0)// 成功=> 具体返回参数根据业务逻辑来
              }
            }else{
              stopStatus[uid] = true;
              reject(res.msg)
            }
          }).catch(err=>{
            stopStatus[uid] = true;
            reject(err)
          })
        })
    // })

  }
 
  // 文件分片
  sliceVideoFile(file,attachmentCode) {
    return new Promise((resolve, reject) => {
      const fileSize = parseInt(file.size);
      this.fileSize = fileSize;
      let offset = 0;
      let chunkIndex = 0;
      let totalChunks = 0;
      if(fileSize > 1024 * 1024 * 100){
        this.chunkSize = 1024 * 1024 * 100;
        totalChunks = Math.ceil(fileSize / this.chunkSize); // 一共多少片
      }
      else{
        this.chunkSize = fileSize;
      }
      const chunkSize = this.chunkSize;
      // const totalChunks = Math.ceil(fileSize / chunkSize); // 一共多少片
      let chunkArr = [];// 保存每片数组 <FormData>
      let arrPromise = [];
      const readNextChunk = () => {
        const blob = file.slice(offset, offset + parseInt(chunkSize));
        const fileReader = new FileReader();
        // 文件加载成功
        fileReader.onload = (e) => {
          const chunkData = e.target.result;
          const chunk = {
            data: chunkData,
            index: chunkIndex,
          };
          offset += parseInt(chunkSize);
          chunkIndex++;
          let sha256 = Sha256.sha256
          // 处理切片数据，例如上传到服务器等操作
          const formData1 = new FormData();
          formData1.append('file', blob);// 文件
          formData1.append('chunk', (chunk.index + 1).toString());// 第几片
          formData1.append('identifier', sha256(file.name+file.type+file.size));// 
          formData1.append('fileName', file.name);// 
          chunkArr.push(formData1);
          this.pendingCount++;
          arrPromise.push(this.upChunk(formData1,attachmentCode,file.uid,(offset < fileSize)));
          console.log("source",source,stopStatus)
          if(!stopStatus[file.uid]){
            if (offset < fileSize) {
              if(this.pendingCount < 10){
                console.log("111111111",this.pendingCount);
                readNextChunk();
                this.queueReadNextChunk = function() {};
              }
              // setTimeout(()=>{
              else
                this.queueReadNextChunk = readNextChunk;
              // })
            } else {
              resolve({
                chunkArr, // 文件 FormData[]
                totalChunks,
                arrPromise
              });
            }
          }else{
            resolve({
              chunkArr, // 文件 FormData[]
              totalChunks, // 总共多少片 mun
              arrPromise
            });
          }
        };
        // 文件加载失败
        fileReader.onerror = (e) => {
          reject(e);
        };
        // 加载文件
        fileReader.readAsArrayBuffer(blob);
      };
      readNextChunk();
    });
  }


  // 缓慢增长到目标进度
  changeProgressBar(targetNum){
    cancelAnimationFrame(this.progressT);// 清除定时器
    const animationLoop = () => {
      // 达到目标或者100 停止更新
      if(this.progress >= targetNum || this.progress >= 100){
      }else{
        this.progress++;
        this.onProgressUpdate(this.progress);// 同步更新上传进度
        this.progressT = requestAnimationFrame(animationLoop);// 继续请求下一帧动画
      }
    }
    this.progressT = requestAnimationFrame(animationLoop);
  }
  // 缓慢增长行不通
  changeStraight(targetNum){
    this.progress = targetNum;
    this.onProgressUpdate(this.progress);// 同步更新上传进度
  }
  // 更新上传进度
  updateProgress(res=null){
    if(res){
      let uploadedfile = res.config.data.get('file');
      this.currentChunks++;// 进度+1
      this.uploadedSize = this.uploadedSize + uploadedfile.size;
      this.percent = (this.uploadedSize / (this.fileSize * (1+this.ramdon))) * 100;
    }else{
      this.percent += this.ramdon / (1+this.ramdon) * 100;
    }
    this.changeStraight(this.percent);
  }

  // 上传完成
  succeed(res){
    this.reset();// 重置参数
    this.onUploadComplete(res);// 触发外部失败回调
    source = CancelToken.source();
  }

  // 上传失败
  fail(errTxt){
    console.log("fail")
    this.reset();// 重置参数
    this.onUploadIncomplete(errTxt);// 触发外部失败失败回调
    source = CancelToken.source();
  }

  cancel(){
    source.cancel();
    source = CancelToken.source();
  }

  // 重置参数
  reset(){
    this.progress = 0;// 重置完成进度
    this.totalChunks = 0; // 文件总片数
    this.currentChunks = 0 ;// 当前上传片数
    this.uploadedSize = 0 ;// 当前上传片数
    this.percent = 0 ;// 当前上传片数
    cancelAnimationFrame(this.progressT);// 清除定时器
  }
	
}