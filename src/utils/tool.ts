import { message, Modal, notification } from "ant-design-vue";
// import { createPinia } from 'pinia'
import router from "@/router";
// import { userStore } from "@/store/user"
// const user_store = userStore() //解决启动报"未安装pinia"错误
import { getToken, removeToken, getTimeCookie } from "@/utils/auth";
import moment from "moment";
import Cookies from "js-cookie";
import { createVNode } from "vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import $ from "jquery";
import { userStore } from "@/store/user";
import emiter from "./Bus";
const whitelist = ['/auth','/health/minimal','/host','/osd','/pool']
const parentNodes = ["cloudContent","uncloudRight","contentPadding", "back-page", "layoutContent"];
var timeoutCount = false;
export const modalBindNode = () => {
  let res = parentNodes.find((item,index)=>{
      return document.getElementsByClassName(item).length > 0;
  })
  return document.getElementsByClassName(res)[0] as any;;
}

//按钮权限判断
export const isShowBtn = (value: string) => {
  const permissions: any = JSON.parse(localStorage.getItem('user')).permissions;
  if (permissions !== null) {
    const returnBol = permissions.includes(value) ? true : false;
    if(returnBol)
    return true;
  } else{
    return false;
  }
};

// 请求错误统一处理
export const handleErr = (error: any) => {
  const token = getToken();
  if (error.response) {
    const data = error.response.data;
    switch (error.response.status) {
      case 403:
        message.error("拒绝访问");
        break;
      case 500:
      case 510:
        if (error.response.statusText === "Internal Server Error" && !(data.message && data.message.includes("Token失效"))) {
          message.error("服务连接失败！");
        } else if (error.response.statusText.includes("timeout")) {
          message.error("网络请求超时！");
        } else {
          message.error({
            type: "error",
            message: data.message,
            duration: 2000
          });
        }
        break;
      case 404:
        message.error("很抱歉，资源未找到！");
        break;
      case 504:
        message.error("网络请求超时！");
        break;
      case 401:
        if (token && !whitelist.includes(error.response.config.url)) {
          message.error(data.message ? data.message : "登录已过期或账号已在别处登录");
          setTimeout(() => {
            removeToken();
            location.reload();
          }, 1500);
        }
        break;
      default:
        if(data.message)
          message.error(data.message);
        else if(data.detail)
          message.error(data.detail);
        break;
    }
  } else if(error.data){
    if(error?.data?.code == 0){
      // 无任何限制
      if(location.pathname == '/login-limit'){
        if(router.options.history.state.back){
          history.replaceState(router.options.history.state.back,null);
        }
        else{
          router.replace('/admin/index')
        }
      }
    }
    if(error.data.code == 401){
      if(!whitelist.includes(error.config.url)){
        message.error(error.data.msg);
        // setTimeout(() => {
          removeToken();
          if(getToken() || (new URLSearchParams(window.location.search)).get('token')){
            if(!timeoutCount && error.config.headers.token == (new URLSearchParams(window.location.search)).get('token')){
            console.log("触发次数",error.data,error.config,error.config.headers)
              timeoutCount = true;
              // setTimeout(()=>{
                window.parent.postMessage("clear", "*");
              // },3000)
            }
          }else{
            location.reload();
          }
        // }, 1500);
      }
    }
    if(error.data.code == 412){
      message.error(error.data.msg);
      localStorage.setItem("loginLimit",JSON.stringify({state:1,msg:(error.data.msg ? error.data.msg : '当前时间或ip不允许登录')}))
      router.push('/login-limit');
    }
    if (error.data.code === 500) {
      message.error(error.data.msg);
    }
  }else{
    if (error.code == 401 || error.status == 401) {
      message.error(error.msg);
      if (token) {
        setTimeout(() => {
          removeToken();
          location.reload();
        }, 1500);
      }
    }
    if (error.code === 500) {
      message.error(error.msg);
    }
  }
};

//获取文本字节长度
export const getLength = (val) => {
	var bytesCount = 0;
	if(val && val.length>0){
		var str = new String(val);
		for (var i = 0 ,n = str.length; i < n; i++) {
			var c = str.charCodeAt(i);
			if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {
				bytesCount += 1;
			} else {
				bytesCount += 3;
			}
		}
	}
	return bytesCount;
}
export const jsReadFiles = (files,vnode:any) => {
  return new Promise(function(resolve,reject){
    let file = files[0];
    let reader = new FileReader();//new一个FileReader实例
        reader.onload = function () {
            // $(vnode).append('<pre>' + reader.result + '</pre>');/
            // item = reader.result as string;
            resolve(reader.result)
            // document.getElementById(vnode).innerHTML = reader.result as string;

        };
        reader.readAsText(file);
  })
}

//updateLocalStorage.js
// 重写setItem事件，当使用setItem的时候，触发，window.dispatchEvent派发事件
export const dispatchEventStroage = () => {
  const signSetItem = localStorage.setItem
  localStorage.setItem = function(key, val) {
      let setEvent:any = new Event('setItemEvent')
      setEvent.key = key
      setEvent.newValue = val
      window.dispatchEvent(setEvent)
      signSetItem.apply(this, arguments)
  }
}

// base64 生成图片
export const dataURLtoBlob = (dataurl) => {
  var arr = dataurl.split(','); 
        var nameTime = new Date().getTime();   //不重名
  var _arr = arr[1].substring(0,arr[1].length-2);
  var mime = arr[0].match(/:(.*?);/)[1],
    bstr =atob(_arr),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], {
    type: mime
  });
        //转成file对象
        //return new File([u8arr],`${nameTime}.png`,{type: mime})
};

// 将base64转换为二进制文件流(binary)
export const dataURLtoFile = (dataurl, filename) => {
  var arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, { type: mime })
}

// 日期时间格式化
export const formDate = (date: Date, formate = "0"): string => {
  const year = date.getFullYear();
  const monthAbroad = date.getMonth();
  let month = "";
  if (monthAbroad + 1 > 9) {
    month = String(monthAbroad + 1);
  } else {
    month = "0" + (monthAbroad + 1);
  }
  const day = date.getDate();
  let dayStr = "";
  if (day < 10) {
    dayStr = "0" + day;
  } else {
    dayStr = day + "";
  }
  let week;
  let formateStr = "";
  if (date.getDay() == 0) week = "星期日";
  if (date.getDay() == 1) week = "星期一";
  if (date.getDay() == 2) week = "星期二";
  if (date.getDay() == 3) week = "星期三";
  if (date.getDay() == 4) week = "星期四";
  if (date.getDay() == 5) week = "星期五";
  if (date.getDay() == 6) week = "星期六";
  if (formate === "0") {
    formateStr = year + "年" + month + "月" + dayStr + "日" + "   " + week;
  }
  if (formate === "1") {
    formateStr = year + "-" + month + "-" + dayStr + "" + "   " + week;
  }
  if (formate === "2") {
    formateStr = year + "-" + month + "-" + dayStr;
  }
  return formateStr;
};

export const copy = (data: any) => {
  let url = data;
  let oInput = document.createElement('input');
  oInput.value = url;
  document.body.appendChild(oInput);
  oInput.select();
  document.execCommand('Copy');
  message.success('复制成功')
  oInput.remove()
}

// resize事件
export const handleRsize = (chart: any) => {
  setTimeout(() => {
    if (chart) {
      // eslint-disable-next-line vue/no-ref-as-operand
      chart.resize();
    }
  }, 100);
};
// 添加resize事件
export const addResize = (chart: any) => {
  window.addEventListener("resize", () => {
    handleRsize(chart);
  });
  const siderBar = document.getElementsByClassName("ant-layout-sider-dark");
  siderBar[0].addEventListener("transitionend", () => {
    handleRsize(chart);
  });
};

// 删除resize事件
export const removeResize = (chart: any) => {
  window.removeEventListener("resize", () => {
    handleRsize(chart);
  });
  const siderBar = document.getElementsByClassName("ant-layout-sider-dark");
  siderBar[0].removeEventListener("resize", () => {
    handleRsize(chart);
  });
};

// 将钱转为逗号分隔
export const formatMoney: any = (num: number) => {
  const money = num.toString().indexOf(".") !== -1 ? num.toLocaleString() : num.toString().replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
  return money;
};

// 获取近7周或近12月的开始与截止时间
export const getTwelveWeekOrMonth = (timeType: string) => {
  const endTime = formDate(new Date(), "2");
  let startTime = "";
  if (timeType === "周") {
    let weekDayNum = new Date().getDay();
    if (weekDayNum === 0) {
      weekDayNum = 7;
    }
    startTime = formDate(new Date(new Date().getTime() - (6 * 7 + weekDayNum) * 3600 * 24 * 1000), "2");
  } else {
    const nowMonth = new Date().getMonth() + 1; //获取当前月份
    let beforeMonth = 0; //12月之前的月份
    let yearStr = new Date().getFullYear();
    if (nowMonth % 12 !== 1) {
      beforeMonth = nowMonth + 1;
      yearStr = yearStr - 1;
    } else {
      beforeMonth = 1;
    }
    startTime = formDate(new Date(yearStr + "-" + beforeMonth + "-01"), "2");
  }
  return { startTime: startTime, endTime: endTime };
};

// 删除cookie中所有的token
export const delAllTokenForCookie = () => {
  // 获取cookie字符串
  const cookieStr = document.cookie;
  const cookieArr = cookieStr.split("; ");
  for (let i = 0; i < cookieArr.length; i++) {
    const cookieKeyOfValue = cookieArr[i].split("=");
    if (cookieKeyOfValue[0].indexOf("token") !== -1) {
      Cookies.remove(cookieKeyOfValue[0], {
        domain: "uniontech.com",
        expires: 0
      });
    }
  }
};

// 设置所有系统的token失效时间
export const setOptionAllSystemTookenOutTime = () => {
  // 获取cookie字符串
  const cookieStr = document.cookie;
  const cookieArr = cookieStr.split("; ");
  for (let i = 0; i < cookieArr.length; i++) {
    const cookieKeyOfValue = cookieArr[i].split("=");
    if (cookieKeyOfValue[0].indexOf("token") !== -1) {
      Cookies.set(cookieKeyOfValue[0], cookieKeyOfValue[1], {
        domain: "uniontech.com",
        expires: new Date(Number(getTimeCookie()) + 120 * 60 * 1000)
      });
    }
  }
};

export const setNotopt = (demo, option,subtext = '暂无数据')=> {
  // var option = {
    option.title = {
          text: ' {a|}',
          x: 'center',
          y: 'center',
          subtext,
          itemGap: -20,
          textStyle: {
              rich: {
                a: {
                  color: '#000',
                  fontSize: '16',
                  height: 80,
                  width: 160,
                  backgroundColor: {
                    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNDEiIHZpZXdCb3g9IjAgMCA2NCA0MSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAxKSIgZmlsbD0ibm9uZSIgZmlsbFJ1bGU9ImV2ZW5vZGQiPg0KICAgIDxlbGxpcHNlIGZpbGw9IiNkZGQiIGN4PSIzMiIgY3k9IjMzIiByeD0iMzIiIHJ5PSI3IiAvPg0KICAgIDxnIGZpbGxSdWxlPSJub256ZXJvIiBzdHJva2U9IiM5OTkiPg0KICAgICAgPHBhdGgNCiAgICAgICAgZD0iTTU1IDEyLjc2TDQ0Ljg1NCAxLjI1OEM0NC4zNjcuNDc0IDQzLjY1NiAwIDQyLjkwNyAwSDIxLjA5M2MtLjc0OSAwLTEuNDYuNDc0LTEuOTQ3IDEuMjU3TDkgMTIuNzYxVjIyaDQ2di05LjI0eiIgLz4NCiAgICAgIDxwYXRoDQogICAgICAgIGQ9Ik00MS42MTMgMTUuOTMxYzAtMS42MDUuOTk0LTIuOTMgMi4yMjctMi45MzFINTV2MTguMTM3QzU1IDMzLjI2IDUzLjY4IDM1IDUyLjA1IDM1aC00MC4xQzEwLjMyIDM1IDkgMzMuMjU5IDkgMzEuMTM3VjEzaDExLjE2YzEuMjMzIDAgMi4yMjcgMS4zMjMgMi4yMjcgMi45Mjh2LjAyMmMwIDEuNjA1IDEuMDA1IDIuOTAxIDIuMjM3IDIuOTAxaDE0Ljc1MmMxLjIzMiAwIDIuMjM3LTEuMzA4IDIuMjM3LTIuOTEzdi0uMDA3eiINCiAgICAgICAgZmlsbD0iI2UxZTFlMSIgLz4NCiAgICA8L2c+DQogIDwvZz4NCjwvc3ZnPg==',
                  }
                },
              }
          },
          subtextStyle: {
            fontSize: 16,
          }
      }
      if(option.yAxis.name != null)
      option.yAxis.name = '';
      else if(option.yAxis?.[0])
      option.yAxis[0].name='';
      
  // }
  demo.setOption(option,true)
}



// 数组格式化， 每隔三位使用逗号分开  type=0 整数   type=1 小数,小数默认保留2位
export const numFormat = (num: number | string, type: number) => {
  if (type === 0) {
    return String(num).replace(/(\d)(?=(\d{3})+$)/g, "$1,");
  } else {
    return Number(num)
      .toFixed(2)
      .replace(/(\d)(?=(\d{3})+\.)/g, "$1,");
  }
};

/**
 * 深拷贝
 * @param data
 */
export const cloneDeep: any = (data: any) => {
  return JSON.parse(JSON.stringify(data));
};

/**
 * 树数据展平         这样处理，是为了穿梭框上方显示多少项 的数字显示正确
 * @param list
 * @param dataSource
 */
export const flatten: any = (list: any, dataSource: any) => {
  list.forEach((item: { children: any }) => {
    dataSource.push(item);
    if (item.children) flatten(item.children, dataSource);
  });
  return dataSource;
};

/**
 * 处理左侧树数据
 * @param data          列表数据
 * @param targetKeys    被选中的数据
 * @param direction     描述，列表数据属于左/右侧
 */
export const handleLeftTreeData: any = (data: any, targetKeys: any, direction = "right") => {
  data.forEach((item: { disabled: boolean; key: any; children: any }) => {
    if (direction === "right") {
      // disabled 右侧数据有选中的，给左侧数据添加 disabled属性
      item.disabled = targetKeys.includes(item.key);
    } else if (direction === "left") {
      if (item.disabled && targetKeys.includes(item.key)) item.disabled = false;
    }
    if (item.children) handleLeftTreeData(item.children, targetKeys, direction);
  });
  return data;
};

/**
 * 是否选中
 * @param selectedKeys
 * @param eventKey
 */
export const isChecked: any = (selectedKeys: string[], eventKey: string) => {
  return selectedKeys.indexOf(eventKey) !== -1;
};
// 处理重组selectNavByCloud返回的云平台菜单，只返回parentId为-1的作为菜单
export const listToTreeCloud = (List: any, parentId = -1) => {
  let templist = List.filter(it=>{ if(it.cloudType == 13 && it.type==1){let {name} = it; it.url = 'vmware/host?id='+it.menuId;it.perms=name;};return (it.parentId==parentId)});
  templist.forEach(item=>{
    item.list = listToTreeCloud(List,item.menuId)
  })
  return templist;
};

/**
 * 数组转树
 * @param list
 * @param tree
 * @param parentId
 * @param key
 */
export const listToTree: any = (list: any, tree: any, parentId = -1, key = "parentId") => {
  list.forEach((item: { [x: string]: number; id: any }) => {
    if (item[key] === parentId) {
      const child: any = {
        ...item,
        children: []
      };
      listToTree(list, child.children as any, item.id, key);
      if (!child.children?.length) delete child.children;
      tree.push(child);
    }
  });
  return tree;
};
// 拓扑
export const listToTreeTopo: any = (list: any, tree: any, parentId = -1, key = "parentId",expandObj) => {
  list.forEach((item: { [x: string]: number; id: any }) => {
    if(expandObj){
      if(!expandObj[item.id] && item.parentId !== -1)
        expandObj[item.id] = item.parentId;
    }
    if (item[key] === parentId) {
      const child: any = {
        ...item,
        key: item.id,
        children: []
      };
      listToTreeTopo(list, child.children as any, item.id, key);
      if (!child.children?.length) delete child.children;
      tree.push(child);
    }
  });
  localStorage.setItem("expandObj",JSON.stringify(expandObj))
  return tree;
};

/**
 * 树转数组
 * @param tree
 * @param hasChildren
 */
// export const treeToList: any = (tree: any, hasChildren = false) => {
//   let queen: any = [];
//   const out: any[] = [];
//   queen = queen.concat(JSON.parse(JSON.stringify(tree)));
//   while (queen.length) {
//     const first = queen.shift() as any;
//     if (first?.children) {
//       queen = queen.concat(first.children);
//       if (!hasChildren) delete first.children;
//     }
//     out.push(first);
//   }
//   return out;
// };

/**
 * 处理右侧树数据
 * @param data
 * @param targetKeys
 * @param direction
 */
// export const handleRightTreeData: any = (data: any, targetKeys: string[], direction = "right") => {
//   console.log(data, targetKeys, "handleRightTreeData");
//   const list = treeToList(data);
//   const arr: any = [];
//   const tree: any[] = [];
//   list.forEach((item: { children: any; key: string }) => {
//     if (direction === "right") {
//       if (targetKeys.includes(item.key)) {
//         const content = { ...item };
//         if (content.children) delete content.children;
//         arr.push({ ...content });
//       }
//     } else if (direction === "left") {
//       if (!targetKeys.includes(item.key)) {
//         const content = { ...item };
//         if (content.children) delete content.children;
//         arr.push({ ...content });
//       }
//     }
//   });
//   listToTree(arr, tree, -1);
//   return tree;
// };


export const handleDel = (data,fn,callback,extraName,error,unMessage,cancel) => {
  if(!data || data.length <= 0){
    message.error("请选择要删除的数据");
    return;
  }
  if(unMessage){
    fn(data)
        .then((res: any) => {
          if (res.code === 0) {
            callback();
          } else {
            error()
          }
        })
        .catch((err: any) => {
          error()
        });
  }else{
    Modal.confirm({
      title: "确认删除",
      icon: createVNode(ExclamationCircleOutlined),
      content: extraName ? extraName:"请确认是否删除？",
      okText: "提交",
      cancelText: "取消",
      maskClosable: true,
      getContainer:modalBindNode,
      onOk() {
        emiter.emit('loading',true)
        let fnParam;
        if(typeof extraName == 'function')
        fnParam = fn();
        else
        fnParam = fn;
        fnParam(data)
          .then((res: any) => {
            if (res.code === 0) {
              if(res.data === false)
              message.error((res.msg == 'success' || !res.msg) ? '删除失败' : res.msg)
              else
                message.success('删除成功')
              callback();
            }
          })
      },
      onCancel(){
        if(cancel)
          cancel()
      }
    })
  }
}

export const getlist = async (loading, fn, params, pagination,loop,isMenu) => {
  loading.value = true;
    let res;
    try{
      res = await fn(params)
    }finally{
      loading.value = false;
      let list = []
      if(res){
        if (res.code === 0) {
          if (res.data.currPage > res.data.totalPage && res.data.totalPage !== 0) {
            params.pageIndex = res.data.totalPage;
            loop();
          } else {
            params.pageIndex = res.data.currPage;
          }
          list = res.data.list;
          params.pageSize ? (params.pageSize = res.data.pageSize) : (params.limit = res.data.pageSize);
          pagination.current = res.data.currPage;
          pagination.pageSize = res.data.pageSize;
          pagination.total = res.data.totalCount;
        } else {
          loading.value = false;
        }
      }
      return isMenu?res.data: list;
    }
}
// props 
// 可传值：子组件参数、true和false
// 解释：  子组件参数（修改和新增共用子组件info.vue的情况）
//       boolean类型（是否修改，true为修改，false为新增），组件只有新增或只有修改或未使用子组件的情况
// formRef: ref绑定的表单节点
export const handleSave = (formRef, save, update, props, data, callback,error,pre,unMessage) => {
  formRef.validate().then(async () => {
    if(pre)
    pre();
    let res: any;
    if (typeof props == 'object' ? props.info.isAdd : !props) {
      delete data.id;
      res = await save(data);
      if (res && res.code == 0) {
        if(!unMessage){
          if(res.data === false){
            message.error((res.msg == 'success' || !res.msg) ? '操作失败' : res.msg)
          }else{
            message.success((res.msg == 'success' || !res.msg) ? '操作成功' : res.msg);
            if(typeof props == 'object')
              props.info.isShow = false;
            if(callback)
              callback();
          }
        }
      } else {
        if(error)
        error();
      }
    } else {
      res = await update(data);
      if (res && res.code == 0) {
        if(res.data === false){
          message.error((res.msg == 'success' || !res.msg) ? '操作失败' : res.msg)
        }else{
          message.success((res.msg == 'success' || !res.msg) ? '操作成功' : res.msg);
        }
          if(typeof props == 'object')
          props.info.isShow = false;
          if(callback)
          callback();
      } else {
        if(error)
        error();
      }
    }
    
  }).catch((err)=>{
    // message.error("请补全必填项")
    if(err.errorFields){
      message.error(err.errorFields[0].errors[0])
      formRef.scrollToField(err.errorFields[0].name)
    }else{
      if(error)
        error();
    }
  });
};

export function termfind(str,cha,num){
  var x=str.lastIndexOf(cha);
  for(var i=0;i<num;i++){
      x=str.lastIndexOf(cha,x-1);
  }
  return x;
};
export function downloadText(fileName, text) {
  const url:any = window.URL || window.webkitURL || window;
  const blob = new Blob([text]);
  const saveLink:any = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
  saveLink.href = url.createObjectURL(blob);
  // 设置 download 属性
  saveLink.download = fileName;
  saveLink.click();
}