export const week = ()=>{
    let dateformat=new Date().toLocaleDateString().split('/')
        let val = new Date().getDay()
        let val1
    if(val == 1){
      val1 = '一'
    }else if(val == 2){
      val1 = '二'
    }else if(val == 3){
      val1 = '三'
    }else if(val == 4){
      val1 = '四'
    }else if(val == 5){
      val1 = '五'
    }else if(val == 6){
      val1 = '六'
    }else{
      val1 = '日'
    }
    return dateformat[0]+'年'+dateformat[1]+'月'+dateformat[2]+'日'+' '+'星期'+val1
}