<!-- 一级页面布局（排除顶部，包括左侧菜单、页面内窗口页签、右侧router-view内容部分） -->
<template>
  <!-- <template v-if="!showLeftMenu || !whitelist.includes(submenuId)">
    <a-layout-sider v-show="showLeftMenu" :trigger="null" width="256" style="background: #f9fafc" collapsible>
      <left/>
    </a-layout-sider>
    <a-layout>
      <a-layout-content class="layoutContent">
        <a-spin :spinning="loading" size="large">
          <router-view v-slot="{ Component, route }">
            <keep-alive>
              <component :is="Component" :key="route.path" v-if="route.meta.keepAlive" />
            </keep-alive>
            <component :is="Component" :key="route.path" v-if="!route.meta.keepAlive" />
          </router-view>
        </a-spin>
      </a-layout-content>
    </a-layout>
  </template> -->
  <splitpanes class="default-theme" @resize="splitterClick">
    <pane :style="{ width: paneLWidth }">
      <a-layout-sider :trigger="null" width="auto" style="background: #f9fafc" collapsible>
        <!-- <left/> -->
        <left-new/>
      </a-layout-sider>
    </pane>
    <pane :style="{width:paneRWidth}">
      <a-layout>
        <a-layout-content class="layoutContent">
          <a-spin :spinning="loading" size="large">
            <router-view v-slot="{ Component, route }">
              <keep-alive>
                <component :is="Component" :key="route.path" v-if="route.meta.keepAlive" />
              </keep-alive>
              <component :is="Component" :key="route.path" v-if="!route.meta.keepAlive" />
            </router-view>
          </a-spin>
        </a-layout-content>
      </a-layout>
    </pane>
  </splitpanes>
</template>
<script setup lang="ts">
import { Splitpanes, Pane } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
import Left from "./casemenu/left.vue";
import LeftNew from "./casemenu/left-new.vue";
import Top from "./top.vue";
import Main from "./main.vue";
import { computed, getCurrentInstance, onMounted, reactive, ref, watch } from "vue";
import { indexStore } from "@/store/index";
import { userStore } from "@/store/user";
import { storeToRefs } from "pinia";
import router from "@/router";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { useTemplateRefsList } from "@vueuse/core";
import emiter from "@/utils/Bus";
import { menuStore } from "@/store/menu";
import { layoutStore } from "@/store/layout";
import $ from "jquery";
const { proxy } = getCurrentInstance();
const index_store = indexStore();
const user_store = userStore();
const menu_store = menuStore();
const layout_store = layoutStore();
const whitelist = ref(['devops','config','storage','vmware','networktopology'])
const menulist = user_store.menulist;
const loading = ref(false);
const showLeftMenu = computed(()=>menu_store.get_ShowLeftMenu);
const submenuId = computed(()=>menu_store.get_submenuId);
// console.log('bottom',menuStore().showLeftMenu)
const tabsRefs: any = useTemplateRefsList<HTMLDivElement>();
const route = useRoute();
const tabScrollbarRef: any = ref();
const isManager = ref(false);
const breadcrumbArr = ref([]);
const paneLWidth = ref(layout_store.get_paneLWidth);
const paneRWidth = ref(layout_store.get_paneRWidth);
const splitterClick = (e,b)=>{
  paneLWidth.value = e[0].size+'%';
  paneRWidth.value = e[1].size+'%';
  layout_store.setPaneLWidth(e[0].size+'%');
  layout_store.setPaneRWidth(e[1].size+'%');
}
// 获取面包屑地址
const getBreadcrumb = () => {
  // console.log('route.matched',route.matched)
  // 判断是否首页的状态
  let indexFlag = false;
  // 获取路由信息，路由信息为一个数组，去掉最开始的第一个元素，如果只有一个元素那就是首页
  breadcrumbArr.value = route.matched.filter(
    item => item.meta && item.meta.title
  );
  breadcrumbArr.value.forEach(item => {
    // 判断是首页
    if (item.path === "/index") {
      indexFlag = true;
    }
  });
  if (breadcrumbArr.value.length > 1) {
    if (indexFlag) {
      breadcrumbArr.value = [breadcrumbArr.value[0]];
    }
  } else {
    breadcrumbArr.value = [];
  }
};
// 深度监听路由，开始就执行；每个菜单和路由选中高亮对应
watch([()=>router.currentRoute.value,showLeftMenu,submenuId],(to,from)=>{
  if(window.location.search && window.location.search.includes("token")){
    paneLWidth.value = '0';
    paneRWidth.value = '100%';
  }else{
    if(to[0])
    getBreadcrumb();
    if(!whitelist.value.includes(to[0].path.split('/')[2])){
      if(!whitelist.value.includes(to[2]))
        $('.splitpanes__splitter').css('display', 'none');
      else{
        $('.splitpanes__splitter').css('display', 'inherit');
      }
      if(to[1] === false){
        $('.splitpanes__splitter').css('display', 'none');
        paneRWidth.value = '100%';
        layout_store.setPaneLWidth('0');
        layout_store.setPaneRWidth('100%');
      }else{
        paneLWidth.value = '256px';
        paneRWidth.value = 'calc(100vw - 263px)';
        layout_store.setPaneLWidth('256px');
        layout_store.setPaneRWidth('calc(100vw - 263px)');
      }
    }
  }
},{immediate:true,deep:true})
onMounted(() => {
  if(window.location.search && window.location.search.includes("token")){
    paneLWidth.value = '0';
    paneRWidth.value = '100%';
  }else{
    paneLWidth.value = layout_store.get_paneLWidth;
    paneRWidth.value = layout_store.get_paneRWidth;
    if(!whitelist.value.includes(submenuId.value))
      $('.splitpanes__splitter').css('display', 'none');
    else{
      $('.splitpanes__splitter').css('display', 'inherit');
    }
    if(!showLeftMenu.value){
      $('.splitpanes__splitter').css('display', 'none');
      paneRWidth.value = '100%';
      layout_store.setPaneLWidth('0');
      layout_store.setPaneRWidth('100%');
    }else{
      paneLWidth.value = '256px';
      paneRWidth.value = 'calc(100vw - 263px)';
      layout_store.setPaneLWidth('256px');
      layout_store.setPaneRWidth('calc(100vw - 263px)');
    }
    console.log("bottom")
    emiter.on("loading", (e) => {
      console.log("loading",e)
      loading.value = e;
    });
  }
});
</script>
<style lang="scss" scoped>
#components-layout-demo-custom-trigger {
  width: 100vw;
  height: 100vh;
}
.ant-spin-container {
  position: relative;
}
.nav-tabs {
  background-color: #fff;
  margin: 0 20px 16px 20px;
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none;
  .arrow-pre,
  .arrow-next {
    cursor: auto;
    color: #666;
    height: 40px;
    width: 20px;
    line-height: 41px;
    position: absolute;
    background-color: #fff;
  }
  .arrow-pre {
    top: 0;
    left: 0;
    box-shadow: 2px 0px 3px #eee;
  }
  .arrow-next {
    top: 0;
    right: 0;
    box-shadow: -2px 0px 3px #eee;
  }
  &::-webkit-scrollbar {
    height: 5px;
  }
  &::-webkit-scrollbar-thumb {
    background: #eaeaea;
    border-radius: var(--el-border-radius-base);
    border: none;
    box-shadow: none;
    -webkit-box-shadow: none;
  }
  &::-webkit-scrollbar-track {
    background-color: #f0f0f0;
  }
  &:hover {
    &::-webkit-scrollbar-thumb:hover {
      background: #c8c9cc;
    }
  }
}
.ba-nav-tab {
  cursor: pointer;
  white-space: nowrap;
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  color: #666;
  border-right: 1px solid #eee;
  &.active {
    color: #1890ff;
    background-color: rgba($color: #1890ff, $alpha: 0.1);
  }
}
.layoutContent{
  padding: 16px 0;
}
// .splitpanes__pane:first-child {
//   width: 256px !important;
// }
// :deep(.ant-spin-container){margin: 0 16px;overflow-x: auto;}
</style>
