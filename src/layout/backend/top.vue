<template>
  <!-- <div> -->
    <!-- <a-affix :offset-top="0.1" style="width:100%;"> -->
      <div class="top">
        <div class="admin-top-logo" title="首页" @click="backToHome">
        <img src="@/assets/logo.png" class="imgShow" alt="" v-if="shortTitle == 'UStack'"/>
        <!-- <img src="@/assets/logo.png" class="imgHidden" alt="" v-show="collapsed" /> -->
        <span v-else>{{shortTitle}}</span>
      </div>
        <div class="admin-top-wrap">
          <div class="left">
            <Topmenu ref="topmenuRef" />
          </div>
          <div class="headuserright">
            <a-tooltip title="通知" v-if="!ticketlist || ticketlist.length <= 0">
              <MailOutlined style="font-size:20px;" @click="Tomessage"/>
            </a-tooltip>
            <a-popover :title="h('b','通知')" placement="bottomLeft" v-else>
              <template #content>
                <a-list size="small" item-layout="horizontal" style="max-height:240px;width:232px;overflow-y:scroll;" :data-source="ticketlist" >
                  <template #renderItem="{ item }">
                    <a-list-item  @click="bttomrigth(item)">
                      <a-list-item-meta style="margin-left:10px ;">
                        <template #title>
                          <a >{{ item.noticeTitle }}</a>
                        </template>
                        <template #description>
                          <div style="height:24px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis" v-html="item.noticeMessage">
                          </div>
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </template>
              <a-badge :count="messagenumber">
                <MailOutlined style="font-size:20px;color:#fff;" @click="Tomessage"/>
              </a-badge>
            </a-popover>
            <a-tooltip title="关于">
              <InfoCircleOutlined @click="info" style="font-size:20px;cursor:pointer;margin:0 20px"/>
            </a-tooltip>
            <a-tooltip title="使用手册">
              <QuestionCircleOutlined @click="about" style="font-size:20px;cursor:pointer"/>
            </a-tooltip>
            
            <div class="userInfoClass" >
            <a-dropdown placement="bottomRight">
                  <div style="height:90%">
                    <img v-if="NewImg!='' " class="userimg" :src="NewImg" alt="">
            <img v-else class="userimg" :src="tempImg"  alt="">
                    {{ loginName }}
                    <DownOutlined />
                  </div>
                <template #overlay>

                 <a-menu mode="horizontal">
                  <a-menu-item key="setting:1" @click="GOPcenter" >
                    <UserOutlined /> {{ $t("m.PersonalCenter") }}
                  </a-menu-item>
                  <!-- <a-menu-item key="setting:2" @click="info" >
                    {{ $t("m.about") }}
                  </a-menu-item> -->
                  <a-menu-item key="setting:3" @click="logOut">
                    <LogoutOutlined /> 退出系统
                  </a-menu-item>
                </a-menu>
                </template>
                </a-dropdown>
              <!-- </a-sub-menu> -->
            <!-- </a-menu> -->
            </div>
           
          </div>
        </div>
      </div>
 <!-- </a-affix> -->
  <!-- </div> -->
</template>
<script setup lang="ts">
import Topmenu from "./casemenu/topmenu.vue";
import { MenuUnfoldOutlined, MenuFoldOutlined } from "@ant-design/icons-vue";
import { getUsername, removeToken, removeUsername, removeUserId, getToken } from "@/utils/auth";
import { delAllTokenForCookie, listToTreeCloud } from "@/utils/tool";
import { filterRouter } from "@/utils/route";
import { Modal, notification } from "ant-design-vue";
import { ref, reactive, computed, watch, onMounted, getCurrentInstance,defineComponent, h } from "vue";
import { useRouter, useRoute,onBeforeRouteUpdate, onBeforeRouteLeave } from "vue-router";
import { indexStore } from "@/store/index"
import { userStore } from "@/store/user"
import { storeToRefs } from "pinia";
import { useTagsStore } from '@/store/tags'
import { CloseOutlined,CloseCircleFilled, UserOutlined,BellOutlined ,MailOutlined } from "@ant-design/icons-vue";
import { useTemplateRefsList } from '@vueuse/core'
import {getSysnoticemessagelsit,selectIndexCloudList,selectNumber} from "@/api/backend";
import {selectCloudList} from "@/api/backend/cloud";
import { selectNavByCloud } from "@/api/backend/systems/menu";
import { selectOpenstList } from "@/api/backend/devops/domain";
import { selectProjectList } from "@/api/backend/devops/project";
import tempImg from "@/assets/touxiang.jpg";
import { selectSysconfig } from "@/api/backend/public";
import { menuStore } from "@/store/menu";
import source from '../../assets/统信云基础设施管理平台用户操作手册-20240229.pdf';

import emiter from "@/utils/Bus";
const ticketlist = ref([]);
const index_store = indexStore()
const user_store = userStore()
const menu_store = menuStore()
const { proxy,appContext }: any = getCurrentInstance();
const topmenuRef = ref()
const loading = ref(false);
const cloudId = ref()
const cloudType = ref(localStorage.getItem('cloudType'))
const domainId = ref(undefined)
const projectId = ref(undefined)
const loginName: any = ref("");
const isManager = ref(false);
const router: any = useRouter();
const route: any = useRoute();
// openstack平台下的路由，这些路由需要显示顶部的切换下拉框
const whitePath = ['/admin/devops/index','/admin/devops/flavor','/admin/devops/floatingip','/admin/devops/image','/admin/devops/network','/admin/devops/openstack',
'/admin/devops/project','/admin/devops/secugroup','/admin/devops/server','/admin/vmware/host','/admin/devops/route','/admin/devops/key','/admin/storage/Volume',
'/admin/devops/topology','/admin/storage/volume','/admin/storage/volumeSnapshot','/admin/storage/volumeType','/admin/config/rule',"/admin/storage/qos","/admin/devops/cluster",
'/admin/devops/profile','/admin/config/ironic']
const maplist:any=ref([])
const shortTitle = ref('UStack')
const tags = useTagsStore();
const NewImg=ref(user_store.img)
const Tomessage=()=>{
  setTimeout(()=>{
    menu_store.setShowLeftMenu(false)
  },500)
  router.push({path:'/admin/systems/MessageCenter'})
  index_store.addTag({menuId: '消息中心', name: '消息中心', url: 'systems/MessageCenter'})
 }
const GOPcenter=()=>{
  setTimeout(()=>{
    menu_store.setShowLeftMenu(false)
  },500)
      router.push({path:'/admin/systems/PersonalCenter'})
      index_store.addTag({menuId: '个人中心', name: '个人中心', url: 'systems/PersonalCenter'})
 }
//  头像位置下的关于弹出对话框
const info = () => {
      Modal.info({
        title: () => '关于',
        content: () => h('div', {style:'margin-top:18px'}, [
          h('p', '版本：UStack V1.3'),
          h('p', '授权用户：统信软件技术有限公司'),
          h('p', {}, [h('span','统信官网：'),h('a', {href: 'https://www.uniontech.com',target:'_blank'}, 'www.uniontech.com')]),
          h('p', '统信软件以“打造操作系统创新生态，给世界更好的选择”为愿景，专注于操作系统的研发与服务，发展和建设以中国技术为核心的创新生态，致力于为不同行业提供安全稳定、智能易用的产品与解决方案，力争在十年内成为全球主要的基础软件供应商。'),
          h('p', '作为以技术创新为基因的高科技企业，统信软件在操作系统研发、行业定制、国际化、迁移适配、交互设计等多方面拥有深厚的技术积淀，能够满足不同用户和应用场景对操作系统产品的广泛需求，可为政企行业信息化、数字经济建设提供坚实可信的基础支撑，重塑信息产业发展底座。'),
        ]),
        okText:'知道了',
        maskClosable: true,
        zIndex:1003
      });
    };
const about = () => {
  window.open('/pdfjs/web/viewer.html?file=' + location.origin + source)
}
const showModal = ref(false);
const messagenumber=ref("")
// 退出
const logOut = () => {
  emiter.emit("allLoading",true)
  user_store.Logout()
    .then((res: any) => {
      emiter.emit("allLoading",false)
      if (res.code === 0) {
        console.log("topClear")
        localStorage.clear();
        removeToken();
        removeUsername();
        removeUserId();
        delAllTokenForCookie();
        // 判断是否使用cas单点登录
        if (import.meta.env.VITE_CAS === "cas") {
          const url = res.data + "?url=" + document.location.href;
          window.location.href = url;
        } else {
          router.push("/admin/login");
        }
      }
    }).catch(()=>{
      emiter.emit("allLoading",false)
    })
};
// 获取消息列表，有消息时鼠标悬浮于图标可显示列表
const getList = async () => {
   await getSysnoticemessagelsit({transactState:"0"}).then((res)=>{
      // console.log("1111111",res)
       ticketlist.value=res.data
      //  ticketlist.value=[]
    })
    await selectNumber({transactState:"0"}).then((res)=>{
      if(res.code==0){
      messagenumber.value=res.data
      // messagenumber.value=2
      // console.log("数量", messagenumber.value)
      }
    })
}
const setTopLogo = () => {
  if(index_store.configs.shortTitle)
    shortTitle.value = index_store.configs.shortTitle;
}
// 点击一条跳转消息详情页
const bttomrigth=(item)=>{
    router.push({path:'/admin/systems/MessageDetails',query:{ticketId:item.id}})
    index_store.addTag({menuId: '消息详情', name: '消息详情', url: 'systems/MessageDetails',query:{ticketId:item.id}})
}
const cloudlist = ref([])
const domainlist = ref([])
const projectlist = ref([])
const menuList = ref([])
const backToHome = ()=>{
  router.push('/admin/index');
  index_store.setTaglist();
  // if(isManager.value)
  // setTimeout(()=>{
    menu_store.setShowLeftMenu(false);
  // })
  menu_store.setMenuId('');
  topmenuRef.value.current = [0];
  menu_store.setOpenKeys([]);
  menu_store.setSelectedKeys([])
}
onMounted(() => {
  setTopLogo()
  // 监听窗口宽度，以折叠展开左侧菜单
  window.addEventListener('resize',(e)=>{
    let clientWidth = document.documentElement.clientWidth;
    if(clientWidth <= 1024){
      index_store.set_collaped(true)
    }else{
      index_store.set_collaped(false)
    }
  })
  // proxy.$mitt.on('setCloudId',(e)=>{getCloudList(e);cloudType.value = localStorage.getItem('cloudType');})
  proxy.$mitt.on('backToHome',backToHome)

  if(user_store.userInfo?.attachmentId){
    const token = getToken();
    let tempImgUrl = import.meta.env.VITE_BASE_API+'/sys/attachment/download?id='+user_store.userInfo?.attachmentId+'&token='+token;
    if(tempImgUrl)
    NewImg.value = tempImgUrl;
    else
    NewImg.value = tempImg;
  }else{
    NewImg.value = tempImg;
  }
    // menu_store.setShowLeftMenu(true)
  // if(user_store.userInfo?.roleIdList.indexOf(1) != -1){
  //   isManager.value = true;
  // }else{
  //   isManager.value = false;
  //   console.log('mana',menu_store.showLeftMenu)
  // }
  proxy.$mitt.on('setAvatar', (e)=>{NewImg.value = e;})
  loginName.value = getUsername();
   getList()
  let timer = null;
//每5min刷新数据
  timer = setInterval(() => {
     getList()
  }, 300000);
 
  // getCloudList();
  proxy.$mitt.on('RefreshNotice',getList)
});
</script>
<style lang="scss" scoped>
.admin-top-logo{
  cursor: pointer;
  min-width: 259px;
  text-align: center;
  img{height: 54px;}
  span{
    vertical-align: middle;
    font-size: 28px;
    font-weight: 1000;
    padding-left: 4px;
    color: #fff;
    // background-image: radial-gradient(at left top,#20CCFE,#2789FC,#063EE1);
    // -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
  }
}
.tags {
  position: relative;
  height: 40px;
  overflow: hidden;
  background: #fff;
  padding-right: 120px;
}
.tags ul {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.tags-li {
    float: left;
    margin: 5px 5px 2px 3px;
    border-radius: 3px;
    font-size: 12px;
    overflow: hidden;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    border: 1px solid #c0c0c0;
    background: #FAFAFA;
    padding: 0 5px 0 12px;
    vertical-align: middle;
    color: #ccc;
    -webkit-transition: all 0.3s ease-in;
    -moz-transition: all 0.3s ease-in;
    transition: all 0.3s ease-in;
}

.tags-li:not(.active):hover {
    background: #1890FF;
}

.tags-li.active {
    color: #ccc;
}

.tags-li-title {
    float: left;
    max-width: 80px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 5px;
    color: #CFCFE7;
}

.tags-li.active .tags-li-title {
    color: #fff;
}

.tags-close-box {
    position: absolute;
    right: 0;
    top: 0;
    box-sizing: border-box;
    padding-top: 1px;
    text-align: center;
    width: 110px;
    height: 30px;
    background: #fff;
    box-shadow: -3px 0 15px 3px rgba(0, 0, 0, 0.1);
    z-index: 10;
}



.top {
  // height: 66px;
  width: 100%;
  display: flex;
  // background-color: #1D2D3D;
  color: #fff;
  width: calc(100% + 4px);
  position: absolute;
  z-index: 999;
  left: -4px;
}
.breadcrumbClass {
  width: 100%;
  // width: calc(100% - 3px);

  border-top: 1px solid #ccc;
  background: #fff;
  position: absolute;
  right: 0;
  height: 45px;
  padding: 1px 10px;
  line-height: 45px;
  top: 65px;
}
.admin-top-wrap {
  // height: 66px;
  // line-height: 66px;
  // position: relative;
  width: calc(100% - 224px);
  display: flex;
    justify-content: space-between;
    overflow-x: auto;
    overflow-y: hidden;
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
  .left{
    // flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
  // overflow: hidden;
  // min-width: 100%;
  // overflow: hidden;
  }
}
.headuserright{
  min-width: 234px;
  float: right;
  
  // background: palegoldenrod;
  display: flex;
  justify-content: space-between;
  align-items: center;
 
}

.userInfoClass {
  // float: right;
  // position: absolute;
  // right: 0;
  // top: 0;
  // right: 0;
  font-size: 16px;
  cursor: pointer;
  margin: 0 20px;
  min-width: 146px;
  display: inline-block;
  // background-color: #1D2D3D;
  // padding-right: 14px;
  
}
.ant-dropdown-menu{padding: 7px 0;};
  :deep(.ant-dropdown-menu-item){padding: 10px 0;text-align: center};
.ant-menu-horizontal {
  border-bottom: 0px;
  background: #1D2D3D;
  color: #fff;
  font-size: 16px;
}
.ant-menu-item-group .ant-menu-item-group-title {
  padding: 0px;
}
.ant-menu-item-group-list .ant-menu-item {
  text-align: center;
  padding: 0px;
}
:deep(.ant-menu-submenu-popup) {
  margin-top: -6px !important;
}
.userimg{
    width: 30px;
    height: 30px;
    object-fit: cover;
    border-radius:50%;
    background-color: #fafafa;
   
}
:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector){
background-color: transparent;
}
:deep(.ant-select-arrow){color: #fff;}
:deep(.ant-select-clear){background-color: #1D2D3D;color: #fff;&:hover{background-color: #1D2D3D;color: #fff;}}
:deep(.ant-select){color: #fff;}
.ant-badge{font-size: 20px;}
.ant-avatar{width: auto;height: auto;line-height: normal;}
:deep(.ant-avatar.ant-avatar-icon){font-size: 20px;}
// :deep(.ant-list){width: 145px;}
:deep(.ant-list-item-meta-title){
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
:deep(.ant-list-sm .ant-list-item){padding: 8px 0;}
// .clearfix:before,
//   .clearfix:after {
//     display: table;
//     content: '';
//   }

//   .clearfix:after {
//     clear: both;
//   }
:deep(.ant-menu.ant-menu-dark){background: transparent;}
:deep(.ant-menu-dark .ant-menu-item){
color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    font-size: 16px;
}

</style>
