<!-- 路由嵌套关系在router/index.ts配置中查看，动态添加的路由在permission.ts中查看 -->
<template>
  <div class="">
    <a-layout>
      <a-spin :spinning="allLoading" size="large">
        <a-layout-header style="background: #1D2D3D; padding: 0">
          <top />
        </a-layout-header>
        <a-layout id="components-layout-demo-custom-trigger">
          <router-view></router-view>
        </a-layout>
      </a-spin>
    </a-layout>
  </div>
</template>
<script setup lang="ts">
import { onBeforeRouteLeave } from "vue-router";
import Top from "./top.vue";
import { indexStore } from "@/store/index"
import { onMounted, ref, watch } from "vue";
import emiter from "@/utils/Bus";
import router from "@/router";
const index_store = indexStore()
const allLoading = ref(false)
watch(()=>router.currentRoute.value,(to,from)=>{
  const metaInfo = router.currentRoute.meta || {}; // 获取当前路由的meta信息
    
  if (!metaInfo.refreshed) {
      console.log('这是第一次加载该页面');
      // emiter.emit('allLoading',true)
      // if(allLoading.value != true)
      // allLoading.value = true;
      // 设置已经刷新过标记
      metaInfo.refreshed = true;
  }
},{immediate:true,deep:true})
onMounted(()=>{
  console.log("allLoading")
  emiter.on("allLoading", (e) => {
    if(allLoading.value != e)
      allLoading.value = e;
  });
})
</script>
<style lang="scss" scoped>
#components-layout-demo-custom-trigger {
  width: 100vw;
  height: 100vh;
}
.ant-layout-sider-dark {
  flex: 0 0 200px !important;
  max-width: 200px !important;
  min-width: 200px !important;
  width: 200px !important;
}
.ant-layout-sider-collapsed {
  flex: 0 0 80px !important;
  max-width: 80px !important;
  min-width: 80px !important;
  width: 80px !important;
}

</style>
