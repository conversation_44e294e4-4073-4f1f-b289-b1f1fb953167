<template>
    <div class=''>
        <a-menu v-model:selectedKeys="current" mode="horizontal" theme="dark" @select="selectMenu">
            <a-menu-item v-for="(item,index) in menulist" :key="item.url" :menuId="item.menuId" :list="item.list">
                <template #icon>
                    <component :is="$icons[item.icon]" />
                </template>
                <span>{{ item.name }}</span>
            </a-menu-item>
        </a-menu>
    </div>
</template>
<script lang='ts' setup>
import router from '@/router';
import { menuStore } from '@/store/menu';
import { userStore } from '@/store/user';
import emiter from '@/utils/Bus';
import { storeToRefs } from 'pinia';
import { getCurrentInstance, onMounted, ref, watch } from 'vue';
import { onBeforeRouteLeave, useRoute } from 'vue-router';
const route = useRoute();
const user_store = userStore();
const menu_store = menuStore();
// selectNav获取的非云平台菜单(菜单管理可控菜单)
const {menulist} = storeToRefs(user_store);
const { submenuId,SelectedKeys } = storeToRefs(menu_store)
const {proxy} = getCurrentInstance();
let temp;
if(SelectedKeys.value.length > 0)
temp = route.path.split('/')[2];
const current = ref<string[]>([temp]);
const single_submenu = ['report','networktopology'];
// key为顶部菜单的key(值为每个目录的url)
const selectMenu = ({item, key, selectedKeys}) => {
    if(key == 'devops'){ //云平台管理
    menu_store.setShowLeftMenu(true)
    router.push("/admin/devops/menu/cloud")
    return;
        emiter.emit('allLoading',true)
        current.value[0] = key;
        // menu_store.setShowLeftMenu(true)
        proxy.$mitt.emit('SelectCloudList',{isAdd:undefined,key})
    }else if(key == 'report'){
        menu_store.setShowLeftMenu(false)
        router.push('/admin/report')
    }else if(key == 'networktopology'){
        menu_store.setMenuId(key)
        menu_store.setShowLeftMenu(true)
        router.push('/admin/networktopology')
    }else{ //其他管理
    console.log("item",item)
        // emiter.emit('allLoading',true)
        if(key)
          menu_store.setMenuId(key)
        console.log("topset",menu_store.submenuId)
        menu_store.setShowLeftMenu(true)
        menu_store.setSystemMenus(item.list);
        // proxy.$mitt.emit('setLeftMenu')
        router.push('/admin/'+item.list[0].url);
    }
}
// 监控全局
watch(()=>router.currentRoute.value,(to,from)=>{
    let whitelist = ['/admin/systems/MessageCenter','/admin/systems/PersonalCenter','/admin/systems/MessageDetails','/admin/index','']
    if(!whitelist.includes(to.path)){
        if(to.path.split('/')[2] == 'vmware' || to.path.split('/')[2] == 'config' || to.path.split('/')[2] == 'storage'){
            current.value[0] = 'devops';
        }else if(to.path.split('/')[2] == 'ldap'){
            current.value[0] = 'systems';
        }else{
            current.value[0] = to.path.split('/')[2];
        }
        if(current.value.length > 0 && to.path.split('/')[2] && to.path.split('/')[2] != 'index' && to.path.split('/')[2] != 'report'){
            setTimeout(()=>{
                menu_store.setShowLeftMenu(true)
            })
            menu_store.setMenuId(to.path.split('/')[2])
        }
    }else{
        current.value = [];
        menu_store.setShowLeftMenu(false)
    }
},{immediate:true,deep:true})
onMounted(() => {
    if(route.path == '/admin/index'){
        setTimeout(()=>{
            menu_store.setShowLeftMenu(false)
        },500)
    }
    window.addEventListener("popstate", function(e) { 
        console.log("e.state",e)
        if(e && e.state && e.state.current){
            if(e?.state?.current == '/admin/index'){
                setTimeout(()=>{
                    menu_store.setShowLeftMenu(false)
                },500)
            }
        }
    }, false);
})

defineExpose({current})
</script>
<style lang='scss' scoped>
:deep(.ant-menu-dark.ant-menu-horizontal > .ant-menu-item:hover){background-color: transparent;}
:deep(.ant-menu.ant-menu-dark .ant-menu-item-selected){background-color: transparent;}
</style>