<template>
<!--  v-if="menu_store.showLeftMenu && !whitelist.includes(submenuId) -->
    <a-menu mode="inline" v-model:selectedKeys="selectedKeys" @select="selectLeftMenu" :openKeys="openKeys" :class="whitelist.includes(submenuId) ? 'lessheight' : 'allheight'">
        <a-menu-item v-for="(item,index) in menulist" :key="'/admin/' + item.url">
            <router-link :to="'/admin/' + item.url">
                {{ item.name }}
            </router-link>
        </a-menu-item>
    </a-menu>
</template>
<script lang='ts' setup>
import router from '@/router';
import { menuStore } from '@/store/menu';
import { onMounted, ref, watch } from 'vue';
const menu_store = menuStore();
const menulist = ref([]);
const whitelist = ['devops','config','storage','vmware'];
const selectedKeys = ref([]);
watch(()=>router.currentRoute.value,(to,from)=>{
    console.log("watch",menu_store.submenuId)
    if(!whitelist.includes(menu_store.submenuId)){
        selectedKeys.value = [router.currentRoute.value.path];
        menulist.value = menu_store.SystemMenus;
    }
},{immediate:true,deep:true})
onMounted(() => {})
</script>
<style lang='scss' scoped>
</style>