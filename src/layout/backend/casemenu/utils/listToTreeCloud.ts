export const listToTreeCloud = (List: any, parentId = -1) => {
    let templist = List.filter(it=>{ 
        if(it.cloudType == 13 && it.type==1){
            let {name} = it; 
            it.url = 'vmware/host?id='+it.menuId;
            it.perms=name;
        }else{
            it.key = '/admin/'+it.url;
            it.title = it.name;
        };
        return (it.parentId==parentId)
    });
    templist.forEach(item=>{
      item.list = listToTreeCloud(List,item.menuId)
    })
    return templist;
  };