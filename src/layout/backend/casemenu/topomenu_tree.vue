<template>
  <a-tree
    :tree-data="treeData"
    v-model:expandedKeys="expandedKeys"
    v-model:selectedKeys="selectedKeys"
  >
  <template #switcherIcon>
    <i class="ant-menu-submenu-arrow"></i>
  </template></a-tree>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue';
import { TreeDataItem } from 'ant-design-vue/es/tree/Tree';
import topomenu from "./topomenu.json";

export default defineComponent({
  setup() {
    const expandedKeys = ref<string[]>([]);
    const selectedKeys = ref<string[]>([]);
    const treeData = ref<TreeDataItem[]>(topomenu);
    // const onLoadData = (treeNode: any) => {
    //   return new Promise((resolve: (value?: unknown) => void) => {
    //     if (treeNode.dataRef.children) {
    //       resolve();
    //       return;
    //     }
    //     setTimeout(() => {
    //       treeNode.dataRef.children = [
    //         { title: 'Child Nodeutdlut.dtysrtsjrtajeuyfgujhg,jh', key: `${treeNode.eventKey}-0` },
    //         { title: 'Child Node', key: `${treeNode.eventKey}-1` },
    //       ];
    //       treeData.value = [...treeData.value];
    //       resolve();
    //     }, 1000);
    //   });
    // };
    return {
      expandedKeys,
      selectedKeys,
      treeData,
    };
  },
});
</script>
<style lang="scss">
// .ant-menu ul{padding: 0 24px !important;}
    // :deep(.ant-tree li){padding: 8px 0;}
.ant-tree{
    // padding: 0 34px 0 24px !important;
    li{
      
        padding: 0 !important;
        .ant-tree-node-content-wrapper{
          width: 100%;
          padding: 0 34px 0 24px !important;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 40px !important;
          height: 40px !important;
        }
        span.ant-tree-switcher{
            float: right;
            right: 14px;
            top: 8px;
            .ant-tree-switcher-icon{
    position: absolute;
    top: 50%;
    right: 10px;
    width: 10px;
    color: #000000d9;
    
    transition: transform .3s cubic-bezier(.645,.045,.355,1);
}
        }
    }
    
} 
.ant-tree-switcher.ant-tree-switcher_close > .ant-tree-switcher-icon{
  transform: translateY(-50%) !important;
}
.ant-tree-switcher.ant-tree-switcher_open > .ant-tree-switcher-icon{
  transform: translateY(-2px) !important;
}
.ant-tree-switcher.ant-tree-switcher_close > .ant-tree-switcher-icon:before{transform:rotate(-45deg) translate(2.5px);}
.ant-tree-switcher.ant-tree-switcher_close > .ant-tree-switcher-icon::after{transform: rotate(45deg) translate(-2.5px);}
.ant-tree-switcher.ant-tree-switcher_open>.ant-tree-switcher-icon:before{transform: rotate(45deg) translate(2.5px);}
.ant-tree-switcher.ant-tree-switcher_open>.ant-tree-switcher-icon:after{transform: rotate(-45deg) translate(-2.5px);}

.ant-tree-switcher-icon:before, .ant-tree-switcher-icon:after {
    position: absolute;
    width: 6px;
    height: 1.5px;
    background-color: currentColor;
    border-radius: 2px;
    transition: background .3s cubic-bezier(.645,.045,.355,1), transform .3s cubic-bezier(.645,.045,.355,1), top .3s cubic-bezier(.645,.045,.355,1), color .3s cubic-bezier(.645,.045,.355,1);
    content: "";
}
</style>
