<template>
    <a-menu mode="inline" :class="class" v-model:selectedKeys="state.selectedKeys" v-model:openKeys="state.openKeys" @click="toMenu" @openChange="(keys)=>onOpenChange(rawKey,keys)">
        <template v-for="(item,index) in data">
            <template v-if="item?.children">
                <sub-menu ref="SubMenu" :data="item" @set_selectkeys="setSelectedKeys" @onIconChange="onIconChange"></sub-menu>
            </template>
            <template v-else>
                <div @click.stop>
                    <a-menu-item :key="item.key">{{item.title}}</a-menu-item>
                </div>
            </template>
        </template>
    </a-menu>
</template>

<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import topomenu from "./topomenu.json";
import subMenu from './subMenu.vue';
import $ from "jquery";
import { topoStore } from '@/store/topo';
import emiter from '@/utils/Bus';
import { getToken } from '@/utils/auth';
import router from '@/router';
import { menuStore } from '@/store/menu';
const topo_store = topoStore();
const menu_store = menuStore();
const emit = defineEmits(["DomainListAPI","ProjectListAPI","MenusListSET"]);
interface subMenu {
  title:string,
  key:number,
  children?: subMenu[]
}
const props = defineProps<{
    class:String,
    data:subMenu[]
}>()
const whitelist = ['devops','config','storage','vmware'];
const SubMenu = ref();
// const data = reactive<subMenu[]>(
//     topomenu
// )
const state = reactive({
    selectedKeys:[],
    openKeys:[]
})
const rawOpenKeys = ref([]);
const rawKey = ref();
const queryformat = (keyPath) => {
    let query = {};
    keyPath.forEach((item,index)=>{
        if(index < 3)
            query[item.split("_")[0]+'Id'] = item.split("_")[1];
    })
    return query;
}
const toMenu = ({ item, key, selectedKeys, keyPath }) => {
    topo_store.setKey(key);
    topo_store.setIsLeaf(1);
    $('.ant-menu-submenu-title').css('background','transparent');
    router.push({path:key,query:{...queryformat(keyPath),thirdProjectId:item.thirdProjectId}})
}
const setSelectedKeys = (e)=>{
    
    // rawOpenKeys.value = state.openKeys;
    rawKey.value = e.key;
    if(rawOpenKeys.value.includes(e.key)){
        // state.openKeys = rawOpenKeys.value;
    }
    state.selectedKeys = [];
    if(e.cloudTypeText == "OPENSTACK"){
        emit("DomainListAPI",e.key);
        router.push({path:"/admin/devops/menu/cloud",query:{cloudId:e.key.split("_")[1],title:e.cloudName}})
    }else if(e.key.includes("domain")){
        emit("ProjectListAPI",{key:e.key,cloudKey:'cloud_'+e.cloudId});
        router.push({path:"/admin/devops/menu/domain",query:{domainId:e.key.split("_")[1],cloudId:e.cloudId,title:e.domainName}})
    }else if(e.key.includes("project")){
        emit("MenusListSET",{key:e.key,domainKey:'domain_'+e.domainId,cloudKey:'cloud_'+e.cloudId});
        router.push({path:"/admin/devops/menu/project",query:{projectId:e.key.split("_")[1],domainId:e.domainId,cloudId:e.cloudId,title:e.projectName}})
    }
    // else if(whitelist.includes(menu_store.submenuId)){
    //     emit("MenusListSET",{key:e.key,domainKey:'domain_'+e.domainId,cloudKey:'cloud_'+e.cloudId});
    // }
    // if(e.key)
    //     router.push("/admin/networktopology")
    // if(e.key > 1)
    //     router.push('/admin/networktopology/'+e.key)
}
const setOpen = (keys) => {
    console.log("keys",keys)
    state.openKeys = [];
}
const onOpenChange = (key,keys) => {
    if(rawOpenKeys.value.includes(key)){
        state.openKeys = [...rawOpenKeys.value];
        topo_store.setopenKeys([...rawOpenKeys.value]);
    }else{
        let typeName = key.split("_")[0];
         let replaced = rawOpenKeys.value.some((item,index)=>{
            if(item.includes(typeName)){
                return item = key;
            }
        })
        if(!replaced){
            rawOpenKeys.value = [...keys];
        }else{
            keys = [...rawOpenKeys.value];
        }
        state.openKeys = [...keys];
        topo_store.setopenKeys([...keys]);
    }
}
const onIconChange = (data) => {
    if(topo_store.openKeys.includes(data.key)){
        state.openKeys = rawOpenKeys.value.filter(item=>{
            if(item != data.key){
                if(data.key.indexOf("cloud") !== -1){
                    return (item.indexOf("domain") === -1 || item.indexOf("project") === -1)
                }else if(data.key.indexOf("domain") !== -1){
                    return (item.indexOf("project") === -1)
                }else
                    return true;
            }
        });
        rawOpenKeys.value = [...state.openKeys];
        topo_store.setopenKeys([...state.openKeys]);
    }else{
        if(rawKey.value == data.key){
            rawOpenKeys.value.push(data.key);
            state.openKeys = rawOpenKeys.value;
            topo_store.setopenKeys([...state.openKeys]);
        }
    }
}
const isFirst = ref(false);
const setOriginKeys = (key,keys) => {
    // console.log("setOriginKeys",rawOpenKeys.value)
    state.selectedKeys = [key];
    if(!isFirst.value){
        isFirst.value = true;
        rawOpenKeys.value = keys;
        state.openKeys = keys;
        topo_store.setopenKeys(keys);
    }else{
        // onOpenChange(key,keys)
        if(!rawOpenKeys.value.includes(key)){
            rawOpenKeys.value = [...keys];
            state.openKeys = [...keys];
            topo_store.setopenKeys([...keys]);
        }
    }
}
watch(()=>router.currentRoute.value,(to,from)=>{
    let projectId = router.currentRoute.value.query.projectId;
    let domainId = router.currentRoute.value.query.domainId;
    let cloudId = router.currentRoute.value.query.cloudId;
    if(router.currentRoute.value.path.includes("/admin/devops/menu/")){
        if(projectId)
            setOriginKeys('project_'+projectId,['project_'+projectId,'domain_'+domainId,'cloud_'+cloudId]);
        else if(domainId)
            setOriginKeys('domain_'+domainId,['domain_'+domainId,'cloud_'+cloudId]);
        else if(cloudId)
            setOriginKeys('cloud_'+cloudId,['cloud_'+cloudId])
    }else if(whitelist.includes(menu_store.submenuId)){
        setOriginKeys(router.currentRoute.value.path,['project_'+projectId,'domain_'+domainId,'cloud_'+cloudId])
    }
    
//   if(router.currentRoute.value.path == '/admin/devops/menu/cloud'){
//     if(router.currentRoute.value.query.cloudId)
//   }else{
//   }
},{immediate:true,deep:true})
onMounted(()=>{
})
onUnmounted(()=>{
    if(!getToken()){
        topo_store.setKey(0);
        topo_store.setopenKeys([]);
    }
})
</script>

<style lang="scss" scoped>
:deep(.ant-menu-inline .ant-menu-item){margin: 0;}
</style>