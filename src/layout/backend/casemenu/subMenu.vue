<template>
    <a-sub-menu :key="data.key" @titleClick="()=>titleClick(data)">
        <template #title>
            {{data.title}}
        </template>
        <template #expandIcon>
            <i class="ant-menu-submenu-arrow" @click.stop="()=>changeIcon(data)"></i>
        </template>
        <template v-for="(item,index) in data.children" :key="item.key">
            <template v-if="item.children">
                <sub-menu :data="item" @set_selectkeys="setMenuBackground" @onIconChange="changeIcon"></sub-menu>
            </template>
            <template v-else-if="item.list && item.list.length > 0">
                <div v-for="(it,ii) in item.list" :key="it.key" @click.stop>
                    <a-menu-item :key="it.key" :thirdProjectId="data.thridProjectId">
                        {{it.title}}
                    </a-menu-item>
                </div>
                <a-divider v-if="index != data.children.length-1"></a-divider>
            </template>
        </template>
    </a-sub-menu>
</template>

<script setup lang="ts">
// import { ref,reactive } from 'vue';
import $ from "jquery";
import { topoStore } from '@/store/topo';
import { nextTick, onMounted, ref, watch } from "vue";
import router from "@/router";
import { menuStore } from "@/store/menu";
const menu_store = menuStore();
const whitelist = ['devops','config','storage','vmware'];
const topo_store = topoStore();
interface subMenu {
  title:string,
  key:number,
  children?: subMenu[]
}
const emit = defineEmits(['set_selectkeys','onIconChange']);
const props = defineProps<{
  data: Object
}>()
const repeatFlag = ref(false);

const titleClick = (data)=>{
    // if(!topo_store.openKeys.includes(data.key))
    //     emit("onIconChange",data)
    setMenuBackground(data)
}
const setMenuBackground = (data,cloudId) => {
    // console.log("topo",topo_store.topo,data.key)
    if(data.children){
        $('.ant-menu-submenu-title').css('background','transparent');
        $('div[data-menu-id="'+data.key+'"]').css('background','#e6f7ff');
        // 除了cloud都是相等的情况，是因为submenu的多层级
        if(topo_store.key != data.key){
            topo_store.setKey(data.key);
            topo_store.setIsLeaf(0);
            emit('set_selectkeys',data);
        }else{
            if(data.key.includes("cloud") && cloudId){
                emit('set_selectkeys',data);
            }else if(data.key.includes("domain")){
                emit('set_selectkeys',data);
            }else if(data.key.includes("project")){
                emit('set_selectkeys',data);
            }
        }
        
    }
    // else{
    //     emit('set_selectkeys',data);
    // }
  
}

const setOriginKeys = (key,cloudId) => {
    nextTick(()=>{
        if(props.data.key == key)
            setMenuBackground(props.data,cloudId)
    })
}

const changeIcon = (data) => {
    if(topo_store.openKeys.includes(data.key)){
        emit("onIconChange",data)
        }else{
        emit("onIconChange",data)
            setMenuBackground(data)
        }
    // }
}
watch(()=>router.currentRoute.value,(to,from)=>{
    let projectId = router.currentRoute.value.query.projectId;
    let domainId = router.currentRoute.value.query.domainId;
    let cloudId = router.currentRoute.value.query.cloudId;
    if(router.currentRoute.value.path.includes('/admin/devops/menu')){
        if(cloudId){
            if(projectId)
                setOriginKeys('project_'+projectId)
            else if(domainId)
                setOriginKeys('domain_'+domainId)
            else if(cloudId)
                setOriginKeys('cloud_'+cloudId,cloudId)
            repeatFlag.value = true;
        }
    }
  // console.log('ok')
    
    // else if(whitelist.includes(menu_store.submenuId)){
    //     setOriginKeys(router.currentRoute.value.path)
    // }
},{immediate:true,deep:true})
onMounted(()=>{
    // setTimeout(()=>{
    //     let rawTopo = topo_store.key ? topo_store.key : props.data?.[0]?.key;
    //     if(!topo_store.isLeaf)
    //         setMenuBackground({key:rawTopo,children:true})
    // })
})
defineExpose({setMenuBackground})
</script>

<style lang="scss" scoped>
.ant-menu-submenu-arrow{padding: 10px 12.5px 10px 7.5px;}
</style>
