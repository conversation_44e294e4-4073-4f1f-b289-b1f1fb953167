<!-- 一级页面左侧菜单 -->
<template>
  <div class="left-platform">
    <!--  v-model:selectedKeys="selectedKeys" :openKeys="openKeys" @openChange="onOpenChange" -->
    <TopoMenu :class="whitelist.includes(submenuId) ? 'lessheight' : 'allheight'" v-if="menu_store.showLeftMenu && submenuId == 'networktopology'" />
      <a-menu mode="inline" v-else v-model:selectedKeys="selectedKeys" @select="selectLeftMenu" :openKeys="openKeys" :class="whitelist.includes(submenuId) ? 'lessheight' : 'allheight'">
        <div v-if="menu_store.showLeftMenu && !whitelist.includes(submenuId)">
            <a-menu-item v-for="(item,index) in menulistItem.list" :key="'/admin/' + item.url">
                <router-link :to="'/admin/' + item.url">
                    {{ item.name }}
                </router-link>
            </a-menu-item>
            <!-- <a-menu-item >
              <router-link to="/admin/flow">流程管理</router-link>
            </a-menu-item> -->
        </div>
        
        <a-sub-menu v-else v-for="(item,index) in submenuFirst" :key="'sub1-'+index" :title="item.cloudName" @titleClick="openSub(item,'sub1-'+index,'sub1',index)">
            <template #expandIcon>
                <i class="ant-menu-submenu-arrow" @click.stop="openSub(item,'sub1-'+index,'sub1',index,null,null,true)"></i>
            </template>
            <template v-if="item?.cloudTypeText == 'vm'">
              <a-menu-item v-for="(t,i) in item.children" :key="'/admin/vmware/host?id='+t.menuId">
                <router-link :to="'/admin/vmware/host?id='+t.menuId" @click="goVm(t.menuId)">{{t.name}}</router-link>
              </a-menu-item>
            </template>
            
            <template v-else>
              <a-sub-menu v-for="(t,i) in item.children" :key="'sub2-'+i" :title="t.domainName" @titleClick="openSub(t,'sub2-'+i,'sub2',i,index)">
                <template #expandIcon>
                  <i class="ant-menu-submenu-arrow" @click.stop="openSub(t,'sub2-'+i,'sub2',i,index,null,true)"></i>
                </template>
                  <a-sub-menu v-for="(tt,ti) in t.children" :key="'sub3-'+ti" @titleClick="openSub(tt,'sub3-'+ti,'sub3',ti,i,index)" v-if="t.children && t.children.length>0 && t.children[0]">
                      <template #expandIcon>
                        <i class="ant-menu-submenu-arrow" @click.stop="openSub(tt,'sub3-'+ti,'sub3',ti,i,index,true)"></i>
                      </template>
                      <template #title>
                          <a-tooltip :title="tt?.projectName" placement="right">
                              {{tt?.projectName}}
                          </a-tooltip>
                      </template>
                      <template v-for="(lt,li) in tt?.children" :key="li">
                          <a-menu-item v-for="(lmt,lmi) in lt.list" :key="'/admin/'+lmt.url" @click="toMenu">
                            <router-link :to="'/admin/'+lmt.url+'?cloudId='+item.id+'&domainId='+t.id+'&projectId='+tt.id+'&thirdProjectId='+tt.thridProjectId">{{lmt.name}}</router-link>
                          </a-menu-item>
                          <a-divider v-if="li != tt?.children.length-1"></a-divider>
                      </template>
                  </a-sub-menu>
              </a-sub-menu>
            </template>
        </a-sub-menu>
      </a-menu>
      
      <router-link to="/admin/devops/recyclery" v-if="menu_store.showLeftMenu && whitelist.includes(submenuId)">
        <div :class="`recyclery${route.path == '/admin/devops/recyclery' ? ' selected_recyclery' : ''}`"><DeleteOutlined />回收站</div>
      </router-link>
  </div>
</template>
<script lang="ts" setup>
import TopoMenu from "./topomenu.vue";
import { SettingOutlined, HomeOutlined, ReadOutlined, UserOutlined, ApartmentOutlined, EditOutlined } from "@ant-design/icons-vue";
import { useRouter, useRoute, onBeforeRouteLeave } from "vue-router";
import { ref, watch, computed, nextTick, onMounted, getCurrentInstance, reactive, h } from "vue";
import { storeToRefs } from "pinia";
import { indexStore } from "@/store/index"
import { userStore } from "@/store/user"
import { menuStore } from "@/store/menu";
import { getCloudInfo, selectCloudList } from "@/api/backend/cloud";
import { selectOpenstList } from "@/api/backend/devops/domain";
import { selectProjectList } from "@/api/backend/devops/project";
import { listToTree, listToTreeCloud } from "@/utils/tool";
import { selectNavByCloud } from "@/api/backend/systems/menu";
import $ from 'jquery';
import { includes } from "lodash";
import { filterRouter } from "@/utils/route";
import { setCephToken } from "@/utils/auth";
import { message } from "ant-design-vue";
import emiter from "@/utils/Bus";
/* 
 *  menulist项目总菜单 submenuFirst云平台总菜单 SubmenuFirst状态库保存的云总菜单
 *  cloudIndex当前云索引 domainIndex当前域索引 projectIndex当前项目索引
 *  openKeys展开的目录key数组，例如["sub1-0","sub2-0","sub3-0"]最多三项
 *  selectedKeys选中的菜单(非目录，包括左侧所有可展示的)数组，只包含一项
 *  lightKeys高亮目录的key
 *  menulistItem顶部总菜单的选中项
 */
const whitelist = ref(['devops','config','storage','vmware'])
const index_store = indexStore()
const user_store = userStore()
const menu_store = menuStore()
const router: any = useRouter();
const route: any = useRoute();
const {routers} = storeToRefs(index_store);
const {menulist} = storeToRefs(user_store);
const {submenuId,Openkeys,leftMenuList,SubmenuFirst,cloudIndex,domainIndex,projectIndex} = storeToRefs(menu_store);
const {proxy} = getCurrentInstance();
// 顶部总菜单的选中项
const menulistItem = reactive({list:[]})
// 云总菜单列表
const submenuFirst = ref([])
const selectedKeys = ref<any>([]);
const openKeys: any = ref(Openkeys.value);
// 高亮目录key，不包括菜单，展开不一定高亮(openSub手动展开的key必高亮)
const lightKeys: any = ref([]);
const routeList = routers;
const isManager = computed(()=>user_store.userInfo.roleIdList.includes(1))
// vm类型云(暂时)
const goVm = (id) => {
  proxy.$mitt.emit("gethostInfo", id);
}
// 选中菜单的同时，把目录的高亮去掉（a-menu没有目录可选中的功能，本项目实现，a-tree样式不理想所以不用）
const toMenu = () => {
  $('.ant-menu-submenu-title').css('background','transparent')
}
// 当前目录高亮(点击或重新获取已选中的)，其他目录不高亮
const setMenuBackground = (key) => {
  $('.ant-menu-submenu-title').css('background','transparent')
  $('div[data-menu-id="'+key+'"]').css('background','#e6f7ff')
}
const openlastapi = async (cloudId,index,preIndex,presIndex) => {
  let res = await selectNavByCloud({cloudId})
  if(res.code == 0){
    user_store.set_cloudmenu([...listToTreeCloud(res.data)]);
    if(index >= 0){
      // 给没有children子菜单的项目初始化children数据类型
      if(!submenuFirst.value[presIndex].children[preIndex].children[index]?.children)
        submenuFirst.value[presIndex].children[preIndex].children[index].children = [];
      submenuFirst.value[presIndex].children[preIndex].children[index].children = [...listToTreeCloud(res.data)]
      menu_store.setSubmenuFirst([...submenuFirst.value])
      let menuList = [...user_store.menulist]
      // 即时更新路由
      const ansyRouter: any = filterRouter(menuList.concat(user_store.cloudmenu));
      ansyRouter.forEach((item: any) => {
        router.addRoute('layout',item);
      });
    }
  }
}
/*
* index 项目的索引 / preIndex 域的索引 / presIndex 云平台的索引
* listToTreeCloud 重组接口数据生成菜单列表
* ××××_store  pinia状态库 / set_cloudmenu 保存最里层子菜单 / setSubmenuFirst 保存云总菜单
*/ 
// 展开项目目录(点击或重新获取已展开的)，获取该项目下菜单列表(openstack)
const openLastMenu = (cloudId,index,preIndex,presIndex) => {
  if(!submenuFirst.value[presIndex].children[preIndex].children[index]?.children){
    if(user_store.cloudmenu && user_store.cloudmenu.length > 0){
      submenuFirst.value[presIndex].children[preIndex].children[index].children = user_store.cloudmenu;
      menu_store.setSubmenuFirst([...submenuFirst.value]);
      let menuList = [...user_store.menulist]
      // 即时更新路由
      const ansyRouter: any = filterRouter(menuList.concat(user_store.cloudmenu));
      ansyRouter.forEach((item: any) => {
        router.addRoute('layout',item);
      });
    }else
      openlastapi(cloudId,index,preIndex,presIndex)
  }
}
// 展开vm云平台的目录
const openLastMenuVM = async (cloudId,index) => {
  let res = await selectNavByCloud({cloudId})
  if(res.code == 0){
    submenuFirst.value[index].children = res.data.slice(1,res.data.length);
    router.push({path:'/admin/vmware/host',query:{id:submenuFirst.value[index].children?.[0]?.menuId,cloudId,title:submenuFirst.value[index].cloudName}})
  }
}
const forceCloudNotFoldUp = (item,key) => {
  if(openKeys.value?.length >= 3){
    openKeys.value = [key,openKeys.value[1],openKeys.value[2]]
    menu_store.setOpenKeys(openKeys.value)
  }else if(3 > openKeys.value?.length && openKeys.value?.length >= 2){
    openKeys.value = [key,openKeys.value[1]]
    menu_store.setOpenKeys(openKeys.value)
  }else{
    openKeys.value = [key]
    menu_store.setOpenKeys(openKeys.value)
    localStorage.setItem('cloudId',item.id)
  }
}
const setCloudDirectory = (item,key) => {
  forceCloudNotFoldUp(item,key)
  // openKeys.value = [key];
  // menu_store.setOpenKeys(openKeys.value);
  lightKeys.value[0] = key;
  setMenuBackground(key);
  router.push({path:'/admin/devops/menu/cloud',query:{cloudId:item.id,title:item.cloudName}})
  menu_store.setCloudInfo(item);
}
const setCloudPage = (item,index) => {
  localStorage.setItem('cloudId',item.id)
  localStorage.setItem('openPrometheus',item.openPrometheus)
  setTimeout(()=>{
    if(route.path == '/admin/devops/menu/cloud'){
      proxy.$mitt.emit('getDomainCount',item.id)
      proxy.$mitt.emit('getInitState',{uuid:undefined,isMounted:true,cloudId:item.id})
      getCloudInfo({id:item.id}).then((res)=>{
        localStorage.setItem('openCeph',res.data.openCeph)
        proxy.$mitt.emit('openCeph',res.data.openCeph)
        item.openCeph = res.data.openCeph;
        if(res.data.openCeph){
          console.log("setCloudPage",router.currentRoute.value.query.cloudId)
          proxy.$mitt.emit('cloudgetsms')
        }
      })
    }
  })
  
  if(!submenuFirst.value[index].children)
    SelectDomainList({cloudId:item.id,index})
  else{
    emiter.emit('allLoading',false)
  }
}
const forceDomainNotFoldUp = (key) => {
  if(openKeys.value?.length >= 3)
    openKeys.value = [openKeys.value[0],key,openKeys.value[2]]
  else
    openKeys.value = [openKeys.value[0],key]
  menu_store.setOpenKeys(openKeys.value)
}
const setDomainDirectory = (item,key) => {
  // openKeys.value = [openKeys.value[0],key];
  // menu_store.setOpenKeys(openKeys.value);
  forceDomainNotFoldUp(key)
  lightKeys.value[1] = key;
  setMenuBackground(key);
  router.push({path:'/admin/devops/menu/domain',query:{domainId:item.id,cloudId:item.cloudId,title:item.domainName}})
}
const setProjectDirectory = (item,key) => {
  router.push({path:'/admin/devops/menu/project',query:{projectId:item.id,cloudId:item.cloudId,domainId:item.domainId,title:item.projectName,pkey:key}})
  openKeys.value = [openKeys.value[0],openKeys.value[1],key];
  menu_store.setOpenKeys(openKeys.value);
  lightKeys.value[2] = key;
  setMenuBackground(key);
  menu_store.setProjectInfo(item);
}
// 点击目录(标题||箭头)
// item点击目录的信息，key点击目录的key，level层级，index在同级中的下标，preIndex上级目录的下标，presIndex上级的上级目录下标，isIcon是否点击的箭头
const openSub = async (item,key,level,index,preIndex,presIndex,isIcon) => {
  selectedKeys.value = []
  menu_store.setSelectedKeys([])
  if(item?.cloudTypeText == 'vm'){
    if(level == 'sub1'){
      if(openKeys.value?.[0] != key){
        emiter.emit('allLoading',true)
        openKeys.value = [key];
        lightKeys.value[0] = key;
        setMenuBackground(key)
        openLastMenuVM(item.id,index)
        menu_store.setCloudIndex(index)
      }else{
        if(isIcon)
        openKeys.value = []
        else{
          openKeys.value = [key]
          lightKeys.value[0] = key;
          setMenuBackground(key)
        }
      }
    }
  }else{
    if(level == 'sub1'){
        lightKeys.value[1] = undefined;
        lightKeys.value[2] = undefined;
        if(openKeys.value?.[0] != key){
          // 这么写或者setCloudDireactory使用注释里的，为了共用setCloudDireactory，这里单独写
          openKeys.value = [];
          menu_store.setOpenKeys(openKeys.value)
          if(!lightKeys.value.includes(key)){
            openlastapi(item.id,-1);
            emiter.emit('allLoading',true)
            setCloudDirectory(item,key)
            setCloudPage(item,index)
            menu_store.setCloudIndex(index)
          }else{
            // 点击目录图标展开，.stop已阻止默认事件，故手动设置
            // 点击目录标题展开，若不设置，收起时也会执行此处
            setCloudDirectory(item,key)
          }
        }else{
          if(isIcon){
            openKeys.value = [];
            menu_store.setOpenKeys(openKeys.value)
          }else{
            // 点击已展开的目录，组件默认收起。要求不收起，故设置强制展开
            setCloudDirectory(item,key)
          }
        }
    }
    if(level == 'sub2'){
      localStorage.setItem('domainId',item.id)
      router.push('/admin/devops/menu/domain')
      if(openKeys.value?.[1] != key){
        openKeys.value = [openKeys.value[0]];
        menu_store.setOpenKeys(openKeys.value)
        if(!submenuFirst.value[preIndex].children[index].children)
          SelectProjectList({cloudId:item.cloudId,domainId:item.id,index,preIndex})
        if(!lightKeys.value.includes(key)){
          // emiter.emit('allLoading',true)
          setDomainDirectory(item,key)
          proxy.$mitt.emit('getPolylist')
          proxy.$mitt.emit('getProjectCount',{domainId:item.id,cloudId:item.cloudId})
          menu_store.setDomainIndex(index)
        }else{
          setDomainDirectory(item,key)
        }
      }else{
        if(isIcon){
          openKeys.value = [openKeys.value[0]]
          menu_store.setOpenKeys(openKeys.value);
        }else{
          console.log('domain')
          setDomainDirectory(item,key)
        }
      }
    }
    if(level == 'sub3'){
      localStorage.setItem('projectId',item.id)
      if(openKeys.value?.[2] != key){
        if(!submenuFirst.value[presIndex].children[preIndex].children[index].children || submenuFirst.value[presIndex].children[preIndex].children[index].children.length <= 0)
            openLastMenu(item.cloudId,index,preIndex,presIndex)
        if(!lightKeys.value.includes(key)){
          console.log("展开3")
          // emiter.emit('allLoading',true)
          setProjectDirectory(item,key)
          proxy.$nextTick(()=>{
            // if(route.path == '/admin/devops/menu/project'){
              proxy.$mitt.emit('refreshcloudIndex',item.id)
            // }
          })
          menu_store.setProjectIndex(index)
        }else{
          console.log("展开4")
          setProjectDirectory(item,key)
        }
      }else{
        console.log("展开5")
        if(isIcon){
          openKeys.value = [openKeys.value[0],openKeys.value[1]]
          menu_store.setOpenKeys(openKeys.value);
        }else{
          console.log("展开6")
          setProjectDirectory(item,key)
        }
      }
    }
  }
}
// 选中菜单的事件
const selectLeftMenu = ({ item, key, selectedKeys }) => {
  // emiter.emit('allLoading',true)
  menu_store.setSelectedKeys(selectedKeys)
  menu_store.setOpenKeys(openKeys.value)
  menu_store.setLeftMenu(submenuFirst.value)
}
// 初始化/更新左侧菜单
const setLeftMenu = (isRefresh,isInit) => {
  let menutemp = menulist.value.find((item,index) => {
    if(submenuId.value == 'vmware')
      return item.url == 'devops'
    else
      return (item.url == submenuId.value)
  })
  Object.assign(menulistItem,menutemp)
  // 根据路由参数对应到菜单
  proxy.$nextTick(()=>{
    if(route.query.cloudId)
      submenuFirst.value.forEach((item,index)=>{
        if(item.id == route.query.cloudId){
          openKeys.value[0] = 'sub1-'+index;
          if(route.query.domainId)
            item.children.forEach((t,i)=>{
              if(t.id == route.query.domainId){
                openKeys.value[1] = 'sub2-'+i;
                if(route.query.projectId)
                  t.children.forEach((tt,ii)=>{
                    if(tt.id == route.query.projectId){
                      openKeys.value[2] = 'sub3-'+ii;
                    }
                  })
              }
            })
        }
      })
    // 刷新目录页保持高亮
    if(route.path == '/admin/devops/menu/cloud'){
      lightKeys.value[0] = openKeys.value[0];
      setMenuBackground(openKeys.value[0])
    }
    console.log("openKeys.value1111",openKeys.value,lightKeys.value)
    if(route.path == '/admin/devops/menu/domain'){
      lightKeys.value[1] = openKeys.value[1];
      setMenuBackground(openKeys.value[1])
    }
    if(route.path == '/admin/devops/menu/project'){
      lightKeys.value[2] = openKeys.value[2];
      setMenuBackground(openKeys.value[2])
    }
    if(route.path == '/admin/vmware/host'){
      lightKeys.value[0] = openKeys.value[0];
      setMenuBackground(openKeys.value[0])
    }
  })
  // 非刷新界面的情况
  if(!isRefresh){
    // 非云平台管理下的菜单列表出现时，默认跳转第一个菜单
    //  && submenuId.value != 'vmware' 
    if(menu_store.showLeftMenu && submenuId.value != 'devops' && submenuId.value != 'vmware'){
      selectedKeys.value = ['/admin/' + menulistItem.list[0].url]
      router.push('/admin/' + menulistItem.list[0].url)
      // 跳转后无网络请求时停止loading
      emiter.emit('allLoading',false)
    }else{
      let initorfirstopenkey = isInit ? 'sub1-'+menu_store.cloudIndex : 'sub1-0';
      let initorfirstIndex = isInit ? menu_store.cloudIndex : 0;
      if(submenuFirst.value[initorfirstIndex]?.cloudTypeText != 'vm'){
        router.push({path:'/admin/devops/menu/cloud'})
        localStorage.setItem('openPrometheus',submenuFirst.value[initorfirstIndex]?.openPrometheus)
        // localStorage.setItem('openCeph',submenuFirst.value[initorfirstIndex]?.openCeph)
        proxy.$mitt.emit('getDomainCount',submenuFirst.value[initorfirstIndex]?.id)
        proxy.$mitt.emit('getInitState',{uuid:undefined,isMounted:true,cloudId:submenuFirst.value[initorfirstIndex]?.id})
        localStorage.setItem('openCeph',submenuFirst.value[initorfirstIndex]?.openCeph)
        if(submenuFirst.value[initorfirstIndex]?.openCeph){
          proxy.$mitt.emit('cloudgetsms',submenuFirst.value[initorfirstIndex]?.id)
        }
        proxy.$mitt.emit('openCeph',submenuFirst.value[initorfirstIndex]?.openCeph)
        proxy.$nextTick(()=>{
          selectedKeys.value = [initorfirstopenkey];
          openKeys.value = [initorfirstopenkey];
          menu_store.setOpenKeys(openKeys.value)
          localStorage.setItem('cloudId',submenuFirst.value[initorfirstIndex]?.id)
          lightKeys.value[0] = initorfirstopenkey;
          setMenuBackground(initorfirstopenkey)
          router.push({path:'/admin/devops/menu/cloud',query:{cloudId:submenuFirst.value[initorfirstIndex]?.id,title:submenuFirst.value[initorfirstIndex]?.cloudName}})
          menu_store.setCloudInfo(submenuFirst.value[initorfirstIndex])
        })
          SelectDomainList({cloudId:submenuFirst.value[initorfirstIndex]?.id,index:initorfirstIndex})
        menu_store.setCloudIndex(initorfirstIndex)
      }else{
        emiter.emit('allLoading',false)
        proxy.$nextTick(()=>{
          openLastMenuVM(submenuFirst.value[0]?.id,0)
          openKeys.value = ['sub1-0'];
          menu_store.setOpenKeys(openKeys.value)
          localStorage.setItem('cloudId',submenuFirst.value[0]?.id)
          menu_store.setCloudIndex(0)
          lightKeys.value[0] = 'sub1-0';
          setMenuBackground('sub1-0')
        })
      }
    }
  }
}
// 获取一级目录(云平台列表)
const SelectCloudList = async (e) => {
  let {isAdd,key,isInit} = e;
  submenuFirst.value = [];
  let res = await selectCloudList();
  if(res.code == 0){
    if(key == 'devops'){
      if(!res.data || res.data.length <= 0){
        if(isManager.value){
          router.push("/admin/index")
          message.error(
            h('span',['您当前没有添加云环境进行管理，需要先进行',h('a',{
              onclick:()=>{emiter.emit("handleAdd");message.destroy()}
            },'添加'),'云环境管理。'])
          )
        }else{
          message.error("非常抱歉，您当前没有权限进行云平台管理操作。如果您需要进行相关操作，请及时联系系统管理员以获得相应的权限。",5)
        }
        emiter.emit('allLoading',false)
          return;
      }
    }else{
      emiter.emit('allLoading',false)
    }
    submenuFirst.value = res.data;
    menu_store.setSubmenuFirst(submenuFirst.value)
    if(e.cloudId){
      openlastapi(e.cloudId,-1);
    }else if(route.query.cloudId){
      openlastapi(route.query.cloudId,-1);
    }else{
      openlastapi(res.data[0]?.id,-1);
    }
    if(isAdd === true){
      SelectDomainList({cloudId:route.query.cloudId,index:Number(openKeys.value[0].substring(5))+1})
      openKeys.value = ['sub1-'+(Number(openKeys.value[0].substring(5))+1),openKeys.value[1],openKeys.value[2]]
      lightKeys.value[0] = openKeys.value[0];
      setMenuBackground(openKeys.value[0])
    }else if(isAdd === false){ //  && submenuFirst.value[menu_store.cloudIndex]?.cloudTypeText != 'vm'
      SelectDomainList({cloudId:route.query.cloudId,index:menu_store.cloudIndex})
      if(submenuFirst.value[menu_store.cloudIndex]?.cloudTypeText != 'vm'){
        router.push({path:'/admin/devops/menu/cloud',query:{cloudId:route.query.cloudId,title:submenuFirst.value[menu_store.cloudIndex].cloudName}})
        
      }
      setLeftMenu(undefined,isInit)
    }else{
      if(key)
          menu_store.setMenuId(key)
    // setTimeout(()=>{
      menu_store.setShowLeftMenu(true)
      setLeftMenu(undefined,isInit)
    }
    // })
  }else{
    emiter.emit('allLoading',false)
    console.log("error")
  }
}
// 获取二级目录(域列表)
const SelectDomainList = async ({cloudId,index,isAdd,isDel}) => {
  emiter.emit('allLoading',false)
  let res = await selectOpenstList({cloudId});
  if(res.code == 0){
    if(submenuFirst.value && submenuFirst.value[index])
    submenuFirst.value[index].children = res.data;
    menu_store.setSubmenuFirst(submenuFirst.value)
    if(openKeys.value.length >= 2 && openKeys.value[1] && isAdd === undefined && isDel === undefined){
      setMenuBackground(lightKeys.value[0])
      SelectProjectList({cloudId:route.query.cloudId,domainId:route.query.domainId,index:domainIndex.value,preIndex:index})
    }else{
      lightKeys.value[0] = openKeys.value[0];
      setMenuBackground(openKeys.value[0])
    }
    if(isAdd === true){
      SelectProjectList({cloudId:route.query.cloudId,domainId:route.query.domainId,index:Number(openKeys.value[1].substring(5))+1,preIndex:index})
      openKeys.value = [openKeys.value[0],'sub2-'+(Number(openKeys.value[1].substring(5))+1)]
    }
    if(isAdd === false){
      SelectProjectList({cloudId:route.query.cloudId,domainId:route.query.domainId,index:domainIndex.value,preIndex:index})
      router.push({path:'/admin/devops/menu/domain',query:{domainId:route.query.domainId,cloudId:route.query.cloudId,title:submenuFirst.value[index].children[domainIndex.value].domainName}})
    }
    if(isDel){
      openKeys.value = [openKeys.value[0]]
      router.push({path:'/admin/devops/menu/cloud',query:{cloudId:route.query.cloudId,title:submenuFirst.value[index].cloudName}})
    }
  }
}
// 获取三级目录(项目列表)
const SelectProjectList = async ({cloudId,domainId,index,preIndex,isAdd,isDel}) => {
  let res = await selectProjectList({cloudId,domainId,enabled:1});
  if(res.code == 0){
    submenuFirst.value[preIndex].children[index].children = []
    submenuFirst.value[preIndex].children[index].children = res.data.map((item,index)=>{
      if(item.id != 'null' && item.id)
        return item;
    });
    menu_store.setSubmenuFirst(submenuFirst.value)
    if(isAdd === undefined && isDel === undefined){
      setMenuBackground(lightKeys.value[1])
      if(openKeys.value.length >= 3 && openKeys.value[2])
        openLastMenu(cloudId,projectIndex.value,index,preIndex)
        menu_store.setProjectInfo(submenuFirst.value[preIndex].children[index].children[projectIndex.value])
    }
    if(isAdd === true && res.data.length > Number(openKeys.value[2].substring(5))+1){
      let key = route.query.pkey;
      lightKeys.value[2] = 'sub3-'+(Number(key.split('-')[1])+1)
      setMenuBackground('sub3-'+(Number(key.split('-')[1])+1))
      if(openKeys.value.length >= 3){
        openKeys.value = [openKeys.value[0],openKeys.value[1],'sub3-'+(Number(openKeys.value[2].substring(5))+1)]
        openLastMenu(cloudId,Number(openKeys.value[2].substring(5)),index,preIndex)
      }
    }
    if(isAdd === false){
      openLastMenu(cloudId,projectIndex.value,index,preIndex)
      if(route.path == '/admin/devops/menu/project')
      router.push({path:'/admin/devops/menu/project',query:{projectId:route.query.projectId,cloudId:route.query.cloudId,domainId:route.query.domainId,title:submenuFirst.value[preIndex].children[index].children[projectIndex.value].projectName}})
    }
    if(isDel){
      lightKeys.value[1] = openKeys.value[1];
      setMenuBackground(openKeys.value[1])
      openKeys.value = [openKeys.value[0],openKeys.value[1]]
      router.push({path:'/admin/devops/menu/domain',query:{domainId:route.query.domainId,cloudId:route.query.cloudId,title:submenuFirst.value[preIndex].children[index].domainName}})
    }
  }
}
// 深度监听路由，开始就执行；每个菜单和路由选中高亮对应
watch(()=>router.currentRoute.value,(to,from)=>{
  selectedKeys.value = [];
  if(router.currentRoute.value.path == '/admin/vmware/host')
    selectedKeys.value.push(router.currentRoute.value.fullPath);
  else
    selectedKeys.value.push(router.currentRoute.value.path);
},{immediate:true,deep:true})

onMounted(()=>{
  submenuFirst.value = (SubmenuFirst.value?.length > 0) ? SubmenuFirst.value : [];
  if(menulistItem.list.length <= 0)
  setLeftMenu(true);
  proxy.$mitt.on('SelectCloudList',SelectCloudList
  // (key)=>{
  //   SelectCloudList(undefined,key);
    
  // }
  )
  proxy.$mitt.on('SelectProjectList',SelectProjectList)
  proxy.$mitt.on('setLeftMenu',setLeftMenu)
  proxy.$mitt.on('SelectDomainList',SelectDomainList)
  proxy.$mitt.on('openLastMenu',openLastMenu)
})
</script>
<style lang="scss" scoped>
.first-menu-item {
  padding-left: 0 !important;
}
.ant-menu:first-child .ant-menu-item-disabled:first-child {
  padding-left: 0 !important;
}
.admin-top-logo {
  padding-left: -16px;
  height: 66px;
  background-color: #001529;
  .imgShow {
    width: 180px;
    margin-top: 5px;
    margin-left: 10px;
  }
  .imgHidden {
    width: 74px;
    margin-top: 22px;
    padding-left: 4px;
  }
}
.ant-menu-dark.ant-menu-inline,.ant-menu-root.ant-menu-inline{overflow-x: hidden;}
.lessheight{height: calc(100vh - 106px);}
.allheight{height: calc(100vh - 106px + 40px);overflow: auto;}
:deep(.ant-menu-inline .ant-menu-item){margin: 0;}
:deep(.ant-menu-inline .ant-menu-submenu-title){margin: 0;}
:deep(.ant-menu-inline .ant-menu-item:not(:last-child)){margin: 0;}
:deep(.ant-divider-horizontal){margin: 5px 0;}
:deep(.ant-menu-light .ant-menu-submenu-active){color: rgba(0, 0, 0, 0.85);}
:deep(.ant-menu-submenu:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow){color: rgba(0, 0, 0, 0.85);}
:deep(.ant-menu-submenu-selected){color: rgba(0, 0, 0, 0.85);}
:deep(.ant-menu-submenu-arrow){padding: 10px 15px 15px 10px;}
:deep(.ant-menu-submenu-open.ant-menu-submenu-inline > .ant-menu-submenu-title > .ant-menu-submenu-arrow) {
    transform: translateY(-14px);
}
:deep(.current-submenu){background: #e6f7ff;color: #1890ff;}
::-webkit-scrollbar{height: 0;width: 0;}
.left-platform{position: relative;}
.recyclery{
  position: absolute;
  left: 0;
  bottom: -42px;
  height: 40px;
  width: 100%;
  // margin-top: 3px;
  text-align: center;
  line-height: 40px;
  background-color: #fff;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 -5px 4px -2px rgb(0 0 0 / 6%);
  &::after{
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    border-right: 3px solid #1890ff;
    transform: scaleY(0.0001);
    opacity: 0;
    transition: transform 0.15s cubic-bezier(0.215, 0.61, 0.355, 1), opacity 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
    content: '';
  }
}
.selected_recyclery{
  background-color: #e6f7ff;
  &::after{
    transform: scaleY(1);
    opacity: 1;
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), opacity 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
}
</style>