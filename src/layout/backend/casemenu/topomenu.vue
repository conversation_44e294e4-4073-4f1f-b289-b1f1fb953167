<template>
    <div class=''>
        <a-table :columns="columns" :data-source="data" row-key="id" :pagination="false"
        size="small"
        class="left-table"
        :showHeader="false"
        :expandedRowKeys="expandedRowKeys" 
        @expand="expand"
        :row-class-name="activeRowClass"
        :customRow="rowClick"
        >
            <!-- <template #networkName="{ text, record }">
                
                <div v-if="record.editable" style="display:inline-block">
                    <a-input
                
                 ref="inputRef"
                v-model:value="record.networkName"
                style="width:calc(100% - 60px)"
                @blur="(e)=>cancelInput(e,record)"
                @pressEnter="(e)=>{save(e,record)}"
                />
                </div>
                <span v-else>
                    {{text}}
                </span>
            </template> -->
            <template #expandIcon="row">
                <template v-if="row.expandable">
                    <div class="customArrow" @click="getRows(row, $event)">
                        <i class="ant-menu-submenu-arrow" :style="{'--transform-before':row.expanded ? 'rotate(45deg) translate(2.5px)' : 'rotate(-45deg) translate(2.5px)','--transform-after':row.expanded ? 'rotate(-45deg) translate(-2.5px)' : 'rotate(45deg) translate(-2.5px)'}"></i>
                    </div>
                </template>
                <template v-else>
                    <div class="customArrow"></div>
                </template>
            </template>
            <template #action="{record,index}">
                <a-button type="link" title="添加" v-if="(record.networkLevel == 1 || record.networkLevel ==2)" @click.stop="handleAdd(record)">
                    <template #icon><plus-outlined /></template>
                </a-button>
                <a-button type="link" title="修改" v-if="record.networkLevel > 1" @click.stop="edit(record)">
                    <template #icon><form-outlined /></template>
                </a-button>
                <a-button type="link" title="删除" v-if="record.networkLevel > 1" @click.stop="del(record)">
                    <template #icon><delete-outlined /></template>
                </a-button>
            </template>
        </a-table>
        <!-- <a-button type="dashed" block @click="handleAdd" v-if="data.length <= 0">
            <PlusOutlined />
            添加根节点
        </a-button> -->
        <a-modal :title="state.isAdd ? '新增拓扑菜单':'修改拓扑菜单'" v-model:visible="state.onEdit" @ok="handleSave" @cancel="cancel" :getContainer="modalBindNode">
            <a-form :model="apiform" ref="apiForm" :label-col="{span:3}" :rules="rules">
                <a-form-item name="networkName" label="名称">
                    <a-input v-model:value="apiform.networkName">
                    </a-input>
                </a-form-item>
                <!-- <a-form-item name="networkLevel" label="等级" v-if="apiform.networkLevel <= 3">
                    <a-select v-model:value="apiform.networkLevel" :options="levelOptions" disabled>
                    </a-select>
                </a-form-item> -->
                <a-form-item name="parentId" label="父节点">
                    <a-select v-model:value="apiform.parentId" disabled>
                        <a-select-option v-for="(item,index) in rawList" :key="item.id" :value="item.id">{{item.networkName}}</a-select-option>
                    </a-select>
                </a-form-item>
                <a-form-item name="type" label="类型" v-if="apiform.networkLevel < 4">
                    <a-select v-model:value="apiform.type" :options="optionObj[apiform.parentType]" :disabled="!state.isAdd">
                    </a-select>
                </a-form-item>
            </a-form>
        </a-modal>
    </div>
</template>
<script lang='ts' setup>
import { delNetworktopology, saveNetworktopology, selectNetworktopology, updateNetworktopology } from '@/api/backend/topo';
import router from '@/router';
import emiter from '@/utils/Bus';
import { listToTreeTopo } from '@/utils/tool';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { Modal } from 'ant-design-vue';
import { createVNode, onMounted, reactive, ref, watch } from 'vue';
import topomenu from "./topomenu.json";
// const defaulttopo = {
//         id:0,
//         networkLevel:0,
//         networkName:'配电网云拓扑',
//         parentId:-1,
//         key:1
//     }
const levelOptions = ref([
    {value:0,label:"广域网"},
    {value:1,label:"数据中心"},
    {value:2,label:"子中心"},
    {value:3,label:"集群"}
])
const optionObj = {
    "DOMAIN":[
        {value:"DATA_CENTER",label:"数据主站"},
        {value:"TERMINAL_CENTER",label:"边侧"}
    ],
    "DATA_CENTER":[
        {value:"DATA_STATION",label:"数据站"}
    ],
    "TERMINAL_CENTER":[
        {value:"TERMINAL_STATION",label:"边侧终端"}
    ],
    "default":[
        {value:"DATA_CENTER",label:"数据主站"},
        {value:"TERMINAL_CENTER",label:"边侧"},
        {value:"DATA_STATION",label:"数据站"},
        {value:"TERMINAL_STATION",label:"边侧终端"}
    ]
}
// const typeOptions = ref([
//     // {value:"cloud",label:"云平台"},
//     // {value:"domain",label:"域"},
//     {value:"TERMINAL_CENTER",label:"边侧"},
//     {value:"TERMINAL_STATION",label:"终端"},
// ])
const mockfirst = {
  "networkLevel": -1,
  "networkName": "配电网云拓扑",
  "parentId": -2,
  "id":-1
}
const rawList = ref([]);
const data = ref([]);

const defaultfrom = {
//   "networkCode": "",
    "cloudId":"",
    "id":"",
    "networkCode":"",
  "networkLevel": 0,
  "networkName": "",
//   "orderNum": 0,
  "parentId": -1,
  "parentType":undefined
}
const apiForm = ref();
const apiform = reactive({
//   "networkCode": "",
    "cloudId":"",
    "id":"",
    "networkCode":"",
  "networkLevel": 0,
  "networkName": "",
//   "orderNum": 0,
  "parentId": -1
})
const columns = [
  {dataIndex: 'networkName', key: 'id', slots: { customRender: 'networkName' }, width:150,ellipsis:true },
  {dataIndex: 'action', key: 'id', slots: { customRender: 'action' }, width:160, className:'action-cell' },
];
const rules = {
    networkName:[{required:true,message:'请输入名称'}],
    type:[{required:true,message:'请选择类型'}]
}
const expandedRowKeys = ref([]);
const inputRef = ref(null);
// 当前是鼠标失焦而非回车失焦，默认鼠标，回车失焦只在空值的时候
const isMouseBlur = ref(true);
let count = 1;
const onEdit = ref(false);
const state = reactive({
    onEdit:false,
    isMouseBlur:true,
    isAdd:true
})
const activeRow = ref(-1);
const activeRowClass = (record) => {
    if(record.type != "TERMINAL_STATION")
    return record.id === activeRow.value ? 'activeRowLight' : '';
}
const rowClick = (record) => {
    return {
        onClick: (e) => {
            // userRef.value.getList(record.id);
            if(record.type != "TERMINAL_STATION"){
                activeRow.value = record.id;
                if(record.networkLevel == 1 || record.networkLevel == 3 || record.networkLevel == 2){
                    router.push({path:'/admin/networktopology/'+record.id,query:{cloudId:record.cloudId,type:record.type}})
                }else if(record.networkLevel !== -1){
                    router.push({path:'/admin/networktopology',query:{id:record.id,type:record.type}});
                }
            }
        }
    }
}
const getRows = (props, event) => {
  // console.log(props)
  props.onExpand(props.record, event)
}

// 手动控制展开收起，添加部门时展开需要
const expand = (expanded, record) => {
    if(expanded)
    expandedRowKeys.value = Array.from(new Set([...expandedRowKeys.value,record.key]))
    else
    expandedRowKeys.value.splice(expandedRowKeys.value.indexOf(record.key), 1)
}
const getExpandKeys = () => {
    let rowKeys = [];
    let expandObj = JSON.parse(localStorage.getItem("expandObj"));
    function loop(key){
        let res = Object.keys(expandObj).some((item,index)=>{
            if(item == key){
                rowKeys.push(expandObj[item]);
                loop(expandObj[item])
                return true;
            }
        })
        if(!res){
            console.log("rowKeys",rowKeys)
            expandedRowKeys.value = rowKeys;
            return rowKeys;
        }
    }
    if(expandObj)
        loop(activeRow.value)
}
const addRoot = () => {
    data.value.push({...apiform,editable:true,key:'key'+count++})
    state.onEdit = true;
    setTimeout(()=>{
        inputRef.value.focus()
    })
    state.isMouseBlur = true;
}
const add = (record) => {
    state.isAdd = true;
    apiform.networkLevel = record.networkLevel + 1;
    apiform.parentId = record.id;
    // if(!record.children)
    //     record.children = []
    if(!state.onEdit){
        expand(true,record);
        state.onEdit = true;
        if(count)
        count += 1;
        else
        count = 1;
        
        record.children.push({
            editable:true,
            key:'key'+count++
        })
        setTimeout(()=>{
            inputRef.value.focus()
        })
        state.isMouseBlur = true;
        record.rawName = record.networkName ? record.networkName : undefined;
    }
    
};
const handleAdd = (record) => {
    state.isAdd = true;
    apiform.cloudId = record.cloudId;
    apiform.networkLevel = record.networkLevel + 1;
    apiform.parentId = record.id;
    apiform.parentType = record.type;
    if(!state.onEdit){
        // if(record)
        //     expand(true,record);
        state.onEdit = true;
    }
}
const edit = (record) => {
    Object.assign(apiform,record)
    apiform.parentType = 'default';
    state.isAdd = false;
    if(!state.onEdit){
        expand(true,record);
        state.onEdit = true;
        record.editable = true;
        setTimeout(()=>{
            inputRef.value.focus()
        })
        state.isMouseBlur = true;
        record.rawName = record.networkName ? record.networkName : undefined;
    }
    
};
const cancel = () => {
    state.onEdit = false;
    apiForm.value.resetFields();
    // Object.assign(apiform,{...defaultfrom})
    Object.keys(apiform).forEach((v,i)=>{
        apiform[v] = undefined
    })
}
const handleSave = () => {
    apiForm.value.validate().then(()=>{
        if(state.isAdd)
            saveNetworktopology(apiform).then((res)=>{
                getTopoMenu(()=>{
                if(router.currentRoute.value.query.type.toUpperCase() == 'CLOUD')
                    emiter.emit("setTopo",router.currentRoute.value.query.id)
                cancel()
            },()=>{
                cancel()
            })
            })
        else{
            updateNetworktopology(apiform).then((res)=>{
                getTopoMenu(()=>{
                    if(router.currentRoute.value.query.type.toUpperCase() == 'CLOUD')
                        emiter.emit("setTopo",router.currentRoute.value.query.id)
                    cancel()
                },()=>{
                    cancel()
                })
            })
        }
    })
}
const save = (e,record) => {
    record.editable = false;
    if(e.target.value){
        state.isMouseBlur = false;
        record.rawName = record.networkName ? record.networkName : undefined;
        apiform.networkName = e.target.value;
        // apiform.networkCode = record.networkCode ? record.networkCode : count++;
        if(state.isAdd)
        saveNetworktopology(apiform).then((res)=>{
            Object.assign(apiform,{...defaultfrom})
            getTopoMenu(()=>{
                if(router.currentRoute.value.query.type.toUpperCase() == 'CLOUD')
                    emiter.emit("setTopo",router.currentRoute.value.query.id)
                cancel()
            },()=>{
                cancel()
            })
        })
        else{
            updateNetworktopology(apiform).then((res)=>{
            Object.assign(apiform,{...defaultfrom})
            getTopoMenu(()=>{
                if(router.currentRoute.value.query.type.toUpperCase() == 'CLOUD')
                    emiter.emit("setTopo",router.currentRoute.value.query.id);
                cancel()
            },()=>{cancel()})
        })
        }
    }else{
        state.isMouseBlur = true;
    }
    state.onEdit = false;
    Object.keys(apiform).forEach((v,i)=>{
        apiform[v] = undefined
    })
    Object.assign(apiform,defaultfrom)
}
function deleteNode({id}) {
    delNetworktopology([id]).then((res)=>{
        getTopoMenu(()=>{
            if(router.currentRoute.value.query.type.toUpperCase() == 'CLOUD')
                emiter.emit("setTopo",router.currentRoute.value.query.id)
            // cancel()
        },()=>{
            // cancel()
        });
    })
}
function deleteNode1(treeData, key) {
  // 递归函数来查找并删除节点
  function findAndDeleteNode(nodes, code) {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].key === code) {
        nodes.splice(i, 1); // 删除节点
        if(nodes[i] && nodes[i].children && nodes[i].children.length <= 0){
            delete nodes[i].children
        }
        return true;
      }
      if (nodes[i].children && findAndDeleteNode(nodes[i].children, code)) {
        if(nodes[i] && nodes[i].children && nodes[i].children.length <= 0){
            delete nodes[i].children
        }
        // 如果在子节点中找到并删除了节点，则停止遍历
        return true;
      }
    }
    return false; // 没有找到节点
  }
  // 从根节点开始查找并删除
  return findAndDeleteNode(treeData, key);
}

const remove = (record,api) => {
    if(record.rawName){
        record.networkName = record.rawName;
        record.editable = false;
    }else{
        if(api)
            deleteNode(record);
        else{
            deleteNode1(data.value,record.key)
        }
    }
    state.onEdit = false;
}
const cancelInput = (e,record) => {
    // e.stopPropagation();
    if(state.isMouseBlur)
    remove(record,false);
}
const del = (record) => {
    expand(true,record);
    Modal.confirm({
        title:"确定删除节点及其子节点？",
        icon: () => createVNode(ExclamationCircleOutlined),
        onOk(){
            remove(record,true);
        }
    })
}
const getTopoMenu = (callback,error,isFirst) => {
    data.value = [];
    selectNetworktopology().then((res)=>{
        rawList.value = [...res.data];
        data.value = listToTreeTopo([...res.data],[],-1,'parentId',{});
        localStorage.setItem("topomenu",JSON.stringify([...res.data]));
        localStorage.setItem("topotree",JSON.stringify([...data.value]));
        if(isFirst && activeRow.value === -1){
            activeRow.value = data.value[0].id;
            // activeRowClass(data.value[0]);
            router.push({path:'/admin/networktopology',query:{id:data.value[0].id,type:data.value[0].type}});
        }
        if(callback){
            callback();
        }
    }).catch(()=>{
        if(error)
            error();
    })
}
watch(()=>router.currentRoute.value,(to,from)=>{
    let tempid = to.query.id;
    let paramId = to.params.id;
    activeRow.value = tempid ? Number(tempid) : (paramId ? Number(paramId) : -1);
    getExpandKeys()
    // activeRowClass(data.value[0]);
},{immediate:true,deep:true})
onMounted(() => {
    getTopoMenu(null,null,true)
    emiter.on("getTopoMenu",(e)=>{
        getTopoMenu(e.callback)
    })
})
</script>
<style lang='scss' scoped>
.left-table{
    :deep(.ant-table-tbody > tr > td){border-bottom: none;}
}
:deep(.action-cell > button){
    visibility: hidden;
}
:deep(.ant-table-row:hover > .action-cell > button){
    visibility: visible;
}
:deep(.activeRowLight){background-color: #e6f7ff;}
:deep(.ant-table-row:hover){
    cursor: pointer;
}
.customArrow{
    display: inline-block;
        padding: 0 10px;
    :deep(.ant-menu-submenu-arrow){
        transform: translateY(-5px);
        position: static;
        display: inline-block;
        padding: 0;
    }
    .ant-menu-submenu-arrow::before{
        transform: var(--transform-before);
    }
    .ant-menu-submenu-arrow::after{
        transform: var(--transform-after);
    }
    .ant-menu-submenu-arrow:before, .ant-menu-submenu-arrow:after{
        position: absolute;
        width: 6px;
        height: 1.5px;
        background-color: currentColor;
        border-radius: 2px;
        transition: background .3s cubic-bezier(.645,.045,.355,1), transform .3s cubic-bezier(.645,.045,.355,1), top .3s cubic-bezier(.645,.045,.355,1), color .3s cubic-bezier(.645,.045,.355,1);
        content: "";
    }
}

</style>