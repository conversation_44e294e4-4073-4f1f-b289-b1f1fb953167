<template>
    <div class='left-platform'>
        <topo-menu :class="whitelist.includes(submenuId) ? 'lessheight' : 'allheight'" v-if="menu_store.showLeftMenu && submenuId == 'networktopology'" ></topo-menu>
        <cloud-menu :class="whitelist.includes(submenuId) ? 'lessheight' : 'allheight'" :data="clouddata" v-else-if="menu_store.showLeftMenu && whitelist.includes(submenuId)" @DomainListAPI="DomainListAPI" @ProjectListAPI="ProjectListAPI" @MenusListSET="MenusListSET"></cloud-menu>
        <single-menu v-else></single-menu>
        <router-link to="/admin/devops/recyclery" v-if="menu_store.showLeftMenu && whitelist.includes(submenuId)">
            <div :class="`recyclery${router.currentRoute.value.path == '/admin/devops/recyclery' ? ' selected_recyclery' : ''}`"><DeleteOutlined />回收站</div>
        </router-link>
    </div>
</template>
<script lang='ts' setup>
import { selectCloudList } from '@/api/backend/cloud';
import { selectOpenstList } from '@/api/backend/devops/domain';
import { selectProjectList } from '@/api/backend/devops/project';
import router from '@/router';
import { menuStore } from '@/store/menu';
import { listToTreeCloud } from './utils/listToTreeCloud';
import { storeToRefs } from 'pinia';
import { onMounted, ref, watch } from 'vue';
import TopoMenu from "./topomenu.vue";
import cloudMenu from "./cloudMenu.vue";
import singleMenu from "./singleMenu.vue";
import { selectNavByCloud } from '@/api/backend/systems/menu';
import { filterRouter } from '@/utils/route';
import emiter from '@/utils/Bus';
import { userStore } from '@/store/user';
const whitelist = ['devops','config','storage','vmware'];
const menu_store = menuStore();
const user_store = userStore();
const {submenuId} = storeToRefs(menu_store);
const clouddata = ref([]);
const {menulist} = storeToRefs(user_store);
const listToMenu = (data,level) => {
    switch (level) {
        case "cloud":
            return data.map((item,index)=>{
                item.title = item.cloudName;
                item.key = item.id ? 'cloud_'+item.id : 'cloud_'+item.cloudId;
                item.children = [];
                return item;
            })
            break;
        case "domain":
            return data.map((item,index)=>{
                item.title = item.domainName;
                item.key = 'domain_'+item.id;
                item.children = [];
                return item;
            })
            break;
        case "project":
            return data.map((item,index)=>{
                item.title = item.projectName;
                item.key = 'project_'+item.id;
                item.children = [];
                return item;
            })
            break;
        default:
            break;
    }
    
}
const CloudListAPI = async (type, action) => {
    // if(type == 'cloud' && action != 'edit'){
        let res = await selectCloudList();
        return listToMenu(res.data,"cloud");
    // }else{
    //     if(menu_store.CloudMenus && menu_store.CloudMenus.length > 0){
    //         return listToMenu(menu_store.CloudMenus,"cloud");
    //     }else{
    //         let res = await selectCloudList();
    //         return listToMenu(res.data,"cloud");
    //     }

    // }
}
const MenusListAPI = async (key) => {
    let res = await selectNavByCloud({cloudId:key.split("_")[1]});
    let temp = [...listToTreeCloud(res.data)];
    menu_store.set_cloudMenu(temp);
    // const ansyRouter: any = filterRouter(temp);
    // ansyRouter.forEach((item: any) => {
    //     router.addRoute('layout',item);
    // });
    return temp;
}
const DomainListAPI = async (key) => {
    if(!menu_store.cloudmenu || menu_store.cloudmenu.length <= 0)
        MenusListAPI(key);
    let record = clouddata.value.find((item,index)=>item.key == key)
    if(record.children && record.children.length > 0){
        return record.children;
    }else{
        let res = await selectOpenstList({cloudId:record.key.split("_")[1]});
        record.children = listToMenu(res.data,"domain");
        return record.children;
    }
}
const ProjectListAPI = async ({key,cloudKey,type,action}) => {
    let children = await DomainListAPI(cloudKey)
    let record = children.find((it,ii)=>it.key == key)
    // if((record.children && record.children.length > 0) && (!type || (type == 'project' && action == 'edit'))){
    //     return record.children;
    // }else{
        let res = await selectProjectList({cloudId:cloudKey.split("_")[1],domainId:key.split("_")[1],enabled:1});
        record.children = listToMenu(res.data,"project");
        return record.children;
    // }
}
const MenusListSET = async ({key,domainKey,cloudKey,type,action}) => {
    let children = await ProjectListAPI({key:domainKey,cloudKey,type,action});
    let record = children.find((it,ii)=>it.key == key);
    if(record.children && record.children.length > 0){
    }else{
        if(menu_store.cloudmenu && menu_store.cloudmenu.length > 0){
            record.children = menu_store.cloudmenu;
        }
        else
            record.children = await MenusListAPI(cloudKey);
    }
    const ansyRouter: any = filterRouter(record.children);
    ansyRouter.forEach((item: any) => {
        router.addRoute('layout',item);
    });
    return record.children;
}
// const dealCloudOperation = (action) => {
//     switch (action) {
//         case 'add':
            
//             break;
    
//         default:
//             break;
//     }
// }
const setMenu = async ({type, action}={}) => {
    clouddata.value = await CloudListAPI(type, action);
    let thirdProjectId = router.currentRoute.value.query.thirdProjectId;
    let projectId = router.currentRoute.value.query.projectId;
    let domainId = router.currentRoute.value.query.domainId;
    let cloudId = router.currentRoute.value.query.cloudId;
    if(router.currentRoute.value.path.includes("/admin/devops/menu/")){
        if(projectId){
            ProjectListAPI({key:'domain_'+domainId,cloudKey:'cloud_'+cloudId})
        }else if(domainId){
            DomainListAPI('cloud_'+cloudId)
        }else if(router.currentRoute.value.path == '/admin/devops/menu/cloud'){
            if(!router.currentRoute.value.query.cloudId){
                // DomainListAPI('cloud_'+clouddata.value[0].key.split("_")[1])
                router.push({path:"/admin/devops/menu/cloud",query:{cloudId:clouddata.value[0].key.split("_")[1],title:clouddata.value[0].cloudName}})
            }else{
                // DomainListAPI('cloud_'+router.currentRoute.value.query.cloudId)
            }
        }
    }else if(whitelist.includes(submenuId.value)){
        if(thirdProjectId){
            MenusListSET({key:'project_'+projectId,domainKey:'domain_'+domainId,cloudKey:'cloud_'+cloudId})
        }
    }
}
watch(()=>router.currentRoute.value,(to,from)=>{
    if(router.currentRoute.value.path == '/admin/devops/menu/cloud'){
        if(!router.currentRoute.value.query.cloudId && menu_store.CloudMenus){
            router.push({path:"/admin/devops/menu/cloud",query:{cloudId:clouddata.value[0].key.split("_")[1],title:clouddata.value[0].cloudName}})
        }
    }
})
onMounted(() => {
    setMenu();
    emiter.on("setMenu",setMenu)
    emiter.on("DomainListAPI",DomainListAPI)
    emiter.on("ProjectListAPI",ProjectListAPI)
    emiter.on("MenusListSET",MenusListSET)
});
</script>
<style lang='scss' scoped>
::-webkit-scrollbar{height: 0;width: 0;}
.ant-menu-dark.ant-menu-inline,.ant-menu-root.ant-menu-inline{overflow-x: hidden;}
.lessheight{height: calc(100vh - 106px);}
.allheight{height: calc(100vh - 106px + 40px);overflow: auto;}
:deep(.ant-divider-horizontal){margin: 5px 0;}
:deep(.ant-menu-inline .ant-menu-submenu-title){margin: 0;}
.recyclery{
  position: absolute;
  left: 0;
  bottom: -42px;
  height: 40px;
  width: 100%;
  // margin-top: 3px;
  text-align: center;
  line-height: 40px;
  background-color: #fff;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 -5px 4px -2px rgb(0 0 0 / 6%);
  &::after{
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    border-right: 3px solid #1890ff;
    transform: scaleY(0.0001);
    opacity: 0;
    transition: transform 0.15s cubic-bezier(0.215, 0.61, 0.355, 1), opacity 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
    content: '';
  }
}
.selected_recyclery{
  background-color: #e6f7ff;
  &::after{
    transform: scaleY(1);
    opacity: 1;
    transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1), opacity 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
}
:deep(.ant-menu-submenu-open.ant-menu-submenu-inline > .ant-menu-submenu-title > .ant-menu-submenu-arrow) {transform: translateY(-50%);}
</style>