<template>
    <div class='display_stage'>
        <Top/>
        <router-view></router-view>
        <Bottom/>
    </div>
</template>
<script lang="ts">
import Top from './top.vue';
import Bottom from './bottom.vue';
export default {
    name: '',
    props: {},
    data() {
        return {};
    },
    computed: {},
    watch: {},
    methods: {},
    components: {Top,Bottom},
    filters: {}
};
</script>
<style lang='scss' scoped>
// .display_stage{
//     height: 100vh;
//     overflow-y: auto;
//     background: url(../../assets/beijing.png) no-repeat;
//     // background-size: cover;
// }
</style>