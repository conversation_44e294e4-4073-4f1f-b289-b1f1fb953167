import router from "./router";
import { getToken, removeToken, removeUsername, setUsername } from "@/utils/auth";
import { filterRouter } from "@/utils/route";
import { menuStore } from '@/store/menu'
import { userStore } from '@/store/user'
import { indexStore } from "@/store/index";
import { message } from "ant-design-vue";
import { selectSysconfig } from "./api/backend/public";
import { Receive } from "./utils/iframe/receive";
const whiteList = ["/admin/login","/","/index","/details","/demo","/login-limit"];
var isFirst = true;
var systemTitle = "UStack";
const setTopLogo = async () => {
  var link:any = document.querySelector("link[rel*='icon']") || document.createElement('link');
  link.type = 'image/x-icon';
  link.rel = 'shortcut icon';
  let res:any = await selectSysconfig();
  if(res.code == 0){
    if(res.data?.systemTitle){
      systemTitle = res.data.systemTitle;
    }
    indexStore().setConfigs(res.data);
    if(res.data?.faviconAttachmentId){
      let url = import.meta.env.VITE_BASE_API+'/sys/public/selectSystemConfigAttachmentById?attachmentId='+res.data.faviconAttachmentId+'&type=icon';
      link.href = url;
      document.getElementsByTagName('head')[0].appendChild(link);
    }
  }
}

setTopLogo()
const navRouter = (to, from, next) => {
  isFirst = false;
  // 获取菜单与权限
  menuStore().SelectNav()
  .then(async (res:any) => {
    console.log("navRouter",res.code)
    if (res.code === 0) {
      const user = JSON.parse(localStorage.getItem('user')) || {};
      let res1:any = await userStore().UserInfo();
      if(res1.code == 0){
        user.userInfo = res1.data;
        setUsername(res1.data.userName);
      }
      const menuList = res.data;
      const permissions = res.permissions;
      user.menulist = menuList;
      user.permissions = permissions;
      localStorage.setItem('user',JSON.stringify(user))
      // if(to.path != '/admin/devops/menu/project'){
        const ansyRouter: any = filterRouter(menuList.concat(menuStore().cloudmenu));
        const specialLayoutRoutes = ["云平台报表", "云平台管理", "网络拓扑 ", "网络管理"];
        ansyRouter.forEach((item: any) => {
          if (specialLayoutRoutes.includes(item.name)) {
            const newName = 'dynamic_' + item.name.trim();
            if (!router.hasRoute(newName)) {
                item.name = newName;
                router.addRoute('layout1', item);
            }
          } else {
            if (item.name === '工单管理') {
                const newName = 'dynamic_工单管理';
                if (!router.hasRoute(newName)) {
                    item.name = newName;
                    router.addRoute('layout', item);
                }
            } else {
                if (!router.hasRoute(item.name)) {
                  router.addRoute('layout', item);
                }
            }
          }
        });
      // }
      // 存在路由但不存在组件文件的不会404，可以通过component判断再addRoute就可以404
      router.addRoute({ path: "/:catchAll(.*)", redirect: "/404" });
      if (to.matched.length === 0) {
        if (to.path === "/admin") {
          next({ path: "/admin/index" });
        } else {
          next({ path: to.path,query:to.query });
        }
      } else {
        if (to.path === "/admin") {
          next({ path: "/admin/index" });
        } else {
          next();
        }
      }
    }
  })
  .catch(err => {
    // console.log(err);
    console.log("navRouter",err.code)
    next();
  });
}

router.beforeEach((to, from, next) => {
  if(isFirst){
    Receive(isFirst);
  }
  document.title = to.meta.title +'-'+systemTitle;
  const token = getToken() ? getToken() : (new URLSearchParams(window.location.search)).get('token');
  let searchpage = (new URLSearchParams(window.location.search)).get('page');
  if (token) {
    if (whiteList.indexOf(to.path) !== -1) {
      console.log("nav.....res....1");
      if(to.path == '/admin/login')
      next({ path: "/admin" });
      else
      next();
    } else {
      console.log("nav.....res....2");
      if(isFirst){
        navRouter(to, from, next)
      }else{
        if (to.matched.length === 0) {
          if (to.path === "/admin") {
            searchpage ? next({path: searchpage}) : next({ path: "/admin/index" });
          } else {
            next({ path: to.path,query:to.query });
          }
        } else {
          if (to.path === "/admin") {
            searchpage ? next({ path: searchpage }) : next({ path: "/admin/index" });
          } else {
            next();
          }
        }
      }
    }
  } else {
    // 清空
    // localStorage.clear();
    removeToken()
    removeUsername()
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      // if(to.path != '/admin/index'){
      //   message.error('请重新登录')
      // }
      next("/admin/login");
    }
  }
});

// 跳转后返回顶部
router.afterEach((to,from,next) => {
    window.scrollTo(0,0)
});