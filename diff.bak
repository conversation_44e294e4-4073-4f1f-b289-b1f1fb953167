diff --git a/src/layout/backend/index.vue b/src/layout/backend/index.vue
index fe5aaf3e..b45c5c67 100644
--- a/src/layout/backend/index.vue
+++ b/src/layout/backend/index.vue
@@ -25,11 +25,15 @@ const allLoading = ref(false)
 watch(()=>router.currentRoute.value,(to,from)=>{
   const metaInfo = router.currentRoute.meta || {}; // 获取当前路由的meta信息
     
+  console.log('lijin------开始加载该页面------eee111', metaInfo);
   if (!metaInfo.refreshed) {
       console.log('这是第一次加载该页面');
+      console.log('这是第一次加载该页面lijin----test');
+
       // emiter.emit('allLoading',true)
       // if(allLoading.value != true)
       // allLoading.value = true;
+
       // 设置已经刷新过标记
       metaInfo.refreshed = true;
   }
diff --git a/src/permission.ts b/src/permission.ts
index ea0cfc9b..1a418b57 100644
--- a/src/permission.ts
+++ b/src/permission.ts
@@ -1,134 +1,155 @@
-import router from "./router";
-import { getToken, removeToken, removeUsername, setUsername } from "@/utils/auth";
-import { filterRouter } from "@/utils/route";
-import { menuStore } from '@/store/menu'
-import { userStore } from '@/store/user'
-import { indexStore } from "@/store/index";
-import { message } from "ant-design-vue";
-import { selectSysconfig } from "./api/backend/public";
-import { Receive } from "./utils/iframe/receive";
-const whiteList = ["/admin/login","/","/index","/details","/demo","/login-limit"];
-var isFirst = true;
-var systemTitle = "UStack";
-const setTopLogo = async () => {
-  var link:any = document.querySelector("link[rel*='icon']") || document.createElement('link');
-  link.type = 'image/x-icon';
-  link.rel = 'shortcut icon';
-  let res:any = await selectSysconfig();
-  if(res.code == 0){
-    if(res.data?.systemTitle){
-      systemTitle = res.data.systemTitle;
-    }
-    indexStore().setConfigs(res.data);
-    if(res.data?.faviconAttachmentId){
-      let url = import.meta.env.VITE_BASE_API+'/sys/public/selectSystemConfigAttachmentById?attachmentId='+res.data.faviconAttachmentId+'&type=icon';
-      link.href = url;
-      document.getElementsByTagName('head')[0].appendChild(link);
-    }
-  }
-}
-
-setTopLogo()
-const navRouter = (to, from, next) => {
-  isFirst = false;
-  // 获取菜单与权限
-  menuStore().SelectNav()
-  .then(async (res:any) => {
-    console.log("navRouter",res.code)
-    if (res.code === 0) {
-      const user = JSON.parse(localStorage.getItem('user'))
-      let res1:any = await userStore().UserInfo();
-      if(res1.code == 0){
-        user.userInfo = res1.data;
-        setUsername(res1.data.userName);
-      }
-      const menuList = res.data;
-      const permissions = res.permissions;
-      user.menulist = menuList;
-      user.permissions = permissions;
-      localStorage.setItem('user',JSON.stringify(user))
-      // if(to.path != '/admin/devops/menu/project'){
-        const ansyRouter: any = filterRouter(menuList.concat(menuStore().cloudmenu));
-        ansyRouter.forEach((item: any) => {
-          console.log("item",item.path)
-          if(item.name == "云平台报表"){
-            router.addRoute('layout1',item);
-          }else
-            router.addRoute('layout',item);
-        });
-      // }
-      // 存在路由但不存在组件文件的不会404，可以通过component判断再addRoute就可以404
-      router.addRoute({ path: "/:catchAll(.*)", redirect: "/404" });
-      if (to.matched.length === 0) {
-        if (to.path === "/admin") {
-          next({ path: "/admin/index" });
-        } else {
-          next({ path: to.path,query:to.query });
-        }
-      } else {
-        if (to.path === "/admin") {
-          next({ path: "/admin/index" });
-        } else {
-          next();
-        }
-      }
-    }
-  })
-  .catch(err => {
-    next();
-  });
-}
-router.beforeEach((to, from, next) => {
-  if(isFirst){
-    Receive(isFirst);
-  }
-  document.title = to.meta.title +'-'+systemTitle;
-  const token = getToken() ? getToken() : (new URLSearchParams(window.location.search)).get('token');
-  let searchpage = (new URLSearchParams(window.location.search)).get('page');
-  if (token) {
-    if (whiteList.indexOf(to.path) !== -1) {
-      console.log("nav.....res....1");
-      if(to.path == '/admin/login')
-      next({ path: "/admin" });
-      else
-      next();
-    } else {
-      console.log("nav.....res....2");
-      if(isFirst){
-        navRouter(to, from, next)
-      }else{
-        if (to.matched.length === 0) {
-          if (to.path === "/admin") {
-            searchpage ? next({path: searchpage}) : next({ path: "/admin/index" });
-          } else {
-            next({ path: to.path,query:to.query });
-          }
-        } else {
-          if (to.path === "/admin") {
-            searchpage ? next({ path: searchpage }) : next({ path: "/admin/index" });
-          } else {
-            next();
-          }
-        }
-      }
-    }
-  } else {
-    // 清空
-    // localStorage.clear();
-    removeToken()
-    removeUsername()
-    if (whiteList.indexOf(to.path) !== -1) {
-      next();
-    } else {
-      // if(to.path != '/admin/index'){
-      //   message.error('请重新登录')
-      // }
-      next("/admin/login");
-    }
-  }
-});
-
-// 跳转后返回顶部
-router.afterEach((to,from,next) => {
-    window.scrollTo(0,0)
-});
\ No newline at end of file
+import router from "./router";
+import { getToken, removeToken, removeUsername, setUsername } from "@/utils/auth";
+import { filterRouter } from "@/utils/route";
+import { menuStore } from '@/store/menu'
+import { userStore } from '@/store/user'
+import { indexStore } from "@/store/index";
+import { message } from "ant-design-vue";
+import { selectSysconfig } from "./api/backend/public";
+import { Receive } from "./utils/iframe/receive";
+import emiter from "./utils/Bus";
+const whiteList = ["/admin/login","/","/index","/details","/demo","/login-limit"];
+var isFirst = true;
+var systemTitle = "UStack";
+const setTopLogo = async () => {
+  var link:any = document.querySelector("link[rel*='icon']") || document.createElement('link');
+  link.type = 'image/x-icon';
+  link.rel = 'shortcut icon';
+  let res:any = await selectSysconfig();
+  if(res.code == 0){
+    if(res.data?.systemTitle){
+      systemTitle = res.data.systemTitle;
+    }
+    indexStore().setConfigs(res.data);
+    if(res.data?.faviconAttachmentId){
+      let url = import.meta.env.VITE_BASE_API+'/sys/public/selectSystemConfigAttachmentById?attachmentId='+res.data.faviconAttachmentId+'&type=icon';
+      link.href = url;
+      document.getElementsByTagName('head')[0].appendChild(link);
+    }
+  }
+}
+
+setTopLogo()
+const navRouter = (to, from, next) => {
+  isFirst = false;
+  // 获取菜单与权限
+  menuStore().SelectNav()
+  .then(async (res:any) => {
+    console.log("navRouter",res.code)
+    console.log('lijin------开始加载该页面------jjj111', res.code);
+    if (res.code === 0) {
+      console.log('lijin------开始加载该页面------jjj333', localStorage.getItem('user'));
+      const user = JSON.parse(localStorage.getItem('user')) || {};
+      let res1:any = await userStore().UserInfo();
+      if(res1.code == 0){
+        user.userInfo = res1.data;
+        setUsername(res1.data.userName);
+      }
+      const menuList = res.data;
+      const permissions = res.permissions;
+      user.menulist = menuList;
+      user.permissions = permissions;
+      localStorage.setItem('user',JSON.stringify(user))
+      console.log('lijin------开始加载该页面------jjj222333', router);
+      // if(to.path != '/admin/devops/menu/project'){
+        const ansyRouter: any = filterRouter(menuList.concat(menuStore().cloudmenu));
+        const specialLayoutRoutes = ["云平台报表", "工单管理", "云平台管理", "网络拓扑 ", "网络管理"];
+        ansyRouter.forEach((item: any) => {
+          if (specialLayoutRoutes.includes(item.name)) {
+            const newName = 'dynamic_' + item.name.trim();
+            if (!router.hasRoute(newName)) {
+                item.name = newName;
+                router.addRoute('layout1', item);
+            }
+          } else {
+            if (!router.hasRoute(item.name)) {
+              router.addRoute('layout', item);
+            }
+          }
+        });
+      // }
+      // 存在路由但不存在组件文件的不会404，可以通过component判断再addRoute就可以404
+      // router.addRoute({ path: "/:catchAll(.*)", redirect: "/404" });
+      if (to.matched.length === 0) {
+        if (to.path === "/admin") {
+          next({ path: "/admin/index" });
+        } else {
+          next({ path: to.path,query:to.query });
+        }
+      } else {
+        if (to.path === "/admin") {
+          next({ path: "/admin/index" });
+        } else {
+          next();
+        }
+      }
+    }
+  })
+  .catch(err => {
+    console.log('lijin------开始加载该页面------jjj666', err);
+    next();
+  });
+}
+
+
+
+
+
+router.beforeEach((to, from, next) => {
+  if(isFirst){
+    Receive(isFirst);
+  }
+  document.title = to.meta.title +'-'+systemTitle;
+  const token = getToken() ? getToken() : (new URLSearchParams(window.location.search)).get('token');
+  let searchpage = (new URLSearchParams(window.location.search)).get('page');
+  if (token) {
+    console.log('lijin------开始加载该页面------hhh111', to.path);
+    if (whiteList.indexOf(to.path) !== -1) {
+      console.log("nav.....res....1");
+      console.log('lijin------开始加载该页面------hhh222');
+      if(to.path == '/admin/login')
+      next({ path: "/admin" });
+      else
+      next();
+    } else {
+      console.log("nav.....res....2");
+      console.log('lijin------开始加载该页面------hhh111');
+      if(isFirst){
+        navRouter(to, from, next)
+        console.log('lijin------开始加载该页面------hhh333');
+      }else{
+        if (to.matched.length === 0) {
+          if (to.path === "/admin") {
+            searchpage ? next({path: searchpage}) : next({ path: "/admin/index" });
+          } else {
+            next({ path: to.path,query:to.query });
+          }
+        } else {
+          if (to.path === "/admin") {
+            searchpage ? next({ path: searchpage }) : next({ path: "/admin/index" });
+          } else {
+            next();
+          }
+        }
+      }
+    }
+  } else {
+    // 清空
+    // localStorage.clear();
+    removeToken()
+    removeUsername()
+    if (whiteList.indexOf(to.path) !== -1) {
+      next();
+    } else {
+      // if(to.path != '/admin/index'){
+      //   message.error('请重新登录')
+      // }
+      next("/admin/login");
+    }
+  }
+});
+
+// 跳转后返回顶部
+router.afterEach((to,from,next) => {
+    window.scrollTo(0,0)
+});
diff --git a/src/utils/iframe/receive.ts b/src/utils/iframe/receive.ts
index f5922952..244c1a32 100644
--- a/src/utils/iframe/receive.ts
+++ b/src/utils/iframe/receive.ts
@@ -6,8 +6,10 @@ import { userStore } from "@/store/user";
 import { filterRouter } from "../route";
 import { layoutStore } from "@/store/layout";
 import $ from "jquery";
+import emiter from "../Bus";
 const layout_store = layoutStore(createPinia());
 const routerPath = (page) => {
+  console.log('lijin------开始加载该页面------cccbbb');
   switch (page) {
     case 'report':
       if(router.currentRoute.value.path != '/admin/report')
@@ -26,6 +28,7 @@ const setMenuAndUser = (isFirst, page) => {
     return menuStore().SelectNav()
   .then(async (res:any) => {
     console.log("setMenuAndUser",res.code)
+    console.log('lijin------开始加载该页面------cccaaa');
     isFirst = false;
     if (res.code === 0) {
       const user = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {};
@@ -41,13 +44,14 @@ const setMenuAndUser = (isFirst, page) => {
       localStorage.setItem('user',JSON.stringify(user))
       // if(to.path != '/admin/devops/menu/project'){
         const ansyRouter: any = filterRouter(menuList.concat(menuStore().cloudmenu));
+        const specialRoutes = ["云平台报表", "安全中心"];
         ansyRouter.forEach((item: any) => {
-        //   console.log("item",item.path)
-          if(item.name == "云平台报表"){
-            router.addRoute('layout1',item);
-          }
-          if(item.name == "安全中心"){
-            router.addRoute('layout1',item);
+          if (specialRoutes.includes(item.name)) {
+            const newName = 'dynamic_iframe_' + item.name;
+            if (!router.hasRoute(newName)) {
+                item.name = newName;
+                router.addRoute('layout1', item);
+            }
           }
         });
         routerPath(page)
@@ -59,6 +63,7 @@ export function Receive(isFirst){
   if(isFirst){
     window.addEventListener('message',e=>{
       // if(e.origin.includes('8088')){
+      console.log('lijin------开始加载该页面------cccddd');
         switch (e.data.page) {
           case 'report':
             setToken(e.data.token)
@@ -78,4 +83,4 @@ export function Receive(isFirst){
       // }
     },false)
   }
-}
\ No newline at end of file
+}
diff --git a/vite.config.js b/vite.config.js
index 4f77cf49..0466c1df 100644
--- a/vite.config.js
+++ b/vite.config.js
@@ -26,6 +26,7 @@ export default defineConfig({
   server: {
     // 访问本机ip时使用0.0.0.0，如果指定一个，则无法通过本地ip访问
     host: '0.0.0.0',
+    //host: '***********',
     port: 8080,
     https:false,
     hmr:{
@@ -35,7 +36,8 @@ export default defineConfig({
       "/api": {
         // target: "http://************:8886/api",
         // target: "http://***********:8886/api",
-        target: "http://************:8886/api",
+        // target: "http://************:8886/api",
+        target: "http://***********:8886/api",
         secure: false,
         ws: true,
         changeOrigin: true,
