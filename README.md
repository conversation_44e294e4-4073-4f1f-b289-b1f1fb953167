## UStack项目（V2，上一级、左多级菜单）

### 技术栈：vue3(主)/vue2、ant-design-vue、vite(打包)、pinia(状态仓库)

### 目录结构：
```
> ustack-web
>>  dist                        *打包后的静态资源包[打包命令使用yarn build/npm run build(生产)、yarn pre/npm run pre(测试)]*
>>>     assets                  *打包后的assets资源(包含字体图标，样式图片、样式文件等)*
>>>     favicon.ico             *打包后简单复制的favicon.ico(网站浏览器窗口logo)*
>>>     index.html              *打包后的服务器识别的上线项目入口文件*
>>  node_modules                *依赖的第三方模块包，git克隆、下载、fork后自行初始化(yarn/npm i)安装依赖后出现，提交git时不需要删除，.gitignore中已忽略*
>>  public                      *public 文件夹的静态资源都会被简单的复制，而不经过 webpack。需要通过绝对路径来引用它们*
>>>     favicon.ico             *网站浏览器窗口logo*
>>  src                         *源代码*
>>>     api                     *接口抛出*
>>>>            backend         *后台接口(前端概念)*
>>>>>               manage.ts   *axios的xhr接口封装(通过不同请求方式封装，按需引入)*
>>>>            frontend        *前台接口(前端概念)*
>>>     assets                  *存放小的静态资源(包含字体图标，样式图片、样式文件等)*
>>>>            ico             *其中的文件是管理员首页的云平台列表中云平台logo*
>>>>            iconfont        *字体图标*
>>>>            index           *首页静态文件*
>>>>            logo            *!涉及创建虚机的页面中不同云平台的logo(文件名和字典中云平台类型的数据项对应，请勿修改或谨慎添加修改删除)*
>>>>            vm              *vm类型云平台下静态资源*
>>>     common                  *全局通用设置(如国际化)*
>>>     components              *封装组件，实现特定功能*
>>>     layout                  *布局文件*
>>>     router                  *基本路由配置，静态路由*
>>>     store                   *状态库，本项目使用pinia，代替vuex*
>>>     styles                  *全局样式*
>>>     utils                   *工具函数*
>>>     views                   *vue视图页面*
>>>     App.vue                 *App.vue是项目的主组件，页面入口文件 ，所有页面都在App.vue下进行切换，app.vue负责构建定义及页面组件归集。*
>>>     main.ts                 *入口文件，主要作用是初始化vue实例并使用需要的插件，并且将内容渲染到主页面上*
>>>     permission.ts           *路由导航守卫，页面级、按钮级权限、动态路由在这里处理*
>>>     shims-vue.d.ts          *为了typescript做的适配定义文件，因为.vue文件不是一个常规的文件类型，TypeScript是不能理解vue文件是干嘛的，加这一段是是告诉 TypeScript，vue文件是这种类型的。没有这个文件，会发现 import 导入的所有.vue类型的文件都会报错。*
>>  static                      *不被打包的静态资源，会直接上传到服务器*
>>  .env.development            *环境变量(开发)*
>>  .env.preproduction          *环境变量(本项目中用于测试环境)*
>>  .env.production             *环境变量(生产)*
>>  .env.test                   *环境变量(本项目未使用，一般为测试)*
>>  .gitignore                  *git忽略的目录、文件*
>>  .prettierrc                 *用来配置格式化代码风格*
>>  babel.config.js             *主要作用是将ECMAScript2015+ 版本的代码,转换为向后兼容的JS语法,以便能够运行在当前和旧版本的浏览器或其它环境中。*
>>  index.html                  *项目入口文件*
>>  package-lock.json           *锁定安装模块的版本号，安装依赖后出现*
>>  package.json                *package.json是一个项目描述文件, 是一个严格的json文件,里面记录了当前项目的信息。比如: 项目名称、版本、作者、gitHub地址、当前项目依赖哪些第三方模块等。*
>>  README.md                    *说明文件*
>>  tsconfig.json               *ts配置文件，目录中存在 tsconfig.json 文件表明该目录是 TypeScript 项目的根目录。 tsconfig.json 文件指定了编译项目所需的根文件和编译器选项。*
>>  vite.config.js              *相当于vue.config.js，vue配置文件，vue.config.js 是一个可选的配置文件，如果项目的 (和 package.json 同级的) 根目录中存在这个文件，那么它会被 @vue/cli-service 自动加载。你也可以使用 package.json 中的 vue 字段，但是注意这种写法需要你严格遵照 JSON 的格式来写。*
```
### 菜单说明：
1、顶部：通过`/sys/menu/selectNav`获取，持久保存在localStorage：`user.menulist`；
2、点击顶部展开后的左侧：左侧又分为两种：①云平台管理②非云平台
①云平台管理下的左侧菜单：一级目录通过`/sys/syscloud/selectList`获取，二级目录通过`/sys/sysdomain/selectList`获取，三级目录通过`/sys/sysproject/selectList`获取，底层的菜单通过`/sys/menu/selectNavByCloud`获取

### 注意：
* 1、node版本>=v14.18.0(除v15.×)
* 2、setup时不使用mapActions、mapGetters等辅助函数，vue2建议使用
* 3、云平台下的新目录菜单开发新页面时，要在layout/top.vue中whitePath变量中加上新菜单的路由path
* 4、main分支(新需求，当前不能发布的功能)，v1分支(同步于测试环境，正式环境的yarn build时可在v1版本上根据额外要求修改后打包)
* 5、menuId用中文是为了统一动态添加和静态配置的路由，动态的路由menuId是确定的，静态的无法规定，只能用一个统一的唯一标识（只用于前台路由的区分，不用于接口的传参）
* 6、ustack-web/src/views/backend/sms/modal/osd/preview.vue 本页请勿格式化文档
* 7、a-modal务必携带centered属性，否则全局loading无法正好在modal的中心。
* 8、该项目前端无请求时长限制，出现“网络请求超时！”相关提示，都是由于后端接口504或501并且响应头的statusText字段为“timeout”所导致。
* 9、由于全局的菜单和路由都是从接口获取的，没有统一的识别标识，都是通过前端再次组装处理，使当前路由对应当前菜单并使其选中效果(请注意注释)