covarage: true
#baseUrl: http://localhost:8081
#baseUrl: http://***********:8088
baseUrl: http://***********:9999
env:
  switchToAdminProject: false
  username: admin
  password: A1A0vWTzbVQfrtAiPp3SqChJDgDyYGMjVtB5lgul
  usernameAdmin: e2e-admin
  passwordAdmin: passW0rdY_
  imageName: cirros
  imageType: Others
  imageFile: null
  imageDownloadUrl: http://download.cirros-cloud.net/0.5.1/cirros-0.5.1-x86_64-disk.img
  imageCanChangePassword: false
  extensions:
    - cinder::backup
    - neutron::qos
    #- neutron::vpn
    - neutron::port-forwarding
    #- octavia
    #- heat
    #- ironic
    #- swift
    #- zun
    #- manila
testFiles:
  - pages/login.spec.js
  - pages/error.spec.js
  # identity
  - pages/identity/role.spec.js
  - pages/identity/domain.spec.js
  - pages/identity/user.spec.js
  - pages/identity/project.spec.js
  - pages/identity/user-group.spec.js
  # storage
  - pages/storage/qos.spec.js
  - pages/storage/volume-type.spec.js
  - pages/storage/volume.spec.js
  - pages/storage/backup.spec.js
  - pages/storage/snapshot.spec.js
  - pages/storage/storage.spec.js
  - pages/storage/swift.spec.js
  # network
  - pages/network/router.spec.js
  - pages/network/network.spec.js
  - pages/network/port.spec.js
  - pages/network/qos-policy.spec.js
  - pages/network/floatingip.spec.js
  - pages/network/security-group.spec.js
  - pages/network/vpn.spec.js
  - pages/network/lb.spec.js
  - pages/network/topology.spec.js
  # compute
  - pages/compute/image.spec.js
  - pages/compute/server-group.spec.js
  - pages/compute/keypair.spec.js
  - pages/compute/instance.spec.js
  - pages/compute/ironic.spec.js
  - pages/compute/aggregate.spec.js
  - pages/compute/hypervisor.spec.js
  - pages/compute/baremetal.spec.js
  - pages/compute/flavor.spec.js
  # - pages/management/recycle-bin.spec.js
  # configuration
  - pages/configuration/metadata.spec.js
  - pages/configuration/system.spec.js
  - pages/configuration/setting.spec.js
  # heat
  - pages/heat/stack.spec.js
  # zun
  - pages/zun/container.spec.js
  - pages/zun/capsule.spec.js
  # manila
  - pages/manila/share-type.spec.js
