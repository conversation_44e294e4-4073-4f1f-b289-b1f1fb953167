import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  base: '/',
  plugins: [
    vue(),
  ],
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false,
        drop_debugger: true
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js'
    }
  },
  server: {
    // 访问本机ip时使用0.0.0.0，如果指定一个，则无法通过本地ip访问
    host: '0.0.0.0',
    //host: '***********',
    port: 8080,
    https:false,
    hmr:{
      overlay:false
    },
    proxy: {
      "/api": {
        // target: "http://************:8886/api",
        // target: "http://***********:8886/api",
        // target: "http://************:8886/api",
        target: "http://***********:8886/api",
        secure: false,
        ws: true,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      // 配置多个代理(配置一个 proxy: 'http://localhost:4000' )
      "/api/ceph": {
        target: "http://************:8888/api/ceph",
        secure: false,
        ws: false,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/ceph/, '')
      },
      "/other": {
        target: "<other_url>"
      }
    }
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: {
          modifyVars: {
            'primary-color': '#1DA57A',
            'link-color': '#1DA57A',
            'border-radius-base': '2px',
          },
          javascriptEnabled: true,
        },
      },
    },
  },
})
