{"name": "ustack", "version": "1.0.3", "description": "UStack is an independent laaS cloud management platform.", "keywords": ["ustack", "uos", "vue", "vite", "ant design vue"], "repository": "https://gitlabbj.uniontech.com/webforserver/ustack-web.git", "bugs": {"url": "https://cooperation.uniontech.com/embed/view/039b4b63-6a69-432c-9725-bac1ff0d250f/659cf2ec3d37f726158f396f/659cf2ec3d37f726158f3973"}, "private": true, "scripts": {"dev": "vite", "serve": "vite preview", "build": "node --max_old_space_size=4096 node_modules/vite/bin/vite.js build", "test": "vite build --mode test", "pre": "vite build --mode preproduction"}, "dependencies": {"@ant-design/icons-vue": "^5.1.7", "@antv/g6": "4.8.21", "@intlify/core-base": "^9.1.9", "@intlify/shared": "^9.1.9", "@intlify/vue-devtools": "^9.1.9", "@logicflow/core": "^1.2.1", "@logicflow/extension": "^1.2.1", "@types/echarts": "^4.9.0", "@vue/devtools-api": "^6.0.0-beta.21.1", "@vueuse/core": "^9.1.1", "ant-design-vue": "2.2.8", "axios": "^0.21.0", "core-js": "^3.6.5", "d3": "6.7.0", "dagre-d3": "^0.6.4", "echarts": "^5.0.1", "echarts-liquidfill": "^3.1.0", "iconfont-tools": "^1.7.13", "jquery": "^3.6.3", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "js-sha256": "^0.9.0", "json5": "^2.2.3", "lodash": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.29.4", "no-vue3-cron": "^1.0.4", "pinia": "^2.0.18", "pinia-plugin-persistedstate": "^2.1.1", "qs": "^6.11.0", "splitpanes": "^3.1.5", "swiper": "^7.4.1", "terser": "^5.31.4", "vis-network": "^9.1.2", "vue": "^3.0.0", "vue-core-video-player": "^0.2.0", "vue-cropper": "^1.1.1", "vue-i18n": "^9.1.9", "vue-pdf-embed": "^1.1.5", "vue-right-click-menu-next": "^1.1.1", "vue-router": "^4.0.0-0", "vue-wangeditor": "^1.3.10", "vue3-cron-antd": "^1.1.1", "vue3-echarts": "^1.0.3", "vue3-pdfjs": "^0.1.6", "vue3-video-play": "^1.3.1-beta.6", "wangeditor": "^4.7.11", "xterm": "^5.3.0", "xterm-addon-attach": "^0.9.0", "xterm-addon-fit": "^0.8.0"}, "devDependencies": {"@types/jquery": "^3.5.16", "@types/js-cookie": "^2.2.6", "@types/node": "^18.6.5", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vitejs/plugin-vue": "^3.0.1", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^6.1.1", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.0.0-0", "prettier": "^1.19.1", "sass": "^1.26.5", "sass-loader": "^8.0.2", "style-loader": "^2.0.0", "typescript": "^4.7.4", "unplugin-vue-components": "^0.25.1", "vite": "^3.0.4", "vue-tsc": "^0.39.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/typescript/recommended", "@vue/prettier", "@vue/prettier/@typescript-eslint"], "parserOptions": {"ecmaVersion": 2022}, "rules": {"@typescript-eslint/no-explicit-any": 0}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}